import Tool from '@/common/tool';
import { getOSSToken, uploadTaskMap } from './index';

// 模块加载状态管理
let OSS = null;
let isLoading = false;
let loadPromise = null;

// 内部加载函数
const ensureOSS = async () => {
    // 如果已经加载过，直接返回
    if (OSS) {
        return OSS;
    }

    // 如果正在加载中，返回已存在的 Promise
    if (isLoading && loadPromise) {
        return loadPromise;
    }

    // 开始新的加载
    isLoading = true;
    loadPromise = import(/* webpackPrefetch: true */ 'ali-oss').then(module => {
        OSS = module.default;
        isLoading = false;
        return OSS;
    }).catch(error => {
        isLoading = false;
        loadPromise = null;
        throw error;
    });

    return loadPromise;
};

// 处理分片上传
function handleMultipartUpload({ ossClient, file, filePath, progress, success, error }) {
    return ossClient.multipartUpload(filePath, file, {
        parallel: 2,
        partSize: 10 * 1024 * 1024,
        progress: (percentage, cpt) => {
            if (progress) {
                progress(percentage, cpt);
            }
        },
        headers: {
            'Cache-Control': 'public, max-age=315360000',
            'Content-Disposition': 'attachment'
        }
    }).then(success).catch(error);
}

// 处理同桶拷贝
function handleCopy({ ossClient, copyOssPath, filePath, progress, success, error }) {
    const uploadId = Tool.genID(11); // 生成唯一的上传ID
    if (progress) {
        progress(0.1, { uploadId });
    } // 拷贝进度1%

    ossClient.copy(filePath, copyOssPath, {
        headers: {
            'Content-Disposition': 'attachment'
        }
    }).then(res => {
        if (progress) {
            progress(1, { uploadId });
        } // 拷贝完成，进度100%
        if (success) {
            success(res);
        }
    }).catch(err => {
        if (progress) {
            progress(0, { uploadId });
        } // 拷贝失败，进度重置为0
        if (error) {
            error(err);
        }
    });
}
// 处理文件上传或拷贝
function handleUploadOrCopy({ ossClient, file, filePath, copyOssPath, progress, success, error }) {
    if (copyOssPath) {
        return handleCopy({ ossClient, copyOssPath, filePath, progress, success, error });
    }
    return handleMultipartUpload({ ossClient, file, filePath, progress, success, error });
}
// 进度处理
function handleProgress({ percentage, cpt, callback, ossClient, uploadId }) {
    let progress = Math.round(percentage * 100)
    if(!uploadTaskMap.hasOwnProperty(uploadId)){
        uploadTaskMap[uploadId] = {}
    }
    if (cpt) {
        uploadTaskMap[uploadId].checkPoint = cpt
    }
    if(ossClient){
        uploadTaskMap[uploadId].ossClient =  ossClient
    }
    uploadTaskMap[uploadId].callback = callback
    uploadTaskMap[uploadId].percentage = percentage
    uploadTaskMap[uploadId].uploading = true
    console.log("progress",uploadId,progress)
    callback && callback("progress", progress,uploadId);
}

// 上传成功处理
const handleUploadSuccess = (res, callback, uploadId) => {
    const url = res.res.requestUrls[0];
    if (callback) {
        callback("complete", url);
    }
    if (uploadTaskMap[uploadId] && uploadTaskMap[uploadId].timer) {
        clearTimeout(uploadTaskMap[uploadId].timer);
        uploadTaskMap[uploadId].timer = null
    }
    delete uploadTaskMap[uploadId];
};

// 上传错误处理
const handleUploadError = ({ e, callback, uploadId }) => {
    console.log("[event] uploadFile -- error",e,uploadTaskMap,uploadId);
    if(!uploadTaskMap[uploadId]){
        return
    }
    if(e&&e.name==='timeout'){
        uploadTaskMap[uploadId].error = true
    }else if(uploadTaskMap[uploadId].pause){
        e.name = 'pause'
    }else{
        uploadTaskMap[uploadId].error = true
    }
    uploadTaskMap[uploadId].uploading = false

    if(uploadTaskMap[uploadId].timer){
        clearTimeout(uploadTaskMap[uploadId].timer)
        uploadTaskMap[uploadId].timer = null
        uploadTaskMap[uploadId].isTimeout = false
    }
    callback && callback("error", e, uploadId);
};
// 重新上传
const handleReUploadFile = ({ ossClient, file, filePath, checkPoint }, { progress, success, error }) => {
    ossClient.multipartUpload(filePath, file, {
        checkpoint: checkPoint,
        progress: (percentage, cpt) => {
            if (progress) {
                progress(percentage, cpt);
            }
        }
    }).then(success).catch(error);
}


const handleTimeoutUpload = (uploadId) => {
    let ossInfo = uploadTaskMap[uploadId];
    if (ossInfo) {
        const ossClient = ossInfo.ossClient;
        handleUploadError({ e: { name: 'timeout' }, callback: ossInfo.callback, uploadId });

        if (ossInfo.timer) {
            clearTimeout(ossInfo.timer);
            ossInfo.timer = null;
        }
    }
}
const handleAliCancelUpload = ({ ossClient, filePath, uploadId })=> {// 取消上传
    if (!ossClient) {
        return;
    }
    const ossInfo = uploadTaskMap[uploadId];
    ossClient.cancel();
    ossInfo.callback && ossInfo.callback("cancel", {name:'cancel'}, uploadId);
    ossClient.listParts(filePath, uploadId, (err, data) => {
        if (err) {
            return console.error('Failed to list parts:', err);
        }
        if (data.parts && data.parts.length > 0) {
            const deleteParts = data.parts.map(part => ({ partNumber: part.partNumber, etag: part.etag }));
            ossClient.abortMultipartUpload(filePath, uploadId, deleteParts, abortErr => {
                if (abortErr) {
                    console.error('Failed to abort multipart upload and delete parts:', abortErr);
                } else {
                    console.log('Multipart upload aborted and parts deleted successfully.');
                }
            });
        }
    });
}
// 获取 OSS 客户端
export async function AliGetOssClient(bucket, fileName) {
    try {
        const data = await getOSSToken(bucket, fileName);
        return new OSS({
            bucket: data.bucket,
            region: data.region,
            accessKeyId: data.accessKeyId,
            accessKeySecret: data.accessKeySecret,
            stsToken: data.stsToken,
            timeout: 60 * 60 * 1000,
            refreshSTSToken: async () => {
                const info = await getOSSToken(bucket, fileName);
                return {
                    accessKeyId: info.accessKeyId,
                    accessKeySecret: info.accessKeySecret,
                    stsToken: info.stsToken
                };
            },
            refreshSTSTokenInterval: new Date(data.expiresAt) - new Date() - 1000 * 60
        });
    } catch (error) {
        throw new Error('获取OSS客户端失败: ' + error.message);
    }
}
// 暂停上传
export const AliPauseUpload =Tool.debounce(function(uploadId){
    const ossInfo = uploadTaskMap[uploadId];
    if (ossInfo) {
        ossInfo.pause = true;
        handleUploadError({ e: { name: 'pause' }, callback: ossInfo.callback, uploadId });
        if (ossInfo.ossClient) {
            ossInfo.ossClient.cancel();
        }
        if (ossInfo.timer) {
            clearTimeout(ossInfo.timer);
            ossInfo.timer = null;
        }
    }
},500,true)

// 恢复上传
export const AliResumeUpload = Tool.debounce( async function({ uploadId }){
    const ossInfo = uploadTaskMap[uploadId];
    if (!ossInfo) {
        return;
    }
    if(ossInfo.isReloading){
        return
    }
    ossInfo.isReloading = true
    clearTimeout(ossInfo.timer);
    ossInfo.isTimeout = false;
    ossInfo.timer = setTimeout(() => handleTimeoutUpload(uploadId), ossInfo.timeout);

    try {
        ossInfo.callback && ossInfo.callback("progress", Math.round(ossInfo.percentage * 100),uploadId);
        const filePathList = ossInfo.copyOssPath ? [ossInfo.copyOssPath, ossInfo.filePath] : ossInfo.filePath;
        const ossClient = await AliGetOssClient(ossInfo.bucketName, filePathList);
        ossInfo.ossClient = ossClient;

        handleReUploadFile({ ossClient, file: ossInfo.checkPoint.file, filePath: ossInfo.filePath, checkPoint: ossInfo.checkPoint }, {
            progress: (percentage, cpt) => {
                handleProgress({ percentage, cpt, callback: ossInfo.callback, ossClient, uploadId })
            },
            success: (res) =>{
                ossInfo.isReloading = false
                handleUploadSuccess(res, ossInfo.callback, uploadId)
            } ,
            error: (e) =>{
                ossInfo.isReloading = false
                handleUploadError({ e, callback: ossInfo.callback, uploadId })
            }
        });

    } catch (error) {
        console.error(error);
    }
},500,true)

// 文件上传
export function AliUploadFile({ bucket = '', file = null, filePath = '', callback = null, timeout = 30000, copyOssPath = '' } = {}) {
    return new Promise(async (resolve, reject) => {
        try {
            // 只加载 OSS
            const OSS = await ensureOSS();

            const systemConfig = window.vm.$store.state.systemConfig;
            const bucketName = bucket || systemConfig.serverInfo.oss_attachment_server.bucket;
            let ossClient = null
            let filePathList = copyOssPath ? [copyOssPath,filePath] :filePath
            try {
                ossClient = await AliGetOssClient(bucketName, filePathList);
            } catch (error) {
                console.error(error,'AliGetOssClient');
                let errorName = {name:'tokenError'}
                if(copyOssPath){
                    errorName = {name:'timeOut'}
                }
                callback && callback("error", errorName);
                return
            }
            let uploadId = '';

            handleUploadOrCopy({
                ossClient, file, filePath, copyOssPath,
                progress: async (percentage, cpt) => {
                    if (cpt && cpt.uploadId && uploadTaskMap[cpt.uploadId] && uploadTaskMap[cpt.uploadId].isResumeUploading) {
                        return
                    }
                    if(cpt&&cpt.uploadId){
                        uploadId = cpt.uploadId
                        if(uploadTaskMap[cpt.uploadId]){
                            //如何之前是超时的，则需要检测一次当前网络状态
                            if(uploadTaskMap[cpt.uploadId].isTimeout){
                                if(Tool.checkAppClient('App')){
                                    try {
                                        const networkType = await Tool.checkNetworkType()
                                        if(networkType !== 'wifi'){
                                            AliPauseUpload(uploadId)
                                            return
                                        }
                                    } catch (error) {
                                        console.error('error')
                                    }

                                }

                            }
                        }
                    }
                    handleProgress({ percentage, cpt, callback, ossClient, uploadId });
                    if(cpt&&cpt.uploadId){
                        uploadId = cpt.uploadId
                        if(uploadTaskMap[cpt.uploadId]){
                            uploadTaskMap[cpt.uploadId].timeout = timeout
                            uploadTaskMap[cpt.uploadId].bucketName = bucketName
                            uploadTaskMap[cpt.uploadId].filePath = filePath
                            uploadTaskMap[cpt.uploadId].copyOssPath = copyOssPath
                            uploadTaskMap[cpt.uploadId].pause = false
                        }
                        if(uploadTaskMap[cpt.uploadId].timer){
                            clearTimeout(uploadTaskMap[cpt.uploadId].timer)
                            uploadTaskMap[cpt.uploadId].timer = null
                            uploadTaskMap[cpt.uploadId].isTimeout = false
                        }
                        uploadTaskMap[cpt.uploadId].timer = setTimeout(() => {
                            uploadTaskMap[cpt.uploadId].isTimeout = true
                            handleTimeoutUpload(uploadId)
                        }, timeout);
                    }
                },
                success: (res) => handleUploadSuccess(res, callback, uploadId),
                error: (e) => handleUploadError({ e, callback, uploadId })
            });

            resolve(ossClient);
        } catch (error) {
            if (callback) {
                callback("error", { name: 'tokenError' });
            }
            reject(error);
        }
    });
}
export function AliCancelUpload(uploadId){
    // 取消上传
    let ossInfo= uploadTaskMap[uploadId]

    if(ossInfo){
        let filePath = ossInfo.filePath
        const ossClient = ossInfo.ossClient
        handleAliCancelUpload({ossClient,filePath,uploadId})
        if(ossInfo.timer){
            clearTimeout(ossInfo.timer)
            ossInfo.timer = null
        }
        delete uploadTaskMap[uploadId]
    }
}
export function AliIsFileExist  ({
    ossClient = null,
    bucket = '',
    filePath='',
}={}) {
    return new Promise(async (resolve,reject)=>{
        try {
            let client = ossClient
            if(!client){
                const systemConfig = window.vm.$store.state.systemConfig;
                const bucketName = bucket || systemConfig.serverInfo.oss_attachment_server.bucket;
                client = await AliGetOssClient(bucketName, filePath);
            }

            await client.head(filePath,{});
            resolve(true);
            console.log('isFileExist',true)
        } catch (error) {
            resolve(false);
            console.log('isFileExist',false,error)
        }
    })
};
