<template>
    <div class="exam-project-container" v-loading="loading">
        <div class="exam-list" v-if="examProjects.length > 0">
            <!-- 修改 v-for 循环以适应新的列表项结构 -->
            <div v-for="project in examProjects" :key="project.id" class="exam-item" @click="enterExamDetail(project)">
                <div class="item-icon">
                    <i class="el-icon-files"></i>
                    <!-- 使用Element UI标准图标作为占位符 -->
                </div>
                <div class="item-content">
                    <div class="title-line-wrapper">
                        <div class="item-title">{{ project.title }}</div>
                        <div class="item-type">
                            <el-tag type="primary" effect="plain" size="small">
                                {{ getTestTypeText(project.testType) }}
                            </el-tag>
                        </div>
                    </div>
                </div>
                <div class="item-status">
                    <template v-if="project.testType === SMART_TECH_TRAINING_TEST_TYPE.ONLINE_QUIZ">
                        <!-- 在线答题类型只显示进度 -->
                        <div class="progress-info">
                            <div class="progress-label">进度：</div>
                            <el-progress
                                type="circle"
                                :percentage="project.progress"
                                :width="50"
                                :stroke-width="5"
                                :color="getOnlineQuizProgressColor(project.progress)"
                                :show-text="false"
                            ></el-progress>
                            <div class="progress-value">{{ project.progress }}%</div>
                        </div>
                    </template>
                    <template v-else>
                        <!-- 附件上传类型只显示通过/不通过 -->
                        <div class="result-info">
                            <div class="result-label">结果：</div>
                            <div
                                class="result-value-tag"
                                :class="{ 'result-value-tag--completed': project.status === 'completed' }"
                            >
                                {{ project.status === "completed" ? "通过" : "未通过" }}
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- 无数据提示 -->
        <div class="empty-state" v-else>
            <i class="el-icon-document"></i>
            <p>暂无考核项目</p>
        </div>
        <router-view></router-view>
    </div>
</template>

<script>
import base from "../../lib/base";
import { SMART_TECH_TRAINING_TEST_TYPE, SMART_TECH_TRAINING_ROLE } from "../../lib/constants";
import Tool from "@/common/tool";
import service from "../../service/service";
export default {
    mixins: [base],
    name: "ExamProject",
    data() {
        return {
            loading: false,
            SMART_TECH_TRAINING_TEST_TYPE: SMART_TECH_TRAINING_TEST_TYPE,
            userRole: '',
            trainingId: '',
            examProjects: [],
        };
    },
    created() {
        this.userRole = this.$route.params.role;
        this.trainingId = this.$route.params.trainingId;
        this.getTestListByTrainingId();
    },
    methods: {
        // 获取状态文本 (旧方法，将被移除或注释)
        /* getStatusText(project) {
            if (project.status === 'completed') {
                return '已通过';
            } else if (project.status === 'in-progress') {
                return project.progress + '%';
            } else {
                return '未通过';
            }
        }, */
        getStatusColor(status) {
            if (status === "completed") {
                return "#00c59d"; // 绿色，对应图片
            } else if (status === "in-progress") {
                return "#409EFF"; // 蓝色，对应图片
            }
            // 对于 'failed' 或其他状态，el-progress 不会显示，所以颜色不重要，但可以设一个默认
            return "#909399";
        },
        getTestTypeText(type) {
            if (type === SMART_TECH_TRAINING_TEST_TYPE.ATTACHMENT_UPLOAD) {
                return "附件上传";
            } else if (type === SMART_TECH_TRAINING_TEST_TYPE.ONLINE_QUIZ) {
                return "在线答题";
            }
            return ""; // 或者其他合适的默认返回值
        },
        // 点击卡片进入相应考核项目的方法
        enterExamDetail(project) {
            // 根据项目类型跳转到不同的考核页面
            console.log("进入考核项目:", project);
            if(this.userRole === SMART_TECH_TRAINING_ROLE.STUDENT){
                if(project.testType === SMART_TECH_TRAINING_TEST_TYPE.ATTACHMENT_UPLOAD){
                    Tool.loadModuleRouter({
                        name:'SmartTechTrainingExamProject_UploadTestOverview',
                        params:{
                            ...this.$route.params,
                            testId:project.id
                        }
                    });
                }else if(project.testType === SMART_TECH_TRAINING_TEST_TYPE.ONLINE_QUIZ){
                    Tool.loadModuleRouter({
                        name:'SmartTechTrainingExamProject_OnlineTestOverview',
                        params:{
                            ...this.$route.params,
                            testId:project.id
                        }
                    });
                }
            }else{
                this.$router.push(this.$route.fullPath + '/correcting_test_overview');
            }

            // 可以根据项目ID或类型跳转到不同页面
            // this.$router.push({ path: `/smartTechTraining/exam/${project.id}` });
        },
        getOnlineQuizProgressColor(progress) {
            if (progress === 100) {
                return "#67C23A"; // 绿色 (Element UI Success)
            } else if (progress >= 20 && progress < 100) {
                return "#E6A23C"; // 黄色 (Element UI Warning)
            } else {
                // progress < 20 (包括 0)
                return "#F56C6C"; // 红色 (Element UI Danger)
            }
        },
        getTestListByTrainingId(){
            this.loading = true;
            service.getTestListByTrainingId({
                trainingID:this.$route.params.trainingId
            }).then(res=>{
                this.loading = false;
                console.log(res,'res');
                // 确保返回数据有效且包含testList数组
                if(res.data.error_code === 0){
                    const testList = res.data.data?.testList;
                    if (Array.isArray(testList)) {
                    // 将API返回的数据适配到examProjects
                        this.examProjects = testList.map(item => {
                            let status;
                            // 明确设置了isPass的情况
                            if (item.isPass === true) {
                                status = "completed";
                            } else if (item.isPass === false) {
                                status = "failed";
                            } else {
                                // 对于在线答题，如果isPass未明确，且进度未达到100，则认为是进行中
                                if (item.type === SMART_TECH_TRAINING_TEST_TYPE.ONLINE_QUIZ && item.progress < 100) {
                                    status = "in-progress";
                                } else {
                                    // 否则根据progress判断，或者默认为failed
                                    status = item.progress === 100 ? "completed" : "failed";
                                }
                            }

                            return {
                                id: item.testID || item._id, // 尝试使用备选字段
                                title: item.title || '',
                                progress: typeof item.progress === 'number' ? item.progress : 0,
                                status: status,
                                testType: item.type || '',
                            };
                        });
                        console.log(this.examProjects,'this.examProjects');
                    } else {
                        // 如果没有有效数据，将examProjects设为空数组
                        this.examProjects = [];
                    }
                }
            }).catch(err=>{
                this.loading = false;
                console.log(err,'err');
                this.examProjects = []; // 错误时确保examProjects为空
            });
        }
    },

};
</script>

<style lang="scss" scoped>
.exam-project-container {
    padding: 20px;
    background-color: #f7f8fa; /* 假设图片背景色偏白或浅灰 */
    position: relative;
    height: 100%;

    .exam-list {
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05); /* 细微阴影 */

        .exam-item {
            display: flex;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #eeeeee; /* 分割线 */
            cursor: pointer;
            transition: background-color 0.2s;

            &:last-child {
                border-bottom: none; /* 最后一个项目无下边框 */
            }

            &:hover {
                background-color: #f9f9f9;
            }

            .item-icon {
                margin-right: 20px;
                font-size: 32px; /* 调整图标大小 */
                color: #cccccc; /* 占位图标颜色 */
                flex-shrink: 0;
            }

            .item-content {
                flex-grow: 1;
                margin-right: 20px;

                .title-line-wrapper {
                    display: flex;
                    align-items: center;
                    margin-bottom: 6px;

                    .item-title {
                        font-size: 16px;
                        font-weight: 500;
                        color: #333333;
                        line-height: 1.4;
                        margin-right: 8px;
                    }

                    .item-type {
                        line-height: 0;
                    }
                }
            }

            .item-status {
                display: flex;
                align-items: center;
                flex-shrink: 0;
                width: 150px; /* Changed from min-width: 120px */
                justify-content: flex-start; /* 从左开始对齐内容 */

                .progress-info {
                    display: flex;
                    align-items: center;

                    .progress-label {
                        font-size: 14px;
                        color: #555555;
                        margin-right: 8px;
                        width: 45px; /* Added fixed width */
                        flex-shrink: 0; /* Added flex-shrink */
                        text-align: left; /* Explicitly set text-align */
                    }

                    .progress-value {
                        font-size: 14px;
                        font-weight: bold;
                        color: #333333;
                    }
                }

                /* el-progress 已经在模板中设置了大小, 这里可以微调 */
                .el-progress {
                    margin-right: 8px;
                }

                .result-info {
                    display: flex;
                    align-items: center;

                    .result-label {
                        font-size: 14px;
                        color: #555555;
                        margin-right: 8px;
                        width: 45px; /* Added fixed width */
                        flex-shrink: 0; /* Added flex-shrink */
                        text-align: left; /* Explicitly set text-align */
                    }

                    .result-value-tag {
                        background-color: #ffeaea;
                        color: #f56c6c;
                        padding: 4px 10px;
                        border-radius: 12px;
                        font-size: 13px;
                        font-weight: 500;

                        &.result-value-tag--completed {
                            background-color: #f0f9eb;
                            color: #67c23a;
                        }
                    }
                }
            }
        }
    }

    .page-title {
        margin-top: 0;
        margin-bottom: 20px;
        color: #333;
        font-size: 22px;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 0;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

        i {
            font-size: 48px;
            color: #dcdfe6;
            margin-bottom: 16px;
        }

        p {
            color: #909399;
            font-size: 16px;
        }
    }
}
</style>
