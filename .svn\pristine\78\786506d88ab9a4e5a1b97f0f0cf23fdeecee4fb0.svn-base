<template>
    <div class="practiceAnswerAction_container">
        <div class="custom_header">
            <div class="back_btn" @click="back">
                <i class="el-icon-arrow-left"></i>
                <span>{{ lang.back_button }}</span>
            </div>
        </div>
        <div class="custom_body" ref="customBody">
            <div class="split-container">
                <div class="left-panel" :style="{ width: leftPanelWidth + '%' }" ref="leftPanel">
                    <QuestionDisplay :clinicalData="questionData" class="question_display" :imageRow="2" />
                </div>

                <div class="splitter" @mousedown="startResize" :class="{ dragging: isDragging }"></div>

                <div class="right-panel">
                    <div class="right-content">
                        <div class="answer_action_container">
                            <div class="input_container" v-if="!hasSubmit">
                                <div class="question_tips">
                                    <p class="tips_content">{{ questionData.questionTips }}</p>
                                </div>
                                <div class="input_label">{{ lang.please_answer_here }}:</div>
                                <el-input
                                    type="textarea"
                                    v-model="userAnswer"
                                    :placeholder="questionData.questionTips"
                                    :rows="8"
                                    resize="none"
                                    maxlength="1000"
                                    show-word-limit
                                ></el-input>
                            </div>
                            <MyAnswerDisplay :answer="answerData" v-if="hasSubmit" class="my_answer_display" />

                            <div v-if="hasAnswer || hasSubmit" class="answering_result_container">
                                <template v-if="hasSubmit && !hasAnswer">
                                    <p class="answering_tips" v-if="!hasError">
                                        {{ lang.answer_automatically_review_tips }}
                                    </p>
                                    <div class="loading-container" v-if="hasError">
                                        <p>{{ lang.ai_analysis_encountered_retry_tips }}</p>
                                        <el-button type="text" class="retry-btn" @click="retrySubmit">{{
                                            lang.retry
                                        }}</el-button>
                                    </div>
                                    <div class="loading-container" v-else>
                                        <i class="el-icon-loading"></i>
                                        <p>{{ lang.please_wait_ai_analysis }}</p>
                                    </div>
                                </template>

                                <AIResult
                                    :resultData="aiAnalysis"
                                    v-if="hasAnswer"
                                    class="answering_result_ai_result"
                                />
                            </div>
                        </div>

                        <div class="custom_footer">
                            <el-button
                                type="primary"
                                @click="submitAnswer"
                                :loading="isSubmitting"
                                :disabled="disabledSubmit"
                                v-if="showSubmitButton"
                            >
                                {{ isSubmitting ? `${lang.evaluating}` : `${lang.submitted_answers}` }}
                            </el-button>
                            <el-button type="primary" @click="reAnswer" v-if="hasAnswer || hasError">{{
                                lang.re_answer
                            }}</el-button>
                            <el-button type="primary" @click="nextQuestion" :loading="getTopicRequesting">{{
                                lang.next_question
                            }}</el-button>
                            <div class="topic-index" v-if="practiceType === 'sequence'">
                                <el-input
                                    v-model.number="currentTopicIndex"
                                    type="number"
                                    size="small"
                                    style="width: 80px"
                                    :min="1"
                                ></el-input>
                            </div>
                            <el-button @click="back">{{ lang.back_button }}</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import QuestionDisplay from "./practiceQuestionDisplay.vue";
import MyAnswerDisplay from "./practiceMyAnswerDisplay.vue";
import AIResult from "./practiceAIResult.vue";
import base from "../../lib/base";
import AIBookServiceInstance from "@/common/aiBookService";
import moment from "moment";
export default {
    mixins: [base],
    name: "PracticeAnswerAction",
    components: {
        QuestionDisplay,
        MyAnswerDisplay,
        AIResult,
    },
    data() {
        return {
            hasAnswer: false,
            hasSubmit: false,
            questionData: {
                bodyPart: "",
                content: [],
                title: "",
                questionTips: "",
                caseID: "",
                hideField: false,
            },
            answerData: {
                timestamp: "",
                answer: "",
            },
            userAnswer: "",
            aiAnalysis: {},
            getTopicRequesting: false,
            aiBookService: AIBookServiceInstance,
            currentTopicIndex: 0,
            timer: null,
            elapsedTime: 0,
            isSubmitting: false,
            practiceType: "random", // random随机, sequence顺序, bodyPart部位
            leftPanelWidth: 60, // 左侧面板宽度百分比
            isDragging: false,
            hasError: false,
        };
    },
    computed: {
        disabledSubmit() {
            return this.isSubmitting || this.hasSubmit || this.userAnswer.trim() === '';
        },
        showSubmitButton() {
            return !this.hasAnswer && !this.hasError;
        },
    },
    created() {
        this.questionData = this.$route.params;
        this.questionData.hideField = true;
        this.practiceType = this.questionData.practiceType;
        if (this.questionData.practiceType === "sequence") {
            this.currentTopicIndex = 1;
        }

        this.startTimer();
    },
    beforeDestroy() {
        this.clearTimer();
    },
    methods: {
        handleReturn() {
            this.$router.back();
        },
        scrollToBottom() {
            // 使用nextTick确保DOM已更新
            this.$nextTick(() => {
                // 延迟执行以确保所有内容都已渲染
                setTimeout(() => {
                    const dialogBody = this.$el.querySelector(".answer_action_container");
                    console.log("dialogBody", dialogBody);

                    if (dialogBody) {
                        // 记录滚动前的高度
                        console.log("滚动前 - scrollHeight:", dialogBody.scrollHeight);
                        console.log("滚动前 - clientHeight:", dialogBody.clientHeight);

                        // 使用scrollTo方法并指定smooth行为
                        dialogBody.scrollTo({
                            top: dialogBody.scrollHeight,
                            behavior: "smooth",
                        });

                        // 备用方案：如果scrollTo不生效，可以尝试使用scrollIntoView
                        if (dialogBody.lastElementChild) {
                            dialogBody.lastElementChild.scrollIntoView({
                                behavior: "smooth",
                                block: "end",
                            });
                        }
                    }
                }, 0); // 增加延迟时间确保内容已渲染
            });
        },
        scrollToTop() {
            this.$nextTick(() => {
                // 使用$refs引用DOM元素，而不是document.querySelector
                const questionDisplay = this.$el.querySelector(".left-panel");
                if (questionDisplay) {
                    // 确保滚动到顶部
                    questionDisplay.scrollTop = 0;

                    // 备用方案：如果直接设置scrollTop不生效，使用scrollTo方法
                    questionDisplay.scrollTo({
                        top: 0,
                        behavior: "smooth",
                    });
                }
            });
        },
        async submitAnswer() {
            if (this.userAnswer.trim() === "") {
                return;
            }
            if (this.isSubmitting) {
                return;
            }
            this.isSubmitting = true;

            const params = {
                userAnswer: this.userAnswer,
                useTime: this.elapsedTime,
                caseID: this.questionData.caseID,
            };
            this.clearTimer();
            this.aiBookService.submitAiPracticeCaseAnswer({
                params,
                onAnswering: (data) => {
                    this.hasSubmit = true;
                    this.isSubmitting = false;
                    this.questionData = {
                        ...this.questionData,
                        hideField: false,
                    };
                    // 提交后滚动到底部
                    this.answerData = {
                        answer: this.userAnswer,
                        timestamp: moment().format("YYYY-MM-DD HH:mm:ss"),
                    };
                    this.$nextTick(() => {
                        this.scrollToBottom();
                    });
                    console.log("服务器已经开始分析:", data);
                },
                onCompleted: (data) => {
                    console.log("AI分析完成", data);
                    this.aiAnalysis = data;
                    this.hasAnswer = true;
                    // 提交后滚动到底部
                    this.$nextTick(() => {
                        this.scrollToBottom();
                    });
                },
                onError: (error) => {
                    console.error("submit answer error:", error);
                    this.hasError = true;
                    this.isSubmitting = false;
                },
            });
        },
        async getAiPracticeCase(oParams) {
            this.getTopicRequesting = true;

            try {
                let params = {
                    ...oParams,
                };
                if (this.practiceType === "sequence") {
                    params.index = this.currentTopicIndex;
                }
                if (this.practiceType === "bodyPart") {
                    params.bodyPart = this.questionData.bodyPart;
                }
                const data = await this.aiBookService.getAiPracticeCase(params);

                this.questionData = {
                    bodyPart: data.bodyPart,
                    content: data.content,
                    title: data.title,
                    questionTips: data.question,
                    caseID: data._id,
                    hideField: true,
                };
                this.startTimer();
                this.getTopicRequesting = false;
                this.$nextTick(() => {
                    this.scrollToTop();
                });
                return data;
            } catch (error) {
                this.getTopicRequesting = false;
                return null;
            }
        },
        clearStatus() {
            this.hasAnswer = false;
            this.hasSubmit = false;
            this.answerData = {
                timestamp: "",
                answer: "",
            };
            this.userAnswer = "";
            this.aiAnalysis = {};
            this.aiBookService.cancelAiPracticeCaseAnswer();
        },
        async nextQuestion() {
            let params = {
                excludeCaseID: this.questionData.caseID,
            };
            if (!this.hasSubmit && this.practiceType !== "sequence") {
                this.$MessageBox.confirm(this.lang.answer_not_submitted_next_tips, this.lang.tip_title, {
                    confirmButtonText: this.lang.confirm_txt,
                    callback: async (action) => {
                        if (action === "confirm") {
                            if (this.practiceType === "sequence") {
                                this.currentTopicIndex++;
                            }
                            await this.getAiPracticeCase(params);
                            this.clearStatus();
                        }
                    },
                });
            } else {
                if (this.practiceType === "sequence") {
                    this.currentTopicIndex++;
                }
                await this.getAiPracticeCase(params);
                this.clearStatus();
            }
        },
        startTimer() {
            this.clearTimer();
            this.elapsedTime = 0;
            this.timer = setInterval(() => {
                this.elapsedTime++;
            }, 1000);
        },
        clearTimer() {
            if (this.timer) {
                clearInterval(this.timer);
                this.timer = null;
            }
        },
        handleBack() {
            // console.log('handleBack')
            // this.$root.eventBus.$emit("refreshGetAiPracticeStatistics")
            this.clearTimer();
            this.back();
        },
        reAnswer() {
            this.$MessageBox.confirm(this.lang.re_answer_cleared_tips, this.lang.tip_title, {
                confirmButtonText: this.lang.confirm_txt,
                callback: (action) => {
                    if (action === "confirm") {
                        // 重置所有状态
                        this.clearStatus();
                        // 重新开始计时
                        this.startTimer();
                    }
                },
            });
        },
        startResize(e) {
            e.preventDefault();
            this.isDragging = true;

            const container = this.$el.querySelector(".split-container");
            const initialX = e.clientX;
            const initialWidth = this.leftPanelWidth;

            // 只在拖动区域禁止选择
            container.style.userSelect = "none";

            const doDrag = (e) => {
                if (!this.isDragging) {
                    return;
                }

                const dx = e.clientX - initialX;
                const containerWidth = container.offsetWidth;
                const newWidthPercent = initialWidth + (dx / containerWidth) * 100;

                // 限制拖动范围在30%到70%之间
                this.leftPanelWidth = Math.min(Math.max(newWidthPercent, 30), 70);
            };

            const stopDrag = () => {
                this.isDragging = false;
                // 恢复文本选择
                container.style.userSelect = "";
                document.removeEventListener("mousemove", doDrag);
                document.removeEventListener("mouseup", stopDrag);
            };

            document.addEventListener("mousemove", doDrag);
            document.addEventListener("mouseup", stopDrag);
        },
        retrySubmit() {
            this.hasError = false;
            this.hasSubmit = false;
            this.submitAnswer();
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync_pc/style/aiChat.scss';
.practiceAnswerAction_container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f0f2f5;
    .custom_header {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #ddd;
        padding: 10px 20px;
        .back_btn {
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #606266;
            font-size: 14px;
        }
    }
    .custom_body {
        height: 100%;
        padding: 0;
        overflow: hidden;
        .split-container {
            display: flex;
            height: 100%;
            position: relative;
            background-color: rgb(235, 239, 242);

            .left-panel {
                height: 100%;
                background-color: #fff;
                border-right: 1px solid #dcdfe6;
                transition: none;
                overflow-y: auto;
                .question_display {
                    min-height: 100%;
                    padding: 20px;
                    box-shadow: none;
                    border-radius: 0;
                    margin-bottom: 0;
                }
            }

            .splitter {
                width: 6px;
                background-color: #dcdfe6;
                cursor: col-resize;
                position: relative;
                z-index: 10;
                transition: background-color 0.3s;

                &:hover,
                &.dragging {
                    background-color: #409eff;
                }

                &::after {
                    content: "";
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    width: 2px;
                    height: 20px;
                    background-color: #fff;
                    border-radius: 2px;
                }
            }

            .right-panel {
                flex: 1;
                height: 100%;
                min-width: 30%;
                overflow: hidden;

                .right-content {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    .answer_action_container {
                        padding: 0;
                        display: flex;
                        flex-direction: column;
                        flex: 1;
                        overflow-y: auto;
                        .my_answer_display {
                            flex: none;
                        }
                        .answering_result_container {
                            background-color: #fff;
                            padding: 20px;
                            flex: 1;
                            .answering_result_ai_result {
                                padding: 0;
                            }
                            .answering_tips {
                                font-size: 15px;
                                color: #606266;
                                margin-bottom: 15px;
                                display: block;
                            }

                            .loading-container {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                gap: 10px;
                                margin-top: 20px;

                                .el-icon-loading {
                                    font-size: 24px;
                                    color: #409eff;
                                }

                                p {
                                    margin: 0;
                                    color: #606266;
                                }
                            }
                        }
                    }
                    .custom_footer {
                        flex-shrink: 0;
                        padding: 20px;
                        background-color: #fff;
                        border-radius: 4px;
                        box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
                    }
                }
            }
        }
    }
    .input_container {
        padding: 20px;
        background: #fff;
        height: 100%;
        display: flex;
        flex-direction: column;
        .question_tips {
            margin-bottom: 20px;
            padding: 16px;
            background-color: #f5f7fa;
            border-radius: 0;
            border-left: 4px solid #409eff;
            flex-shrink: 1;
            .tips_content {
                font-size: 14px;
                color: #606266;
                line-height: 1.6;
                margin: 0;
            }
        }

        .input_label {
            font-size: 14px;
            color: #333;
            margin-bottom: 10px;
            font-weight: 500;
            flex-shrink: 1;
        }
        .el-textarea {
            flex: 1;
            overflow: hidden;
        }
        :deep(.el-textarea__inner) {
            background-color: #f5f7fa;
            border: 1px solid #e4e7ed;
            font-size: 14px;
            line-height: 1.5;
            height: 100%;
        }

        .button_group {
            margin-top: 20px;
            text-align: right;

            .el-button {
                margin-left: 10px;
            }
        }
    }
    .custom_footer {
        padding: 20px 30%;
        display: flex;
        justify-content: space-around;
        align-items: center;
        border-top: 1px solid #eee;
        background-color: #fff;

        .el-button {
            min-width: 120px;
            height: 40px;
            font-size: 14px;

            &.el-button--primary {
                @extend .ai-theme-background;

                &:hover {
                    opacity: 0.9;
                }
                &:active {
                    opacity: 0.8;
                }
            }

            &:last-child {
                background: #fff;
                border: 1px solid #dcdfe6;
                color: #606266;

                &:hover {
                    color: #2196f3;
                    border-color: #2196f3;
                    background-color: #fff;
                }
            }

            &.is-disabled {
                background: #a0cfff !important;
                border-color: #a0cfff !important;
                color: #fff !important;
                cursor: not-allowed;
                opacity: 0.6;

                &:hover {
                    background: #a0cfff !important;
                    border-color: #a0cfff !important;
                    opacity: 0.6;
                }
            }
        }
        .topic-index {
            display: flex;
            align-items: center;

            :deep(.el-input__inner) {
                text-align: center;
                padding: 0 5px;
                height: 32px;
            }
        }
    }
    .loading-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        margin-top: 20px;

        .el-icon-loading {
            font-size: 24px;
            color: #409eff;
        }

        p {
            margin: 0;
            color: #606266;
        }

        .retry-btn {
            color: #409eff;
            text-decoration: underline;
            padding: 0;
            font-size: 14px;
            margin-left: 10px;

            &:hover {
                color: #66b1ff;
            }
        }
    }
    :deep(.el-dialog__body) {
        height: calc(90vh - 120px);
        padding: 0;
        overflow: hidden;
    }
}

// 只在拖动时对特定区域生效
.dragging {
    .splitter,
    .left-panel,
    .right-panel {
        &::selection {
            background: transparent;
        }
    }
}
</style>
