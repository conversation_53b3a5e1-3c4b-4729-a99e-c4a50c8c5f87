<template>
    <div class="training-main">
        <div class="tab-container">
            <div class="custom-tabs-header">
                <div class="back-button" @click="back">
                    <i class="el-icon-arrow-left"></i>
                </div>
                <el-tabs v-model="activeTab" class="smart-tech-tabs smart-tech-tabs--large" @tab-click="handleTabClick" v-if="tabsConfig.length">
                    <el-tab-pane
                        v-for="tabItem in tabsConfig"
                        :key="tabItem.name"
                        :label="tabItem.label"
                        :name="tabItem.name"
                    ></el-tab-pane>
                </el-tabs>
            </div>

            <div class="tab-content">
                <keep-alive>
                    <router-view></router-view>
                </keep-alive>
            </div>
        </div>
    </div>
</template>

<script>
import base from "../../lib/base";
import Tool from "@/common/tool";
import { SMART_TECH_TRAINING_ROLE } from "../../lib/constants";
export default {
    mixins: [base],
    name: "TrainingTab",
    components: {},
    data() {
        return {
            activeTab: "",
            userRole: SMART_TECH_TRAINING_ROLE.STUDENT,
            trainingId: '',
            tabsConfig: []
        };
    },
    computed: {
    },
    watch: {
        $route: {
            immediate: true,
            handler(to) {
                this.setActiveTabByRoute(to);
            },
        },
    },
    created() {
        this.userRole = this.$route.params.role;

        if(this.userRole === SMART_TECH_TRAINING_ROLE.STUDENT){
            this.trainingId = this.$route.params.trainingId;
            this.tabsConfig = [
                { label: '考核要求', name: 'exam',routerName:'SmartTechTrainingExamProject' }
            ];


        }else if(this.userRole === SMART_TECH_TRAINING_ROLE.PI_TEACHER){
            this.tabsConfig = [
                { label: '学员概况列表', name: 'student_overview',routerName:'SmartTechTrainingStudentOverview' },
                { label: '批改审核', name: 'grading_review',routerName:'SmartTechTrainingGradingReview' },
                { label: '学员管理', name: 'student_management',routerName:'SmartTechTrainingStudentManagement' },
                { label: 'supervisor管理', name: 'supervisor_management', routerName: 'SmartTechTrainingSupervisorManagement' }
            ];
        }else if(this.userRole === SMART_TECH_TRAINING_ROLE.SUPERVISOR){
            this.tabsConfig = [
                { label: '学员概况列表', name: 'student_overview',routerName:'SmartTechTrainingStudentOverview' },
                { label: '批改审核', name: 'grading_review',routerName:'SmartTechTrainingGradingReview' },
            ];
        }
        this.setDefaultTabByRoute();
    },
    methods: {
        setActiveTabByRoute(to) {
            const routerName = to.name;
            const matchedTab = this.tabsConfig.find(tab => tab.routerName === routerName);
            if (matchedTab) {
                this.activeTab = matchedTab.name;
            }

        },
        setDefaultTabByRoute(routerName) {
            if (!this.tabsConfig || this.tabsConfig.length === 0) {
                this.activeTab = null;
                return;
            }
            const defaultTabForRole = this.tabsConfig[0];
            console.log(defaultTabForRole);
            if (defaultTabForRole) {
                this.activeTab = defaultTabForRole.name;
                console.log(this.$route.name,defaultTabForRole.routerName);
                if (this.$route.name !== defaultTabForRole.routerName) {
                    Tool.loadModuleRouter({
                        name:defaultTabForRole.routerName,
                        params:{
                            ...this.$route.params
                        }
                    },true);
                }
            }
            // let matchedTab = null;
            // for (const tab of this.tabsConfig) {
            //     if (routerName === tab.routerName) {
            //         matchedTab = tab;
            //         break;
            //     }
            // }

            // const cid = this.$route.params.cid;
            // console.log(matchedTab,this.tabsConfig);
            // if (matchedTab) {
            //     this.activeTab = matchedTab.name;
            // } else {
            //     const defaultTabForRole = this.tabsConfig[0];
            //     console.log(defaultTabForRole);
            //     if (defaultTabForRole) {
            //         this.activeTab = defaultTabForRole.name;
            //         console.log(this.$route.name,defaultTabForRole.routerName);
            //         if (this.$route.name !== defaultTabForRole.routerName) {
            //             Tool.loadModuleRouter({
            //                 name:defaultTabForRole.routerName,
            //                 params:{
            //                     ...this.$route.params
            //                 }
            //             },true);
            //         }
            //     } else {
            //         this.activeTab = null;
            //     }
            // }
        },
        handleTabClick(clickedTab) {
            console.log(clickedTab);
            const name = clickedTab.name;
            const routerName = this.tabsConfig.find(tab => tab.name === name).routerName;
            Tool.loadModuleRouter({
                name:routerName,
                params:{
                    ...this.$route.params
                }
            },true);
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync_pc/style/smartTechTraining.scss';
.training-main {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 20;
    display: flex;
    flex-direction: column;
    background-color: #f7f9fc;
}

.tab-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fff;
}

.custom-tabs-header {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
    padding: 0 10px;
    position: relative;

    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        cursor: pointer;
        margin-right: 10px;
        font-weight: 600;
        i {
            font-size: 25px;
            color: #000;
        }

        &:hover i {
            color: #409eff;
        }
    }
    :deep(.el-tabs__content){
        margin-top: 0;
    }
}

.tab-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.coming-soon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;

    h1 {
        font-size: 28px;
        color: #909399;
        font-weight: 400;
    }
}
</style>
