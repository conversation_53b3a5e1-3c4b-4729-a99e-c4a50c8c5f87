import Tool from '@/common/tool.js'
import store from '../store'
const lang = store.state.language
let isRequestServiceJoining = false
let hasDoppler =  0
window.livingStatus = 0;//0未开播，1加入中，2正在直播
import {getLiveRoomObj} from "../lib/common_base"
export function getCid(){
    return window.vm.$route.query.cid||window.vm.$route.params.cid
}
export function resetRequestJoining(){
    isRequestServiceJoining = false
}
export async function ServiceGetCurrentMainInfo(cid) {
    return new Promise((resolve, reject) => {
        window.main_screen.conversation_list[cid].getCurrentMainInfo(
            {},
            (res) => {
                if (res.error_code === 0) {
                    resolve(res.data);
                } else {
                    console.error(res,'ServiceGetCurrentMainInfo')
                    reject(res);
                }
            }
        );
    });
};

export async function getDeviceStatusFromNative(){
    return new Promise((resolve,reject)=>{

        Tool.createCWorkstationCommunicationMng({
            name: "GetDeviceStatusFromNative",
            emitName: "NotifyGetDeviceStatusFromNative",
            params:{},
            timeout: 3000,
        }).then((res)=>{
            resolve(res)
            window.vm.$store.commit('device/updateDeviceInfo',{
                doppler:res.data.doppler,
            })
            hasDoppler = Number(res.data.doppler)
        }).catch(error=>{
            window.vm.$store.commit('device/updateDeviceInfo',{
                doppler:0,
            })
            hasDoppler=0
            resolve(error)
        })

    })
}

// 由于动态修改本地时间后，浏览器需要重新刷新才能生效导致获取不准确问题，优先向原生获取
export async function getLocalTimestamp(){
    return new Promise((resolve,reject)=>{
        Tool.createCWorkstationCommunicationMng({
            name: "GetLocalTimestamp",
            emitName: "NotifyGetLocalTimestamp",
            params:{},
            timeout: 1000,
        }).then((res)=>{
            console.log(res,'GetLocalTimestamp')
            resolve(res.data.timestamp)
        }).catch(error=>{
            resolve(new Date().getTime())
        })
    })

}

export function joinRoom({main=0,aux=0,isSender=0,top=0,left=0,width=0,height=0,videoSource='',from='chatComponent',autoPushStream=false},ocid){
    const globalParams=store.state.globalParams
    const systemConfig = store.state.systemConfig
    return new Promise(async(resolve,reject)=>{
        try{
            let Toast=window.vm.$root.platformToast
            window.livingStatus = 1
            if (!globalParams.isCef) {
                Toast(lang.use_app_tip,3);
                window.livingStatus = 0
                return reject(lang.use_app_tip)
            }
            if (!globalParams.functionsStatus.live) {
                console.log('区域配置关闭了直播功能')
                window.livingStatus = 0
                return reject('!globalParams.functionsStatus.live')
            }
            let liveRoom = getLiveRoomObj(ocid)

            if(!liveRoom){
                window.livingStatus = 0
                return reject('!liveRoom')
            }
            if(window.main_screen.CMonitorWallPush.joined){ //此时如果电视墙在默认推起，则暂时先关闭
                window.main_screen.CMonitorWallPush.LeaveChannelSilenceTmp()
                await Tool.sleep(500)
            }
            let cid = ocid||getCid()
            let conversation = window.main_screen.conversation_list[cid]
            if(isRequestServiceJoining === true){
                window.livingStatus = 0
                return reject('isRequestServiceJoining === true')
            }
            const timestamp = new Date().getTime()
            isRequestServiceJoining = true
            try {
                conversation.requestAgoraUid({
                    main,
                    aux,
                    ts:timestamp
                },async(res)=>{
                    console.log('requestAgoraUid',res)
                    isRequestServiceJoining = false
                    if(res.error_code===0){
                        let data = res.data
                        let mainUidInfo = {}
                        let auxUidInfo = {}
                        data.uidList.forEach(ele=>{
                            if(ele.stream_type === 1){
                                mainUidInfo = ele
                            }else{
                                auxUidInfo = ele
                            }
                        })

                        if(mainUidInfo.uid===0||auxUidInfo.uid===0){//存在Uid为0的，则认为本次申请有问题 不执行

                            if(mainUidInfo.uid === 0 && (from!=='live_management')){ //除去直播预约场景，主讲人可以在申请不到主流时，依然进入直播
                                try {
                                    const data = await ServiceGetCurrentMainInfo(cid)
                                    let userName = data.mainStreamUserName||lang.someone
                                    Toast(`${userName}${lang.mainstream_is_sharing}`)
                                    window.livingStatus = 0
                                    return reject(`${userName}${lang.mainstream_is_sharing}`)
                                } catch (error) {
                                    console.error('currentMain',error)
                                    window.livingStatus = 0
                                    return reject(`${lang.someone}${lang.mainstream_is_sharing}`)
                                }

                            }else if(auxUidInfo.uid === 0){
                                Toast(lang.applying_join_room_error)
                                window.livingStatus = 0
                                return reject(lang.applying_join_room_error)
                            }
                        }

                        Tool.initNativeAgoraSdk(data.appid).then(()=>{
                            if(mainUidInfo.uid&&auxUidInfo.uid){ //同时有主辅流，先让辅流进
                                liveRoom.JoinChannelAux({ //辅流先进房间
                                    channelId:data.channelId,//房间名
                                    uid:auxUidInfo.uid,
                                    isSender,
                                    token:auxUidInfo.token,
                                    top,
                                    left,
                                    width,
                                    height,
                                    from,
                                    autoPushStream
                                }).then((joinAuxRes)=>{
                                    if(joinAuxRes&&mainUidInfo.uid){
                                        liveRoom.data.videoSource = videoSource
                                        liveRoom.JoinChannelMain({// 后主流加入
                                            channelId:data.channelId,//房间名
                                            uid:mainUidInfo.uid,
                                            token:mainUidInfo.token,
                                            top,
                                            left,
                                            width,
                                            height,
                                            autoPushStream
                                        }).then(()=>{
                                            window.livingStatus = 2
                                            return resolve(true)
                                        }).catch((error)=>{
                                            window.livingStatus = 0
                                            return reject(error)

                                        })

                                    }
                                }).catch((error)=>{
                                    console.error(error)
                                    window.livingStatus = 0
                                    return reject(error)
                                })



                            }else{
                                if(auxUidInfo.uid){
                                    liveRoom.JoinChannelAux({
                                        channelId:data.channelId,//房间名
                                        uid:auxUidInfo.uid,
                                        isSender,
                                        token:auxUidInfo.token,
                                        top,
                                        left,
                                        width,
                                        height,
                                        from,
                                        autoPushStream
                                    }).then(()=>{
                                        window.livingStatus = 2
                                        return resolve(true)
                                    }).catch((error)=>{
                                        window.livingStatus = 0
                                        return reject(error)
                                    })
                                }
                                if(mainUidInfo.uid){
                                    liveRoom.data.videoSource = videoSource
                                    liveRoom.JoinChannelMain({
                                        channelId:data.channelId,//房间名
                                        uid:mainUidInfo.uid,
                                        token:mainUidInfo.token,
                                        top,
                                        left,
                                        width,
                                        height,
                                        autoPushStream
                                    }).then(()=>{
                                        window.livingStatus = 2
                                        return resolve(true)
                                    }).catch((error)=>{
                                        // window.livingStatus = 0
                                        return reject(error)
                                    })
                                }
                                //
                            }
                        }).catch((error)=>{
                            console.error(error)
                            window.livingStatus = 0
                            return reject(error)
                        })



                    }else{
                        if(res.hasOwnProperty('key')){
                            Toast(lang.error[res.key],1)
                        }
                        window.livingStatus = 0
                        return reject(res.error_msg)

                    }
                })
                setTimeout(()=>{ //发起超时
                    if(isRequestServiceJoining){
                        isRequestServiceJoining = false
                        Toast(lang.requestTimeout,1)
                        window.livingStatus = 0
                        return reject(false)
                    }
                },10000)
            } catch(error) {
                isRequestServiceJoining = false
                window.livingStatus = 0
                return reject(error)

            }
        }catch (error) {
            isRequestServiceJoining = false
            window.livingStatus = 0
            return reject(error)

        }

    })


}

export function joinRoomWeb({main=0,aux=0,isSender=0,group_title='',videoSource='',from='chatComponent'},ocid){
    const globalParams=store.state.globalParams
    return new Promise(async(resolve,reject)=>{
        try{
            let Toast=window.vm.$root.platformToast
            let cid = ocid||getCid()
            let conversation = window.main_screen.conversation_list[cid]
            window.livingStatus = 1
            let liveRoom = getLiveRoomObj(cid)
            if(!liveRoom){
                Toast('初始化失败，请稍后尝试')
                window.livingStatus = 0
                return reject('!liveRoom,初始化失败，请稍后尝试')
            }
            if(isRequestServiceJoining === true){
                window.livingStatus = 0
                return reject(false)
            }
            if (!globalParams.functionsStatus.live) {
                console.log('区域配置关闭了直播功能')
                window.livingStatus = 0
                return reject(false)
            }
            isRequestServiceJoining = true
            console.log('ts',new Date().getTime())
            try {
                conversation.requestAgoraUid({
                    main:main,
                    aux:aux,
                    ts:new Date().getTime()
                },async(res)=>{
                    console.log ('requestAgoraUid',JSON.stringify(res))
                    isRequestServiceJoining = false
                    if(res.error_code===0){
                        let data = res.data
                        let mainUidInfo = {}
                        let auxUidInfo = {}
                        data.uidList.forEach(ele=>{
                            if(ele.stream_type === 1){
                                mainUidInfo = ele
                            }else{
                                auxUidInfo = ele
                            }
                        })

                        if(mainUidInfo.uid===0||auxUidInfo.uid===0){//存在Uid为0的，则认为本次申请有问题 不执行

                            if(mainUidInfo.uid === 0 && (from!=='live_management')){ //除去直播预约场景，主讲人可以在申请不到主流时，依然进入直播
                                try {
                                    const data = await ServiceGetCurrentMainInfo(cid)
                                    let userName = data.mainStreamUserName||lang.someone
                                    Toast(`${userName}${lang.mainstream_is_sharing}`)
                                    window.livingStatus = 0
                                    return reject(`${userName}${lang.mainstream_is_sharing}`)
                                } catch (error) {
                                    console.error('currentMain',error)
                                    window.livingStatus = 0
                                    return reject(`${lang.someone}${lang.mainstream_is_sharing}`)
                                }

                            }else if(auxUidInfo.uid === 0){
                                Toast(lang.applying_join_room_error)
                                window.livingStatus = 0
                                return reject(lang.applying_join_room_error)
                            }
                        }
                        if(mainUidInfo.uid&&auxUidInfo.uid){ //同时有主辅流，先让辅流进
                            liveRoom.JoinChannelAux({ //辅流先进房间
                                channelId:data.channelId,//房间名
                                uid:auxUidInfo.uid,
                                isSender,
                                token:auxUidInfo.token,
                                group_title,
                                from,
                                appId:data.appid,
                            }).then((joinAuxRes)=>{
                                console.error(joinAuxRes,mainUidInfo.uid)
                                resolve(true)
                                if(joinAuxRes&&mainUidInfo.uid){
                                    liveRoom.data.videoSource = videoSource
                                    liveRoom.JoinChannelMain({// 后主流加入
                                        channelId:data.channelId,//房间名
                                        uid:mainUidInfo.uid,
                                        token:mainUidInfo.token,
                                        appId:data.appid,
                                    }).then(()=>{

                                    }).catch((error)=>{


                                    })

                                }
                            }).catch((error)=>{
                                console.error(error)
                                window.livingStatus = 0
                                return reject(error)
                            })



                        }else{
                            if(auxUidInfo.uid){
                                liveRoom.JoinChannelAux({
                                    channelId:data.channelId,//房间名
                                    uid:auxUidInfo.uid,
                                    isSender,
                                    token:auxUidInfo.token,
                                    group_title,
                                    from,
                                    appId:data.appid,
                                }).then(()=>{
                                    window.livingStatus = 2
                                    return resolve(true)
                                }).catch((error)=>{
                                    window.livingStatus = 0
                                    return reject(error)
                                })
                            }
                            if(mainUidInfo.uid){
                                liveRoom.data.videoSource = videoSource
                                liveRoom.JoinChannelMain({
                                    channelId:data.channelId,//房间名
                                    uid:mainUidInfo.uid,
                                    token:mainUidInfo.token,
                                    appId:data.appid,
                                }).then(()=>{
                                    window.livingStatus = 2
                                    return resolve(true)
                                }).catch((error)=>{
                                    // window.livingStatus = 0
                                    return reject(error)
                                })
                            }
                            //
                        }
                        // liveRoom.JoinChannelAux({
                        //     channelId:data.channelId,//房间名
                        //     uid:auxUidInfo.uid,
                        //     isSender,
                        //     token:auxUidInfo.token,
                        //     group_title,
                        //     from,
                        //     appId:data.appid,
                        // }).then(()=>{
                        //     window.livingStatus = 2
                        //     return resolve(true)
                        // }).catch((error)=>{
                        //     window.livingStatus = 0
                        //     return reject(error)
                        // })

                    }else{
                        if(res.hasOwnProperty('key')){
                            Toast(lang.error[res.key],1)
                        }
                        window.livingStatus = 0
                        return reject(res.error_msg)

                    }
                })
                setTimeout(()=>{ //发起超时
                    if(isRequestServiceJoining){
                        isRequestServiceJoining = false
                        Toast(lang.requestTimeout,1)
                        window.livingStatus = 0
                        return reject(false)
                    }
                },10000)
            } catch(error) {
                isRequestServiceJoining = false
                window.livingStatus = 0
                return reject(error)

            }
        }catch(error){
            isRequestServiceJoining = false
            window.livingStatus = 0
            return reject(error)
        }

    })


}
