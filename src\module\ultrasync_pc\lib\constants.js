export const CHAT_TYPE = {
    CHAT_WINDOW: 1, //聊天窗口
    GALLERY: 2,//画廊
    CHAT_HISTORY: 3,//聊天历史记录
    CONFERENCE:4,//直播
};
export const EMOJI_LIST = [
    '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '😤',
    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
    '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔',
    '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥',
    '😌', '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮',
    '🤧', '😵', '🤯', '🤠', '😎', '🤓', '🧐', '😕', '😟', '🙁',
    '😮', '😯', '😲', '😳', '🥺', '😦', '😧', '😨', '😰', '😥',
    '😢', '😭', '😱', '😖', '😣', '😞', '😓', '😩', '😫', '🥱',
    '❤️', '😡', '😠', '🤬', '🤯', '😈', '👿', '💀', '☠️', '💩',
    '🤡', '👹', '👺', '👻', '👽', '👾', '🤖', '😺', '😸', '😹',
    '😻', '😼', '😽', '🙀', '😿', '😾', '🙈', '🙉', '🙊', '💋',
]

export const CLOUD_TEST_TYPE = Object.freeze({
    VIEW: 1,         // 查看试卷
    ANSWER: 2,       // 答卷
    CORRECT: 3,      // 批卷
    VIEW_RESULT: 4,  // 查看结果
    EDIT: 5,         // 编辑试卷
});

export const BODY_PART = Object.freeze({
    SUPERFICIAL: 'superficial',         // 浅表介入
    ABDOMEN: 'abdomen',       // 腹部
    CARDIO: 'cardiac',      // 心血管
    GYN: 'obgyn',  // '妇产'
});

export const SMART_TECH_TRAINING_TEST_TYPE = Object.freeze({
    ATTACHMENT_UPLOAD: 1, // 附件上传
    ONLINE_QUIZ: 2,       // 在线答题
});
export const SMART_TECH_TRAINING_ROLE = Object.freeze({
    STUDENT: 'student', // 学生
    PI_TEACHER: 'PI', // 导师
    SUPERVISOR: 'supervisor', // 督导
});
export const SEX = Object.freeze({
    MALE: 0, // 男
    FEMALE: 1, // 女
    UNKNOWN: 2, // 未知
});

export const SMART_TECH_TRAINING_TEST_RETRY_TYPE = Object.freeze({
    //多次作答，单次作答
    MULTIPLE: 2, // 多次作答
    SINGLE: 1, // 单次作答
});