<!-- 会话下画廊或统计下画廊 ai信息弹框 -->
<template>
    <div class="iworks_test_image_report" v-show="true">
        <div class="content" v-if="currentFile && currentFile.msg_type != systemConfig.msg_type.EXPIRATION_RES">
            <template v-if="ai_type == 'abdomen' || ai_type == 'cardiac'">
                <template
                    v-if="
                        ai_report &&
                        ai_report.hasOwnProperty('error') &&
                        !ai_report.error &&
                        nameDeal(ai_report) != '--'
                    "
                >
                    <div class="row">
                        <div class="label">{{ lang.exam_type_text }}</div>
                        <div class="value">
                            {{
                                lang.exam_types[newFileObj.file.exam_type + ""]
                                    ? lang.exam_types[newFileObj.file.exam_type + ""]
                                    : "--"
                            }}
                        </div>
                    </div>
                    <div class="row" v-if="newFileObj.child_name">
                        <div class="label">{{ lang.view_name_text }}</div>
                        <div class="value">{{ newFileObj.child_name }}</div>
                    </div>
                    <div class="row" v-else>
                        <div class="label">{{ lang.view_name_text }}</div>
                        <div class="value">{{ nameDeal(ai_report) }}</div>
                    </div>
                    <template v-if="ai_type != 'cardiac'">
                        <div class="row">
                            <div class="label">{{ lang.compliance_rate }}:</div>
                            <div class="value">
                                {{ ai_report.score != undefined ? toFixedNumber(ai_report.score * 10, 0) + "%" : "--" }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="label">{{ lang.view_quality_text }}</div>
                            <div class="value">{{ qualityDeal(ai_report) }}</div>
                        </div>
                    </template>
                    <div class="row">
                        <div class="label">{{ lang.structure_evaluation_text }}</div>
                        <div class="value">
                            <template v-if="ai_report.dispalyStructs && ai_report.dispalyStructs.length > 0">
                                <!-- {{view.groupItemList}} -->
                                <table>
                                    <thead>
                                        <tr>
                                            <td>{{ lang.structure_name }}</td>
                                            <td>{{ lang.box_color }}</td>
                                            <td class="operation_col">
                                                <el-checkbox
                                                    v-if="ai_report.dispalyStructs.filter((v) => v.isExist).length"
                                                    class="el-checkbox__input"
                                                    :class="{
                                                        'is-indeterminate':
                                                            isStructImage &&
                                                            ai_report.dispalyStructs.filter((v) => v.isExist).length !=
                                                                ai_report.dispalyStructs.filter(
                                                                    (v) => v.isChecked && v.isExist
                                                                ).length,
                                                    }"
                                                    v-model="isStructImage"
                                                    @change="checkedAllItem"
                                                    :title="lang.select_all + '/' + lang.cancel_select_all"
                                                >
                                                </el-checkbox>
                                            </td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(item, index) of ai_report.dispalyStructs" :key="index">
                                            <td>{{ lang[item.id] }}</td>
                                            <td>
                                                <span
                                                    class="arear"
                                                    :style="{ background: item.color }"
                                                    v-if="item.isExist"
                                                >
                                                </span>
                                                <template v-else>
                                                    <span class="is_required" v-if="item.is_required">
                                                        {{ lang.deletion }}
                                                    </span>
                                                    <span class="is_optional" v-else>
                                                        {{ lang.is_optional }}
                                                    </span>
                                                </template>
                                            </td>
                                            <td>
                                                <el-checkbox
                                                    v-model="item.isChecked"
                                                    @change="checkedOneItem"
                                                    v-if="item.isExist"
                                                ></el-checkbox>
                                            </td>
                                            <!-- <td>{{item[0].rate}}</td> -->
                                        </tr>
                                    </tbody>
                                </table>
                            </template>
                            <template v-else> -- </template>
                        </div>
                    </div>
                    <div class="tips">{{ lang.results_reference_only }}</div>
                </template>
                <div v-else class="no_data">{{ lang.no_ai_result }}</div>
            </template>
            <div v-else-if="ai_type == 'breast'" class="">
                <!-- <p v-if="newFileObj.file.mark_list[0]&&newFileObj.file.mark_list[0].BI_RADS_features">
                    {{lang.BI_RADS_features[1][newFileObj.file.mark_list[0].BI_RADS_features[1]]}}，
                    {{lang.BI_RADS_features[0][newFileObj.file.mark_list[0].BI_RADS_features[0]]}}，
                    <template v-if="Array.isArray(newFileObj.file.mark_list[0].BI_RADS_features[2])">
                        {{newFileObj.file.mark_list[0].BI_RADS_features[2][0]==1?lang.BI_RADS_features[2][0]:''}}
                        {{newFileObj.file.mark_list[0].BI_RADS_features[2][1]==1?lang.BI_RADS_features[2][1]:''}}
                        {{newFileObj.file.mark_list[0].BI_RADS_features[2][2]==1?lang.BI_RADS_features[2][2]:''}}
                        {{newFileObj.file.mark_list[0].BI_RADS_features[2][3]==1?lang.BI_RADS_features[2][3]:''}}
                        {{newFileObj.file.mark_list[0].BI_RADS_features[2][4]==1?lang.BI_RADS_features[2][4]:''}}，
                    </template>
                    <template v-else>
                        {{lang.BI_RADS_features[2][newFileObj.file.mark_list[0].BI_RADS_features[2]]}}，
                    </template>
                    {{lang.BI_RADS_features[3][newFileObj.file.mark_list[0].BI_RADS_features[3]]}}，
                    {{lang.BI_RADS_features[4][newFileObj.file.mark_list[0].BI_RADS_features[4]]}}，
                    {{lang.BI_RADS_features[5][newFileObj.file.mark_list[0].BI_RADS_features[5]]}}，
                    {{lang.BI_RADS_features[6][newFileObj.file.mark_list[0].BI_RADS_features[6]]}}
                </p> -->
                <!-- <p v-else>{{lang.no_BI_RADS_features}}</p> -->
                <template v-if="newFileObj.file.summary">
                    <div class="row">
                        <div class="label">{{ lang.case_database_fliter.bi_rads_type_text }}</div>
                        <div class="value">{{ newFileObj.file.summary.BI_RADS_results }}</div>
                    </div>
                    <div class="row">
                        <div class="label">{{ lang.BI_RADS_features_results_tip }}</div>
                        <div class="value">
                            <template
                                v-if="
                                    (newFileObj.file.summary.BI_RADS_features_results[0] &&
                                        newFileObj.file.summary.BI_RADS_features_results[0] > -1) ||
                                    (newFileObj.file.summary.BI_RADS_features_results[2] &&
                                        Array.isArray(newFileObj.file.summary.BI_RADS_features_results[2]) &&
                                        newFileObj.file.summary.BI_RADS_features_results[2][0] > 0) ||
                                    (newFileObj.file.summary.BI_RADS_features_results[3] &&
                                        newFileObj.file.summary.BI_RADS_features_results[3] > -1)
                                "
                            >
                                {{
                                    lang.BI_RADS_features[1][newFileObj.file.summary.BI_RADS_features_results[1]]
                                        ? lang.BI_RADS_features[1][
                                              newFileObj.file.summary.BI_RADS_features_results[1]
                                          ] + "，"
                                        : ""
                                }}
                                {{
                                    lang.BI_RADS_features[0][newFileObj.file.summary.BI_RADS_features_results[0]]
                                        ? lang.BI_RADS_features[0][
                                              newFileObj.file.summary.BI_RADS_features_results[0]
                                          ] + "，"
                                        : ""
                                }}
                                <template v-if="Array.isArray(newFileObj.file.mark_list[0].BI_RADS_features[2])">
                                    {{
                                        newFileObj.file.summary.BI_RADS_features_results[2][0] == 1
                                            ? lang.BI_RADS_features[2][0]
                                            : ""
                                    }}
                                    {{
                                        newFileObj.file.summary.BI_RADS_features_results[2][1] == 1
                                            ? lang.BI_RADS_features[2][1]
                                            : ""
                                    }}
                                    {{
                                        newFileObj.file.summary.BI_RADS_features_results[2][2] == 1
                                            ? lang.BI_RADS_features[2][2]
                                            : ""
                                    }}
                                    {{
                                        newFileObj.file.summary.BI_RADS_features_results[2][3] == 1
                                            ? lang.BI_RADS_features[2][3]
                                            : ""
                                    }}
                                    {{
                                        newFileObj.file.summary.BI_RADS_features_results[2][4] == 1
                                            ? lang.BI_RADS_features[2][4]
                                            : ""
                                    }}
                                </template>
                                <template v-else>
                                    {{
                                        lang.BI_RADS_features[2][newFileObj.file.summary.BI_RADS_features_results[2]]
                                            ? lang.BI_RADS_features[2][
                                                  newFileObj.file.summary.BI_RADS_features_results[2]
                                              ] + "，"
                                            : ""
                                    }}
                                </template>
                                {{
                                    lang.BI_RADS_features[3][newFileObj.file.summary.BI_RADS_features_results[3]]
                                        ? lang.BI_RADS_features[3][
                                              newFileObj.file.summary.BI_RADS_features_results[3]
                                          ] + "，"
                                        : ""
                                }}
                                {{
                                    lang.BI_RADS_features[4][newFileObj.file.summary.BI_RADS_features_results[4]]
                                        ? lang.BI_RADS_features[4][
                                              newFileObj.file.summary.BI_RADS_features_results[4]
                                          ] + "，"
                                        : ""
                                }}
                                {{
                                    lang.BI_RADS_features[5][newFileObj.file.summary.BI_RADS_features_results[5]]
                                        ? lang.BI_RADS_features[5][
                                              newFileObj.file.summary.BI_RADS_features_results[5]
                                          ] + "，"
                                        : ""
                                }}
                                {{
                                    lang.BI_RADS_features[6][newFileObj.file.summary.BI_RADS_features_results[6]]
                                        ? lang.BI_RADS_features[6][
                                              newFileObj.file.summary.BI_RADS_features_results[6]
                                          ] + "，"
                                        : ""
                                }}
                            </template>
                            <template v-else> {{ lang.no_BI_RADS_features_results_tip }} </template>
                        </div>
                    </div>
                    <div class="tips">{{ lang.results_reference_only }}</div>
                </template>
                <div v-else class="no_data">{{ lang.no_ai_result }}</div>
                <!-- <p class="tip">{{lang.results_reference_only}}</p> -->
            </div>
            <div v-else class="no_data">{{ lang.no_ai_result }}</div>
            <template v-if="newFileObj.hasStruct">
                <!--  <div class='row'>
                    <div class='label'> {{lang.exam_picture}} </div>
                </div> -->
                <div
                    class="row"
                    v-if="newFileObj.type != 'breast' && newFileObj.file.msg_type == systemConfig.msg_type.OBAI"
                >
                    <div class="label">
                        {{ lang.display_exam_original_picture }}
                    </div>
                    <div class="value">
                        <el-switch v-model="isDisplayBColor" @change="displayBColorImage()"> </el-switch>
                        <!-- <el-checkbox v-model="isStructImage" @change="reloadImage()"></el-checkbox> -->
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>
<script>
import base from "../../lib/base";
import { cloneDeep } from "lodash";
import { getMessageAiReportFromLocal, toFixedNumber } from "../../lib/common_base";
import { tag_emoji_err } from "@/common/language/RU";
export default {
    mixins: [base],
    name: "iworksTestImageReport",
    components: {},
    props: {
        currentFile: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    data() {
        return {
            toFixedNumber,
            getMessageAiReportFromLocal,
            ai_type: "",
            breat_report: {},
            // abdomen_report: { dispalyStructs: [] },
            ai_report: { dispalyStructs: [] },
            colors: this.$store.state.aiPresetData.colors,
            newFileObj: {},
            isStructImage: true,
            displayColors: this.$store.state.aiPresetData.colors,
            useColors: [],
            isDisplayBColor: false, //是否显示图像
            // defaultColorsCheck: this.$store.state.aiPresetData.colors.reduce((h,v)=>{
            //     h[v]={isChecked:true,items:[],color:v};return h
            // }, {}),
        };
    },
    computed: {
        aiPresetData() {
            return this.$store.state.aiPresetData;
        },
        aiViews() {
            if (this.ai_type == "cardiac") {
                return this.$store.state.aiPresetData?.cardiacViews || {};
            }
            return this.$store.state.aiPresetData?.iworksAbdomenTest || {};
        },
    },
    created() {
        setTimeout(() => {
            this.checkReportExist(this.currentFile);
        }, 0);
        // this.displayColors = cloneDeep(this.defaultColorsCheck)
    },
    mounted() {},
    watch: {
        currentFile: {
            handler(newValue, oldValue) {
                this.displayColors = this.$store.state.aiPresetData.colors;
                this.useColors = [];
                this.newFileObj = this.checkReportExist(newValue);
                if (
                    newValue &&
                    oldValue &&
                    newValue.resource_id &&
                    oldValue.resource_id &&
                    newValue.resource_id != oldValue.resource_id
                ) {
                    this.isDisplayBColor = false;
                    this.isStructImage = true;
                }
            },
            immediate: true,
            deep: true,
        },
        "$store.state.gallery.commentObj": {
            handler(val) {
                if (val && this.currentFile && this.currentFile.resource_id && val[this.currentFile.resource_id]) {
                    if (
                        val[this.currentFile.resource_id].ai_analyze_report &&
                        val[this.currentFile.resource_id].ai_analyze_report.status
                    ) {
                        this.$nextTick(() => {
                            this.newFileObj = this.checkReportExist(this.currentFile);
                            // this.newFileObj = this.checkReportExist({...this.currentFile,ai_analyze_report:val[this.currentFile.resource_id].ai_analyze_report})
                        });
                    }
                }
            },
        },
    },
    methods: {
        displayBColorImage() {
            this.checkedAllItem(true);
        },
        checkedAllItem(checked) {
            this.isStructImage = checked;
            this.ai_report.dispalyStructs = (this.ai_report.dispalyStructs || []).map((v) => {
                v.isChecked = this.isStructImage;
                return v;
            });
            this.reloadImage();
        },
        checkedOneItem() {
            this.isStructImage = this.ai_report.dispalyStructs.filter((v) => v.isChecked && v.isExist).length > 0;
            this.reloadImage();
        },
        reloadImage() {
            this.displayColors = this.ai_report.dispalyStructs
                .filter((v) => v.isChecked && v.isExist && this.useColors.indexOf(v.color) > -1)
                .map((v) => {
                    return v.color;
                });
            this.$emit("reloadImage", this.isStructImage, this.displayColors, this.isDisplayBColor);
            this.newFileObj = this.checkReportExist(this.currentFile);
        },
        nameDeal(ai_report) {
            let key = this.aiViews.views[ai_report.id] ? this.aiViews.views[ai_report.id].key : "";
            if (key) {
                if (key.toLowerCase() == "undefined") {
                    key = "";
                }
            } else {
                if (this.aiViews.views && ai_report.clip_id >= 0) {
                    for (let k in this.aiViews.views) {
                        let view = this.aiViews.views[k];
                        if (view.key == ai_report.clip_id || view.id == ai_report.clip_id) {
                            key = view.key;
                            if (key) {
                                if (key.toLowerCase() == "undefined") {
                                    key = "";
                                }
                            }
                        }
                    }
                }
            }
            if (key) {
                return this.lang[key];
            } else {
                return "--";
            }
        },
        qualityDeal(ai_report) {
            let quality = ai_report.quality;
            if (quality == undefined || quality == null) {
                if (this.aiViews && ai_report.clip_id >= 0) {
                    for (let k in this.aiViews) {
                        let view = this.aiPresetData[k];
                        if (this.aiViews.standard_desc) {
                            view = { ...view, ...this.aiViews.standard_desc };
                        }
                        if (view.key == ai_report.clip_id || view.key == ai_report.clip_id) {
                            quality = 1;
                            if (ai_report.score > view.ai_height.lowest) {
                                quality = 0;
                            }
                            if (ai_report.score <= view.ai_lower.highest) {
                                quality = 2;
                            }
                        }
                    }
                }
            }
            if (quality == 2) {
                return this.lang.non_standard;
            } else if (quality == 1) {
                return this.lang.basic_standard;
            } else if (quality == 0) {
                return this.lang.standard;
            }

            return "--";
        },
        checkReportExist(file) {
            let newFile = cloneDeep(file);
            let hasStruct = false;
            this.breat_report = {};
            this.ai_report = {};
            let ai_analyze_report = this.getMessageAiReportFromLocal(newFile);
            //type = 2
            newFile.ai_analyze_report = ai_analyze_report;
            let child_name = "";
            if (
                ai_analyze_report &&
                (ai_analyze_report.type == this.aiPresetData.typeIndex.abdomen ||
                    ai_analyze_report.type == this.aiPresetData.typeIndex.cardiac)
            ) {
                this.ai_type = this.aiPresetData.type[ai_analyze_report.type];
                if (ai_analyze_report.clips && ai_analyze_report.clips[file.resource_id]) {
                    this.ai_report = ai_analyze_report.clips[file.resource_id][0] || {};
                }
                this.ai_report.error = ai_analyze_report.error;
                const child_id =
                    this.ai_report.child_id &&
                    this.ai_report.child_id >= 0 &&
                    this.aiViews.views[this.ai_report.child_id]
                        ? this.ai_report.child_id
                        : -1;
                if (this.ai_report.id >= 0 && this.aiViews.views[this.ai_report.id]) {
                    let viewObj = this.aiViews.views[this.ai_report.id];
                    if (child_id > -1) {
                        viewObj = this.aiViews.views[child_id];
                        child_name = this.lang[viewObj.key] || "--";
                    }
                    let dispalyStructs = viewObj.details.map((v) => {
                        return { ...v, isChecked: false };
                    });

                    for (let i in dispalyStructs) {
                        let struct = viewObj.details[i];
                        for (let j in this.ai_report.struct || []) {
                            let item = this.ai_report.struct[j];
                            if (item.type == struct.id) {
                                item.isExist = item.position && item.position.length > 0;
                                hasStruct = hasStruct || item.isExist;
                                dispalyStructs[i] = { ...dispalyStructs[i], ...item };
                                dispalyStructs[i].isChecked = true; //this.isStructImage? this.itemIds.length>0? this.itemIds.indexOf(dispalyStructs[i].id)>-1 : true : false
                            }
                        }
                    }
                    this.ai_report.dispalyStructs = this.getArearcolor(dispalyStructs);
                    this.ai_report.dispalyStructs = this.ai_report.dispalyStructs.map((v) => {
                        if (this.displayColors.indexOf(v.color) > -1) {
                            v.isChecked = true;
                        } else {
                            v.isChecked = false;
                        }
                        if (v.isExist) {
                            this.useColors.push(v.color);
                        }

                        return v;
                    });
                    this.ai_report.dispalyStructs = this.ai_report.dispalyStructs.sort((a, b) => {
                        return b.isExist - a.isExist;
                    });
                }
            }
            //type = 1
            if (ai_analyze_report && ai_analyze_report.type == this.aiPresetData.typeIndex.breast) {
                hasStruct =
                    hasStruct ||
                    (ai_analyze_report.mark_list &&
                        ai_analyze_report.mark_list[file.resource_id] &&
                        ai_analyze_report.mark_list[file.resource_id][0].point &&
                        ai_analyze_report.mark_list[file.resource_id][0].point.length > 0);
                this.ai_type = this.aiPresetData.type[ai_analyze_report.type];
                if (ai_analyze_report.mark_list && ai_analyze_report.mark_list[newFile.resource_id]) {
                    let mark_lists = ai_analyze_report.mark_list[newFile.resource_id] || [[]];
                    newFile.mark_list = mark_lists;
                    newFile.summary = ai_analyze_report.summary || {};
                }
            }
            return { hasStruct: hasStruct, file: newFile, child_name };
        },
        getArearcolor(structure) {
            let n = 0;
            let colorStruc = [];
            for (let k in structure) {
                let v = structure[k];
                if (v.isExist) {
                    v.color = this.colors[n];
                    colorStruc.push(v);
                } else {
                    colorStruc.push(v);
                }
                n++;
            }
            return colorStruc;
        },
    },
};
</script>
<style lang="scss" scoped>
.iworks_test_image_report {
    display: flex;
    flex-direction: column;
    padding: 15px 10px 15px 10px;
    overflow: auto;
    .content {
        .row {
            display: flex;
            flex-direction: row;
            .label {
                width: 120px;
                text-align: left;
                font-size: 14px;
                color: #6c878c;
                margin-right: 10px;
                margin-bottom: 15px;
            }
            .value_list {
                width: calc(100% - 120px);
                font-size: 14px;
                color: #6c878c;
                display: flex;
                flex-direction: column;
                div {
                    margin-bottom: 8px;
                }
            }
            .value {
                width: calc(100% - 120px);
                font-size: 14px;
                color: #6c878c;
                display: flex;
                margin-bottom: 8px;
                flex-direction: column;
                table {
                    color: #333;
                    border: 1px solid #bbb;
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 10px;
                    th,
                    td {
                        border: 1px solid #bbb;
                        margin: auto;
                        padding: 5px;
                        border: 1px solid #bbb;
                        padding: 6px;
                        text-align: center;
                        font-family: "Arial Normal", "Arial";
                        font-weight: 400;
                        font-style: normal;
                        font-size: 13px;
                        text-align: center;
                        line-height: normal;
                        vertical-align: middle;
                        color: #537378;
                    }
                    .name {
                        color: #0000cc;
                    }
                    .is_required {
                        color: red;
                    }
                    .is_optional {
                        color: #0000cc;
                    }
                    .arear {
                        display: inline-block;
                        height: 13px;
                        width: 25px;
                        border: 0px solid;
                        box-shadow: 2px 4px 10px 5px #99999954;
                    }
                }
            }
        }
        .operation_col {
            width: 55px;
        }
        .tips {
            position: absolute;
            bottom: 0px;
            font-size: 14px;
            color: #6c878c;
            background: #ecf6f6;
            display: block;
            width: 100%;
        }
        .no_data {
            text-align: center;
            font-size: 15px;
            color: #6c878c;
        }
    }
}
</style>
