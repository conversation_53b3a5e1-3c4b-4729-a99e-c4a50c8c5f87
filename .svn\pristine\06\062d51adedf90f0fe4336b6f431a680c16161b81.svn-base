<template>
    <transition name="slide">
    	<div class="setting_page second_level_page">
            <mrHeader>
                <template #title>
                    {{lang.setting_title}}
                </template>
            </mrHeader>
    		<div class="setting_list_group">
                <!-- <van-cell :title="lang.current_account_storage" is-link url="#/index/setting/localstorage"></van-cell> -->
                <!-- <van-cell :title="lang.other_account_storage" is-link url="#/index/setting/localstorage_other"></van-cell> -->
                <van-cell :title="lang.file_storage_manage" is-link url="#/index/setting/localstorage_file"></van-cell>
                <van-cell :title="lang.network_setting" is-link v-if="isUltraSoundMobile" @click="linkToSystemNetworkSetting"></van-cell>
                <van-cell :title="lang.push_stream_setting" is-link v-if="functionsStatus.live&&isUltraSoundMobile" url="#/index/setting/push_stream_setting"></van-cell>
                <van-cell :title="lang.data_traffic_waring" is-link v-if="isUltraSoundMobile" @click="linkToSystemDataTrafficSetting"></van-cell>
                <van-cell :title="lang.storeState" is-link v-if="$root.isInitVConsole" url="#/index/setting/store_state_view"></van-cell>
                <van-cell :title="lang.upload_log_file" is-link v-if="!isBrowser" @click="uploadLogFile"></van-cell>
                <van-cell :title="lang.international_title" is-link  url="#/index/setting/international"></van-cell>
                <div class="system_setting_item">
                    <span class="item_label">{{lang.desensitization_reception}}</span>
                    <div class="item_content">
                        <van-switch v-model="isDesensitization"  active-color="#00c59d" inactive-color="#D9D9D9" @change="toggleDesensitization" />
                    </div>
                </div>
                <div class="system_setting_item">
                    <span class="item_label">摄像头默认设置</span>
                    <div class="item_content">
                        <van-switch v-model="isCameraDefaultSetting"  active-color="#00c59d" inactive-color="#D9D9D9" @change="toggleCameraDefaultSetting" />
                    </div>
                </div>
                <van-cell is-link :title="lang.mode_select" :value="noticePushMode" @click="showModeSelect"></van-cell>
                <van-popup v-model="isShowModeSelect" position="bottom" closeable round>
                    <div class="mode_select_options">
                        <p class="mode_select_title">{{ lang.mode_select }}</p>
                        <van-cell :title="lang.simplified_mode" @click="updateNoticePush(0)"></van-cell>
                        <van-cell :title="lang.detailed_mode" @click="updateNoticePush(1)"></van-cell>
                    </div>
                </van-popup>
                <van-cell :title="lang.auto_recognition_threshold" is-link  url="#/index/setting/auto_recognition_threshold"></van-cell>
    		</div>
            <transition name="slide">
                <router-view></router-view>
            </transition>
    	</div>
	</transition>
</template>
<script>
import base from '../lib/base'
import { Cell, Toast, Switch, Popup } from 'vant';
import service from '../service/service'
import Tool from '@/common/tool'
export default {
    mixins: [base],
    name: 'SettingPage',
    components: {
        VanCell:Cell,
        VanSwitch: Switch,
        VanPopup: Popup,
    },
    computed:{
        isUltraSoundMobile(){
            return this.$store.state.device.isUltraSoundMobile
        },
        isBrowser(){
            return this.systemConfig.clientType == 5
        },
        noticePushMode(){
            return this.noticePushData == 0? this.lang.simplified_mode : this.lang.detailed_mode
        }

    },
    data(){
        return {
            isDesensitization:false,
            isShowModeSelect: false,
            noticePushData: 0,
            isCameraDefaultSetting: false
        }
    },
    created(){
        this.isDesensitization=this.user.preferences.is_desensitization==1?true:false;
        this.initNoticePush();
        this.initCameraDefaultSetting();
    },
    methods:{
        linkToSystemNetworkSetting(){
            window.CWorkstationCommunicationMng.LinkToSystemNetworkSetting()
        },
        linkToSystemDataTrafficSetting(){
            window.CWorkstationCommunicationMng.linkToSystemDataTrafficSetting()
        },
        uploadLogFile(){
            window.CWorkstationCommunicationMng.uploadLogFile()
            // Toast(this.lang.upload_log_file_success);
        },
        toggleDesensitization(){
            let isDesensitization=this.isDesensitization?1:0;
            const data={
                is_desensitization:isDesensitization
            }
            this.$root.socket.emit("set_user_other_info", data,(is_succ,info)=>{
                if (is_succ) {
                    let preferences=this.user.preferences;
                    preferences.is_desensitization=isDesensitization;
                    this.$store.commit('user/updateUser',{
                        preferences
                    })
                }else{
                    Toast(this.lang.update_failed_text)
                    this.isDesensitization=!this.isDesensitization;
                }
            });
        },
        initNoticePush(){
            service.getNoticePush({}).then((res)=>{
                if (res.data.error_code==0) {
                    this.noticePushData = res.data.data;
                    // console.log('获取数据'+JSON.stringify(res.data)+this.user.username);
                    // this.noticePushMode = noticePushData == 0? this.lang.simplified_mode : this.lang.detailed_mode;
                }
            })
        },
        showModeSelect() {
            this.isShowModeSelect = true;
        },
        updateNoticePush(noticePushData) {
            this.isShowModeSelect = false;
            service.updateNoticePush({text_enable:noticePushData}).then((res)=>{
                if (res.data.error_code==0) {
                    this.noticePushData = noticePushData;
                    Toast(this.lang.operate_success);
                }
            });
        },
        initCameraDefaultSetting() {
            this.isCameraDefaultSetting = Tool.getCameraDefaultSetting();
        },
        toggleCameraDefaultSetting() {
            Tool.setCameraDefaultSetting(this.isCameraDefaultSetting);
        }
    }
}

</script>
<style lang="scss">
.setting_page{
    .setting_list_group{
        .van-cell{
            font-size: .8rem;
            line-height: 1.2rem;
            padding: .5rem .8rem;
        }

        .system_setting_item {
            font-size: 0.8rem;
            padding: .5rem .8rem;
            line-height: 1.2rem;
            display: flex;
            .item_label{
                flex:1;
            }
        }
        .mode_select_options{
           .mode_select_title{
               text-align: center;
               padding: .5rem 0;
               font-size: .9rem;
           }
        }
    }

}
</style>
