<template>
    <transition name="fade">
        <div class="visiting_card_page third_level_page">
            <mrHeader>
                <template #title>
                    {{ lang.group_visiting_card_title }}
                </template>
            </mrHeader>
            <div class="container" v-loading="loading">
                <div class="card_item">
                    <div class="card_line card_avatar clearfix">
                        <div class="avatar">
                            <mr-avatar :url="getLocalAvatar(groupObj)" :origin_url="groupObj.avatar" :radius="4" :showOnlineState="false" :key="groupObj.avatar"></mr-avatar>
                        </div>
                        <div class="card_titile">
                            <p class="nickname">
                                <span class="nickname_text longwrap"
                                    >{{ groupObj.subject }}</span
                                >
                            </p>
                        </div>
                    </div>
                </div>
                <div class="card_item">
                    <div v-if="status===joinGroupState.joined" class="card_line" >{{ lang.joined_txt }}</div>
                    <div v-if="status===joinGroupState.unapply" class="card_line card_btn" @click.stop="requestAddGroup">{{ lang.group_card_apply_btn }}</div>
                    <div v-else class="card_line" >{{ lang.applying_txt }}</div>
                </div>
            </div>
        </div>
    </transition>
</template>
<script>
import base from "../lib/base";
import { Toast } from 'vant';
import { parseImageListToLocal,getLocalAvatar} from "../lib/common_base";
export default {
    mixins: [base],
    name: "group_visiting_card",
    components: {},
    data() {
        return {
            getLocalAvatar,
            loading: false,
            groupObj: {},
            ConversationConfig: this.$store.state.systemConfig.ConversationConfig,
            joinGroupState:this.$store.state.systemConfig.joinGroupState,
            inviteCode:'',
            inviterInfo:{},
            autoMakeFriend:false,
            from:'',
            status:0,
        };
    },
    beforeRouteEnter(to, from, next) {
        if (!from.name) {
            next(false)
            window.location.replace(`#/index`);
        } else {
            next();
        }
    },
    mounted() {
        this.inviteCode = this.$store.state.relationship.urlQuery.inviteCode
        this.from = this.$store.state.relationship.urlQuery.from;
        this.loading = true
        window.main_screen.getInviteInfo({
            code:this.inviteCode
        },(res)=>{
            this.loading = false
            if (res.error_code==0) {
                const data = res.data
                const groupList = this.$store.state.groupList;
                let isInnerShip = false
                // 群成员扫码：进入群组会话
                for(const group of groupList) {
                    if(group.id == data.gid) {
                        isInnerShip = true

                        setTimeout(() => {
                            this.openConversation(data.gid, 5)
                        },50)
                        break
                    }
                }
                // 非群成员扫码：进入群卡片申请加入
                if(!isInnerShip) {
                    this.autoMakeFriend = data.autoMakeFriend;
                    const group = data.groupInfo
                    console.log("########group:",data)
                    if(group.is_obsolete) {
                        Toast(this.lang.group_has_deleted_text)
                        return
                    }
                    group.is_single_chat = 0
                    // 图片本地化
                    parseImageListToLocal([group],'avatar')
                    this.setDefaultImg([group])
                    this.inviterInfo = data.inviterInfo;
                    this.groupObj = group;
                    this.status= data.status||0;
                }
            }else{
                this.back();
            }
        })
    },
    computed: {
        isApplying() {
            let applyFriendList = this.$store.state.relationship.applyFriendList;
            for (let id of applyFriendList) {
                if (id == this.inviterInfo.id) {
                    return true;
                }
            }
            return false;
        },
        isSelf() {
            return this.inviterInfo.id == this.me.uid;
        },
        isFriend() {
            let friendList = this.$store.state.friendList.list;
            for (let friend of friendList) {
                if (friend.id == this.inviterInfo.id) {
                    return true;
                }
            }
            return false;
        },
        me() {
            return this.$store.state.user;
        },
    },
    methods: {
        requestAddFriend() {
            var user = this.inviterInfo;
            this.$root.socket.emit("request_add_friend", { id: user.id });
            this.$store.commit("relationship/addApplyFriend", user);
        },
        requestAddGroup() {
            const that = this;
            const group = this.groupObj;
            console.log("+++++++GroupObj:", this.groupObj);
            const source = this.from ==='weChat'?4:3
            const is_public = group.is_public;
            const nickname = that.inviterInfo.nickname;
            const gid = group.id;
            const uid = this.inviterInfo.id;
            const avatar = group.avatar;
            const avatar_local = group.avatar_local;
            that.loading = true;
            window.main_screen.applyJoinGroup({
                mark:'',
                gid:gid,
                inviterID:this.inviterInfo.id,
                source:source,
            },(res)=>{
                that.loading = false;
                if (res.error_code==0) {
                    // 申请加入操作成功，使用checkJoinGroupStatus检查状态
                    window.main_screen.checkJoinGroupStatus({
                        gid: gid
                    }, (checkRes) => {
                        if (checkRes.error_code === 0) {
                            const enterStatus = checkRes.data.enterStatus;
                            
                            // 根据enterStatus判断显示不同的提示和处理逻辑
                            if (enterStatus === true) {
                                // 已经加入或直接加入成功
                                Toast(that.lang.add_group_successful);
                                that.addGroupAndOpenConversation(gid, group, is_public);
                            } else {
                                // 需要申请或申请中
                                that.status = that.joinGroupState.applyed; // 显示为申请中
                                Toast(that.lang.group_apply_success);
                            }
                        } else {
                            console.log('群组加入状态检查失败:', checkRes.error_message);
                            // 检查失败时的默认处理
                            if (group.more_details && group.more_details.join_check) {
                                that.status = that.joinGroupState.applyed; // 显示为申请中
                                Toast(that.lang.group_apply_success);
                            } else {
                                Toast(that.lang.add_group_successful);
                                that.addGroupAndOpenConversation(gid, group, is_public);
                            }
                        }
                    });

                    // 首先，如果满足条件，则发送好友请求
                    // 自动加好友需要满足4个条件：1、开关开着 2、分享者不在申请列表内 3、分享者与自己不为好友 4、不是自己
                    if (this.autoMakeFriend && !that.isApplying && !that.isFriend && !that.isSelf) {
                        that.requestAddFriend();
                    }
                }
            })
        },
        
        // 添加群组并打开会话的通用方法
        addGroupAndOpenConversation(gid, groupInfo, is_public) {
            const that = this;
            const groupTemp = {
                id: gid,
                subject: groupInfo.subject,
                is_single_chat: 0,
                avatar: groupInfo.avatar,
                avatar_local: groupInfo.avatar_local,
                type: that.ConversationConfig.type.Group,
                service_type: that.systemConfig.ServiceConfig.type.None,
                is_public: is_public,
            };
            that.setDefaultImg([groupTemp]);
            parseImageListToLocal([groupTemp], "avatar");
            that.$root.updateGroupAvatarUserList[gid] = groupTemp;
            that.$store.commit("groupList/addGroup", groupTemp);
            
            that.openConversation(gid, 5);
        }
    },
};
</script>
<style lang="scss">
// .visiting_card_page{
//     .container{
//         background:#fff;
//         padding-bottom:1rem;
//         .avatar{
//             display:block;
//             width:8rem;
//             height:8rem;
//             background-size: 100% 100%;
//             margin: 2rem auto 0.5rem;
//             border-radius: 50%;
//             box-shadow: 0px 0.3rem 0.5rem #aaa;
//         }
//         .nickname{
//             position: relative;
//             display: inline-block;
//             left: 50%;
//             transform: translate(-50%);
//             font-size: 1.2rem;
//             color: #333;
//             line-height: 2rem;
//             height: 2rem;
//         }
//     }
//     .btn_container{
//         padding:0 1rem;
//         .chat_btn{
//             display: block;
//             width: 100%;
//             border: none;
//             font-size: 1rem;
//             line-height: 2rem;
//             margin: 1rem 0 .6rem;
//             border-radius: .2rem;
//         }
//         .applying_tip{
//             text-align: center;
//             margin: 0.5rem 0;
//             color: #666;
//         }

//     }
// }
</style>
