<template>
    <div class="club_entry">
        club
    </div>
</template>
<script>
import base from '../../lib/base';
export default {
    name:'clubEntry',
    mixins:[base],
    
    components:{
    },
    data(){
        return {
        }
    },
    computed:{
        
    },
    watch:{
        
    },
    mounted(){
        
    },
    beforeDestroy(){
        
    },
    methods:{
        
    }
}
</script>
<style lang="scss">
.club_entry{
    position:fixed;
    z-index:1;
    top: 0;
    bottom:0;
    width: 100%;
    background: #fff;
    transform:translate3d(0,0,0);
    display: flex;
    flex-direction: column;
    background: #fff;
    
}
</style>
