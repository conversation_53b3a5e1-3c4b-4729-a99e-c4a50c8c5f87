<template>
    <transition name="slide">
        <div class="practice-answer-action_page fifth_level_page">
            <mrHeader>
                <template #title>
                    {{ questionData.hideField?lang.clinical_thinking_practice_title:questionData.title || lang.clinical_thinking_practice_title }}
                </template>
                <template #right>
                    <div class="elapsed-time-container" v-if="!hasSubmit">
                        <span class="elapsed-time-value">{{ elapsedTime }} {{ lang.live_replay_second }}</span>
                    </div>
                </template>
            </mrHeader>

            <div class="custom_body" ref="customBody">
                <QuestionDisplay :clinicalData="questionData" :imagePreviewShow.sync="imagePreviewShow"/>
                <div class="answer_action_container">
                    <MyAnswerDisplay :answer="answerData" v-if="hasSubmit" class="my_answer_display" />

                    <div v-if="hasAnswer || hasSubmit" class="answering_result_container">
                        <template v-if="hasSubmit && !hasAnswer">
                            <p class="answering_tips" v-if="!hasError">
                                {{ lang.answer_automatically_review_tips }}
                            </p>
                            <div class="loading-container" v-if="hasError">
                                <p>{{lang.ai_analysis_encountered_retry_tips}}</p>
                                <span @click="retrySubmit" class="retry-btn">{{lang.retry}}</span>
                            </div>
                            <div class="loading-container" v-else>
                                <van-loading type="spinner" color="#1989fa" size="0.8rem" />
                                <p>{{ lang.please_wait_ai_analysis }}</p>
                            </div>
                        </template>

                        <AIResult :resultData="aiAnalysis" v-if="hasAnswer" class="answering_result_ai_result" />
                    </div>
                </div>
            </div>

            <div class="custom_footer">
                <div class="top_container">
                    <div class="input_container" v-if="!hasSubmit">
                        <van-field
                            v-model="userAnswer"
                            type="textarea"
                            :placeholder="`${lang.please_answer_here}：${questionData.questionTips}`"
                            :rows="5"
                            autosize
                            maxlength="1000"
                            show-word-limit
                        />
                    </div>
                </div>
                <div class="bottom_container">
                    <van-button
                        type="primary"
                        block
                        :loading="isSubmitting"
                        @click="submitAnswer"
                        class="action-button"
                        v-if="showSubmitButton"
                        :disabled="disabledSubmit"
                        :loading-text="lang.evaluating"
                    >
                        {{ lang.submitted_answers }}
                    </van-button>
                    <van-button type="primary" block @click="reAnswer" class="action-button" v-if="hasAnswer || hasError">
                        {{ lang.re_answer }}
                    </van-button>
                    <van-button
                        type="primary"
                        block
                        :loading="getTopicRequesting"
                        @click="nextQuestion"
                        class="action-button"
                    >
                        {{ lang.next_question }}
                    </van-button>
                    <!-- <van-button block @click="back" class="action-button"> 返回 </van-button> -->
                </div>
            </div>
        </div>
    </transition>
</template>

<script>
import QuestionDisplay from "./practiceQuestionDisplay.vue";
import MyAnswerDisplay from "./practiceMyAnswerDisplay.vue";
import AIResult from "./practiceAIResult.vue";
import base from "../../lib/base";
import { Button, Field, Loading, Dialog, Toast } from "vant";
import Tool from "@/common/tool";
import AIBookServiceInstance from "@/common/aiBookService";
import moment from "moment";
export default {
    mixins: [base],
    name: "PracticeAnswerAction",
    components: {
        QuestionDisplay,
        MyAnswerDisplay,
        AIResult,
        [Button.name]: Button,
        [Field.name]: Field,
        [Loading.name]: Loading,
    },
    beforeRouteLeave(to, from, next) {
        if(this.imagePreviewShow){
            next()
            return
        }
        if (this.userAnswer.trim() && !this.hasSubmit) {
            Tool.openMobileDialog({
                message: this.lang.answer_not_submitted_leave_tips,
                confirm: () => {
                    next();
                },
                reject: () => {
                    next(false);
                },
                cancel: () => {
                    next(false);
                },
            });
        } else {
            next();
        }
    },
    computed: {
        disabledSubmit() {
            return this.isSubmitting || this.hasSubmit || this.userAnswer.trim() === '';
        },
        showSubmitButton() {
            return !this.hasAnswer&&!this.hasError;
        },
    },
    data() {
        return {
            hasAnswer: false,
            hasSubmit: false,
            questionData: {
                bodyPart: "",
                content: {
                    txt: [],
                    image: [],
                    video: [],
                },
                title: "",
                questionTips: "",
                caseID: "",
                hideField: false,
            },
            answerData: {
                timestamp: "",
                answer: "",
            },
            userAnswer: "",
            aiAnalysis: {},
            getTopicRequesting: false,
            aiBookService: AIBookServiceInstance,
            isSubmitting: false,
            timer: null,
            elapsedTime: 0,
            practiceType: "random",
            hasError: false,
            imagePreviewShow: false,
        };
    },
    created() {
        this.questionData = this.$route.params;
        this.questionData.hideField = true
        this.practiceType = this.$route.params.practiceType;
        this.startTimer();
    },
    beforeDestroy() {
        this.clearTimer();
    },
    methods: {
        back() {
            this.$router.back();
        },
        scrollToBottom() {
            const dialogBody = this.$refs.customBody;
            if (dialogBody) {
                dialogBody.scrollTo({
                    top: dialogBody.scrollHeight,
                    behavior: "smooth",
                });
            }
        },
        scrollToTop() {
            const dialogBody = this.$refs.customBody;
            if (dialogBody) {
                dialogBody.scrollTo({
                    top: 0,
                    behavior: "smooth",
                });
            }
        },
        async submitAnswer() {
            if (this.userAnswer.trim() === "") {
                return;
            }
            if (this.isSubmitting) {
                return;
            }
            this.isSubmitting = true;

            const params = {
                userAnswer: this.userAnswer,
                useTime: this.elapsedTime,
                caseID: this.questionData.caseID,
            };
            this.clearTimer();

            this.aiBookService.submitAiPracticeCaseAnswer({
                params,
                onAnswering: (data) => {
                    this.hasSubmit = true;
                    this.isSubmitting = false;
                    this.hasError = false;
                    this.questionData = {
                        ...this.questionData,
                        hideField: false
                    };
                    this.answerData = {
                        answer: this.userAnswer,
                        timestamp: moment().format("YYYY-MM-DD HH:mm:ss"),
                    };
                    this.$nextTick(() => {
                        this.scrollToBottom();
                    });
                },
                onCompleted: (data) => {
                    this.aiAnalysis = data;
                    this.hasAnswer = true;
                    this.hasError = false;
                    this.$nextTick(() => {
                        this.scrollToBottom();
                    });
                },
                onError: (error) => {
                    Toast("submit error");
                    this.hasError = true;
                    this.isSubmitting = false;
                },
            });
        },
        reAnswer() {
            Tool.openMobileDialog({
                message: this.lang.re_answer_cleared_tips,
                confirm: () => {
                    this.clearStatus();
                    this.startTimer();
                },
            });
        },
        clearStatus() {
            this.hasAnswer = false;
            this.hasSubmit = false;
            this.answerData = {
                timestamp: "",
                answer: "",
            };
            this.userAnswer = "";
            this.aiAnalysis = {};
            this.hasError = false;
            this.aiBookService.cancelAiPracticeCaseAnswer();
        },
        startTimer() {
            this.clearTimer();
            this.elapsedTime = 0;
            this.timer = setInterval(() => {
                this.elapsedTime++;
            }, 1000);
        },
        clearTimer() {
            if (this.timer) {
                clearInterval(this.timer);
                this.timer = null;
            }
        },
        async getAiPracticeCase(oParams) {
            this.getTopicRequesting = true;
            try {
                let params = {
                    ...oParams,
                };
                if (this.practiceType === "bodyPart") {
                    params.bodyPart = this.questionData.bodyPart;
                }
                const data = await this.aiBookService.getAiPracticeCase(params);
                this.questionData = {
                    bodyPart: data.bodyPart,
                    content: data.content,
                    title: data.title,
                    questionTips: data.question,
                    caseID: data._id,
                    hideField: true,
                };
                this.getTopicRequesting = false;
                setTimeout(() => { // 延迟100ms后执行 IOS需要延迟
                    this.scrollToTop();
                }, 100);
                return data;
            } catch (error) {
                console.log(error, 22);
                this.getTopicRequesting = false;
                return null;
            }
        },
        async nextQuestion() {
            let params = {
                excludeCaseID: this.questionData.caseID,
            };
            if (!this.hasSubmit) {
                Tool.openMobileDialog({
                    message: this.lang.answer_not_submitted_next_tips,
                    confirm: async () => {
                        await this.getAiPracticeCase(params);
                        this.clearStatus();
                        this.startTimer();
                    },
                });
            } else {
                await this.getAiPracticeCase(params);
                this.clearStatus();
                this.startTimer();
            }
        },
        formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
        },
        retrySubmit() {
            this.hasError = false;
            this.hasSubmit = false;
            this.submitAnswer();
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync/style/aiChat.scss';
.practice-answer-action_page {
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    height: 100%;
    .elapsed-time-container {
        display: flex;
        align-items: center;
        font-size: 0.7rem;
        .elapsed-time-label {
            white-space: nowrap;
        }

        .elapsed-time-value {
            min-width: 2rem; // 确保数字部分有固定宽度
            text-align: right;
        }
    }
    .custom_body {
        flex: 1;
        padding: 0.6rem;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        background-color: rgb(235, 239, 242);

        .answer_action_container {
            display: flex;
            flex-direction: column;
            gap: 0.6rem;
            padding-bottom: 0.6rem;
            background-color: transparent;
        }

        .my_answer_display {
            background-color: #fff;
            box-shadow: 0 0.087rem 0.437rem rgba(0, 0, 0, 0.05);
        }

        .answering_result_container {
            background-color: #fff;
            box-shadow: 0 0.087rem 0.437rem rgba(0, 0, 0, 0.05);
            padding: 0.6rem;

            .answering_result_ai_result {
                padding: 0;
            }

            .answering_tips {
                font-size: 0.7rem;
                color: #606266;
                margin-bottom: 0.6rem;
                display: block;
            }
        }
    }

    .input_container {
        max-height: 7rem;
        overflow-y: auto;
        background-color: #f5f7fa;
        border: 0.05rem solid #e4e7ed;
        padding: 0.4rem;
        border-radius: 0.2rem;
        .input_label {
            font-size: 0.7rem;
            color: #333;
            margin-bottom: 0.4rem;
            font-weight: 500;
        }

        :deep(.van-field__control) {
            font-size: 0.7rem;
            line-height: 1.5;
            overflow: hidden;
        }
        :deep(.van-field) {
            padding: 0;
            background: none;
        }
    }

    .custom_footer {
        padding: 0.6rem;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        background-color: #fff;
        box-shadow: 0 -0.05rem 0.2rem rgba(0, 0, 0, 0.05);
        .top_container {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        .bottom_container {
            display: flex;
            flex-direction: row;
            gap: 0.5rem;
            .action-button {
                @extend .ai-theme-background;
                height: 2rem;
                font-size: 0.7rem;
                border-radius: 0.2rem;
            }
        }
    }

    .loading-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 0.6rem;

        p {
            margin: 0;
            color: #606266;
            font-size: 0.7rem;
        }

        .retry-btn {
            color: #1989fa;
            text-decoration: underline;
            padding: 0;
            font-size: 0.7rem;
            margin-left: 0.5rem;
        }
    }
}
</style>
