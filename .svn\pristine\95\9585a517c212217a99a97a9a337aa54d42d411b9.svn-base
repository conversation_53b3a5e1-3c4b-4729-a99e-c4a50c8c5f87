/////////////////////////////////////////// 新版音视频交互 end ////////////////////////////////////////

import requestManager from '@/common/CommunicationMng/requestManager';
import { logger } from '@/common/CommunicationMng/CLogger';
export default function CLiveConferenceBridge(CWorkstationCommunicationMng){
    //js通知app  通知原生初始化声网appid等 js-->pc {appId:'',proxy:{isProxy: true,proxyIp:"http://*************:4433"}}
    CWorkstationCommunicationMng.initNativeAgoraSdk = function (params) {
        logger.log({message: 'CWorkstationCommunicationMng.initNativeAgoraSdk',data: params})
        // CWorkstationCommunicationMng.resetApp();
        this.query('initNativeAgoraSdk:' + JSON.stringify(params))
    }
    //app通知js  通知原生初始化声网appid等 js-->pc {error_code:0,error_message:'success'}
    CWorkstationCommunicationMng.NotifyInitNativeAgoraSdk = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyInitNativeAgoraSdk',data: json})
        requestManager.handleResponse('NotifyInitNativeAgoraSdk', json)
    }
    //主流申请加入房间 js-->app params {channelId:'', uid:'', token:''}
    CWorkstationCommunicationMng.JoinChannelMain = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.JoinChannelMain',data: params})
        CWorkstationCommunicationMng.query('JoinChannelMain:' + JSON.stringify(params))
    }
    //辅流申请加入房间 js-->app params {channelId:'', uid:'', token:'',group_title:'',recordFileName:'',isHost:false,}
    CWorkstationCommunicationMng.JoinChannelAux = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.JoinChannelAux',data: params})
        CWorkstationCommunicationMng.query('JoinChannelAux:' + JSON.stringify(params))
    }
    //通知主流加入房间结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyJoinChannelMain = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyJoinChannelMain',data: json})
        requestManager.handleResponse('NotifyJoinChannelMain', json)

    }
    //通知辅流加入房间结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyJoinChannelAux = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyJoinChannelAux',data: json})
        requestManager.handleResponse('NotifyJoinChannelAux', json)
    }
    //通知主流重新加入频道回调结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyRejoinChannelMain = function(json_str){
        logger.log({message: 'CWorkstationCommunicationMng.NotifyRejoinChannelMain',data: json_str})
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        requestManager.handleResponse('NotifyRejoinChannelMain', json)
    }
    //通知辅流重新加入频道回调结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyRejoinChannelAux= function(json_str){
        logger.log({message: 'CWorkstationCommunicationMng.NotifyRejoinChannelAux',data: json_str})
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        requestManager.handleResponse('NotifyRejoinChannelAux', json)
    }
    //主流申请离开房间 js-->app params {}
    CWorkstationCommunicationMng.LeaveChannelMain = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.LeaveChannelMain',data: params})
        CWorkstationCommunicationMng.query('LeaveChannelMain:' + JSON.stringify(params))
    }
    //辅流申请离开房间 js-->app params {}
    CWorkstationCommunicationMng.LeaveChannelAux = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.LeaveChannelAux',data: params})
        CWorkstationCommunicationMng.query('LeaveChannelAux:' + JSON.stringify(params))
    }
    //通知主流离开房间结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyLeaveChannelMain = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyLeaveChannelMain',data: json})
        requestManager.handleResponse('NotifyLeaveChannelMain', json)
    }
    //通知辅流离开房间结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyLeaveChannelAux = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyLeaveChannelAux',data: json})
        requestManager.handleResponse('NotifyLeaveChannelAux', json)
    }
    //通知有其他用户加入房间 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyOtherUserJoined = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyOtherUserJoined',data: json})
        requestManager.handleResponse('NotifyOtherUserJoined', json)
    }
    //通知有其他用户离开房间 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyOtherUserOffline = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyOtherUserOffline',data: json})
        requestManager.handleResponse('NotifyOtherUserOffline', json)
    }
    //申请开/关本地音频流 js-->app params {uid:xxx,isMute:true,micId:'',speakerId:''}
    CWorkstationCommunicationMng.MuteLocalAudioStream = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.MuteLocalAudioStream',data: params})
        CWorkstationCommunicationMng.query('MuteLocalAudioStream:' + JSON.stringify(params))
    }
    //通知开/关音频流结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyMuteLocalAudioStream = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyMuteLocalAudioStream',data: json})
        requestManager.handleResponse('NotifyMuteLocalAudioStream', json)
    }
    //申请开/关本地视频流 js-->app params {uid:xxx,isMute:true,videoSource:'desktop/device',deviceId:'',}
    CWorkstationCommunicationMng.MuteLocalVideoStream = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.MuteLocalVideoStream',data: params})
        CWorkstationCommunicationMng.query('MuteLocalVideoStream:' + JSON.stringify(params))
    }
    //通知开/关视频流结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyMuteLocalVideoStream = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyMuteLocalVideoStream', json)
        requestManager.handleResponse('NotifyMuteLocalVideoStream', json)
    }
    //通知远端用户音频流变化结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyRemoteAudioStateChanged = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        // console.info('CWorkstationCommunicationMng.NotifyRemoteAudioStateChanged', json)
        requestManager.handleResponse('NotifyRemoteAudioStateChanged', json)
    }
    //通知远端用户视频流变化结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyRemoteVideoStateChanged = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        // console.info('CWorkstationCommunicationMng.NotifyRemoteVideoStateChanged', json)
        requestManager.handleResponse('NotifyRemoteVideoStateChanged', json)
    }
    //通知远端用户音频流统计信息结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyRemoteAudioStats = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        // console.info('CWorkstationCommunicationMng.NotifyRemoteAudioStats', json)
        requestManager.handleResponse('NotifyRemoteAudioStats', json)
    }
    //通知远端用户视频流统计信息结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyRemoteVideoStats = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        // console.info('CWorkstationCommunicationMng.NotifyRemoteVideoStats', json)
        requestManager.handleResponse('NotifyRemoteVideoStats', json)
    }
    //通知本地音频流变化结果 app-->js json {error_code:0,data:{uid:xxx,state:2},error_message:'success'}
    CWorkstationCommunicationMng.NotifyLocalAudioStateChanged = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        // console.info('CWorkstationCommunicationMng.NotifyLocalAudioStateChanged', json)
        requestManager.handleResponse('NotifyLocalAudioStateChanged', json)
    }
    //通知本地视频流变化结果 app-->js json {error_code:0,data:{uid:xxx,state:2},error_message:'success'}
    CWorkstationCommunicationMng.NotifyLocalVideoStateChanged = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        // console.info('CWorkstationCommunicationMng.NotifyLocalVideoStateChanged', json)
        requestManager.handleResponse('NotifyLocalVideoStateChanged', json)
    }
    //订阅远端流 js-->app params {uid:xxx}
    CWorkstationCommunicationMng.SubscribeRemoteStreamMain = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.SubscribeRemoteStreamMain',data: params})
        CWorkstationCommunicationMng.query('SubscribeRemoteStreamMain:' + JSON.stringify(params))
    }
    //订阅远端流 js-->app params {uid:xxx}
    CWorkstationCommunicationMng.SubscribeRemoteStreamAux = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.SubscribeRemoteStreamAux',data: params})
        CWorkstationCommunicationMng.query('SubscribeRemoteStreamAux:' + JSON.stringify(params))
    }
    //通知订阅远端流结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifySubscribeRemoteStreamMain = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifySubscribeRemoteStreamMain',data: json})
        requestManager.handleResponse('NotifySubscribeRemoteStreamMain', json)
    }
    //通知订阅远端流结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifySubscribeRemoteStreamAux= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifySubscribeRemoteStreamAux',data: json})
        requestManager.handleResponse('NotifySubscribeRemoteStreamAux', json)
    }
    //停止订阅远端主流 js-->app(only pc) params {uid:'xxx'}
    CWorkstationCommunicationMng.StopSubscribeRemoteStreamMain = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.StopSubscribeRemoteStreamMain',data: params})
        CWorkstationCommunicationMng.query('StopSubscribeRemoteStreamMain:' + JSON.stringify(params))
    }
    //通知停止订阅远端主流结果 app(only pc)-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyStopSubscribeRemoteStreamMain = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyStopSubscribeRemoteStreamMain',data: json})
        requestManager.handleResponse('NotifyStopSubscribeRemoteStreamMain', json)
    }
    //停止订阅远端辅流 js-->app params {uid:'xxx'}
    CWorkstationCommunicationMng.StopSubscribeRemoteStreamAux = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.StopSubscribeRemoteStreamAux',data: params})
        CWorkstationCommunicationMng.query('StopSubscribeRemoteStreamAux:' + JSON.stringify(params))
    }
    //通知停止订阅远端辅流结果 app-->js json {error_code:0,data:{uid:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyStopSubscribeRemoteStreamAux = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyStopSubscribeRemoteStreamAux',data: json})
        requestManager.handleResponse('NotifyStopSubscribeRemoteStreamAux', json)
    }
    //切换当前云++窗口大小至直播小窗 js(only pc)-->app params {width:'', height:''}
    CWorkstationCommunicationMng.StartConference = function (params) {
        logger.log({message: 'CWorkstationCommunicationMng.StartConference',data: params})
        this.query('StartConference:' + JSON.stringify(params))
    }
    //从直播小窗恢复至云++大窗口 js(only pc)-->app
    CWorkstationCommunicationMng.StopConference = function () {
        logger.log({message: 'CWorkstationCommunicationMng.StopConference',data:{}})
        this.query('StopConference:' + JSON.stringify({}))
    }
    //音频设备状态变化 pc(only pc)-->js
    CWorkstationCommunicationMng.NotifyAudioDeviceChanged = function (json_str) {
        logger.log({message: 'CWorkstationCommunicationMng.NotifyAudioDeviceChanged',data: json_str})
        // let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        // console.info('CWorkstationCommunicationMng.NotifyAudioDeviceChanged',json)
        // requestManager.handleResponse('NotifyAudioDeviceChanged', json)
    }
    //RTC状态变化 pc(only pc)-->js
    CWorkstationCommunicationMng.NotifyRtcStats = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyRtcStats',data: json})
        requestManager.handleResponse('NotifyRtcStats', json)
    }
    //通知点击原生端播放器按钮响应 pc(only pc)-->js
    CWorkstationCommunicationMng.NotifyClickNativePlayerButton = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'NotifyClickNativePlayerButton',data: json})
        requestManager.handleResponse('NotifyClickNativePlayerButton', json)
    }
    //通知点击原生端播放器按钮响应 mobile(only mobile)-->js {error_code:0,data:{uid:xx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyClickNativeClosePlayerButton = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'NotifyClickNativeClosePlayerButton',data: json})
        requestManager.handleResponse('NotifyClickNativeClosePlayerButton', json)
    }
    //PC通知js 用户点击了PC端直播界面的开/关主流按钮 app(only pc)-->js json {error_code:0,data:{uid:xxx,isMute:true},error_message:'success'}
    CWorkstationCommunicationMng.NotifyApplyPermissionOfMainStream= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyApplyPermissionOfMainStream',data: json})
        requestManager.handleResponse('NotifyApplyPermissionOfMainStream', json)
    }
    //PC通知js 用户点击了PC端直播界面的开/关麦克风按钮 app(only pc)-->js json {error_code:0,data:{uid:xxx,isMute:true},error_message:'success'}
    CWorkstationCommunicationMng.NotifyApplyPermissionOfMuteAudio= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyApplyPermissionOfMuteAudio',data: json})
        requestManager.handleResponse('NotifyApplyPermissionOfMuteAudio', json)
    }
    //PC通知js 用户点击了PC端直播界面的开/关摄像头按钮 app(only pc)-->js json {error_code:0,data:{uid:xxx,isMute:true},error_message:'success'}
    CWorkstationCommunicationMng.NotifyApplyPermissionOfMuteVideo= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyApplyPermissionOfMuteVideo',data: json})
        requestManager.handleResponse('NotifyApplyPermissionOfMuteVideo', json)
    }
    //js通知PC 所执行的开麦动作或者是开主流动作结果 js(only pc)-->pc {action:speaking/mainStream,message:''}
    CWorkstationCommunicationMng.CallApplyPermissionResult = function (params) {
        logger.log({message: 'CWorkstationCommunicationMng.CallApplyPermissionResult',data: params})
        this.query('CallApplyPermissionResult:' + JSON.stringify(params))
    }
    //js通知PC 打开音视频配置界面 js(only pc)-->pc
    CWorkstationCommunicationMng.OpenNativeRtcSetting = function (params) {
        logger.log({message: 'CWorkstationCommunicationMng.OpenNativeRtcSetting',data: params})
        this.query('OpenNativeRtcSetting:' + JSON.stringify(params))
    }
    //pc通知js 关闭音视频配置界面 pc(only pc)-->js
    CWorkstationCommunicationMng.NotifySwitchNativeRtcSettingStatus = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifySwitchNativeRtcSettingStatus',data: json})
        requestManager.handleResponse('NotifySwitchNativeRtcSettingStatus', json)
    }
    //通知声网连接超时 app-->js json {error_code:0,data:{uid:xxx,channelId:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyConnectionLost= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyConnectionLost',data: json})
        requestManager.handleResponse('NotifyConnectionLost', json)
    }
    //通知声网Token已过期 main app-->js json {error_code:0,data:{uid:xxx,channelId:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyTokenExpiredMain= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyTokenExpiredMain',data: json})
        requestManager.handleResponse('NotifyTokenExpiredMain', json)
    }
    //通知声网Token已过期 aux app-->js json {error_code:0,data:{uid:xxx,channelId:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyTokenExpiredAux= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyTokenExpiredAux',data: json})
        requestManager.handleResponse('NotifyTokenExpiredAux', json)
    }
    //通知声网Token即将过期 main app-->js json {error_code:0,data:{uid:xxx,channelId:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyTokenPrivilegeWillExpireMain= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyTokenPrivilegeWillExpireMain',data: json})
        requestManager.handleResponse('NotifyTokenPrivilegeWillExpireMain', json)
    }
    //通知声网Token即将过期 aux app-->js json {error_code:0,data:{uid:xxx,channelId:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyTokenPrivilegeWillExpireAux= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyTokenPrivilegeWillExpireAux',data: json})
        requestManager.handleResponse('NotifyTokenPrivilegeWillExpireAux', json)
    }
    //js通知PC 更新token js-->pc params {newToken:xxx,channelId:xx,uid:xx}
    CWorkstationCommunicationMng.RenewTokenMain = function (params) {
        logger.log({message: 'CWorkstationCommunicationMng.RenewTokenMain',data: params})
        this.query('RenewTokenMain:' + JSON.stringify(params))
    }
    //通知更新token结果 main app-->js json {error_code:0,data:{uid:xxx,channelId:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyRenewTokenMain= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyRenewTokenMain',data: json})
        requestManager.handleResponse('NotifyRenewTokenMain', json)
    }
    //js通知PC 更新token js-->pc params {newToken:xxx,channelId:xx,uid:xx}
    CWorkstationCommunicationMng.RenewTokenAux = function (params) {
        logger.log({message: 'CWorkstationCommunicationMng.RenewTokenAux',data: params})
        this.query('RenewTokenAux:' + JSON.stringify(params))
    }
    //通知更新token结果 main app-->js json {error_code:0,data:{uid:xxx,channelId:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyRenewTokenAux= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyRenewTokenAux',data: json})
        requestManager.handleResponse('NotifyRenewTokenAux', json)
    }
    //通知声网房间的连接状态 app-->js json {error_code:0,data:{state,reason},error_message:'success'}
    CWorkstationCommunicationMng.NotifyConnectionStateChanged= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyConnectionStateChanged',data: json})
        requestManager.handleResponse('NotifyConnectionStateChanged', json)
    }
    //原生申请开启云录制 app-->js json {error_code:0,data:{uid:[]},error_message:'success'}
    CWorkstationCommunicationMng.NotifyCallApplyTurnOnCloudRecording= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyCallApplyTurnOnCloudRecording',data: json})
        requestManager.handleResponse('NotifyCallApplyTurnOnCloudRecording', json)
    }
    //原生申请关闭云录制 app-->js json {error_code:0,data:{uid},error_message:'success'}
    CWorkstationCommunicationMng.NotifyCallApplyTurnOffCloudRecording= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyCallApplyTurnOffCloudRecording',data: json})
        requestManager.handleResponse('NotifyCallApplyTurnOffCloudRecording', json)
    }

    /**
     * @description  js通知PC 更新当前直播间内的相关状态 js-->pc
     * @param {Object} 参数{
     * recording_status:0/1[Number](云录制状态),
     * living_time:0[Number](已进行的直播时长，单位s),
     * role:'host/audience'[String](host主讲人 audience观众),
     * cloud_record_auth:0/1[Number](0不可操作云录制 1可操作云录制),
     * group_title:''[String](群名称),
     * isShowLeaveConferenceBtn:true[Boolean](是否展示离开直播按钮),
     * isShowCloseConferenceBtn:true[Boolean](是否展示结束直播按钮),
     * recordFileName:''[String](提供给原生使用的本地录制文件名)
     * remoteControlStatus:0/1/2[Number](0不可用 1可用 2有用户正在连接中 3被控制中)
     * isLocalControl:true/false[Boolean]是否已经和远端多普勒建立socket连接
     * controlledUserInfo:{nickname:xxx} 被反向控制方(doppler)的用户信息
     * roomUserMap:{10102:{},80502:{}}每个用户的当前用户信息
     * lock_aux_auth:0/1 [Number]当前用户是否有锁定辅流权限
     * currentLockAuxUid：0 [Number]当前房间被锁定的辅流uid
     * unReadMsgCount:0 [Number]当前直播时，未打开群聊页面时，有新消息未读数据
     * }
     */
    CWorkstationCommunicationMng.CallConferenceWebStatus = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.CallConferenceWebStatus',data: params})
        CWorkstationCommunicationMng.query('CallConferenceWebStatus:' + JSON.stringify(params))
    }
    //js通知PC 结束直播服务器已完成，并解散的房间 js-->pc params {channelId}
    CWorkstationCommunicationMng.CallServiceDestroyChannel = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.CallServiceDestroyChannel',data: params})
        CWorkstationCommunicationMng.query('CallServiceDestroyChannel:' + JSON.stringify(params))
    }
    //原生申请打开聊天/成员列表窗口 app-->js json {error_code:0,data:{action:'chat/members'},error_message:'success'}
    CWorkstationCommunicationMng.NotifyApplyOpenConferenceWebWindow = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyApplyOpenConferenceWebWindow',data: json})
        requestManager.handleResponse('NotifyApplyOpenConferenceWebWindow', json)
    }
    //js通知PC 打开前端直播窗口 js-->pc params js(only pc)-->app params {width:'', height:''}
    CWorkstationCommunicationMng.OpenConferenceWebWindow = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.OpenConferenceWebWindow',data: params})
        CWorkstationCommunicationMng.query('OpenConferenceWebWindow:' + JSON.stringify(params))
    }
    //js通知PC 关闭前端直播窗口 js-->pc params {}
    CWorkstationCommunicationMng.CloseConferenceWebWindow = function(params){
        logger.log({message: 'CWorkstationCommunicationMng.CloseConferenceWebWindow',data: params})
        CWorkstationCommunicationMng.query('CloseConferenceWebWindow:' + JSON.stringify(params))
    }
    //原生通知js 用户点击了直播实时分析按钮 app-->js json格式的字符串
    // data = JSON.stringify(json_str)
    // data = {
    //     switch: true/false,
    //     aiInfo:{
    //         originIdX: int,
    //         originIdY: int,
    //         width: 1,
    //         height: 1,
    //         viewId: "str",
    //         lowestScore: "int",
    //         patientId: "str",
    //         examId: "str",
    //         type: "str", //检查类型-需要前端转换
    //         isUpload: true/false
    //     }
    // }
    CWorkstationCommunicationMng.NotifyApplySwitchAIAnalyze= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        logger.log({message: 'CWorkstationCommunicationMng.NotifyApplySwitchAIAnalyze',data: json})

        if(json.aiInfo && json.aiInfo.type ){
            if(json.aiInfo.type == '4'){//window.vm.$store.state.systemConfig.ExamType['4']
                // json.aiInfo.type = window.vm.$store.state.aiPresetData.typeIndex.abdomen
                console.error('abd')
                requestManager.handleResponse('NotifyApplySwitchAIAnalyze', json)
                return
            }
        }
        CWorkstationCommunicationMng.CallApplySwitchAIAnalyzeResult(
            {
                error_code:-1,
                data:{
                    switch: false
                },
                error_message: 'PAI_ANALYZE_PARAMS_TYPE_ERROR',
                key: 'PAI_ANALYZE_PARAMS_TYPE_ERROR'
            }
        )
        return
    }
    //js通知原生 所执行的点击实时分析按钮的结果 js(only pc)-->pc {data:{switch:true/false},error_code:0/-1,error_message:''}
    CWorkstationCommunicationMng.CallApplySwitchAIAnalyzeResult = function (params) {
        logger.log({message: 'CWorkstationCommunicationMng.CallApplySwitchAIAnalyzeResult',data: params})
        this.query('CallApplySwitchAIAnalyzeResult:' + JSON.stringify(params))
    }
    //js通知原生 强制结束所有直播
    CWorkstationCommunicationMng.forceLeaveChannel = function (params) {
        logger.log({message: 'CWorkstationCommunicationMng.forceLeaveChannel',data: params})
        this.query('forceLeaveChannel:' + JSON.stringify(params))
    }
    // 隐藏病人隐私信息 JS->APP 参数：{}
    CWorkstationCommunicationMng.HidePrivateInfo = function (params) {
        console.info('CWorkstationCommunicationMng.HidePrivateInfo', params)
        CWorkstationCommunicationMng.query('HidePrivateInfo:' + JSON.stringify(params))
    }
    //app返回隐藏病人隐私信息状态 app-->js 返回：json {data:{}, error_code:0, error_message:'success'}
    CWorkstationCommunicationMng.NotifyHidePrivateInfoState = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyHidePrivateInfoState', json)
        requestManager.handleResponse('NotifyHidePrivateInfoState', json);
    }
    //展示病人信息 JS->APP 参数：{}
    CWorkstationCommunicationMng.ShowPrivateInfo = function (params) {
        console.info('CWorkstationCommunicationMng.ShowPrivateInfo', params)
        CWorkstationCommunicationMng.query('ShowPrivateInfo:' + JSON.stringify(params))
    }
    //app返回展示病人信息状态 app-->js 返回：json {data:{}, error_code:0, error_message:'success'}
    CWorkstationCommunicationMng.NotifyShowPrivateInfo = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyShowPrivateInfo', json)
        requestManager.handleResponse('NotifyShowPrivateInfo', json);
    }
    //获取AI自动识别阈值接口 JS->APP 参数：{}
    CWorkstationCommunicationMng.GetAutoRecognitionThreshold = function (params) {
        console.info('CWorkstationCommunicationMng.GetAutoRecognitionThreshold', params)
        CWorkstationCommunicationMng.query('GetAutoRecognitionThreshold:' + JSON.stringify(params))
    }
    //app返回AI自动识别阈值 app-->js 返回：json {data:{[ "type": "2","lowestScore": 6}, {"type": "4","lowestScore": 6}]}, error_code:0, error_message:'success'}
    CWorkstationCommunicationMng.NotifyGetAutoRecognitionThreshold = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyGetAutoRecognitionThreshold', json)
        requestManager.handleResponse('NotifyGetAutoRecognitionThreshold', json);
    }
    //设置AI自动识别阈值接口 JS->APP 参数：{threshold:[ "type": "2","lowestScore": 6}, {"type": "4","lowestScore": 6}]}
    CWorkstationCommunicationMng.SetAutoRecognitionThreshold = function (params) {
        console.info('CWorkstationCommunicationMng.SetAutoRecognitionThreshold', params)
        CWorkstationCommunicationMng.query('SetAutoRecognitionThreshold:' + JSON.stringify(params))
    }
    //app返回设置AI自动识别阈值状态 app-->js 返回：json {data:{}, error_code:0, error_message:'success'}
    CWorkstationCommunicationMng.NotifySetAutoRecognitionThreshold = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifySetAutoRecognitionThreshold', json)
        requestManager.handleResponse('NotifySetAutoRecognitionThreshold', json);
    }
}
