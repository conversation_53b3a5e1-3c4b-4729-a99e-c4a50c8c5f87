import Tool from '@/common/tool';
import { uploadTaskMap, getOSSToken } from './index';

// AWS SDK 加载状态管理
let AWS = null;
let isLoading = false;
let loadPromise = null;

// 内部加载函数
const ensureAWS = async () => {
    if (AWS) {
        return AWS;
    }

    if (isLoading && loadPromise) {
        return loadPromise;
    }

    isLoading = true;
    loadPromise = import(/* webpackPrefetch: true */ 'aws-sdk').then(module => {
        AWS = module.default;
        isLoading = false;
        return AWS;
    }).catch(error => {
        isLoading = false;
        loadPromise = null;
        throw error;
    });

    return loadPromise;
};

// 处理 AWS 分片上传
async function handleMultipartUpload({ s3Client, file, filePath, progress, success, error, callback, uploadId }) {
    const createParams = {
        Bucket: s3Client.bucket,
        Key: filePath,
        ContentType: file.type || 'application/octet-stream'
    };

    try {
        let newUploadId = uploadId
        let parts = [];
        const partSize = 10 * 1024 * 1024; // 每片10MB
        const totalParts = Math.ceil(file.size / partSize);
        // 如果已存在上传任务，则直接恢复
        let uploadInfo = {};
        if (uploadId && uploadTaskMap[uploadId]) {
            uploadInfo = uploadTaskMap[uploadId]
            newUploadId = uploadInfo.uploadId;
            parts = uploadInfo.parts || [];
            console.log(`Resuming upload for UploadId: ${uploadId}`);
        } else {
            // 启动新的分片上传
            const multipart = await s3Client.createMultipartUpload(createParams).promise();
            newUploadId = multipart.UploadId;
            uploadTaskMap[newUploadId] = {}
            uploadInfo = uploadTaskMap[newUploadId]
            console.log(`Starting new upload with UploadId: ${newUploadId}`);
        }
        // 找到下一个未上传的分片号
        const startPartNumber = parts.length > 0 ? Math.max(...parts.map(part => part.PartNumber)) + 1 : 1;
        uploadInfo.uploadId = newUploadId
        // 定义一个递归函数来逐步上传分片
        const uploadPartRecursive = async (partNumber) => {
            // 检查暂停状态
            console.error('uploadInfo111', uploadInfo.paused)
            if (uploadInfo && uploadInfo.paused) {
                console.log(`Upload paused for UploadId: ${newUploadId}`);
                return; // 停止上传
            }

            // 结束条件：所有分片上传完成
            if (partNumber > totalParts) {
                // 完成分片上传
                const completeParams = {
                    Bucket: s3Client.bucket,
                    Key: filePath,
                    UploadId: newUploadId,
                    MultipartUpload: { Parts: parts },
                };
                const completeResponse = await s3Client.completeMultipartUpload(completeParams).promise();
                if (success) {
                    success(completeResponse);
                }
                return;
            }
            // 跳过已上传的分片
            if (parts.find(part => part.PartNumber === partNumber)) {
                await uploadPartRecursive(partNumber + 1); // 跳到下一个分片
                return;
            }
            // 计算当前分片数据
            const start = (partNumber - 1) * partSize;
            const end = Math.min(partNumber * partSize, file.size);
            const partData = file.slice(start, end);

            const uploadPartParams = {
                Bucket: s3Client.bucket,
                Key: filePath,
                PartNumber: partNumber,
                UploadId: newUploadId,
                Body: partData,
            };

            try {
                const uploadedPart = await s3Client.uploadPart(uploadPartParams).promise();
                if (uploadInfo && uploadInfo.paused) { //如果下载完发现已经暂停了，丢弃这一次上传结果
                    return
                }
                parts.push({ ETag: uploadedPart.ETag, PartNumber: partNumber });
                // 更新任务状态
                const percentage = partNumber / totalParts;
                uploadInfo.percentage = percentage
                uploadInfo.parts = parts
                uploadInfo.file = file
                uploadInfo.filePath = filePath
                uploadInfo.s3Client = s3Client
                uploadInfo.callback = callback
                // 更新上传进度

                if (progress) {
                    progress(percentage, { uploadId: newUploadId });
                }


                // 递归上传下一个分片
                await uploadPartRecursive(partNumber + 1);
            } catch (err) {
                if (error) {
                    error(err);
                }
                throw err;
            }
        };

        // 启动递归上传
        await uploadPartRecursive(startPartNumber);
    } catch (err) {
        if (error) {
            error(err);
        }
        throw err;
    }
}

// 处理同桶拷贝（AWS S3 没有直接同桶拷贝的接口，需要使用复制）
function handleCopy({ s3Client, copyOssPath, filePath, progress, success, error }) {
    const uploadId = Tool.genID(11); // 生成唯一的上传ID
    if (progress) {
        progress(0.1, { uploadId });
    }

    const params = {
        Bucket: s3Client.bucket,
        Key: filePath,
        copyOssPath: copyOssPath,
        ContentDisposition: 'attachment',
    };

    s3Client.copyObject(params, (err, data) => {
        if (err) {
            if (progress) {
                progress(0, { uploadId });
            }
            if (error) {
                error(err);
            }
        } else {
            if (progress) {
                progress(1, { uploadId });
            }
            if (success) {
                success(data);
            }
        }
    });
}

// 处理文件上传或拷贝
function handleUploadOrCopy({ s3Client, file, filePath, copyOssPath, progress, success, error, callback }) {
    if (copyOssPath) {
        return handleCopy({ s3Client, copyOssPath, filePath, progress, success, error, callback });
    }
    return handleMultipartUpload({ s3Client, file, filePath, progress, success, error, callback });
}

// 进度处理
function handleProgress({ percentage, cpt, callback, s3Client, uploadId }) {
    console.log("AWSProgress", percentage, cpt, callback, s3Client, uploadId);
    let progress = Math.round(percentage * 100);
    if (!uploadTaskMap.hasOwnProperty(uploadId)) {
        uploadTaskMap[uploadId] = {};
    }
    if (cpt) {
        uploadTaskMap[uploadId].checkPoint = cpt;
    }
    if (s3Client) {
        uploadTaskMap[uploadId].s3Client = s3Client;
    }
    uploadTaskMap[uploadId].callback = callback;
    uploadTaskMap[uploadId].percentage = percentage;
    uploadTaskMap[uploadId].uploading = true;

    callback && callback("progress", progress, uploadId);
}

// 上传成功处理
const handleUploadSuccess = (res, callback, uploadId) => {
    const url = res.Location;
    if (callback) {
        callback("complete", url);
    }
    if (uploadTaskMap[uploadId] && uploadTaskMap[uploadId].timer) {
        clearTimeout(uploadTaskMap[uploadId].timer);
        uploadTaskMap[uploadId].timer = null;
    }
    delete uploadTaskMap[uploadId];
};

// 上传错误处理
const handleUploadError = ({ e, callback, uploadId }) => {
    console.log("[event] uploadFile -- error", e, uploadTaskMap, uploadId);
    if (!uploadTaskMap[uploadId]) {
        return;
    }

    uploadTaskMap[uploadId].error = true;
    uploadTaskMap[uploadId].uploading = false;

    if (uploadTaskMap[uploadId].timer) {
        clearTimeout(uploadTaskMap[uploadId].timer);
        uploadTaskMap[uploadId].timer = null;
    }
    callback && callback("error", e, uploadId);
};

// 暂停上传
export const AWSPauseUpload = Tool.debounce(function (uploadId) {
    const uploadInfo = uploadTaskMap[uploadId];
    if (uploadInfo) {
        try {
            // 标记为暂停状态
            uploadInfo.paused = true;
            uploadInfo.uploading = false;
            console.error(uploadInfo);
            // 保存已上传的分片信息到 uploadTaskMap
            if (!uploadInfo.parts) {
                uploadInfo.parts = [];
            }

            // 记录暂停状态
            handleUploadError({ e: { name: 'pause' }, callback: uploadInfo.callback, uploadId });
        } catch (error) {
            console.error(`Failed to pause upload with UploadId: ${uploadId}`, error);
        }
    }
}, 500, true);

// 获取 AWS S3 客户端
async function AWSGetS3Client(bucket, fileName) {
    const AWS = await ensureAWS();
    const credentials = await getOSSToken(bucket, fileName);
    const client = new AWS.S3({
        region: credentials.region,
        accessKeyId: credentials.accessKeyId,
        secretAccessKey: credentials.accessKeySecret,
        sessionToken: credentials.stsToken,
        httpOptions: {
            timeout: 60 * 60 * 1000
        },
    });
    client.bucket = credentials.bucket;
    return client;
}

// 恢复上传
export const AWSResumeUpload = Tool.debounce(function ({ uploadId }) {
    console.error('AWSResumeUpload')
    const uploadInfo = uploadTaskMap[uploadId];
    if (!uploadInfo) {
        console.error("No upload information found to resume.");
        return;
    }
    uploadInfo.paused = false
    uploadInfo.uploading = true
    let s3Client = uploadInfo.s3Client
    let file = uploadInfo.file
    let filePath = uploadInfo.filePath
    let callback = uploadInfo.callback
    handleMultipartUpload({
        s3Client,
        file,
        filePath,
        uploadId,
        callback,
        progress: (percentage, cpt) => {
            handleProgress({ percentage, cpt, callback, s3Client, uploadId });
        },
        success: (res) => handleUploadSuccess(res, callback, uploadId),
        error: (e) => handleUploadError({ e, callback, uploadId }),
    });
    // 恢复上传
    // await handleMultipartUpload({
    //     s3Client: uploadInfo.s3Client,
    //     file: uploadInfo.file,
    //     filePath: uploadInfo.filePath,
    //     progress: uploadInfo.callback,
    //     success: (res) => console.log("Upload resumed and completed successfully:", res),
    //     error: (err) => console.error("Error resuming upload:", err),
    //     callback: uploadInfo.callback,
    // });
}, 500, true);

// 文件上传
export function AWSUploadFile({ bucket = '', file = null, filePath = '', callback = null, copyOssPath = '' } = {}) {
    return new Promise(async (resolve, reject) => {
        try {
            const systemConfig = window.vm.$store.state.systemConfig;
            const bucketName = bucket || systemConfig.serverInfo.oss_attachment_server.bucket;
            let filePathList = copyOssPath ? [copyOssPath, filePath] : filePath;
            const s3Client = await AWSGetS3Client(bucketName, filePathList);

            let uploadId = '';
            handleUploadOrCopy({
                s3Client,
                file,
                filePath,
                copyOssPath,
                callback,
                progress: (percentage, cpt) => {
                    if (cpt && cpt.uploadId) {
                        uploadId = cpt.uploadId;
                    }
                    handleProgress({ percentage, cpt, callback, s3Client, uploadId });
                },
                success: (res) => handleUploadSuccess(res, callback, uploadId),
                error: (e) => handleUploadError({ e, callback, uploadId }),
            });

            resolve(s3Client);
        } catch (error) {
            if (callback) {
                callback("error", { name: 'tokenError' });
            }
            reject(error);
        }
    });
}

export function AWSCancelUpload(uploadId) {
    // 取消上传
    let uploadInfo = uploadTaskMap[uploadId]
    uploadInfo.callback && uploadInfo.callback("cancel", {name:'cancel'}, uploadId);
    if (uploadInfo && uploadInfo.upload) {
        uploadInfo.upload.abort();
        if (uploadInfo.timer) {
            clearTimeout(uploadInfo.timer)
            uploadInfo.timer = null
        }
        delete uploadTaskMap[uploadId]
    }
}
