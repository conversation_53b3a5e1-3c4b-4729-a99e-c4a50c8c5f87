// 智教培主题色
$smart-tech-theme-gradient:  linear-gradient(79deg, #1F98F1 0%, #01BBD5 100%);

// 滚动条样式占位符
%smart-tech-scrollbar-style {
    &::-webkit-scrollbar {
        width: 4px;
    }
w
    &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 2px;

        &:hover {
            background: rgba(33, 150, 243, 0.5);
        }
    }
  }
  // AI主题滚动条样式
// 通用div滚动条
div {
    @extend %smart-tech-scrollbar-style;
  }

  // el-table__body-wrapper滚动条
.el-table__body-wrapper {
    @extend %smart-tech-scrollbar-style;
}

::v-deep .el-button--primary {
    background: $smart-tech-theme-gradient;
    border: none;
    color: #fff;
    &:hover,
    &:focus {
        background: linear-gradient(134deg, #7795e6 0%, #27c4c5 96%);
    }
    &.is-disabled,
    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        &:hover,
        &:focus {
            background: $smart-tech-theme-gradient;
        }
    }
}
::v-deep  .gradient_btn{
    background: $smart-tech-theme-gradient;
    border: none;
    color: #fff;
    &:hover,
    &:focus {
        background:  linear-gradient(79deg, #1F98F1 0%, #01BBD5 100%);;
    }
    &.is-disabled,
    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        &:hover,
        &:focus {
            background: $smart-tech-theme-gradient;
        }
    }
}
::v-deep .el-table {
    border-radius: 6px;
    overflow: hidden;

    th {
        background-color: #f8f9fb;
        color: #606266;
        font-weight: 600;
        padding: 10px 0;
    }

    td {
        padding: 10px 0;
    }

    .el-button--text {
        padding: 0 8px;
        font-weight: 500;
    }
    .el-progress-bar__inner {
        background: $smart-tech-theme-gradient;
    }
    .status-wrapper {

        .status-text {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;

            &.status-waiting {
                background-color: #e6f1fc;
                color: #409eff;
            }

            &.status-success {
                background-color: #f0f9eb;
                color: #67c23a;
            }

            &.status-error {
                background-color: #fef0f0;
                color: #f56c6c;
            }

            &.status-cancelled {
                background-color: #f4f4f5;
                color: #909399;
            }
        }

        .el-progress {
            width: 90%;
        }
    }
    .operation-buttons-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        .option-btn {
            color: #409eff;
            &:hover {
                color: #66b1ff;
            }
        }
    }
    .result-text {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 13px;
        font-weight: 500;

        &.result-pass {
            background-color: #f0f9eb;
            color: #67c23a;
        }

        &.result-fail {
            background-color: #fef0f0;
            color: #f56c6c;
        }
    }
}
