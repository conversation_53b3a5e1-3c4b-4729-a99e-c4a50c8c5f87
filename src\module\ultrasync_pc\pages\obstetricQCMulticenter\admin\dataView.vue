<template>
    <div :class="{ data_view_page_parent: true, over_all: !isAdmin && !isMcQC }">
        <div class="header_name new_exam_form">
            <div class="data_select">
                <queryForm @handleSearch="handleSearch" :activeView="activeView"> </queryForm>
            </div>
        </div>
        <div class="data_view_page_content">
            <div class="content_header_button">
                <div>
                    <p class="title rate">{{ lang.data_list }}</p>
                    <!-- <el-button native-type="submit" type="primary" class='button' @click="handleNewExam" >
                    {{lang.create_new_exam}}
                </el-button> -->
                    <el-button native-type="submit" type="primary" class="button" @click="handleExport">
                        {{ lang.export_comment }}
                    </el-button>
                    <!--   <el-button native-type="submit" type="primary" class='button'  @click="handleSwitchView">
                    {{viewName}}
                </el-button> -->
                    <el-select v-model="activeView" class="view_select" @change="changeView()" size="mini">
                        <el-option
                            v-for="item in viewOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <p class="rate">
                        {{ activeView == "exams" ? lang.exam_compliance_rate : lang.view_compliance_rate }}
                        {{ total_compliance_rate }}%

                        <template v-if="activeView == 'exams'">
                            , {{ lang.integrity_rate }} {{ total_integrity_rate }}%
                        </template>

                        <template v-if="activeView == 'exams'">
                            ,{{ lang.standardization_rate }} {{ total_standardization_rate }}%
                        </template>
                    </p>
                </div>
            </div>
            <div class="content_list" ref="content_list">
                <div
                    class="pagelist obstetric_qc_data_view_page_table"
                    v-if="activeView == 'images'"
                    v-loading="loading"
                >
                    <el-table
                        v-if="!isNoData"
                        :data="imageList"
                        row-key="id"
                        ref="imageList"
                        :header-cell-style="{ color: 'black' }"
                        class="tableAuto"
                        style="width: 100%"
                        :max-height="tabHeight"
                        border
                    >
                        <el-table-column
                            v-for="item in imageTableColumn"
                            :prop="item.field"
                            :label="lang[item.key] + item.unit"
                            stripe
                            :min-width="item.width"
                            :show-overflow-tooltip="true"
                            :key="item.key"
                        >
                            <template slot-scope="scope" slot="header">
                                <el-tooltip class="item" effect="dark" :content="scope.column.label" placement="top">
                                    <span>{{ scope.column.label }}</span>
                                </el-tooltip>
                            </template>
                            <template slot-scope="scope">
                                <div v-if="item.field == 'index'">
                                    {{ (tab_conf.pageNo - 1) * tab_conf.pageSize + scope.$index + 1 }}
                                </div>
                                <div v-if="item.field == 'name'">
                                    {{ getItemName(scope.row) }}
                                </div>
                                <div v-else-if="item.field == 'exams'">
                                    {{ scope.row.exams.length }}
                                </div>
                                <div v-else>
                                    {{ scope.row[item.field] }}
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="no_data" v-if="isNoData">{{ noDataTips }}</div>
                    <el-image v-if="isNoData" class="md-data-empty" src="static/resource_pc/images/nodata.png" />
                </div>
                <div
                    class="pagelist obstetric_qc_data_view_page_table"
                    v-else-if="activeView == 'exams'"
                    v-loading="loading"
                >
                    <el-table
                        v-if="!isNoData"
                        :data="examList"
                        ref="examList"
                        row-key="id"
                        :header-cell-style="{ color: 'black' }"
                        style="width: 100%"
                        :max-height="tabHeight"
                        class="tableAuto"
                        border
                    >
                        <el-table-column
                            v-for="item in examTableColumn"
                            :prop="item.field"
                            :label="lang[item.key] + item.unit"
                            :show-overflow-tooltip="true"
                            stripe
                            :min-width="item.width"
                            :key="item.key"
                        >
                            <template slot-scope="scope" slot="header">
                                <el-tooltip class="item" effect="dark" :content="scope.column.label" placement="top">
                                    <span>{{ scope.column.label }}</span>
                                </el-tooltip>
                            </template>
                            <template slot-scope="scope">
                                <div v-if="item.field == 'index'">
                                    {{ (tab_conf.pageNo - 1) * tab_conf.pageSize + scope.$index + 1 }}
                                </div>
                                <div v-else-if="item.field == 'operation'" class="opeation_icon">
                                    <el-tooltip class="item" effect="dark" :content="lang.view_btn" placement="top">
                                        <span
                                            class="icon iconfont icondakaiwenjianjia"
                                            @click="getExamImage(scope.row)"
                                        ></span>
                                    </el-tooltip>
                                    <el-tooltip
                                        class="item"
                                        effect="dark"
                                        :content="lang.modify_btn_text"
                                        placement="top"
                                    >
                                        <span
                                            class="icon iconfont iconxiugai"
                                            @click="editExternalId(scope.row)"
                                            v-if="enterByGroup.cid"
                                            :class="{ disable: !hasEditPermission(scope.row) }"
                                        ></span>
                                    </el-tooltip>

                                    <el-tooltip
                                        class="item"
                                        effect="dark"
                                        :content="lang.re_analyze_title"
                                        placement="top"
                                    >
                                        <span
                                            class="icon iconfont iconAI"
                                            @click="reAnalyze(scope.row)"
                                            v-if="enterByGroup.cid"
                                            :class="{ disable: !hasEditPermission(scope.row) }"
                                        ></span>
                                    </el-tooltip>

                                    <!-- <span class="icon iconfont iconel-icon-delete2 " @click="deleteExamInMc(scope.row)"  :title="lang.action_delete_text" v-if="enterByGroup.cid" :class="{disable:!hasEditPermission((scope.row))}"></span> -->
                                </div>
                                <div v-else-if="item.field == 'patient_name'" @click="getExamImage(scope.row)">
                                    {{ scope.row.patient_name }}
                                </div>
                                <div v-else-if="item.field == 'recognition_number'">
                                    <div>
                                        <!-- <el-popover
                                    placement="top-start"
                                    width="200"
                                    height="400"
                                    v-model="scope.row.open_popover"
                                    popper-class="obstetric_qc_data_view_page_section_tips"
                                    trigger="click">
                                    <div class='popover_tips' >
                                        <div class="close_button" >
                                            <i class="el-dialog__close el-icon el-icon-close" @click="closePopover(scope.row)"></i>
                                        </div>
                                        <div class="content">
                                            <table>
                                                <thead>
                                                    <tr>
                                                        <th>{{lang.index_num}}</th>
                                                        <th>{{lang.view_type}}</th>
                                                        <th>{{lang.view_name}}</th>
                                                        <th>{{lang.score_value}}</th>
                                                        <th>{{lang.view_quality}}</th>
                                                        <th>{{lang.proportion_weight}}(%)</th>
                                                    </tr>
                                                </thead>
                                                <tbody  >
                                                    <tr  v-for="(item,index,) of setProveDisplayView(scope.row)" :key="index" v-show="item.enabled">
                                                        <td>{{ Number(index)+1}}</td>
                                                        <td class="vertical_text" :rowspan="mc_options.more_details.activedNumber[item.view_class]" v-if="item.dispaly_view_class">{{ lang.obstetric_qc[viewClass[item.view_class].name] }}</td>
                                                        <td>{{ item.name }}</td>
                                                        <template v-if="item.is_group_view&&false" >
                                                            <td class="vertical_text" >{{ item.group_mc_resource_map? toFixedNumber(item.group_mc_resource_map.score||0) : 0 }}
                                                            </td>
                                                            <td class="vertical_text" :class="{error:!item.mc_resource_map}">{{ item.group_mc_resource_map? item.group_mc_resource_map.ai_report.report.quality : ( item.view_class==obstetricEarlyPregnancy.viewClass.base_view ? lang.deletion : '') }}</td>
                                                            <td class="vertical_text">
                                                                {{item.group_id}}
                                                            </td>
                                                        </template>
                                                        <template>
                                                            <td>{{ item.mc_resource_map? toFixedNumber(item.mc_resource_map.ai_report.report.view_score) : 0 }}</td>
                                                            <td :class="{error:!item.mc_resource_map,nonstand:item.mc_resource_map&&item.mc_resource_map.quality==2}">{{ item.mc_resource_map? item.mc_resource_map.ai_report.report.quality : ( item.view_class==obstetricEarlyPregnancy.viewClass.base_view ? lang.deletion : '') }}</td>
                                                            <td class="vertical_text">
                                                                {{ toFixedNumber(item.weight.weight_numb*100,3) }}
                                                            </td>
                                                        </template>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <span slot="reference" class='name'>{{scope.row.recognition_num}}/{{ mc_options.more_details.activedNumber.total}}</span>
                                </el-popover> -->
                                        <span slot="reference"
                                            >{{ scope.row.recognition_num }}/{{
                                                mc_options.more_details.activedNumber.total
                                            }}</span
                                        >
                                    </div>
                                </div>
                                <div v-else-if="item.field == 'group_names'">
                                    {{ scope.row.group_names.join(",") }}
                                </div>

                                <div v-else-if="item.field == 'apply_reconsider'">
                                    <div v-if="scope.row.is_reconsider">
                                        <el-popover
                                            placement="top-start"
                                            width="300"
                                            height="400"
                                            v-model="scope.row.open_reconsider_popover"
                                            popper-class="obstetric_qc_data_view_page_section_tips"
                                            trigger="click"
                                        >
                                            <div class="popover_tips">
                                                <div class="close_button">
                                                    <i
                                                        class="el-dialog__close el-icon el-icon-close"
                                                        @click="closePopover(scope.row)"
                                                    ></i>
                                                </div>
                                                <div class="content">
                                                    <table>
                                                        <thead>
                                                            <tr>
                                                                <th>{{ lang.index_num }}</th>
                                                                <th>{{ lang.view_type }}</th>
                                                                <th>{{ lang.view_name }}</th>
                                                                <th>{{ lang.score_value }}</th>
                                                                <th>{{ lang.view_name_uploader }}</th>
                                                                <th>{{ lang.remark_text }}</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr
                                                                v-for="(item, index) of setProveDisplayView(
                                                                    scope.row,
                                                                    true
                                                                )"
                                                                :key="index"
                                                            >
                                                                <td>{{ Number(index) + 1 }}</td>
                                                                <td
                                                                    class="vertical_text"
                                                                    :rowspan="item.total"
                                                                    v-if="item.dispaly_view_class"
                                                                >
                                                                    {{
                                                                        lang.obstetric_qc[
                                                                            viewClass[item.view_class].name
                                                                        ]
                                                                    }}
                                                                </td>
                                                                <td>{{ item.name }}</td>
                                                                <td>
                                                                    {{
                                                                        item.mc_resource_map
                                                                            ? toFixedNumber(
                                                                                  item.mc_resource_map.ai_report.report
                                                                                      .view_score
                                                                              )
                                                                            : 0
                                                                    }}
                                                                </td>
                                                                <td>
                                                                    {{
                                                                        item.mc_resource_map &&
                                                                        item.mc_resource_map.ai_report &&
                                                                        item.mc_resource_map.ai_report.isReconsider &&
                                                                        mc_options &&
                                                                        mc_options.more_details &&
                                                                        mc_options.more_details.listObj &&
                                                                        mc_options.more_details.listObj[
                                                                            item.mc_resource_map.ai_report
                                                                                .reconsiderType
                                                                        ]
                                                                            ? mc_options.more_details.listObj[
                                                                                  item.mc_resource_map.ai_report
                                                                                      .reconsiderType
                                                                              ].name
                                                                            : ""
                                                                    }}
                                                                </td>
                                                                <td>
                                                                    {{
                                                                        item.mc_resource_map &&
                                                                        item.mc_resource_map.ai_report &&
                                                                        item.mc_resource_map.ai_report.isReconsider
                                                                            ? item.mc_resource_map.ai_report
                                                                                  .reconsiderInfo
                                                                            : ""
                                                                    }}
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            <span slot="reference" :class="{ name: scope.row.is_reconsider }">
                                                {{
                                                    scope.row.is_reconsider
                                                        ? lang.confirm_button_text
                                                        : lang.cancel_button_text
                                                }}</span
                                            >
                                        </el-popover>
                                    </div>
                                    <div v-else>
                                        <span slot="reference">
                                            {{
                                                scope.row.is_reconsider
                                                    ? lang.confirm_button_text
                                                    : lang.cancel_button_text
                                            }}</span
                                        >
                                    </div>
                                </div>
                                <div v-else>
                                    {{ scope.row[item.field] }}
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>

                    <div class="no_data" v-if="isNoData">{{ noDataTips }}</div>
                    <el-image v-if="isNoData" class="md-data-empty" src="static/resource_pc/images/nodata.png" />
                </div>
            </div>
            <div class="content_page_num">
                <el-pagination
                    v-if="!isNoData"
                    background
                    :current-page="tab_conf.pageNo"
                    :layout="tab_conf.layout"
                    :page-size="tab_conf.pageSize"
                    :total="tab_conf.tatal"
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                />
            </div>
        </div>
        <!-- <div class="data_view_page" v-else> -->
        <!-- <newPatient @finalNewExam="handleNewExam"></newPatient> -->
        <!-- </div> -->

        <!-- AI识别信息组件 -->
        <!-- <examAnalyzeResult v-if="isShowExamDialog" :currentExam="examList[0]" @change="handleHidenExamDialog" @openGallery="openGallery"></examAnalyzeResult> -->
        <!-- 画廊组件 -->

        <subSectionGallery
            ref="subSectionGallery"
            class="sub_section_gallery"
            :currentExam="currentExam"
            @examUpdataMCResourceMapRconsider="handleExamUpdataMCResourceMapRconsider"
        >
        </subSectionGallery>
        <examAnalyzeResult
            :currentExam="currentExam"
            :isShowExamSummary="isShowExamSummary"
            @change="isShowExamSummary = !isShowExamSummary"
            @requestOpenGallery="requestOpenGallery"
        >
        </examAnalyzeResult>
        <a ref="link" style="display: none"></a>

        <!-- <el-dialog
            :title="lang.edit_txt"
            :custom-class="'obstetric_qc_edit_exam self_define_height show_el-dialog__header' "
            :visible.sync="editDialogVisible"
            :modal-append-to-body="false"
            width="40%"
        > -->
        <CommonDialog
            :title="lang.edit_txt"
            :custom-class="'obstetric_qc_edit_exam self_define_height'"
            :show.sync="editDialogVisible"
            :modal-append-to-body="false"
            width="40%"
        >
            <div>
                <div class="row">
                    <div class="label">{{ lang.multi_center_patient_id }}</div>
                    <div class="value">
                        <el-input
                            v-model="currentExternalId"
                            :clearable="true"
                            :maxlength="65"
                            :minlength="1"
                        ></el-input>
                    </div>
                </div>
            </div>
            <span slot="footer">
                <el-button @click="editDialogVisible = false">{{ lang.cancel_btn }}</el-button>
                <el-button type="primary" @click="updateExternalId">{{ lang.confirm_txt }}</el-button>
            </span>
            <!-- </el-dialog> -->
        </CommonDialog>
    </div>
</template>
<script>
import base from "../../../lib/base";
import Tool from "@/common/tool.js";
import service from "../../../service/multiCenterService.js";
import commonService from "../../../service/service.js";
import subSectionGallery from "../../../components/obstetricQC/subSectionGallery";
import examAnalyzeResult from "../../../components/obstetricQC/examAnalyzeResult";
import queryForm from "../../../components/obstetricQC/queryForm";
import moment from "moment";
import { cloneDeep, sortBy, map } from "lodash";
import obstetricTool from "../../../lib/obstetricTool";
import { transferPatientInfo, getRealUrl, deDuplicatingImg, toFixedNumber } from "../../../lib/common_base";
import CommonDialog from "../../../MRComponents/commonDialog.vue";
export default {
    mixins: [base, obstetricTool],
    components: { subSectionGallery, queryForm, examAnalyzeResult, CommonDialog },
    data() {
        return {
            toFixedNumber,
            config: this.$store.state.multicenter.config,
            obstetricEarlyPregnancy: this.$store.state.multicenter.obstetricEarlyPregnancy,
            types: this.$store.state.multicenter.type,
            //分页数据
            tab_conf: {
                pageNo: 1,
                pageSize: 10,
                layout: "total, sizes, prev, pager, next, jumper",
                tatal: 0,
            },
            //是否加载中
            loading: false,
            hasData: false,
            //搜索条件
            queryForm: {
                multi_center_patient_id: "",
                patient_name: "",
                organization_name: "",
                dateRange: [
                    moment().subtract(1, "years").format("YYYY-MM-DD z"),
                    moment(new Date()).format("YYYY-MM-DD z"),
                ],
                feedbacker_ids: "",
                feedback_date_range: [
                    moment().subtract(1, "years").format("YYYY-MM-DD z"),
                    moment(new Date()).format("YYYY-MM-DD z"),
                ],
                group_id: "", //群id 是否申请复议
                view_id: "", //切面id 是否申请复议
                uploader_id: "", //上传者
            },
            //图片或者检查列表
            imageList: [],
            examList: [],
            initExamList: [],
            total_standardization_rate: 0, //规范率
            total_integrity_rate: 0, //完整性

            total_compliance_rate: "", //达标率
            //检查或者图片视图
            viewOptions: [],
            activeView: "exams", //'exams',
            //日期选项控制
            pickerOptions: {
                shortcuts: [
                    {
                        text: window.vm.$store.state.language.recent_two_week,
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 13);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: window.vm.$store.state.language.recent_one_month,
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setMonth(start.getMonth() - 1);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: window.vm.$store.state.language.recent_two_month,
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setMonth(start.getMonth() - 2);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                ],
                disabledDate: (time) => {
                    return time.getTime() > new Date().getTime();
                },
            },
            //预置数据
            //当前的检查
            currentExam: {},
            examTableColumn: [
                { key: "index_num", field: "index", width: "70px", unit: "" },
                { key: "operation", field: "operation", width: "90px", unit: "" },
                { key: "multi_center_patient_id", field: "external_id", width: "150px", unit: "" },
                { key: "exam_time", field: "exam_date", width: "95px", unit: "" },
                { key: "patient_name", field: "patient_name", width: "95px", unit: "" },
                { key: "group_by", field: "group_names", width: "95px", unit: "" },
                { key: "image_number", field: "image_num", width: "75px", unit: "" },
                { key: "ai_score", field: "score", width: "80px", unit: "" },
                { key: "compliance_rate", field: "compliance_rate", width: "95px", unit: "(%)" },
                { key: "integrity_rate", field: "integrity_rate", width: "95px", unit: "(%)" },
                { key: "standardization_rate", field: "standardization_rate", width: "95px", unit: "(%)" },

                // { key: "ai_recognition_view", field: "recognition_number", width: "120px", unit: "" },
                // { key: "standard", field: "standard", width: "70px", unit: "" },
                // { key: "basic_standard", field: "basic_standard", width: "95px", unit: "" },
                // { key: "non_standard", field: "non_standard", width: "80px", unit: "" },
                // {key:'apply_reconsider',field:'apply_reconsider',width:'95px', unit:''},
                { key: "uploader", field: "uploader_name", width: "80px", unit: "" },
                { key: "last_update_time", field: "updatedAt", width: "120px", unit: "" },
            ],
            imageTableColumn: [
                { key: "index_num", field: "index", width: "70px", unit: "" },
                { key: "view_name", field: "name", width: "100px", unit: "" },
                { key: "cloud_statistics_exam_number", field: "exams", width: "100px", unit: "" },
                { key: "latest_exam_time", field: "latest_time", width: "150px", unit: "" },
                { key: "image_number", field: "image_num", width: "80px", unit: "" },
                { key: "standard", field: "standard", width: "70px", unit: "" },
                { key: "basic_standard", field: "basic_standard", width: "100px", unit: "" },
                { key: "non_standard", field: "non_standard", width: "95px", unit: "" },
                { key: "compliance_rate", field: "compliance_rate", width: "100px", unit: "(%)" },
            ],
            isShowExamSummary: false, //是否显示检查总结
            editDialogVisible: false,
            currentExternalId: 0,
            defaultTabHeight: 420,
            tabHeight: "420px",
            groupNameById: {},
        };
    },
    computed: {
        // viewName(){
        //     let name = this.activeView=='images'? '切面视图' : '检查视图'
        //     return name
        // },
        noDataTips() {
            if (this.loading) {
                return this.lang.searching;
            } else {
                if (this.hasData) {
                    return this.lang.searching;
                } else {
                    return this.lang.no_relative_data_find;
                }
            }
            // return this.loading? this.lang.searching : this.lang.no_relative_data_find
        },

        enterByGroup() {
            return this.$store.state.multicenter.enterByGroup;
        },
        isNoData() {
            if (this.activeView == "exams") {
                return this.examList.length == 0;
            } else {
                return this.imageList.length == 0;
            }
        },
        mc_options() {
            return this.$store.state.multicenter.optionList[this.obstetricEarlyPregnancy.mcOpId];
        },
        currentMulticenter() {
            return this.$store.state.multicenter.currentMulticenter || {};
        },
        isAdmin() {
            return this.config[this.currentMulticenter.type].role.admin == this.currentMulticenter.userInfo.role;
        },
        //质控者
        isMcQC() {
            let allRole = this.config[this.currentMulticenter.type];
            let userRole = this.currentMulticenter.userInfo.role;
            return allRole.role.normal == userRole;
        },
        viewClass() {
            let viewClass = this.obstetricEarlyPregnancy.viewClass;
            let list = [];
            for (let k in viewClass) {
                list[viewClass[k]] = { name: k };
            }
            return list;
        },
    },
    created() {
        this.debounceSearch = Tool.debounce(this.search, 1000);
        this.viewOptions = [
            { value: "exams", label: this.lang.exam_view_page },
            { value: "images", label: this.lang.view_view_page },
        ];
        this.uploadDateRange = [
            moment().subtract(1, "years").format("YYYY-MM-DD z"),
            moment(new Date()).format("YYYY-MM-DD z"),
        ];
        this.enterByGroup.cid ? (this.queryForm.group_id = this.enterByGroup.cid) : "";
        this.init();
    },
    mounted() {},
    updated() {},
    methods: {
        hasEditPermission(exam) {
            if (!exam) {
                return false;
            }
            if (exam.uploader_ids.indexOf(this.user.uid) > -1) {
                return true;
            }
            if (this.enterByGroup.cid) {
                let conversation = this.$store.state.conversationList[this.enterByGroup.cid] || null;
                if (!conversation) {
                    return true;
                }
                if (conversation.creator_id == this.user.uid) {
                    return true;
                }
                let is_manager = Object.values(conversation.attendeeList).reduce((h, v) => {
                    return v.userid == this.user.uid ? h || v.role == this.systemConfig.groupRole.manager : h;
                }, false);
                return is_manager;
            } else {
                return false;
            }
        },
        //删除检查
        deleteExamInMc(exam) {
            let that = this;
            if (that.hasEditPermission(exam)) {
                that.$MessageBox.confirm(that.lang.delete_case_confirm, that.lang.tip_title, {
                    confirmButtonText: that.lang.confirm_txt,
                    callback: async (action) => {
                        if (action === "confirm") {
                            // imageList
                            let isSuccess = await that.deleteExam(exam);
                            if (isSuccess) {
                                if (that.examList.length == 1) {
                                    that.tab_conf.pageNo = 1;
                                }
                                await that.fetchData();
                            }
                        }
                    },
                });
            }
        },
        editExternalId(item) {
            if (this.hasEditPermission(item)) {
                this.currentExam = item;
                this.currentExternalId = item.external_id;
                this.editDialogVisible = !this.editDialogVisible;
            }
        },
        updateExternalId() {
            let that = this;
            that.editDialogVisible = false;
            that.currentExternalId = that.currentExternalId.replace(/^\s+|\s+$/g, "");
            if (that.currentExternalId.length < 1) {
                that.$message.error(that.lang.multi_center_patient_id_empty);
                return;
            } else {
                if (that.currentExternalId != that.currentExam.external_id) {
                    commonService
                        .updatePatientExternalId({
                            patient_id: that.currentExam.patient_id,
                            external_id: that.currentExternalId,
                            group_ids: this.currentExam.group_ids,
                        })
                        .then(async (res) => {
                            that.loading = false;
                            if (res.data) {
                                if (res.data.error_code) {
                                    that.$message.error(that.lang.error.key);
                                } else {
                                    that.$message.success(that.lang.update_success_text);
                                    that.examList.map((v) => {
                                        if (v.patient_id == that.currentExam.patient_id) {
                                            v.external_id = that.currentExternalId;
                                        }
                                    });
                                }
                            } else {
                                that.$message.error(that.lang.update_failed_text);
                            }
                            // console.error('res:',res)
                        });
                }
            }
        },
        setProveDisplayView(item, isReconsider = false) {
            let listObj = item.preset_mc_view_option;
            let groupObj = item.preset_mc_group_option;
            let values = Object.values(listObj);
            if (isReconsider) {
                values = values.filter((v) => {
                    return (
                        v.enabled &&
                        v.mc_resource_map &&
                        v.mc_resource_map.ai_report &&
                        v.mc_resource_map.ai_report.isReconsider
                    );
                });
            } else {
                values = values.filter((v) => {
                    return v.enabled && !v.group_view_id;
                });
                for (let key in groupObj) {
                    let v = cloneDeep(groupObj[key]);
                    if (v.enabled) {
                        if (item.group_mc_resource_map && item.group_mc_resource_map[key]) {
                            v.mc_resource_map = item.group_mc_resource_map[key];
                        }
                        values.push(v);
                    }
                }
            }
            values = sortBy(values, (v) => {
                return -1 * (v.mc_resource_map ? v.mc_resource_map.score : 0);
            });
            values = sortBy(values, (v) => {
                return Number(v.view_class);
            });
            let current_class = -1;
            let view_class_number = {};
            for (let k in this.obstetricEarlyPregnancy.viewClass) {
                view_class_number[this.obstetricEarlyPregnancy.viewClass[k]] = { index: 0, total: 0, enabled: false };
            }
            values = values.reduce((h, v, i) => {
                if (current_class != v.view_class) {
                    current_class = v.view_class;
                    v.dispaly_view_class = true;
                    view_class_number[current_class].index = i;
                    view_class_number[current_class].enabled = true;
                    ++view_class_number[current_class].total;
                } else {
                    v.dispaly_view_class = false;
                    ++view_class_number[current_class].total;
                }
                h.push(v);
                return h;
            }, []);
            for (let k in view_class_number) {
                if (view_class_number[k].enabled) {
                    values[view_class_number[k].index].total = view_class_number[k].total;
                }
            }
            return values;
        },
        acitvieView() {
            let list = [];
            if (this.mc_options) {
                list = Object.values(this.mc_options.more_details.listObj).filter((v) => {
                    return v;
                });
            }
            let h = [];
            for (let k in list) {
                let v = list[k];
                if (v && v.enabled) {
                    h.push(v);
                }
            }
            return h;
        },
        renderHeader(h, { column, $index }) {
            let span = document.createElement("span");
            span.innerText = column.label;
            document.body.appendChild(span);
            column.realWidth = span.getBoundingClientRect().width + 38;
            column.minWidth = column.realWidth;
            document.body.removeChild(span);
            return h("span", column.label);
        },

        requestOpenGallery(exam, type, view_ids = []) {
            if (exam.exam_id == this.currentExam.exam_id) {
                this.openGallery(type, view_ids);
            }
        },
        closePopover(exam) {
            exam.open_popover = false;
            exam.open_reconsider_popover = false;
        },
        async init() {
            await this.fetchData();
            this.fetchOptions();
        },
        async fetchData() {
            // this.getInfoByPatientId()
            // if(true){
            //     return
            // }

            let that = this;
            that.examList = [];
            that.imageList = [];
            that.loading = true;
            that.hasData = false;
            let condition = {
                start_time: moment(that.queryForm.dateRange[0]).format("YYYY-MM-DD z") + " 00:00:00", //时间范围 默认展示最近3个月，最长不能超过12个月；
                end_time: moment(that.queryForm.dateRange[1]).format("YYYY-MM-DD z") + " 23:59:59",
                multi_center_patient_id: that.queryForm.multi_center_patient_id || "", //病人id, 模糊查询
                patient_name: that.queryForm.patient_name || "", //病人姓名, 模糊查询
                Order: that.queryForm.Order || "", //排序
            };
            if (that.queryForm.organization_name !== "") {
                condition.organization_name = that.queryForm.organization_name;
            }
            if (that.queryForm.group_id && that.queryForm.group_id !== "") {
                condition.group_id = that.queryForm.group_id;
            }
            if (that.queryForm.view_id !== "") {
                condition.view_id = [];
                let view_ids = [];
                if (
                    that.mc_options &&
                    that.mc_options.more_details &&
                    that.mc_options.more_details.groupObj &&
                    that.mc_options.more_details.groupObj[that.queryForm.view_id]
                ) {
                    view_ids = that.mc_options.more_details.groupObj[that.queryForm.view_id].types;
                } else {
                    view_ids = [that.queryForm.view_id];
                }

                for (let key in that.mc_options.more_details.listObj) {
                    let item = that.mc_options.more_details.listObj[key];
                    if (item.enabled && view_ids.indexOf(item.id) > -1) {
                        condition.view_id.push(item.id);
                    }
                }
            }

            if (that.queryForm.sender_ids) {
                if (Array.isArray(that.queryForm.sender_ids)) {
                    if (that.queryForm.sender_ids.length > 0) {
                        condition.sender_ids = that.queryForm.sender_ids;
                    }
                } else {
                    condition.sender_ids = [that.queryForm.sender_ids];
                }
            }
            condition.pageNo = that.tab_conf.pageNo;
            condition.pageSize = that.tab_conf.pageSize;
            if (that.activeView == "exams") {
                let data = await that.fetchByExam(condition);
                that.tab_conf.tatal = data.total;
                that.examList = data.list;
                that.total_standardization_rate = data.total_standardization_rate;
                that.total_integrity_rate = data.total_integrity_rate;
                that.total_compliance_rate = data.total_compliance_rate;
                this.$nextTick(() => {
                    this.$refs.examList && this.$refs.examList.doLayout();
                });
            } else {
                let data = await that.fetchByImage(condition);
                that.tab_conf.tatal = data.total;
                that.total_compliance_rate = data.total_compliance_rate;
                that.imageList = data.list;
                this.$nextTick(() => {
                    this.$refs.imageList && this.$refs.imageList.doLayout();
                });
            }
            return;
        },
        getInfoByPatientId() {
            service
                .getInfoByPatientId({
                    mcID: 3,
                    patientID: "150_ll4uztts",
                })
                .then(async (res) => {
                    console.error("res:", res);
                });
        },
        async fetchByExam(condition) {
            let that = this;
            let result = {
                total: 0,
                list: [],
                total_standardization_rate: 0,
                total_integrity_rate: 0,
                total_compliance_rate: 0,
            };
            console.log("fetchByExam:", condition);
            this.loading = true;
            return new Promise(async (resolve, reject) => {
                service
                    .getExamListByExam({
                        mcID: that.currentMulticenter.id, //多中心id
                        page: condition.pageNo,
                        pageSize: condition.pageSize,
                        condition,
                    })
                    .then(async (res) => {
                        that.loading = false;
                        console.log(res);

                        if (res.data.error_code == 0) {
                            let examList = [];
                            let list = res.data.data.list;
                            that.hasData = list.length > 0;
                            for (let item of list) {
                                item.patientInfo = transferPatientInfo(item);
                                // let no_patient_sex = this.lang.exam_patient_sex+this.lang.not_upload_text;
                                // if(item.patientInfo.patient_sex_str == no_patient_sex){
                                //     item.patientInfo.patient_sex_str = this.lang.not_upload_text;
                                // }
                                examList.push(item);
                            }
                            that.initExamList = cloneDeep(examList);
                            let { newList, total_standardization_rate, total_integrity_rate, total_compliance_rate } =
                                await this.dealExamList(examList, res.data.data.groupNameList);
                            resolve({
                                total: res.data.data.total,
                                list: newList || [],
                                total_standardization_rate,
                                total_integrity_rate,
                                total_compliance_rate,
                            });
                            return;
                        } else {
                            reject(result);
                            return;
                        }
                    });
            });
        },
        async fetchByImage(condition) {
            let that = this;
            console.log("fetchByImage:", condition);
            let result = { total: 0, list: [], total_compliance_rate: 0 };
            return new Promise(async (resolve, reject) => {
                service
                    .getExamListByView({
                        mcID: that.currentMulticenter.id, //多中心id
                        page: condition.pageNo,
                        pageSize: condition.pageSize,
                        condition,
                    })
                    .then(async (res) => {
                        that.loading = false;
                        console.log(res);
                        if (res.data.error_code == 0) {
                            let imageList = [];
                            let list = res.data.data.list;
                            that.hasData = list.length > 0;
                            let { newList, total_compliance_rate } = await this.dealImageList(res.data.data);
                            resolve({ total: res.data.data.total, list: newList || [], total_compliance_rate });
                            return;
                        } else {
                            reject(result);
                            return;
                        }
                    });
            });
        },
        fetchOptions() {
            service
                .getCenteOptions({
                    mcID: this.currentMulticenter.id,
                })
                .then(async (res) => {
                    this.loading = false;
                    if (res.data.error_code == 0) {
                        // this.mc_options = res.data.data[0]
                        this.$store.commit("multicenter/updateMCOptionList", res.data.data);
                    }
                });
        },

        async dealExamList(list, groupNameList = []) {
            let a = 0;
            const groupNameById = groupNameList.reduce((h, v) => {
                h[v.id] = v.subject;
                return h;
            }, {});
            this.groupNameById = { ...this.groupNameById, ...groupNameById };
            list = await this.dealExamListWithGroupView(list, this.mc_options);
            // console.error(list)
            let temp_total_compliance_rate = 0;
            let temp_total_standardization_rate = 0;
            let temp_total_integrity_rate = 0;
            let time_info = {};
            let new_list = list.reduce((h, v) => {
                v.exam_date = this.formatDate(v.exam_date, "YYYY-MM-DD HH:mm:ss");
                v.updatedAt = this.formatDate(v.updatedAt, "YYYY-MM-DD HH:mm:ss");
                v.image_num = v.mc_resource_map ? v.mc_resource_map.length : 0;
                v.is_reconsider = false;
                v.non_standard = 0;
                v.basic_standard = 0;
                v.standard = 0;
                v.compliance_rate = 0;
                v.unfinshed_num = 0;
                v.recognition_num = 0;
                v.recognition_base_num = 0;
                v.no_standart_base_num = 0;
                v.score = 0;
                v.uploader_name = "";
                v.organization_name = "";
                v.open_popover = false;
                v.open_reconsider_popover = false;
                v.max_id = 0;
                v.group_id = null;
                v.group_ids = [];
                v.group_names = [];
                v.uploader_ids = [];
                v.preset_mc_group_option = cloneDeep(this.mc_options.more_details.groupObj);
                v.preset_mc_view_option = cloneDeep(this.mc_options.more_details.listObj);
                v.integrity_rate = 0; //规范率 识别到/基础
                v.standardization_rate = 0; //完整率 识别到标准+基本标准/识别到
                let uploader_names = [];
                let last_update_time = v.updatedAt;
                if (v.image_num < 1) {
                    h.push(v);
                    return h;
                }
                let imageIdList = [];
                let mc_resource_maps = {};
                let max_mc_resource_map = { id: 0 };
                if (this.currentExam && this.currentExam.exam_id == v.exam_id) {
                    v.image_list = this.currentExam.image_list;
                    // this.currentExam = { ...v, image_list: this.currentExam.image_list };
                }
                for (let i = v.image_num - 1; i >= 0; i--) {
                    let mc_resource_map = v.mc_resource_map[i];
                    let ai_report = mc_resource_map.ai_report;
                    if (imageIdList.indexOf(mc_resource_map.img_id) < 0) {
                        imageIdList.push(mc_resource_map.img_id);
                    }
                    if (v.max_id < mc_resource_map.resource_id) {
                        v.max_id = mc_resource_map.resource_id || 0;
                    }
                    if (v.group_ids.indexOf(mc_resource_map.group_id) < 0) {
                        v.group_ids.push(mc_resource_map.group_id);
                        let group_name = this.groupNameById[mc_resource_map.group_id];
                        if (group_name && (group_name === "single chat" || group_name === "single_chat")) {
                            group_name = this.lang.single_chat;
                        }
                        if (group_name && v.group_names.indexOf(group_name) < 0) {
                            v.group_names.push(group_name);
                        }
                    }
                    if (time_info[v.exam_id]) {
                        if (moment(time_info[v.exam_id]) > moment(mc_resource_map.created_at)) {
                            time_info[v.exam_id] = moment(mc_resource_map.created_at).format("YYYY-MM-DD HH:mm:ss z");
                        } else {
                            time_info[v.exam_id] = "";
                        }
                        // v.organization_name = mc_resource_map.sender && mc_resource_map.sender.organization_name
                        v.organization_name = v.exam_custom_info ? v.exam_custom_info.organization || "" : "";
                    } else {
                        time_info[v.exam_id] = moment(mc_resource_map.created_at).format("YYYY-MM-DD HH:mm:ss z");
                        // v.organization_name = mc_resource_map.sender && mc_resource_map.sender.organization_name
                        v.organization_name = v.exam_custom_info ? v.exam_custom_info.organization || "" : "";
                    }
                    v.organization_name = v.organization_name || this.lang.not_upload_text;
                    v.patient_name = v.patient_name || this.lang.not_upload_text;
                    if (max_mc_resource_map.id < mc_resource_map.id) {
                        max_mc_resource_map = mc_resource_map;
                    }
                    if (max_mc_resource_map.sender && max_mc_resource_map.sender.nickname) {
                        uploader_names.indexOf(max_mc_resource_map.sender.nickname) > -1
                            ? ""
                            : uploader_names.push(max_mc_resource_map.sender.nickname);
                        v.uploader_ids.push(max_mc_resource_map.sender.id);
                    }
                    if (new Date(last_update_time) <= new Date(max_mc_resource_map.created_at)) {
                        last_update_time = max_mc_resource_map.created_at;
                    }
                    if (ai_report.finshed) {
                        v.is_reconsider = v.is_reconsider ? true : ai_report.isReconsider;
                        let report = ai_report.report || {};
                        let struct = report.structure || [];
                        if (report.isSuccess) {
                            if (mc_resource_maps[report.view_type]) {
                                if (
                                    parseFloat(report.view_score) >
                                    parseFloat(mc_resource_maps[report.view_type].ai_report.report.view_score)
                                ) {
                                    mc_resource_maps[report.view_type] = mc_resource_map;
                                }
                            } else {
                                mc_resource_maps[report.view_type] = mc_resource_map;
                                if (
                                    this.obstetricEarlyPregnancy.viewClass.base_view ==
                                        v.preset_mc_view_option[report.view_type].view_class &&
                                    !v.preset_mc_view_option[report.view_type].group_view_id &&
                                    v.preset_mc_view_option[report.view_type].enabled
                                ) {
                                    v.recognition_base_num = v.recognition_base_num + 1;
                                    // no_standart_base_num
                                }
                            }
                        }
                    } else {
                        v.unfinshed_num = v.unfinshed_num + 1;
                    }
                }
                //去重后的数据
                v.mc_resource_maps_info = mc_resource_maps;
                let total_score = 0;

                for (let key in mc_resource_maps) {
                    let mc_resource_map = mc_resource_maps[key];
                    let view = this.mc_options.more_details.listObj[key];
                    let report = cloneDeep(mc_resource_map.ai_report.report);
                    report.quality = "";
                    report.view_score = report.view_score * 100;
                    if (report.view_score <= view.ai_lower.highest) {
                        if (
                            !v.preset_mc_view_option[report.view_type].group_view_id &&
                            v.preset_mc_view_option[report.view_type].enabled
                        ) {
                            ++v.non_standard;
                            ++v.recognition_num;
                            if (
                                this.obstetricEarlyPregnancy.viewClass.base_view ==
                                v.preset_mc_view_option[report.view_type].view_class
                            ) {
                                ++v.no_standart_base_num;
                            }
                        }
                        report.quality = this.lang.non_standard;
                    }
                    if (report.view_score > view.ai_middle.lowest && report.view_score <= view.ai_middle.highest) {
                        report.quality = this.lang.basic_standard;
                        if (
                            !v.preset_mc_view_option[report.view_type].group_view_id &&
                            v.preset_mc_view_option[report.view_type].enabled
                        ) {
                            if (
                                this.obstetricEarlyPregnancy.viewClass.base_view ==
                                v.preset_mc_view_option[report.view_type].view_class
                            ) {
                                v.compliance_rate =
                                    v.compliance_rate +
                                    parseFloat(v.preset_mc_view_option[report.view_type].weight.weight_numb);
                            }
                            ++v.basic_standard;
                            ++v.recognition_num;
                        }
                    }
                    if (report.view_score > view.ai_height.lowest && report.view_score <= view.ai_height.highest) {
                        if (
                            !v.preset_mc_view_option[report.view_type].group_view_id &&
                            v.preset_mc_view_option[report.view_type].enabled
                        ) {
                            if (
                                this.obstetricEarlyPregnancy.viewClass.base_view ==
                                v.preset_mc_view_option[report.view_type].view_class
                            ) {
                                v.compliance_rate =
                                    v.compliance_rate +
                                    parseFloat(v.preset_mc_view_option[report.view_type].weight.weight_numb);
                            }
                            ++v.standard;
                            ++v.recognition_num;
                        }
                        report.quality = this.lang.standard;
                    }
                    total_score = total_score + report.view_score;
                    if (
                        !v.preset_mc_view_option[report.view_type].group_view_id &&
                        this.obstetricEarlyPregnancy.viewClass.base_view ==
                            v.preset_mc_view_option[report.view_type].view_class
                    ) {
                        v.score =
                            v.score +
                            report.view_score *
                                parseFloat(v.preset_mc_view_option[report.view_type].weight.weight_numb);
                    }
                    if (v.preset_mc_view_option[report.view_type]) {
                        v.preset_mc_view_option[report.view_type].mc_resource_map = mc_resource_map;
                    }
                }
                if (v.group_mc_resource_map) {
                    for (let key in v.group_mc_resource_map) {
                        let group_mc_resource_map = v.group_mc_resource_map[key];
                        let report =
                            group_mc_resource_map &&
                            group_mc_resource_map.ai_report &&
                            group_mc_resource_map.ai_report.report
                                ? group_mc_resource_map.ai_report.report
                                : { view_score: 0 };
                        report.view_score = report.view_score * 100;
                        v.compliance_rate = v.compliance_rate + group_mc_resource_map.compliance_rate;
                        v.score =
                            v.score +
                            report.view_score *
                                parseFloat(v.preset_mc_group_option[group_mc_resource_map.type].weight.weight_numb);

                        if (v.preset_mc_group_option[group_mc_resource_map.type].enabled) {
                            if (group_mc_resource_map.quality == 0) {
                                ++v.standard;
                                ++v.recognition_num;
                            }
                            if (group_mc_resource_map.quality == 1) {
                                ++v.basic_standard;
                                ++v.recognition_num;
                            }
                            if (group_mc_resource_map.quality == 2) {
                                ++v.non_standard;
                                ++v.recognition_num;
                                ++v.no_standart_base_num;
                            }
                            ++v.recognition_base_num;
                        }
                    }
                }
                // console.error( v.compliance_rate)
                v.score = this.toFixedNumber(v.score);
                // v.image_num = mc_resource_maps.length
                v.compliance_rate = this.toFixedNumber(v.compliance_rate * 100);
                // v.compliance_rate = this.toFixedNumber(((v.standard+v.basic_standard)/this.acitvieView().length)*100)
                v.uploader_name = max_mc_resource_map.sender && max_mc_resource_map.sender.nickname;
                temp_total_compliance_rate = temp_total_compliance_rate + parseFloat(v.compliance_rate);
                if (!v.exam_date) {
                    v.exam_date = time_info[v.exam_id];
                }
                if (!v.updatedAt) {
                    v.updatedAt = time_info[v.exam_id];
                }
                v.uploader_name = uploader_names.join(",");
                v.updatedAt = moment(last_update_time).format("YYYY-MM-DD HH:mm:ss z");
                v.image_num = imageIdList.length;

                if (this.enterByGroup.cid) {
                    v.group_id = this.enterByGroup.cid;
                }

                const total_base_num = this.mc_options.more_details.activedNumber[0];
                v.integrity_rate =
                    total_base_num > 0 ? this.toFixedNumber((100 * v.recognition_base_num) / total_base_num) : 0; //规范率 识别到/基础
                // v.standardization_rate =
                //     v.recognition_base_num > 0
                //         ? this.toFixedNumber(
                //               (100 * (v.recognition_base_num - v.no_standart_base_num)) / v.recognition_base_num
                //           )
                //         : 0; //完整率 识别到标准+基本标准/识别到
                if (v.recognition_base_num > 0) {
                    v.standardization_rate = this.toFixedNumber(
                        (100 * (v.recognition_base_num - v.no_standart_base_num)) / v.recognition_base_num
                    );
                } else {
                    v.standardization_rate = 0;
                }
                temp_total_standardization_rate += v.standardization_rate;
                temp_total_integrity_rate += v.integrity_rate;
                h.push(v);
                return h;
            }, []);
            // console.log("new_list:",new_list)
            // console.log("time_info:",time_info)
            const total_compliance_rate = this.toFixedNumber(temp_total_compliance_rate / list.length);
            const total_standardization_rate = this.toFixedNumber(temp_total_standardization_rate / list.length);
            const total_integrity_rate = this.toFixedNumber(temp_total_integrity_rate / list.length);

            let values = sortBy(new_list, (v) => {
                return -1 * v.max_id;
            });
            return { newList: values, total_standardization_rate, total_integrity_rate, total_compliance_rate };
        },
        async dealImageListWithGroupView(list) {
            let groupObj = this.mc_options.more_details.groupObj;
            let listObj = this.mc_options.more_details.listObj;
            let examImageList = {};
            for (let i in list) {
                let v = list[i];
                examImageList[v.webim_exam_id] = examImageList[v.webim_exam_id] || {};
                if (v.ai_report) {
                    if (
                        v.view_type != null &&
                        v.view_type >= 0 &&
                        listObj[v.view_type] &&
                        listObj[v.view_type].group_view_id
                    ) {
                        let item = groupObj[listObj[v.view_type].group_view_id];
                        examImageList[v.webim_exam_id][item.id] = examImageList[v.webim_exam_id][item.id] || [];
                        examImageList[v.webim_exam_id][item.id].push(v);
                    }
                }
            }
            let new_list = [];
            for (let webim_exam_id in examImageList) {
                let group_views = examImageList[webim_exam_id];
                // new_list[webim_exam_id] =  new_list[webim_exam_id]||[]
                for (let group_view_id in group_views) {
                    let group_ids = [];
                    let isSuccess = false;
                    let score = 0;
                    let quality = 1;
                    let views = group_views[group_view_id];
                    let group_view = groupObj[group_view_id];
                    let items = {};
                    let mc_op_id = 0;
                    let mc_id = 0;
                    for (let i in group_view.item) {
                        let item = group_view.item[i];
                        items[item.id] = item;
                        items[item.id].score = 0;
                    }
                    for (let j in views) {
                        let mc_resource_map = views[j];
                        group_ids.push(mc_resource_map.group_id);
                        if (
                            mc_resource_map &&
                            mc_resource_map.ai_report &&
                            mc_resource_map.ai_report.report &&
                            mc_resource_map.ai_report.report.group_item &&
                            mc_resource_map.ai_report.report.group_item.length > 0
                        ) {
                            isSuccess = true;
                            mc_op_id = mc_resource_map.mc_op_id;
                            mc_id = mc_resource_map.mc_id;
                            let group_items = mc_resource_map.ai_report.report.group_item[0].score || [];
                            for (let k in group_items) {
                                if (k in items) {
                                    let score = group_items[k];
                                    if (score >= items[k].score) {
                                        items[k].score = score;
                                    }
                                }
                            }
                        }
                    }
                    for (let l in items) {
                        score = score + items[l].score * items[l].rate;
                    }
                    if (score * 100 > group_view.ai_height.lowest) {
                        quality = 0;
                    }
                    if (score * 100 <= group_view.ai_lower.highest) {
                        quality = 2;
                    }
                    let new_mc_resource_map = {
                        ai_report: {
                            report: {
                                item: items,
                                isSuccess: isSuccess,
                                structure: [],
                                view_type: group_view_id,
                                group_item: items,
                                view_score: score,
                                view_quality: quality,
                                view_type: group_view_id,
                                group_view_type: group_view_id,
                                group_view_score: score,
                            },
                            finshed: true,
                            isReconsider: false,
                            reconsiderInfo: false,
                            reconsiderType: false,
                        },
                        more_details: null,
                        reconsider_report: null,
                        mc_id: mc_id,
                        mc_op_id: mc_op_id,
                        group_id: "",
                        group_ids: group_ids,
                        webim_exam_id: webim_exam_id,
                        view_type: group_view_id,
                        been_withdrawn: false,
                        view_score: score,
                        view_quality: quality,
                        is_group_view: true,
                    };
                    // new_list[webim_exam_id].push(new_mc_resource_map)
                    new_list.push(new_mc_resource_map);
                }
            }
            return new_list;
        },
        async dealImageList(datas) {
            let group_view_list = await this.dealImageListWithGroupView(datas.list);
            let data = {};
            let temp_total_compliance_rate = 0;
            let default_obj = {
                image_num: 0,
                exams: [],
                non_standard: 0,
                basic_standard: 0,
                standard: 0,
                compliance_rate: 0,
                name: "",
                key: "",
                latest_time: "",
                max_id: 0,
            };
            let list = datas.list;
            let typeExamLastUpate = datas.typeExamLastUpate || {};
            let all_list = [...list, ...group_view_list];
            all_list.reduce((h, v) => {
                let value = {};
                if (v && v.view_type != undefined && v.view_type != null) {
                    let view_type = v.view_type;
                    let view = this.mc_options.more_details.listObj[view_type];
                    if (v.is_group_view) {
                        view = this.mc_options.more_details.groupObj[view_type];
                    }
                    if (view.group_view_id) {
                        return h;
                    }
                    let view_score = v.view_score * 100;
                    if (data[view_type]) {
                        value = data[view_type];
                    } else {
                        value = cloneDeep(default_obj);
                        value.name = (view && view.name) || "";
                        value.key = (view && view.key) || "";
                    }
                    if (value.max_id < v.id) {
                        value.max_id = v.id;
                    }
                    // console.log('view_type:',view_type)
                    value.image_num = value.image_num + 1;
                    value.exams.indexOf(v.webim_exam_id) > -1 ? "" : value.exams.push(v.webim_exam_id);
                    if (view_score <= view.ai_lower.highest) {
                        value.non_standard = value.non_standard + 1;
                    }
                    if (view_score > view.ai_middle.lowest && view_score <= view.ai_middle.highest) {
                        value.basic_standard = value.basic_standard + 1;
                    }
                    if (view_score > view.ai_height.lowest && view_score <= view.ai_height.highest) {
                        value.standard = value.standard + 1;
                    }
                    if (value.latest_time) {
                        moment(value.latest_time) > moment(v.created_at)
                            ? ""
                            : (value.latest_time = moment(v.created_at).format("YYYY-MM-DD HH:mm:ss z"));
                    } else {
                        value.latest_time = moment(v.created_at).format("YYYY-MM-DD HH:mm:ss z");
                    }
                    data[view_type] = value;
                    if (typeExamLastUpate[view_type]) {
                        data[view_type].latest_time = moment(typeExamLastUpate[view_type]).format(
                            "YYYY-MM-DD HH:mm:ss z"
                        );
                    }
                } else {
                    let view_type = this.lang.no;
                    if (data[view_type]) {
                        value = data[view_type];
                    } else {
                        value = cloneDeep(default_obj);
                        value.name = this.lang.no;
                        value.compliance_rate = 0;
                    }
                    if (value.latest_time) {
                        moment(value.latest_time) > moment(v.created_at)
                            ? ""
                            : (value.latest_time = this.formatDate(v.created_at, "YYYY-MM-DD HH:mm:ss z"));
                    } else {
                        value.latest_time = this.formatDate(v.created_at, "YYYY-MM-DD HH:mm:ss z");
                    }
                    value.image_num = value.image_num + 1;
                    value.non_standard = value.non_standard + 1;
                    value.exams.indexOf(v.webim_exam_id) > -1 ? "" : value.exams.push(v.webim_exam_id);
                    data[view_type] = value;
                }
            }, []);
            let total_image_num = 0;
            let total_unstandard_image_num = 0;
            let total_compliance_rate = 0;
            for (let key in data) {
                if (key != this.lang.no) {
                    let item = data[key];
                    // console.error(item)
                    item.compliance_rate = this.toFixedNumber((1 - item.non_standard / item.image_num) * 100);
                    total_image_num = total_image_num + item.image_num;
                    total_unstandard_image_num = total_unstandard_image_num + item.non_standard;
                }
            }
            if (total_image_num > 0) {
                total_compliance_rate = this.toFixedNumber((1 - total_unstandard_image_num / total_image_num) * 100);
            }
            let result = Object.values(data) || [];
            let values = sortBy(result, (v) => {
                return -1 * v.max_id;
            });

            return { newList: values, total_compliance_rate };
        },
        handleSizeChange(val) {
            // console.error('this.$refs.content_list.style',this.$refs.content_list.offsetHeight)
            if (this.$refs.content_list && this.$refs.content_list.offsetHeight > this.defaultTabHeight) {
                this.tabHeight = this.$refs.content_list.offsetHeight + "px";
            } else {
                this.tabHeight = this.defaultTabHeight + "px";
            }
            this.tab_conf.pageNo = 1;
            this.tab_conf.pageSize = val;
            this.fetchData();
        },
        handleCurrentChange(val) {
            this.tab_conf.pageNo = val;
            this.fetchData();
        },
        handleSwitchView() {
            this.activeView = this.activeView == "exams" ? "images" : "exams";
        },
        handleSearch(queryForm) {
            if (this.loading) {
                return;
            }
            this.tab_conf.pageNo = 1;
            this.queryForm = queryForm;
            console.log("queryForm:", queryForm);
            this.fetchData();
        },

        changeView() {
            this.tab_conf.pageNo = 1;
            this.tab_conf.pageSize = 10;
            this.fetchData();
        },
        openGallery(type, view_ids = []) {
            const exam = this.currentExam;
            let index = 0;
            let mc_resource_maps = (exam.image_list || []).filter((v) => {
                if (view_ids.length > 0) {
                    return v.mc_resource_map && view_ids.indexOf(v.mc_resource_map.type) >= 0;
                } else {
                    return v.mc_resource_map && v.exam_id;
                }
            });
            let img = mc_resource_maps[0];
            if (mc_resource_maps && mc_resource_maps.length) {
                if (type != null && type != undefined && type >= 0) {
                    mc_resource_maps.map((v, j) => {
                        if (v.mc_resource_map && v.mc_resource_map.type == type) {
                            index = j;
                            img = v;
                        }
                    });
                }
                if (view_ids.length > 0) {
                    index = 0;
                    img = mc_resource_maps[0];
                }
                let t = cloneDeep(exam);
                t.image_list = mc_resource_maps;

                console.log("assignment1 -->> openGallery", img, index, t);
                this.$refs.subSectionGallery.openGallery(img, index, t);
            } else {
                console.log("assignment2 -->> openGallery", img, index, exam);
                this.$refs.subSectionGallery.openGallery(img, index, exam);
            }
        },
        getExamImage(exam) {
            // this.loading=true
            let that = this;
            that.currentExam = {};
            let condition = {
                mcID: this.currentMulticenter.id,
                protocolGUID: exam.protocol_guid,
                examId: exam.exam_id,
            };
            if (that.queryForm.group_id && that.queryForm.group_id !== "") {
                condition.groupID = that.queryForm.group_id;
            }
            if (that.queryForm.view_id !== "") {
                condition.viewIDs = [];
                let viewIDs = [];
                if (
                    that.mc_options &&
                    that.mc_options.more_details &&
                    that.mc_options.more_details.groupObj &&
                    that.mc_options.more_details.groupObj[that.queryForm.view_id]
                ) {
                    viewIDs = that.mc_options.more_details.groupObj[that.queryForm.view_id].types;
                } else {
                    viewIDs = [that.queryForm.view_id];
                }
                for (let key in that.mc_options.more_details.listObj) {
                    let item = that.mc_options.more_details.listObj[key];
                    if (item.enabled && viewIDs.indexOf(item.id) > -1) {
                        condition.viewIDs.push(item.id);
                    }
                }
            }

            this.loading = true;
            service.getExamImages(condition).then((res) => {
                console.log("getExamImage:", res);
                this.loading = false;
                if (res.data.error_code == 0) {
                    this.isShowExamSummary = true;
                    exam.image_list = res.data.data.image_list; //deDuplicatingImg(res.data.data.image_list);
                    that.currentExam = exam;
                    exam.openState = !exam.openState;
                    // that.openGallery(exam)
                    //检查去重复
                }
                // exam.initImageList=true;
                // console.log('fetchExamData')
                // exam.openState=!exam.openState
                // this.loading=false
            });
        },

        async handleExamUpdataMCResourceMapRconsider(exam) {
            console.log("updataMCResourceMapRconsider:updataMCResourceMapRconsider:", exam);
            let index = -1;
            for (let i = 0; i <= this.initExamList.length; i++) {
                let item = this.initExamList[i];
                if (item && item.exam_id == exam.exam_id && exam.mc_resource_map && exam.mc_resource_map.ai_report) {
                    item.mc_resource_map = (item.mc_resource_map || []).reduce((h, v) => {
                        if (v.img_id == exam.mc_resource_map.img_id) {
                            v.mc_resource_map = exam.mc_resource_map; //{ ...v, ...exam.mc_resource_map };
                            h.push(v);
                        } else {
                            h.push(v);
                        }
                        return h;
                    }, []);
                    this.initExamList[i] = item;
                    index = i;
                    break;
                }
            }
            if (index > -1) {
                let { newList, total_standardization_rate, total_integrity_rate, total_compliance_rate } =
                    await this.dealExamList(cloneDeep(this.initExamList));
                this.total_standardization_rate = total_standardization_rate;
                this.total_integrity_rate = total_integrity_rate;
                this.total_compliance_rate = total_compliance_rate;
                this.examList = newList;
                // this.examList = list
                // this.$set(this.examList,index, list[index])
                this.currentExam = this.examList.find((v) => v.exam_id == exam.exam_id);
                this.currentExam.image_list = (this.currentExam.image_list || []).reduce((h, v) => {
                    if (v.img_id == exam.mc_resource_map.img_id) {
                        v.mc_resource_map = exam.mc_resource_map; //{ ...v, ...exam.mc_resource_map };
                        h.push(v);
                    } else {
                        h.push(v);
                    }
                    return h;
                }, []);
                this.$nextTick(() => {
                    this.$refs.examList && this.$refs.examList.doLayout();
                });
            }
        },
        escapeCsvCharacter(v) {
            let result = v;
            if (v) {
                result = (v + "").replace(/\"/g, '""');
            }
            return result;
        },
        async handleExport() {
            let that = this;
            console.log("导出");
            that.loading = true;
            let condition = {
                start_time: moment(that.queryForm.dateRange[0]).format("YYYY-MM-DD z") + " 00:00:00", //时间范围 默认展示最近3个月，最长不能超过12个月；
                end_time: moment(that.queryForm.dateRange[1]).format("YYYY-MM-DD z") + " 23:59:59",
                multi_center_patient_id: that.queryForm.multi_center_patient_id || "", //病人id, 模糊查询
                patient_name: that.queryForm.patient_name || "", //病人姓名, 模糊查询
                organization_name: that.queryForm.organization_name || "", //医院, 模糊查询
                Order: that.queryForm.Order || "", //排序
            };
            let activeViewNum = that.acitvieView().length;
            if (that.queryForm.organization_name !== "") {
                condition.organization_name = that.queryForm.organization_name;
            }
            if (that.queryForm.group_id && that.queryForm.group_id !== "") {
                condition.group_id = that.queryForm.group_id;
            }
            if (that.queryForm.view_id !== "") {
                let view_ids = [];
                condition.view_id = [];
                if (
                    that.mc_options &&
                    that.mc_options.more_details &&
                    that.mc_options.more_details.groupObj &&
                    that.mc_options.more_details.groupObj[that.queryForm.view_id]
                ) {
                    view_ids = that.mc_options.more_details.groupObj[that.queryForm.view_id].types;
                } else {
                    view_ids = [that.queryForm.view_id];
                }
                for (let key in that.mc_options.more_details.listObj) {
                    let item = that.mc_options.more_details.listObj[key];
                    if (item.enabled && view_ids.indexOf(item.id) > -1) {
                        condition.view_id.push(item.id);
                    }
                }
            }

            if (that.queryForm.sender_ids) {
                if (Array.isArray(that.queryForm.sender_ids)) {
                    if (that.queryForm.sender_ids.length > 0) {
                        condition.sender_ids = that.queryForm.sender_ids;
                    }
                } else {
                    condition.sender_ids = [that.queryForm.sender_ids];
                }
            }
            condition.pageNo = 1;
            condition.pageSize = 10000;
            let list = [];

            if (that.activeView == "exams") {
                let data = await that.fetchByExam(condition);
                let desc = `${this.lang.exam_compliance_rate}:${data.total_compliance_rate}%, ${this.lang.integrity_rate}:${data.total_integrity_rate}%, ${this.lang.standardization_rate}:${data.total_standardization_rate}%`;
                list = data.list.reduce((h, v, index) => {
                    h.push(
                        [
                            '"' + this.escapeCsvCharacter(index + 1) + '"',
                            '"' + this.escapeCsvCharacter(v.external_id) + '"',
                            '"' + this.escapeCsvCharacter(v.exam_date) + '"',
                            '"' + this.escapeCsvCharacter(v.patient_name) + '"',
                            '"' + this.escapeCsvCharacter(v.group_names.join(",")) + '"',
                            '"' + this.escapeCsvCharacter(v.image_num) + '"',
                            '"' + this.escapeCsvCharacter(v.score) + "" + '"',
                            '"' + this.escapeCsvCharacter(v.compliance_rate) + '"',
                            '"' + this.escapeCsvCharacter(v.integrity_rate) + '"',
                            '"' + this.escapeCsvCharacter(v.standardization_rate) + '"',
                            '"' + this.escapeCsvCharacter(v.uploader_name) + '"',
                            '"' + this.escapeCsvCharacter(v.updatedAt) + '"' + "\r\n",
                        ].join(",")
                    );
                    return h;
                }, []);

                list.unshift(
                    [
                        this.lang.index_num,
                        this.lang.multi_center_patient_id,
                        this.lang.exam_time,
                        this.lang.patient_name,
                        this.lang.group_by,
                        this.lang.image_number,
                        this.lang.ai_score,
                        this.lang.compliance_rate + "(%)",
                        this.lang.integrity_rate + "(%)",
                        this.lang.standardization_rate + "(%)",
                        this.lang.uploader,
                        this.lang.last_update_time,
                        desc + "\r\n",
                    ].join(",")
                );
            } else {
                let data = await that.fetchByImage(condition);
                let desc = `${this.lang.view_compliance_rate}:${data.total_compliance_rate}%`; //, ${ this.lang.integrity_rate  }:${data.total_integrity_rate}%, ${this.lang.compliance_rate  }:${data.total_compliance_rate}%`
                list = data.list.reduce((h, v, index) => {
                    h.push(
                        [
                            index + 1,
                            this.getItemName(v),
                            v.exams.length,
                            v.latest_time,
                            v.image_num,
                            v.standard,
                            v.basic_standard,
                            v.non_standard,
                            v.compliance_rate + "\r\n",
                        ].join(",")
                    );
                    return h;
                }, []);
                list.unshift(
                    [
                        this.lang.index_num,
                        this.lang.view_name,
                        this.lang.cloud_statistics_exam_number,
                        this.lang.latest_exam_time,
                        this.lang.image_number,
                        this.lang.standard,
                        this.lang.basic_standard,
                        this.lang.non_standard,
                        this.lang.compliance_rate + "(%)",
                        desc + "\r\n",
                    ].join(",")
                );
            }
            // let blob = new Blob(list,{type: "application/csv" });
            let blob = new Blob(["\uFEFF" + list.join("")], { type: "text/csv;charset=utf-8;" });
            const url = window.URL.createObjectURL(blob); // 设置路径
            const link = this.$refs.link;
            link.href = url;
            link.download = `${this.lang.obstetric_qc_multicenter}.csv`; // 设置文件名
            link.click();
            URL.revokeObjectURL(url); // 释放内存
        },
        getItemName(item) {
            if (item.key && item.key in this.lang) {
                return this.lang[item.key];
            }
            return item.name;
        },
        async reAnalyze(exam) {
            let that = this;
            // 检查是否有编辑权限
            if (!this.hasEditPermission(exam)) {
                return;
            }

            that.$MessageBox.confirm(that.lang.re_analyze_tips, that.lang.re_analyze_title, {
                confirmButtonText: that.lang.confirm_txt,
                callback: async (action) => {
                    if (action === "confirm") {
                        let mc_resource_map_ids = map(exam.mc_resource_map, "id");
                        that.loading = true;
                        commonService
                            .requestStartAiAnalyze({
                                type: "0",
                                file_list: [],
                                more_details: { action: "reanalyze", mc_resource_map_ids: mc_resource_map_ids },
                            })
                            .then(async (res) => {
                                if (
                                    res.data &&
                                    !res.data.error_code &&
                                    res.data.data &&
                                    !res.data.data.error &&
                                    res.data.data.length > 0
                                ) {
                                    exam.mc_resource_map = [];
                                    for (let i in res.data.data) {
                                        let msg = res.data.data[i];
                                        exam.mc_resource_map.push(msg.mc_resource_map);
                                        this.updateToStore(cloneDeep(msg));
                                    }

                                    let { newList } = await this.dealExamList([cloneDeep(exam)], []);

                                    for (let index = 0; index < that.examList.length; index++) {
                                        if (newList[0].exam_id == that.examList[index].exam_id) {
                                            that.examList.splice(index, 1, newList[0]);
                                            break;
                                        }
                                    }
                                    let {
                                        newList: list,
                                        total_standardization_rate,
                                        total_integrity_rate,
                                        total_compliance_rate,
                                    } = await this.dealExamList(that.examList, []);
                                    that.examList = list;
                                    that.total_standardization_rate = total_standardization_rate;
                                    that.total_integrity_rate = total_integrity_rate;
                                    that.total_compliance_rate = total_compliance_rate;
                                    that.initExamList = list;
                                    that.loading = false;
                                } else {
                                    that.loading = false;
                                    that.$message.error(that.lang.analyze_fail_tip);
                                }
                            });
                    }
                },
            });
        },
        updateToStore(msg) {
            let that = this;
            that.$store.commit("conversationList/updateMessageMCAiReport", msg);
            that.$store.commit("conversationList/updateGalleryObjMCAiReport", msg);
            //更新store数
            //exmalist
            that.$store.commit("examList/updateExamListMCAiReport", msg);
            //gallerylist
            that.$store.commit("gallery/updateGalleryMCAiReport", msg);
        },
    },
};
</script>
<style lang="scss" scoped>
.data_view_page_parent {
    // width:100%;
    min-width: 1150px;
    background-color: rgb(242, 246, 249);
    height: calc(100% - 40px);
    flex-direction: column;
    justify-content: space-around;
}
.over_all {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 2;
    overflow-y: auto;
}
.no_data {
    text-align: center;
    font-size: 16px;
}
.popover_tips {
    min-height: 130px;
    max-height: 500px;
    .close_button {
        font-size: 20px;
        height: 30px;
        text-align: right;
        margin-top: -10px;
        :hover {
            color: #779a98;
        }
    }
    .content {
        min-height: 100px;
        max-height: 470px;
        overflow: auto;
        th {
            word-break: keep-all;
        }
        td {
        }
    }
}
.data_view_page_content {
    padding: 0px 20px 15px 20px;
    background-color: rgb(242, 246, 249);
    padding-bottom: 20px;
    display: flex;
    flex: 1 1 auto;
    flex-direction: column;
    justify-content: space-around;
    .content_list {
        flex: 1 1 auto;
    }
    .content_page_num {
        height: 34px;
        padding: 6px 0 0 0;
    }
    .content_header_button {
        height: 40px;
        margin-bottom: 10px;
        display: -webkit-flex;
        -webkit-flex-wrap: wrap;
        justify-content: space-between;
        flex-direction: row;
        .title {
            display: inline;
            line-height: 40px !important;
            margin-right: 10px;
        }
        .view_select {
            height: 30px !important;
            margin-bottom: 15px;
            margin-left: 15px;
        }
        .button {
            color: white;
            background: #779a98;
            border: 0px;
            font-size: 15px;
            margin-top: 3px;
            padding: 5px 11px 8px 11px;
            margin-left: 15px;
        }
        .rate {
            font-size: 18px;
            color: black;
            font-weight: bold;
            line-height: 40px;
        }
        div {
            height: 40px;
        }
    }
    .pagelist {
        // height:calc(100% - 100px);
        display: flex;
        justify-content: center;
        flex-direction: column;
        word-break: break-all;
        word-wrap: break-word;
        th {
            word-break: keep-all !important;
            word-wrap: break-word !important;
        }
        .name {
            color: #0000cc;
        }
        .index {
            color: #0000cc;
        }
        .md-data-empty {
            margin: auto;
            height: 400px;
            text-align: center;
        }
        .disable {
            color: #cdd1d1;
        }
        .opeation_icon {
            display: flex;
            justify-content: space-between;
            color: #779a98;
            padding: 0 10px;
        }
    }
    .table_header {
        .cell {
            color: black;
        }
        color: black;
    }
}
.search_box {
    width: 100%;
    height: 32px;
    padding: 0 10px;
    line-height: 32px;
    border-bottom: 1px solid #dbdbdb;
    margin-bottom: 2px;
    .el-input {
        width: 100%;
    }
    i,
    input {
        vertical-align: middle;
        height: 100%;
    }
    input {
        margin-left: 5px;
        border: 1px solid black;
        color: #666;
        width: 90%;
        border: none !important;
    }
}
.el-range-separator {
    margin-top: -8px;
}
.header_name {
}
.mini_date_ragnge {
    width: 250px;
}
.new_exam_form {
    display: -webkit-flex;
    -webkit-flex-wrap: wrap;
    justify-content: space-between;
    flex-direction: column;
    .title {
        font-size: 18px;
        font-weight: bold;
        color: black;
    }
    .el-form {
        padding: 0px;
        margin: 0px;
    }
    .field_form {
        height: 100px;
    }
    .field_arear {
        width: 100%;
        & > div {
            width: 100%;
            display: -webkit-flex;
            -webkit-flex-wrap: wrap;
            justify-content: space-between;
            flex-direction: row;
        }
        .field {
            width: 30%;
            text-align: left;
        }
        .last {
            text-align: right;
        }
    }
    .tips_arear {
        width: 100%;
        height: 100px;
        display: -webkit-flex;
        -webkit-flex-wrap: nowrap;
        justify-content: space-between;
        flex-direction: row;
        .left {
            width: 50%;
        }
        .right {
            width: 30%;
            color: red;
        }
    }
    .button_arear {
        width: 100%;
        height: 100px;
        display: -webkit-flex;
        -webkit-flex-wrap: wrap;
        justify-content: center;
        flex-direction: row;
        .button_ele {
            width: 200xp;
            margin: 30px 50px;
        }
        .general_cancel_button {
            height: 30px;
            padding-top: 7px;
            background: white;
            border: 1px solid black;
            color: black;
        }
        .general_confirm_button {
            height: 30px;
            padding-top: 7px;
        }
    }
    .pictures_arear {
        width: 100%;
        display: -webkit-flex;
        -webkit-flex-wrap: wrap;
        flex-direction: column;
        justify-content: center;
        & > div {
            padding-top: 10px;
            padding-bottom: 10px;
            text-align: center;
        }
    }
}
</style>
<style lang="scss">
.obstetric_qc_self_define_height {
    .el-dialog__header {
        padding: 4px 14px;
        height: 36px;
        border-bottom: 1px solid #ccc;
    }
}
.el-table.scroll-tab {
    overflow: auto;
    max-height: calc(100% - 200px);
}
.scroll-tab .el-table__header-wrapper {
    position: sticky;
    top: 0; //这个值根据实际情况而定
    z-index: 10;
}
.obstetric_qc_edit_exam {
    margin-top: 20% !important;
    height: 20% !important;
    .row {
        display: flex;
        flex-deriection: row;
        flex-wrap: nowrap;
        .label {
            width: 150px;
            text: left;
            display: inline;
            line-height: 40px;
        }
        .value {
            flex: 2;
        }
    }
}
.obstetric_qc_data_view_page_table {
    overflow: auto;
    .el-loading-mask {
        background-color: rgba(255, 255, 255, 0);
    }
    .el-table__header-wrapper {
        .el-table__header {
            margin: 0;
        }
        th {
            .cell {
                // word-break: keep-all;
                // // word-wrap: break-word;
                // white-space: pre-wrap;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
    .el-table__body-wrapper {
        td {
            padding: 6px 0px;
            div {
                div {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                width: calc(100% - 0px) !important;
            }
        }
    }
    .el-table--scrollable-x .el-table__body-wrapper {
        z-index: 2;
    }
}
.obstetric_qc_data_view_page_section_tips {
    width: 750px !important;
    background: white;
    th,
    td {
        border: 1px solid #bbb;
        font-size: 14px;
        padding: 6px 0px;
        text-align: center;
        word-wrap: break-word;
        word-break: keep-all;
    }

    .nonstand {
        color: #ff9933;
    }
    table {
        width: 100% !important;
        color: #333;
        border: 1px solid #bbb;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 10px;
        .name {
            color: #0000cc;
        }
        .vertical_text {
            margin: auto;
            vertical-align: middle;
        }
    }
    .el-form-item--mini .el-form-item__content,
    .el-form-item--mini .el-form-item__label {
        line-height: 16px;
        word-wrap: break-word;
        word-break: break-all;
    }
    .error {
        color: red;
    }
    .fit_contanier {
        object-fit: contain !important;
    }
    .el-table--scrollable-x .el-table__body-wrapper {
        z-index: 2;
    }
}
</style>
