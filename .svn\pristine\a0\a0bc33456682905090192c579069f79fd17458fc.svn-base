<template>
    <div class="ai-main">
        <div class="tab-container">
            <div class="custom-tabs-header">
                <div class="back-button" @click="goBack">
                    <i class="el-icon-arrow-left"></i>
                </div>
                <el-tabs v-model="activeTab" class="custom-tabs" @tab-click="handleTabClick">
                    <el-tab-pane label="AI问答" name="ai_chat"></el-tab-pane>
                    <el-tab-pane label="AI临床思维训练" name="practice_overview"></el-tab-pane>
                    <el-tab-pane label="超声报告质控" name="ultrasound_report_qc_index"></el-tab-pane>
                </el-tabs>
            </div>

            <div class="tab-content">
                <keep-alive>
                    <router-view></router-view>
                </keep-alive>
            </div>
        </div>
    </div>
</template>

<script>
import base from "../../lib/base";
import Tool from "@/common/tool";
export default {
    mixins: [base],
    name: "<PERSON><PERSON><PERSON>",
    components: {},
    data() {
        return {
            activeTab: "ai_chat",
            role: "admin",//admin 主任  normal 医生
        };
    },
    computed: {},
    watch: {
        $route: {
            immediate: true,
            handler(to) {
                this.setActiveTabByRoute(to.path);
            },
        },
    },
    created() {
        this.setActiveTabByRoute(this.$route.path);
    },
    methods: {
        goBack() {
            const cid = this.$route.params.cid;
            this.$router.replace(`/index/chat_window/${cid}`);
        },
        setActiveTabByRoute(path) {
            if (path.includes("/ai_main/ai_chat")) {
                this.activeTab = "ai_chat";
            } else if (path.includes("/ai_main/practice_overview")) {
                this.activeTab = "practice_overview";
            } else if (path.includes("/ai_main/ultrasound_report_qc_index")) {
                this.activeTab = "ultrasound_report_qc_index";
            }
        },
        handleTabClick(tab) {
            const cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/ai_main/${tab.name}`);
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync_pc/style/aiChat.scss';
.ai-main {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 20;
    display: flex;
    flex-direction: column;
    background-color: #f7f9fc;
}

.tab-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fff;
}

.custom-tabs-header {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
    padding: 0 10px;
    position: relative;

    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        cursor: pointer;
        margin-right: 10px;
        font-weight: 600;
        i {
            font-size: 25px;
            color: #000;
        }

        &:hover i {
            color: #409eff;
        }
    }
}

.custom-tabs {
    flex: 1;

    :deep(.el-tabs__header) {
        margin-bottom: 0;
        border-bottom: none;

        .el-tabs__nav-wrap {
            &::after {
                display: none;
            }
        }

        .el-tabs__nav {
            border: none;
        }

        .el-tabs__item {
            height: 80px;
            line-height: 80px;
            font-size: 20px;
            color: #000;
            border: none;

            &.is-active {
                font-weight: bold;
            }
        }

        .el-tabs__active-bar {
            height: 4px;
            background: $ai-theme-gradient;
            border-radius: 3px;
        }
    }
}

.tab-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.coming-soon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;

    h1 {
        font-size: 28px;
        color: #909399;
        font-weight: 400;
    }
}
</style>
