import Tool from '@/common/tool'
import { uploadTaskMap } from './index'

// AWS SDK 加载状态管理
let AWS = null;
let isLoading = false;
let loadPromise = null;

// 全局变量保持不变
let currentBucket = ''
let currentFilePath = ''
let currentFile = null
let currentProgressFn = null
let currentSuccessFn = null
let currentErrorFn = null
let currentClient = null

// 内部加载函数
const ensureAWS = async () => {
    if (AWS) {
        return AWS;
    }

    if (isLoading && loadPromise) {
        return loadPromise;
    }

    isLoading = true;
    loadPromise = import(/* webpackPrefetch: true */ 'aws-sdk').then(module => {
        AWS = module.default;
        isLoading = false;
        return AWS;
    }).catch(error => {
        isLoading = false;
        loadPromise = null;
        throw error;
    });

    return loadPromise;
};

function handleMinioProgress ({percentage,callback,ossClient,uploadId}) {
    let progress = Math.round(percentage * 100)
    if(!uploadTaskMap.hasOwnProperty(uploadId)){
        uploadTaskMap[uploadId] = {}
    }
    if(ossClient){
        uploadTaskMap[uploadId].ossClient =  ossClient
    }
    uploadTaskMap[uploadId].callback = callback
    uploadTaskMap[uploadId].percentage = percentage
    uploadTaskMap[uploadId].uploading = true
    console.log("progress",uploadId,progress)
    callback && callback("progress", progress,uploadId);
}

const handleMinioUploadSuccess = async (res,callback,uploadId) => {
    console.log("[event] uploadMinioFile -- success", res);
    const url = res.Location;
    callback && callback("complete", url);
    delete uploadTaskMap[uploadId]
}

const handleMinioUploadError = async ({e,callback,uploadId}) => {
    console.log("[event] uploadFile -- error",e,uploadTaskMap,uploadId);
    if(!uploadTaskMap[uploadId]){
        return
    }
    uploadTaskMap[uploadId].error = true
    uploadTaskMap[uploadId].uploading = false
    callback && callback("error", e, uploadId);
}

const handleUpload = ({ossClient,file,filePath},{progress,success,error})=>{
    currentFilePath = filePath
    currentFile = file
    currentProgressFn = progress
    currentSuccessFn = success
    currentErrorFn = error
    currentClient = ossClient
    const params = {
        Bucket: currentBucket,
        Key: filePath,
        Body: file,
        ContentType: file.type
    };

    ossClient.uploadManaged = ossClient.upload(params)

    ossClient.uploadManaged.on('httpUploadProgress', function(evt) {
        const percent = evt.loaded / evt.total;
        console.log(`已上传: ${percent}`);
        progress&&progress(percent);
    })
    ossClient.uploadManaged.send((err, data) => {
        if (err) {
            console.log(err, err.stack);
            error&&error(err)
        } else {
            console.log(data);
            success&&success(data)
        }
    });
}

const handleCancelUpload = ()=>{
    currentClient.uploadManaged.abort();
}

export async function MinioGetOssClient(bucket,fileName) {
    try {
        let ping = true
        if(Tool.checkAppClient('Huawei')){
            ping = false
        }

        await Tool.handleAfterMainScreenCreated(ping)
        const minioConfig = window.vm.$store.state.systemConfig.serverInfo.minio
        const storageReplaceInfo = window.vm.$store.state.systemConfig.serverInfo.storageReplaceInfo
        const nginx_address = storageReplaceInfo.nginx_address
        const isReplace = storageReplaceInfo.replace
        const storage_address = storageReplaceInfo.storage_address
        let minioEndPoint = `${minioConfig.protocol}${minioConfig.endPoint}:${minioConfig.port}`
        if(isReplace&&nginx_address){
            minioEndPoint = nginx_address
        }
        currentBucket = bucket

        // 动态加载 AWS SDK
        const AWS = await ensureAWS();

        AWS.config.update({
            region: 'your-region',
            endpoint: new AWS.Endpoint(minioEndPoint),
            accessKeyId: minioConfig.accessKey,
            secretAccessKey: minioConfig.secretKey,
            s3ForcePathStyle: true,
        });

        return new AWS.S3();
    } catch (error) {
        throw error;
    }
}

export function MinioUploadFile ({
    bucket='',
    file=null,
    filePath ='',
    callback = null,
    timeout = 30000
}){
    return new Promise(async(resolve, reject) => {
        const systemConfig = window.vm.$store.state.systemConfig;
        const bucketName = bucket || systemConfig.serverInfo.oss_attachment_server.bucket;
        let ossClient = null
        try {
            ossClient = await MinioGetOssClient(bucketName, filePath);
        } catch (error) {
            callback && callback("error", {name:'tokenError'});
            return
        }

        let uploadId = Tool.genID(11)
        handleUpload({ossClient,file,filePath},{
            progress:async(percentage)=>{
                handleMinioProgress({percentage,callback,ossClient,uploadId})
            },
            success:(res)=>{
                handleMinioUploadSuccess(res,callback,uploadId)
            },
            error:(e)=>{
                handleMinioUploadError({e,callback,uploadId})
            },
        })
        resolve(ossClient)
    });
}

export function MinioCancelUpload(uploadId){
    let ossInfo= uploadTaskMap[uploadId]
    if(ossInfo){
        ossInfo.callback && ossInfo.callback("cancel", {name:'cancel'}, uploadId);
        const ossClient = ossInfo.ossClient
        handleCancelUpload({ossClient})
        delete uploadTaskMap[uploadId]
    }
}

export function MinioResumeUpload({uploadId}){
    MinioCancelUpload(uploadId)
    MinioUploadFile({
        ossClient:currentClient,
        file:currentFile,
        filePath:currentFilePath
    },{
        progress:currentProgressFn,
        success:currentSuccessFn,
        error:currentErrorFn
    })
}

export function MinioPauseUpload(){
    console.error('暂不支持停止上传')
}

