<template>
    <div class="top_video_btn_container" v-show="LiveConferenceData.joinedAux">
        <span class="svn_btn" v-show="isConferenceAudioStream === 1" @click.stop="switchToMuteAudio(true)">
            <!-- <svg class="svg_icon">
                            <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-mic_open"></use>
                        </svg> -->
            <img class="svg_icon" src="static/resource/images/svg-image/mic_open.png" alt="" />
            <!-- <p>点击静音</p> -->
        </span>
        <span class="svn_btn" v-show="isConferenceAudioStream === 0" @click.stop="switchToMuteAudio(false)">
            <!-- <svg class="svg_icon">
                            <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-mic_close"></use>
                        </svg> -->
            <img class="svg_icon" src="static/resource/images/svg-image/mic_close.png" alt="" />
            <!-- <svg class="svg_icon_hands" v-show="realtimeVoice.show_layim_voice_tool_receive_no_hands_up">
                            <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="static/resource/svg/all.svg#hands"></use>
                        </svg> -->
            <!-- <p>取消静音</p> -->
        </span>
        <div v-show="!checkShowVideoOperateBtn">
            <span class="svn_btn" v-show="isConferenceVideoStream === 1" @click.stop="switchToMuteVideo(true)">
                <!-- <svg class="svg_icon" aria-hidden="true">
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-camera_open"></use>
                            </svg> -->
                <img class="svg_icon" src="static/resource/images/svg-image/camera_open.png" alt="" />
                <!-- <p>点击关闭摄像头</p> -->
            </span>
            <span class="svn_btn" v-show="isConferenceVideoStream === 0" @click.stop="switchToMuteVideo(false)">
                <!-- <svg class="svg_icon">
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-camera_close"></use>
                            </svg> -->
                <img class="svg_icon" src="static/resource/images/svg-image/camera_close.png" alt="" />
                <!-- <p>点击开启摄像头</p> -->
            </span>
        </div>

        <span
            class="svn_btn"
            v-show="LiveConferenceData.joinedMain && isUltraSoundMobile"
            @click.stop="switchMainChannel(0)"
        >
            <!-- <svg class="svg_icon">
                            <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-doppler_open"></use>
                        </svg> -->
            <img class="svg_icon" src="static/resource/images/svg-image/doppler_open.png" alt="" />
        </span>
        <span
            class="svn_btn"
            v-show="!LiveConferenceData.joinedMain && isUltraSoundMobile"
            @click.stop="switchMainChannel(1)"
        >
            <!-- <svg class="svg_icon">
                            <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-doppler_close"></use>
                        </svg> -->
            <img class="svg_icon" src="static/resource/images/svg-image/doppler_close.png" alt="" />
        </span>
        <!-- 发言面板-->
        <span class="svn_btn" @click="openSpeechPanelVisible">
            <svg class="svg_icon">
                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-speech_panel"></use>
            </svg>
        </span>
        <!-- 更多-->
        <span class="svn_btn_more" @click="openMoreConferenceOperationVisible">
            <!-- <svg class="svg_icon">
                            <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-share_panel"></use>
                        </svg> -->
            <svg class="svg_icon">
                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-more"></use>
            </svg>
        </span>
    </div>
</template>
<script>
import base from "../../lib/base";
import Tool from "@/common/tool";
export default {
    mixins: [base],
    props: {
        cid: {
            type: [String, Number],
            default: 0,
        },
        liveRoom: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    data() {
        return {
            isConferenceVideoStream: 0, //是否直播开启视频
            isConferenceAudioStream: 0, //是否直播开启声音
        };
    },
    computed: {
        LiveConferenceData() {
            const data =
                (this.$store.state.liveConference[this.cid] &&
                    this.$store.state.liveConference[this.cid].LiveConferenceData) ||
                {};
            this.isConferenceVideoStream = data.localVideoStream > 0 ? 1 : 0;
            this.isConferenceAudioStream = data.localAudioStream > 0 ? 1 : 0;
            return data;
        },
        checkShowVideoOperateBtn() {
            return this.systemConfig.clientType === 5;
        },
    },
    methods: {
        switchMainChannel: Tool.debounce(function(action) {
            if (action === 1) {
                // 主流重新申请加入房间
                this.$emit("startJoinRoom", { main: 1, aux: 0, isSender: 0,cid:this.cid});
            } else {
                // 主流退出房间
                this.liveRoom.LeaveChannelMain();
            }
        }, 300,true),
        switchToMuteAudio: Tool.debounce(function(isMute) {
            this.liveRoom.MuteLocalAudioStream({
                uid: this.liveRoom.data.localAuxUid,
                isMute,
            });
        }, 300,true),
        switchToMuteVideo: Tool.debounce(function(isMute) {
            this.liveRoom.MuteLocalVideoStream({
                uid: this.liveRoom.data.localAuxUid,
                isMute,
            });
        }, 300,true),
        openSpeechPanelVisible() {
            this.$emit("openSpeechPanelVisible");
        },
        openMoreConferenceOperationVisible() {
            this.$emit("openMoreConferenceOperationVisible");
        },
    },
};
</script>
<style lang="scss" scoped>
.top_video_btn_container {
    background: #00c59d;
    color: #fff;
    display: flex;
    flex-direction: row;
    height: 60px;
    justify-content: space-around;
    align-items: center;
    padding: 0 1.5rem;
    position: relative;
    .svn_btn {
        height: 50px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        position: relative;
        p {
            color: #fff;
            font-size: 12px;
        }
        .svg_icon {
            width: 2.4rem;
            height: 2.4rem;
            fill: #fff;
        }
        .svg_icon_hands {
            width: 1rem;
            height: 1rem;
            position: absolute;
            right: 0;
            top: 0;
        }
        .custom-popup {
            border-radius: 8px;
            padding: 10px;
            left: -1rem;
            top: 2.5rem;
            position: absolute;
            border-radius: 8px;
            transform: translate(0, 0);
            color: #333;
            text-align: left;
            padding: 0.2rem 0.5rem;
            font-weight: normal;
            z-index: 2000;
            background-color: #fff;
            &:before {
                display: inline-block;
                width: 0;
                height: 0;
                border: solid transparent;
                border-width: 10px;
                border-bottom-color: #fff;
                content: "";
                position: absolute;
                top: -1rem;
                left: 1.8rem;
            }
        }
    }
    .svn_btn_more {
        position: absolute;
        right: 0.4rem;
        top: 0.4rem;
        .svg_icon {
            width: 1rem;
            height: 1rem;
            fill: #fff;
        }
    }
}
</style>
