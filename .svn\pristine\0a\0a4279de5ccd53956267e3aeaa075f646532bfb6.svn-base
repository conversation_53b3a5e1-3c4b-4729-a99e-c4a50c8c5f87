<template>
  <div class="progress-indicator">
    <div class="header">
      <span class="header-title">{{headerTitle}}</span>
    </div>
    <div class="content">
      <div class="question-list">
        <div
          v-for="(item, index) in progressList"
          :key="index"
          class="question-item"
          :class="item.status"
          @click="jumpTo(index)"
        >
          {{ index + 1 }}
        </div>
      </div>
      <div class="progress-text">
        <template v-if="topicType === CLOUD_TEST_TYPE.CORRECT">
          {{lang.homework.progress.completionStatus.replace('{done}', doneCount).replace('{total}', progressList.length)}}
          <div class="total-score" v-if="!isPassMode">{{lang.paper_results}}: <span class="score-value">{{totalScore}}</span> {{lang.point_tip}}</div>
          <div class="pass-stats" v-else>
            <div class="pass-rate">通过率: <span class="pass-value">{{passedCount}}/{{passedCount + failedCount}}</span></div>
            <div class="pass-detail">
              <span class="passed">通过: {{passedCount}}</span>
              <span class="failed">不通过: {{failedCount}}</span>
            </div>
          </div>
        </template>
        <template v-else>
          {{lang.homework.progress.completionStatus.replace('{done}', doneCount).replace('{total}', progressList.length)}}
        </template>
      </div>
      <div class="actions">
        <button v-if="topicType !== CLOUD_TEST_TYPE.CORRECT && showSaveBtn" class="save-btn" @click="$emit('save')">{{lang.save_txt}}</button>
        <button v-if="showSubmitBtn" class="submit-btn" @click="$emit('submit')">{{lang.submit_btn}}</button>
      </div>
      <div class="legend">
        <span class="legend-item">
          <span class="box none"></span>{{topicType === CLOUD_TEST_TYPE.ANSWER ? lang.homework.progress.legend.notDone : (isPassMode ? '未评定' : lang.homework.progress.legend.ungraded)}}</span>
        <span class="legend-item">
          <span class="box done"></span>{{topicType === CLOUD_TEST_TYPE.ANSWER ? lang.homework.progress.legend.alreadyDone : (isPassMode ? '已评定' : lang.homework.progress.legend.graded)}}</span>
        <span class="legend-item">
          <span class="box current"></span>{{lang.homework.progress.legend.current}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import base from '../../../lib/base';
import { CLOUD_TEST_TYPE } from '../../../lib/constants';

export default {
    mixins: [base],
    name: "ProgressIndicator",
    props: {
        progressList: {
            type: Array,
            required: true,
        },
        topicType: {
            type: Number,
            required: true
        },
        totalScore: {
            type: [Number, String],
            default: 0
        },
        isPassMode: {
            type: Boolean,
            default: false
        },
        showSaveBtn: {
            type: Boolean,
            default: true
        },
        showSubmitBtn: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            CLOUD_TEST_TYPE: CLOUD_TEST_TYPE,
        };
    },
    computed: {
        doneCount() {
            return this.progressList.filter(item => item.completed).length;
        },
        // remainingCount() {
        //     return this.progressList.length - this.doneCount;
        // },
        headerTitle() {
            return this.topicType === CLOUD_TEST_TYPE.ANSWER ? this.lang.homework.progress.answeringHeader : this.lang.homework.progress.correctionHeader;
        },
        passedCount() {
            return this.progressList.filter(item => item.isPassed === true).length;
        },
        failedCount() {
            return this.progressList.filter(item => item.isPassed === false).length;
        }
    },
    methods: {
        jumpTo(index) {
            this.$emit("jump", index);
        }
    }
};
</script>

<style lang="scss" scoped>
.progress-indicator {
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 270px;
  background: #ffffff;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  z-index: 1000;
  overflow: hidden;
  font-family: Arial, sans-serif;

  .header {
    background-color: #00c59d;
    color: #fff;
    padding: 10px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
  }

  .content {
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .question-list {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .question-item {
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 4px;
        border: 1px solid #ccc;
        font-size: 14px;
        cursor: pointer;
        box-sizing: border-box;

        /* 状态颜色 */
        &.none {
          background-color: transparent;
          color: #000;
        }
        &.done {
          background-color: #ccc;
          color: #fff;
        }
        &.current {
          background-color: #00c59d;
          color: #fff;
        }
      }
    }

    .progress-text {
      font-size: 14px;
      text-align: center;

      .total-score { // Assuming .total-score is always within .progress-text
        // No specific styles other than those inherited or from .score-value
      }
      .pass-stats {
        margin-top: 8px;
        font-size: 14px; // This is redundant if .progress-text already sets it and it's the same. Kept for explicitness.
        display: flex;
        flex-direction: column;
        gap: 5px;

        .pass-rate {
          font-weight: bold;
        }

        .pass-value {
          color: #00c59d;
        }

        .pass-detail {
          display: flex;
          justify-content: space-around;

          .passed {
            color: #00c59d;
            font-weight: bold;
          }

          .failed {
            color: #f56c6c;
            font-weight: bold;
          }
        }
      }
    }

    .actions {
      display: flex;
      justify-content: space-around;

      button {
        flex: 1;
        margin: 0 4px;
        padding: 6px;
        font-size: 14px;
        border: none;
        border-radius: 4px;
        cursor: pointer;

        &.save-btn {
          background-color: #00c59d;
          color: #fff;
        }
        &.submit-btn {
          background-color: #ff675c;
          color: #fff;
        }
      }
    }

    .legend {
      display: flex;
      justify-content: space-around;
      font-size: 12px;
      text-align: center;

      .legend-item {
        display: flex;
        align-items: center;

        .box {
          display: inline-block;
          width: 12px;
          height: 12px;
          margin-right: 4px;
          border: 1px solid #ccc;
          border-radius: 2px;

          &.none {
            background-color: transparent;
          }
          &.done {
            background-color: #ccc;
          }
          &.current {
            background-color: #00c59d;
          }
        }
      }
    }
  }
  .score-value { // This was a top-level class, if it's used outside of .total-score, it should remain top-level or be specifically nested.
                 // Based on template, it's inside .total-score, which is inside .progress-text.
                 // If .score-value is *only* used within .total-score, it can be nested there.
                 // For now, keeping it less nested to avoid breaking if used elsewhere, but ideally it would be:
                 // .progress-text .total-score .score-value { color: #ff675c; }
    color: #ff675c;
  }
}
</style>
