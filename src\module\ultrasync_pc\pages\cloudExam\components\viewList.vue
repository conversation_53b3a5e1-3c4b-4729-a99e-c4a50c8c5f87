<template>
  <div class="view_list">
    <draggable
      v-model="currentList"
      :disabled="disableDelete || type !== CLOUD_TEST_TYPE.EDIT"
      class="draggable-container"
      @end="onDragEnd"
      handle=".drag-handle"
    >
      <div class="view_item" v-for="(image,k_index) of currentList" :key="k_index" @click="showViewImage(currentList,k_index)">
        <img v-if="getFileDisplayType(image) === 'image'" :src="image.url">
        <div v-else-if="getFileDisplayType(image) === 'video'" class="video">
          <i class="icon iconfont iconvideo_fill_light"></i>
        </div>
        <i v-if="!disableDelete" @click.stop="showDeleteImage(k_index)" class="iconfont iconel-icon-delete2"></i>
        <div v-if="!disableDelete && type === CLOUD_TEST_TYPE.EDIT" class="drag-handle">
          <i class="el-icon-rank"></i>
        </div>
      </div>
    </draggable>
    <div v-if="!disableUpload" class="view_item" @click="showUploadClick">
      <i class="icon iconfont iconplus"></i>
    </div>
    <base-gallery ref="baseGallery"></base-gallery>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import { CLOUD_TEST_TYPE } from '../../../lib/constants';
import Tool from '@/common/tool.js';
import baseGallery from '../../../MRComponents/baseGallery.vue';

export default {
    name: 'viewList',
    components: {
        draggable,
        baseGallery,
    },
    props: {
        imageList: {
            type: Array,
            default: () => []
        },
        disableDelete: {
            type: Boolean,
            default: false
        },
        disableUpload: {
            type: Boolean,
            default: false
        },
        type: {
            type: Number,
            default: 1
        }
    },
    computed: {
        currentList: {
            get() {
                return this.imageList || []
            },
            set(value) {
                this.$emit('update:imageList', value)
            }
        }
    },
    data() {
        return {
            CLOUD_TEST_TYPE: CLOUD_TEST_TYPE,
        }
    },
    methods: {
        showViewImage(imageList, index) {
            const processedImageList = imageList.map(item => {
                const fileType = this.getFileDisplayType(item);
                let mappedFileType = fileType;
                if (fileType === 'unknown') {
                    mappedFileType = item.url ? 'image' : 'unknown';
                }
                return {
                    ...item,
                    fileType: mappedFileType
                };
            });
            this.$refs.baseGallery.openGallery(processedImageList, index);
        },
        showDeleteImage(index) {
            this.$emit('delete-image', index)
        },
        showUploadClick() {
            this.$emit('upload-click')
        },
        onDragEnd(evt) {
            this.$emit('order-changed', {
                oldIndex: evt.oldIndex,
                newIndex: evt.newIndex
            })
        },
        getFileDisplayType(item) {
            if (!item || !item.url) {
                return 'unknown';
            }
            const extension = Tool.getFileType(item.url);
            if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
                return 'image';
            } else if (['mp4', 'webm', 'ogg', 'mov', 'avi', 'flv'].includes(extension)) {
                return 'video';
            }
            return 'unknown'; // Default for other types or if extension is not matched
        }
    }
}
</script>

<style lang="scss" scoped>
.view_list{
  display: flex;
  margin: 10px 0;
  flex-wrap: wrap;

  .draggable-container {
    display: flex;
    flex-wrap: wrap;
  }

  .view_item{
    position: relative;
    width: 160px;
    height: 120px;
    background: #000;
    border-radius: 6px;
    margin: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    overflow: hidden;
    &>img{
      max-width: 100%;
      max-height: 100%;
    }
    .video{
      width: 100%;
      height: 100%;
      background: #000;
      .iconvideo_fill_light{
        position: absolute;
        top: 50%;
        left: 50%;
        font-size: 32px;
        color: #fff;
        line-height: 1;
        z-index: 2;
        transform: translate(-50%, -50%);
      }
    }
    .iconplus{
      width: 60px;
      height: 60px;
      text-align: center;
      line-height: 60px;
      border: 2px solid #00c59d;
      border-radius: 50%;
      color: #00c59d;
      font-size: 36px;
    }
    .iconel-icon-delete2{
      position: absolute;
      top: 4px;
      right: 4px;
      color: #ff675c;
    }
    .drag-handle {
      position: absolute;
      bottom: 4px;
      right: 4px;
      color: #fff;
      background-color: rgba(0, 0, 0, 0.5);
      width: 20px;
      height: 20px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: move;

      .icondrag {
        font-size: 14px;
      }
    }
  }
}
</style>
