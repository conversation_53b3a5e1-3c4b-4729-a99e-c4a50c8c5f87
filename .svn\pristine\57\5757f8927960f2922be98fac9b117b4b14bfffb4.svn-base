<template>
    <div>
        <van-popup v-model="dialogVisible" :get-container="getContainer" class="more_conference_operation"
            position="bottom" :close-on-click-overlay="true" :overlay="true" :lockScroll="false">
            <div class="operate-sheet__header">
                <h2 class="operate-sheet__title">{{ lang.more_settinngs }}</h2>
            </div>
            <div class="operate-sheet__options">
                <div role="button" tabindex="0" class="operate-sheet__option" v-if="!LiveConferenceData.isRecording">
                    <van-button type="default" class="operate-sheet__btn" plain @click="startRecordConference"
                        :loading="recordingRequestLoading">
                        <i class="icon iconfont icon-record-circle-line"></i>
                    </van-button>
                    <p class="operate-sheet__name">{{ lang.start_cloud_record }}</p>
                </div>
                <div role="button" tabindex="0" class="operate-sheet__option" v-else>
                    <van-button type="default" class="operate-sheet__btn" plain @click="stopRecordConference"
                        :loading="recordingRequestLoading">
                        <i class="icon iconfont icon-stop-circle-line"></i>
                    </van-button>
                    <p class="operate-sheet__name" v-if="LiveConferenceData.cloud_record_auth">{{ lang.end_cloud_record
                    }}
                    </p>
                    <p class="operate-sheet__name" v-else>{{ lang.is_recording_text }}</p>
                </div>
                <div role="button" tabindex="0" class="operate-sheet__option">
                    <van-button type="default" class="operate-sheet__btn" plain @click="openShareCodeVisible">
                        <i class="icon iconfont icon-QRcode"></i>
                    </van-button>
                    <p class="operate-sheet__name">{{ lang.share_QR_code }}</p>
                </div>
                <div role="button" tabindex="0" class="operate-sheet__option" v-if="!isIOS&&!isBrowser">
                    <van-button type="default" class="operate-sheet__btn" plain @click="captureLiveStream">
                        <van-icon name="photo-o" size="1.2rem" />
                    </van-button>
                    <p class="operate-sheet__name">{{ lang.screen_shot }}</p>
                </div>
                <div role="button" tabindex="0" class="operate-sheet__option" v-if="isUltraSoundMobile">
                    <van-button type="default" :class="['operate-sheet__btn', {'active-btn': isHidePatientInfo}]" plain @click="toggleHidePatientInfo">
                        <van-icon name="shield-o" size="1.2rem" :class="{'active-icon': isHidePatientInfo}" />
                    </van-button>
                    <p class="operate-sheet__name">{{ lang.desensitization_reception }}</p>
                </div>
            </div>
            <button type="button" class="operate-sheet__cancel" @click="dialogVisible = false">{{ lang.cancel_btn
            }}</button>
        </van-popup>
        <ShareConferenceModal :show.sync="shareConferenceVisible" :LiveConferenceData="LiveConferenceData"
            v-bind="$attrs"></ShareConferenceModal>
    </div>
</template>
<script>
import base from "../../lib/base";
import dialogPopstate from "../../lib/dialogPopstate";
import { Icon,  Button, Popup, Toast} from "vant";
import ShareConferenceModal from './shareConferenceModal.vue'
import { getLiveRoomObj } from '../../lib/common_base'
import Tool from "@/common/tool.js";
export default {
    name: 'moreConferenceOperationComponent',
    mixins: [base, dialogPopstate],
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        cid: {
            type: [String, Number],
            default: 0,
        }
    },
    filters: {},
    data() {
        return {
            recordingRequestLoading: false,
            shareConferenceVisible: false,
            currentHistoryHash: '',
            liveRoom: null,
            isIOS:false,
            isBrowser:false,
            isHidePatientInfo: false,
            isUltraSoundMobile:false
        };
    },
    components: {
        VanButton: Button,
        VanPopup: Popup,
        VanIcon: Icon,
        ShareConferenceModal
    },
    computed: {
        dialogVisible: {
            get() {
                if (!this.show) {
                    if (this.shareConferenceVisible) {
                        this.shareConferenceVisible = false
                    }
                }

                return this.show;
            },
            set(val) {
                this.$emit("update:show", val);
            },
        },
        conversation() {
            return this.$store.state.conversationList[this.cid] || {}
        },
        LiveConferenceData() {
            return this.$store.state.liveConference[this.cid] && this.$store.state.liveConference[this.cid].LiveConferenceData || {}
        },
    },
    watch: {

    },
    created() {
        this.isIOS = Tool.checkAppClient('IOS');
        this.isBrowser = Tool.checkAppClient('Browser');
        this.isUltraSoundMobile = Tool.checkAppClient('UltraSoundMobile');
    },
    activated() {
        this.$nextTick(() => {

        })

    },
    mounted() { },
    methods: {
        getContainer() {
            return document.querySelector("body");
        },
        async startRecordConference() {
            let liveRoom = getLiveRoomObj()
            if (!liveRoom) {
                return
            }
            if (!this.LiveConferenceData.cloud_record_auth) {
                Toast(this.lang.only_host_initiate_cloud_recording)
                return
            }
            this.recordingRequestLoading = true
            try {
                await liveRoom.ServiceStartConferenceRecording()
                Toast(this.lang.recording_turned_on)
            } catch (error) {
                console.error(error)
            } finally {

                this.recordingRequestLoading = false
            }
        },
        async stopRecordConference() {
            let liveRoom = getLiveRoomObj()
            if (!liveRoom) {
                return
            }
            if (!this.LiveConferenceData.cloud_record_auth) {
                Toast(this.lang.only_host_end_cloud_recording)
                return
            }
            this.recordingRequestLoading = true
            if (!this.LiveConferenceData.joinedAux) {
                return
            }
            try {
                await liveRoom.ServiceStopConferenceRecording()
                Toast(this.lang.recording_ended)
            } catch (error) {
                console.error(error)
            } finally {
                this.recordingRequestLoading = false
            }

        },
        openShareCodeVisible() {
            // this.dialogVisible = false
            this.shareConferenceVisible = true
            this.$nextTick(() => {

            })

        },
        captureLiveStream() {
            window.CWorkstationCommunicationMng.captureLiveStream()
        },
        async toggleHidePatientInfo() {

            try {
                if (this.isHidePatientInfo) {
                    const res = await Tool.createCWorkstationCommunicationMng({
                        name: "ShowPrivateInfo",
                        emitName: "NotifyShowPrivateInfoState",
                        params: {},
                    });
                    if (res && res.error_code === 0) {
                        this.isHidePatientInfo = false;
                    }
                } else {
                    const res = await Tool.createCWorkstationCommunicationMng({
                        name: "HidePrivateInfo",
                        emitName: "NotifyHidePrivateInfoState",
                        params: {},
                        timeout:null
                    });
                    if (res && res.error_code === 0) {
                        this.isHidePatientInfo = true;
                    }
                }
            } catch (error) {
                console.error('Error calling native privacy function in toggleHidePatientInfo:', error);
            }
        }
    },
};
</script>
<style lang="scss">
.more_conference_operation {
    .operate-sheet__header {
        padding: 12px 16px 4px;
        text-align: center;
        .operate-sheet__title {
            margin-top: 8px;
            color: #323233;
            font-weight: normal;
            font-size: 14px;
            line-height: 20px;
        }
    }
    .operate-sheet__options {
        position: relative;
        display: flex;
        padding: 16px 0 16px 8px;
        overflow-x: auto;
        overflow-y: visible;
        -webkit-overflow-scrolling: touch;
        .operate-sheet__option {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            user-select: none;
            .operate-sheet__icon {
                width: 48px;
                height: 48px;
                margin: 0 16px;
            }
            .operate-sheet__name {
                margin-top: 8px;
                padding: 0 4px;
                color: #646566;
                font-size: 12px;
            }
        }
        .operate-sheet__btn{
            border-radius: 50%;
            width: 2.4rem;
            height: 2.4rem;
            background: #F2F3F5;
            margin: 0 1rem;
            .iconfont{
                font-size: 1.2rem;
                color: #646566;
            }
            .icon-stop-circle-line{
                color: red;
                font-weight: 600;
            }
            &.active-btn {
                background: #00c59d !important;
                .active-icon {
                    color: white;
                }
            }
        }
    }
    .operate-sheet__cancel {
        &::before {
            display: block;
            height: 8px;
            background-color: #f7f8fa;
            content: " ";
        }
        display: block;
        width: 100%;
        padding: 0;
        font-size: 16px;
        line-height: 48px;
        text-align: center;
        background: #fff;
        border: none;
        cursor: pointer;
    }
}
</style>
