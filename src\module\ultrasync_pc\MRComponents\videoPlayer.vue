<template>
    <div class="video_container">
        <video
            ref="videoPlayer"
            class="video-js"
            playsinline
            controls
            crossorigin="anonymous"
            :poster="poster"
            :key="playerKey"
        >
            <!-- 不直接设置 source，而是通过 HLS.js 动态加载 -->
        </video>
    </div>
</template>

<script>
import Plyr from "plyr";

export default {
    name: "AutoVideoPlayer",
    props: {
        videoSrc: {
            type: String,
            required: true,
        },
        poster: {
            type: String,
            default: "", // 默认没有封面
        },
        autoPlay: {
            type: Boolean,
            default: true,
        },
        id: {
            type: [String, Number],
            default: () => `video-player-${Date.now()}-${Math.floor(Math.random() * 1000)}`
        },
        // 新增props用于处理播放事件上报
        enableReporting: {
            type: Boolean,
            default: false, // 默认不启用上报
        },
        fileInfo: {
            type: Object,
            default: () => ({}) // 包含resource_id和group_id等信息
        }
    },
    data() {
        return {
            playerKey: this.generatePlayerKey(),
            player: null,
            hls: null,  // 添加 hls 实例存储
            // 上报相关状态
            isReporting: false,
            reportStartTime: 0,
            reportInterval: null,
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initPlayer();
        });
    },
    beforeDestroy() {
        this.stopReporting();
        if (this.player) {
            this.player.destroy();
        }
        if (this.hls) {
            this.hls.destroy();
        }
    },
    methods: {
        generatePlayerKey() {
            return `${this.id}-${this.videoSrc}`;
        },
        initPlayer() {
            if (!this.$refs.videoPlayer || this.player) {
                return;  // 如果播放器已经初始化，直接返回
            }

            // 初始化 Plyr
            this.player = new Plyr(this.$refs.videoPlayer, {
                controls: ["play-large", "play", "current-time", "progress", "fullscreen"],
            });

            // 添加播放事件监听
            this.setupVideoEventListeners();

            // 首次加载视频
            this.loadVideo(this.videoSrc);
        },
        async loadVideo(src) {
            if (!this.player || !this.$refs.videoPlayer) {
                return;
            }

            const videoElement = this.$refs.videoPlayer;

            // 处理 HLS 视频
            if (src.endsWith(".m3u8")) {
                try {
                    // 动态引入 Hls.js
                    const { default: Hls } = await import(/* webpackPrefetch: true */ 'hls.js');

                    if (Hls.isSupported()) {
                        if (!this.hls) {
                            this.hls = new Hls();
                        }
                        
                        // 添加 HLS 错误事件监听
                        this.hls.on(Hls.Events.ERROR, (event, data) => {
                            console.error('HLS error:', event, data);
                            if (data.fatal) {
                                this.$emit('error', { type: 'hls', event, data });
                            }
                        });
                        
                        this.hls.loadSource(src);
                        this.hls.attachMedia(videoElement);
                        this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
                            if (this.autoPlay) {
                                videoElement.play();
                            }
                        });
                    }
                } catch (error) {
                    console.error('Failed to load HLS:', error);
                    this.$emit('error', { type: 'hls_init', error });
                }
            } else {
                // 普通视频直接加载
                videoElement.src = src;
                
                // 添加错误事件监听
                videoElement.addEventListener("error", (event) => {
                    console.error('Video load error:', src, event);
                    this.$emit('error', event);
                });
                
                videoElement.addEventListener("loadedmetadata", () => {
                    if (this.autoPlay) {
                        videoElement.play();
                    }
                });
            }
        },
        // 设置视频事件监听器
        setupVideoEventListeners() {
            if (!this.player || !this.enableReporting) {
                return;
            }

            const videoElement = this.$refs.videoPlayer;
            
            // 监听播放事件
            this.player.on('play', () => {
                console.log('Video play event triggered');
                this.startReporting();
                this.$emit('play', this.fileInfo);
            });

            // 监听暂停事件
            this.player.on('pause', () => {
                console.log('Video pause event triggered');
                this.pauseReporting();
                this.$emit('pause', this.fileInfo);
            });

            // 监听结束事件
            this.player.on('ended', () => {
                console.log('Video ended event triggered');
                this.stopReporting();
                this.$emit('ended', this.fileInfo);
            });

            // 监听错误事件
            this.player.on('error', (error) => {
                console.error('Video error:', error);
                this.stopReporting();
                this.$emit('error', { error, fileInfo: this.fileInfo });
            });
        },
        // 开始上报
        startReporting() {
            if (!this.enableReporting || this.isReporting) {
                return;
            }

            this.isReporting = true;
            this.reportStartTime = Date.now();
            
            // 立即上报一次
            this.reportVideoTime();
            
            // 每5秒上报一次
            this.reportInterval = setInterval(() => {
                this.reportVideoTime();
            }, 5000);

            // console.log('Started video reporting for:', this.fileInfo.resource_id);
        },
        // 暂停上报（但不清除状态）
        pauseReporting() {
            if (this.reportInterval) {
                clearInterval(this.reportInterval);
                this.reportInterval = null;
            }
            
            // 暂停时上报一次当前的观看时长
            if (this.isReporting) {
                this.reportVideoTime();
            }
            
            console.log('Paused video reporting');
        },
        // 停止上报并清除状态
        stopReporting() {
            if (this.reportInterval) {
                clearInterval(this.reportInterval);
                this.reportInterval = null;
            }
            
            // 停止时最后上报一次
            if (this.isReporting) {
                this.reportVideoTime();
            }
            
            this.isReporting = false;
            this.reportStartTime = 0;
            
            // console.log('Stopped video reporting');
        },
        // 上报视频观看时间
        reportVideoTime() {
            if (!this.enableReporting || !this.reportStartTime || !this.fileInfo.resource_id) {
                return;
            }

            const duration = Date.now() - this.reportStartTime;
            const reportData = {
                type: 5,
                cid: this.fileInfo.group_id,
                business_data: {
                    resource_id: this.fileInfo.resource_id,
                    uuid: this.reportStartTime,
                    duration: duration,
                },
                showErrorToast: false,
            };

            console.log('Reporting video time:', Math.round(duration / 1000), 'seconds');
            
            // 发射事件给父组件处理上报
            this.$emit('reportTime', reportData);
        },
    }
};
</script>

<style scoped lang="scss">
.video_container {
    width: 100%;
    height: 100%;
    ::v-deep {
        .plyr--video {
            width: 100%;
            height: 100%;
        }
    }
}
</style>
