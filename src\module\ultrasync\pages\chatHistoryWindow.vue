<template>
    <transition name="fade">
        <div class="chat_window second_level_page" ref="chatHistoryWindow">
            <mrHeader>
                <template #title>
                    {{ chatSubject }}
                </template>
            </mrHeader>
            <div class="second_level_content chat_window_container">
                <chat-message-list
                    :chatMessageList="chatHistoryMessageList"
                    @loadTopHistory="loadTopHistory({})"
                    ref="chatHistoryMessageListCom"
                    @loadBottomHistory="loadBottomHistory"
                    :options="chatListOptions"
                    v-if="chatHistoryMessageList.length > 0"
                    :isReload="false"
                    :soundPlayerName="soundPlayerName"
                    :chatType="CHAT_TYPE.CHAT_HISTORY"
                >
                </chat-message-list>
                <template v-else>
                    <div class="noDataBox" v-loading="isLoading">
                        {{ isLoading ? lang.bottom_loading_text : lang.no_more_text }}
                    </div>
                </template>
            </div>
            <router-view></router-view>
        </div>
    </transition>
</template>
<script>
import base from "../lib/base";
import chatMessageList from "../components/chatMessageList.vue";
import { cloneDeep } from "lodash";
import { Toast } from "vant";
import {
    setWithDrawData,
    setExpirationResource,
    setIworksInfoToMsg,
    parseImageListToLocal,
    htmlEscape,
    transferPatientInfo,
    htmlUnescape,
} from "../lib/common_base";
import Tool from "@/common/tool";
import { CHAT_TYPE } from "../lib/constants";
export default {
    mixins: [base],
    components: { chatMessageList },
    computed: {
        conversation() {
            return this.$store.state.conversationList[this.cid] || {};
        },
        cid() {
            return this.$route.params.cid;
        },
        currentMsg() {
            return this.$route.params.message || {};
        },
        gmsgId() {
            return this.currentMsg.gmsg_id;
        },
        resource_id() {
            return this.currentMsg.copied_from || this.currentMsg.resource_id;
        },
        remarkMap() {
            return this.$store.state.friendList.remarkMap;
        },
        chatSubject() {
            if (this.conversation.service_type === 0 || this.conversation.service_type === 104) {
                return this.remarkMap[this.conversation.fid] || this.conversation.subject;
            } else if (this.conversation.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze) {
                return this.lang.ai_analyze;
            } else if (this.conversation.service_type == this.systemConfig.ServiceConfig.type.DrAiAnalyze) {
                return this.lang.dr_ai_analyze;
            } else if (this.conversation.service_type == this.systemConfig.ServiceConfig.type.FileTransferAssistant) {
                return this.lang.file_transfer_assistant;
            } else {
                return "";
            }
        },
    },
    data() {
        return {
            CHAT_TYPE: CHAT_TYPE,
            chatHistoryMessageList: [],
            chatListOptions: {
                isLoadTop: true, //是否启用下拉加载
                isLoadBottom: true, //是否启用上拉加载
            },
            isLoading: false,
            soundPlayerName: "historySoundPlayer",
        };
    },
    created() {},
    activated() {
        this.getChatHistoryMessageListByGmsgId();
    },
    deactivated() {
        this.chatHistoryMessageList = [];
    },
    mounted() {},
    beforeDestroy() {
        this.$refs[this.soundPlayerName].removeEventListener("ended", this.resetSounding, false);
    },
    beforeRouteEnter(to, from, next) {
        if (!from.name) {
            //二级页面刷新时返回上级页面
            // window.location.replace(`#/index`)
            // window.location.reload()
            next();
        } else {
            next();
        }
    },
    beforeRouteLeave(to, from, next) {
        if (Tool.checkMobileDialogShow()) {
            //如果使用到了beforeRouteLeave，需要手动处理关闭弹窗
            Tool.closeMobileDialog();
            next(false);
            return;
        }
        this.closeChatWindow(to, from, next);
    },
    methods: {
        goBack() {
            this.$router.go(-1);
        },
        isPreventScroll(e) {
            if (e.target.classList[0] == "content_text") {
                //滑动文本输入区域滚动条
            } else {
                e.preventDefault();
                e.stopPropagation();
            }
        },
        async getChatHistoryMessageListByGmsgId() {
            let params = {
                start: this.gmsgId,
                count: this.systemConfig.historyMessageNum,
                mode: "init",
                resource_id: this.resource_id,
            };
            if (this.$route.params.uid && Number(this.$route.params.uid)) {
                params.start = 0;
                params.mode = "older";
                params.sender_id = this.$route.params.uid;
                params.group_id = this.cid;
            }

            if (Number(this.$route.params.uid) == 0) {
                params.start = 0;
                params.mode = "init";
                params.datetime = this.$route.query.date;
                params.group_id = this.cid;
            }
            this.isLoading = true;
            const res = await this.conversation.socket.request("get_history_chat_messages_by_search", params);
            let data = this.preHandleMessageList(res.messages);
            // console.log('res',res)
            // if(Object.keys(this.currentMsg).length>0 ){
            //     data.unshift(this.currentMsg)
            // }

            if (data.length < 20 && data.length > 0) {
                //如果此时data数量少于20 则继续往旧数据加载20条
                // await this.loadTopHistory({
                //     oStart:data[0].gmsg_id ,
                //     oCount:this.systemConfig.historyMessageNum - data.length +20,
                //     oList:data,
                //     calcScroll:true
                // })
                this.chatHistoryMessageList = data;
            } else {
                this.chatHistoryMessageList = data;
            }
            this.isLoading = false;
        },
        preHandleMessageList(oData) {
            let data = cloneDeep(oData);
            data = data.sort((a, b) => {
                return a.send_ts > b.send_ts ? 1 : -1;
            });
            parseImageListToLocal(data, "avatar");
            parseImageListToLocal(data, "url");
            setExpirationResource(data, this.cid);
            setWithDrawData(data);
            this.setDefaultImg(data);
            for (let message of data) {
                message.msg_body = this.parseMessageBody(message.msg_body);
                message.patientInfo = transferPatientInfo(message);
                message.sending = false;
                message.downloading = false;
                message.sendFail = message.sendFail || false;
                if (message.msg_type == this.systemConfig.msg_type.AI_ANALYZE) {
                    parseImageListToLocal(message.ai_analyze && message.ai_analyze.messages, "url");
                }
                if (message.protocol_guid) {
                    //消息存在iworks信息
                    setIworksInfoToMsg(message);
                }
            }
            return data;
        },
        sortSendTime() {},
        async loadTopHistory({ oStart = "", oCount = 0, oList = [], calcScroll = false }) {
            let start = oStart || (this.chatHistoryMessageList.length > 0 ? this.chatHistoryMessageList[0].gmsg_id : 0);
            let count = oCount || this.systemConfig.historyMessageNum + 20;
            let list = oList.length > 0 ? oList : this.chatHistoryMessageList;
            if (!start || count < 1) {
                this.$refs.chatHistoryMessageListCom.listLoaded(this.chatHistoryMessageList, null, null, () => {});
                return;
            }
            let params = {
                start: start,
                count: count,
                mode: "older",
                resource_id: this.resource_id,
            };
            if (this.$route.params.uid && Number(this.$route.params.uid)) {
                params.sender_id = this.$route.params.uid;
                params.group_id = this.cid;
            }

            this.isLoading = true;
            const res = await this.conversation.socket.request("get_history_chat_messages_by_search", params);
            let data = res.messages;
            // console.log('res',res)
            if (data && data.length < 1) {
                Toast(this.lang.loaded_all_message);
            }
            data = this.preHandleMessageList(data);

            this.chatHistoryMessageList = data.concat(list);
            let scrollFn = null;
            this.$nextTick(() => {
                if (calcScroll) {
                    scrollFn = () => {
                        let index = data.length;
                        this.$nextTick(() => {
                            let lastItem = this.$refs.chatHistoryMessageListCom.$refs["message_item_" + index][0];
                            lastItem.scrollIntoView && lastItem.scrollIntoView(true);
                        });
                    };
                }
                this.$refs.chatHistoryMessageListCom.listLoaded(this.chatHistoryMessageList, null, null, scrollFn);
            });
            this.isLoading = false;
        },
        async loadBottomHistory() {
            let start = this.chatHistoryMessageList[this.chatHistoryMessageList.length - 1].gmsg_id;
            let count = this.systemConfig.historyMessageNum;
            console.log("start", start, count);
            if (!start || count < 1) {
                return;
            }
            let params = {
                start: start,
                count: count,
                mode: "newer",
                resource_id: this.resource_id,
            };

            if (this.$route.params.uid && Number(this.$route.params.uid)) {
                params.sender_id = this.$route.params.uid;
                params.group_id = this.cid;
            }
            if (this.$route.params.date) {
            }
            this.isLoading = true;
            const res = await this.conversation.socket.request("get_history_chat_messages_by_search", params);
            let data = res.messages;
            if (data && data.length < 1) {
                Toast(this.lang.loaded_all_message);
            }
            let status = null;
            if (data.length === 0) {
                status = "nomore";
                this.$refs.chatHistoryMessageListCom.listLoaded([], "bottom", status);
                return;
            } else {
                data = this.preHandleMessageList(data);
                this.chatHistoryMessageList = this.chatHistoryMessageList.concat(data);
                this.$nextTick(() => {
                    if (data.length < this.systemConfig.historyMessageNum) {
                        status = "nomore";
                    }
                    this.$refs.chatHistoryMessageListCom.listLoaded(this.chatHistoryMessageList, "bottom", status);
                });
            }

            this.isLoading = false;
        },
        closeChatWindow(to, from, next) {
            if (window.location.hash.indexOf("force=1") > -1) {
                //强制回退
                next(true);
                return;
            }
            next(true);
        },
    },
};
</script>
<style scoped lang="scss">
.noDataBox {
    margin-top: 1.5rem;
    text-align: center;
    color: #666;
    font-size: 0.8rem;
}
</style>
