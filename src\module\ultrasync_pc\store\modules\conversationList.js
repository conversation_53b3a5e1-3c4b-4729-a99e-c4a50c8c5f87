import {cloneDeep,uniqBy} from 'lodash'
import Vue from 'vue'
const initState ={
    // "1":{
    //     attendeeList:{'attendee_1':{}},
    //     chatMessageList:[],//聊天记录
    //     creator_id:1,
    //     id:1,
    //     is_public:1,
    //     record_mode:0,
    //     voice_ctrl_mode:0,
    //     subject:'',
    //     is_single_chat:1,
    //     socket:{},
    //     type:1,
    //     video_list:[],
    //     rtc_voice_list:[],
    //     galleryObj:{
    //         gallery_list:[]addAttendee
    //     },

    // }
}

export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'conversationList',cloneDeep(initState))
            }
        },
        setConversation(state, valObj) {

            var cid=valObj.id+""
            if(!state[cid]){
                this.commit('conversationList/initConversation',cid)
            }
            valObj.chatMessageList=[]
            valObj.galleryObj={
                gallery_list:[],
                gallery_index:0,
                image_list:[],
                image_index:0,
                video_list:[],
                video_index:0,
                exam_list:[],
                exam_index:0
            },
            valObj.device_list=[];
            valObj.multicenter_list=[];
            valObj.multicenter_type='';
            valObj.multicenter_info=null;
            valObj.is_loaded_history_list = false;
            valObj.loadedGallery=false;
            valObj.preferences.is_mute = valObj.preferences.is_mute||0;
            valObj.applyCount = 0;
            if(state[cid]&&state[cid].is_loaded_history_list){
                delete valObj.is_loaded_history_list
                delete valObj.chatMessageList
                delete valObj.galleryObj
            }
            for(let key in valObj.attendeeList){
                valObj.attendeeList[key].video_status=-1;
                valObj.attendeeList[key].realtime_voice_state=valObj.attendeeList[key].realtime_voice_state||0;
                valObj.attendeeList[key].is_speek=valObj.attendeeList[key].is_speek||0;

            }
            Object.keys(valObj).forEach((key)=>{
                Vue.set(state[cid],key,valObj[key]);
            })

        },
        initConversation(state,cid){
            //初始化会话，在数据没返回前能进入聊天界面
            var defaultConversation={
                id:cid,
                attendeeList:{},
                subject:'',
                chatMessageList:[],
                galleryObj:{
                    gallery_list:[],
                    gallery_index:0,
                    image_list:[],
                    image_index:0,
                    video_list:[],
                    video_index:0,
                    exam_list:[],
                    exam_index:0
                },
                is_loaded_history_list:false,
                loadedGallery:false,
                conferencePlanList:{},
                preferences:{
                    is_mute:false
                },
                device_list:[],
                multicenter_list:[],
                is_obstetric_qc_mc:'',
                obstetric_qc_multicenter:null,
                applyCount:0,
            }
            // state[cid]=defaultConversation
            Vue.set(state,cid,defaultConversation)
        },
        clearConversation(state){
            //清空所有会话
            for(let key in state){
                delete state[key]
            }
        },
        initGalleryObj(state,valObj){
            for(let item of valObj.gallery_list){
                item.loaded=false
            }
            if(valObj.gallery_list.length>0){
                valObj.gallery_index = valObj.gallery_index||valObj.gallery_list[valObj.gallery_list.length-1].resource_id
            }
            Vue.set(state[valObj.cid].galleryObj,'gallery_list',valObj.gallery_list)
            Vue.set(state[valObj.cid].galleryObj,'gallery_index',valObj.gallery_index)
            Vue.set(state[valObj.cid],'loadedGallery',true)
        },
        addFileToConversation(state,data){
            console.log('addFileToConversation',data)
            let msg_type = window.vm.$store.state.systemConfig.msg_type;
            const imageType = [msg_type.Image, msg_type.Frame, msg_type.OBAI];
            const videoType = [msg_type.Video, msg_type.Cine, msg_type.RealTimeVideoReview, msg_type.VIDEO_CLIP];
            //为会话文件列表添加文件
            data.message.loaded=false
            data.message.realUrl=''
            data.message.new_receive=true;
            if(!data.message.nickname){
                data.message.nickname= state[data.cid].attendeeList['attendee_'+data.message.creator_id].nickname
            }
            let gallery_list=state[data.cid].galleryObj.gallery_list
            gallery_list[0]&&(gallery_list[0].new_receive=false)
            gallery_list.unshift(data.message)

            if(imageType.includes(data.message.msg_type)){
                let image_list=state[data.cid].galleryObj.image_list
                let image_index=state[data.cid].galleryObj.image_index
                image_index&&image_list.unshift(data.message)
            }else if(videoType.includes(data.message.msg_type)){
                let video_list=state[data.cid].galleryObj.video_list
                let video_index = state[data.cid].galleryObj.video_index
                video_index&&video_list.unshift(data.message)
            }
            if(data.message.hasOwnProperty('exam_id')){
                let exam_list=state[data.cid].galleryObj.exam_list
                let exam_index=state[data.cid].galleryObj.exam_index
                exam_index&&exam_list.unshift(data.message)
            }
        },
        setChatMessage(state,data){
            //将消息加入聊天记录
            let cid=data&&data.cid
            if(Number(cid)===0||!cid){
                return
            }
            if(!state[cid]){
                return
            }
            let is_localdb_msg=data.is_localdb_msg||0;
            let chatMessageList=state[cid].chatMessageList;
            let len=chatMessageList.length;
            for(let message of data.list){
                let type=data.type;
                let flag = 0;
                if(message.gmsg_id && message.gmsg_id != 0){
                    for(let chatMessage of state[cid].chatMessageList){
                        if(message.gmsg_id == chatMessage.gmsg_id){
                            flag = 1;
                            break;
                        }
                    }
                }
                if(flag == 1) {
                    console.log("ignore message %%%%%%%%%%")
                    continue;
                }
                if(state[cid] && state[cid].attendeeList&&state[cid].attendeeList['attendee_'+message.sender_id]){
                    if(!message.nickname){
                        message.nickname= state[cid].attendeeList['attendee_'+message.sender_id].nickname
                    }
                    message.avatar=state[cid].attendeeList['attendee_'+message.sender_id].avatar
                    message.avatar_local=state[cid].attendeeList['attendee_'+message.sender_id].avatar_local
                }
                message.is_localdb_msg = is_localdb_msg;
                if (message.origin_gmsg_id) {
                    //聚合消息更新
                    let isUpdate=false;
                    for (var i = chatMessageList.length - 1; i >= 0; i--) {
                        //从聊天记录尾部遍历，提高效率
                        let item=chatMessageList[i]
                        if (item.origin_gmsg_id==message.origin_gmsg_id||item.gmsg_id==message.origin_gmsg_id) {
                            if (item.gmsg_id>=message.gmsg_id) {
                                // 旧的聚合消息晚到达直接忽略
                                isUpdate=true
                                break;
                            }
                            message.ai_analyze_list = message.ai_analyze_list || []
                            item.ai_analyze_list = item.ai_analyze_list || []
                            let ai_analyze_ids = (item.ai_analyze_list).reduce((h,v)=>{
                                h.push(v.id);return h;
                            },[])
                            let ai_analyze_list = item.ai_analyze_list
                            if(message.ai_analyze){
                                if(ai_analyze_ids.indexOf(message.ai_analyze.id)<0){
                                    message.ai_analyze_list = [message.ai_analyze,...item.ai_analyze_list]
                                }
                            }
                            message.ai_analyze_list  = uniqBy(message.ai_analyze_list, 'id')
                            chatMessageList.splice(i,1,message);
                            isUpdate=true
                            break;
                        }
                    }
                    if (isUpdate) {
                        continue ;
                    }
                }
                if (type=='append') {
                    // 聚合视图如果收到第一条消息晚于其他聚合消息，则忽略聚合消息
                    let ignore = false;
                    for(let item of chatMessageList){
                        if (item.origin_gmsg_id&&item.origin_gmsg_id == message.gmsg_id) {
                            ignore = true;
                            break
                        }
                    }
                    if (ignore) {
                        continue;
                    }
                    state[cid].chatMessageList.push(message)
                    if (message.send_ts&&chatMessageList[len-1]&&chatMessageList[len-1].send_ts) {
                        let time1=new Date(message.send_ts)
                        let time2=new Date(chatMessageList[len-1].send_ts)
                        if (time1.valueOf()-time2.valueOf()<0) {
                            //接收消息的时间早于最后一条消息，重新排序
                            chatMessageList.sort((a,b)=>{
                                if (a.send_ts&&b.send_ts) {
                                    if (new Date(a.send_ts).valueOf()<new Date(b.send_ts).valueOf()) {
                                        return -1
                                    }
                                }
                                return 1
                            })
                        }
                    }
                }else if(type=='prepend'){
                    state[cid].chatMessageList.unshift(message)
                }else if(type=='splice'){
                    let lastMsg=state[cid].chatMessageList[chatMessageList.length-1]
                    if (lastMsg&&lastMsg.gmsg_id==message.gmsg_id) {
                        //未开启会话转发图片会导致say和history得到重复消息
                    }else{
                        state[cid].chatMessageList.unshift(message);
                    }
                }
            }
        },
        updateMessageList(state,{cid,list}){
            let conversation=state[cid]
            if (conversation) {
                Vue.set(conversation,'chatMessageList',list)
            }
        },
        deleteChatMessage(state,valObj){
            //删除某个消息
            let cid=valObj.cid
            state[cid].chatMessageList.splice(valObj.index,1)
        },
        deleteUploadChatMessage(state,{cid,file_id}){
            for(let i = state[cid].chatMessageList.length-1; i >= 0; i--){
                if(state[cid].chatMessageList[i].file_id===file_id){
                    state[cid].chatMessageList.splice(i,1)
                    break
                }
            }
        },
        sendAck(state,data){
            //消息确认送达
            var chatMessageList=state[data.cid].chatMessageList
            for (var i = chatMessageList.length - 1; i >= 0; i--) {
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.tmp_gmsg_id==data.message.tmp_gmsg_id) {
                    clearTimeout(message.sendingTimer)
                    data.message.sending=false
                    data.message.sendFail=false
                    data.message.timeout=message.timeout
                    data.message.nickname=data.message.nickname||message.nickname;
                    data.message.loaded=false;
                    data.message.avatar=data.message.avatar||message.avatar
                    data.message.avatar_local=data.message.avatar_local||message.avatar_local
                    data.message.sender_ids = data.message.sender_id
                    clearTimeout(data.message.timeout)
                    if (data.message.origin_gmsg_id) {
                        //发送检查图片时聚合
                        chatMessageList.splice(i,1)
                        this.commit('conversationList/setChatMessage',{
                            list:[data.message],
                            cid:data.cid,
                            type:'replace',
                        })
                    }else{
                        chatMessageList.splice(i,1,data.message)
                    }

                    break;
                }
            }
        },
        setSendFail(state,data){
            //消息发送失败
            var chatMessageList=state[data.cid].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.tmp_gmsg_id==data.tmp_gmsg_id) {
                    message.sending=false
                    message.sendFail=true
                    chatMessageList.splice(i,1,message)
                    break;
                }
            }

        },
        setCommentToConversation(state,data){
            //已废弃
            let gallery_list=state[data.cid].galleryObj.gallery_list
            for(let commentKey in data.list){
                for(let i=0;i<gallery_list.length;i++){
                    let item=gallery_list[i]
                    if(commentKey==item.resource_id){
                        item.comment_list=data.list[commentKey].comment_list
                        item.tag_names=data.list[commentKey].tag_names
                        item.tags_list=data.list[commentKey].tags_list
                        //强制视图更新
                        gallery_list.splice(i,1,item)
                        break;
                    }
                }
            }
        },
        //更新ai分析数据到图片
        updateMessageMCAiReport(state,data){
            //更新文件上传进度
            let msg=data;
            if(!msg.group_id||state[msg.group_id]==undefined||msg.mc_resource_map==undefined){
                return
            }
            let chatMessageList=state[msg.group_id].chatMessageList
            // console.error('msg.mc_resource_map--:',msg.mc_resource_map)
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if(message.img_id&& msg.mc_resource_map && message.img_id==msg.mc_resource_map.img_id){
                    message.mc_resource_map = msg.mc_resource_map
                    // chatMessageList.splice(i,1,message)
                    Vue.set(state[msg.group_id].chatMessageList[i],'mc_resource_map',msg.mc_resource_map)
                }
                if(message.resourceList&&message.resourceList.length){
                    let resourceList = message.resourceList
                    for(let j=resourceList.length-1;j>=0;j--){
                        let resource=resourceList[j]
                        if(msg.mc_resource_map.resource_id == resource.id){
                            // message.mc_resource_map = msg.mc_resource_map
                            // console.error('resource---:',resource)
                            resource.obstetric_finshed = resource.obstetric_finshed || msg.mc_resource_map.ai_report.finshed
                            resource.obstetric_view_quality = msg.mc_resource_map.quality
                            resource.obstetric_view_type = msg.mc_resource_map.type
                            message.resourceList[j] = resource
                            // console.error('---123')
                            // chatMessageList.splice(i,1,message)
                            Vue.set(state[msg.group_id].chatMessageList[i].resourceList,j,resource)
                        }

                    }
                }
                if(message.imageList&&message.imageList.length){
                    let imageList = message.imageList
                    for(let k=imageList.length-1;k>=0;k--){
                        let image=imageList[k]
                        if(msg.mc_resource_map.img_id == image.img_id){
                            // message.mc_resource_map = msg.mc_resource_map
                            if(image.mc_resource_map){
                                image.mc_resource_map =  msg.mc_resource_map
                                message.imageList[k] = image
                            }
                            // chatMessageList.splice(i,1,message)
                            const new_img = {...image, ...msg}
                            state[msg.group_id].chatMessageList[i].imageList.splice(k,1,new_img)
                            // Vue.set(state[msg.group_id].chatMessageList[i].imageList,k,image)
                        }
                    }
                }
            }

        },
        updateGalleryObjMCAiReport(state, data){
            let msg=data;
            if(!msg.group_id||state[msg.group_id]==undefined){
                return
            }
            let list=state[msg.group_id].galleryObj.gallery_list
            for(let index=0;index<list.length;index++){
                if (msg.resource_id==list[index].resource_id) {
                    list.splice(index,1,msg)
                    break;
                }
            }
        },
        updateMessageImageLocalUrl(state,data){
            //更新聊天消息图像本地地址
            let index=data.index;
            let gmsg_id=data.gmsg_id;
            let cid=data.cid;
            let list=state[cid].chatMessageList

            if (gmsg_id) {
                for (let key in list) {
                    let item=list[key];
                    if (item && item.gmsg_id==gmsg_id) {
                        index=key;
                        break;
                    }
                }
            }

            let item=list[index]
            if (item) {
                item.url_local=data.url_local
                list.splice(index,1,item)
            }
        },
        updateMessageAvatarLocalUrl(state,data){
            //更新聊天消息头像本地地址
            let index=data.index;
            let gmsg_id=data.gmsg_id;
            let cid=data.cid;
            let list=state[cid].chatMessageList

            if (gmsg_id) {
                for (let key in list) {
                    let item=list[key];
                    if (item && item.gmsg_id==gmsg_id) {
                        index=key;
                        break;
                    }
                }
            }

            let item=list[index]
            if (item) {
                item.avatar_local=data.avatar_local
                list.splice(index,1,item)
            }
        },
        updateMessageAudioLocalUrl(state,data){
            //更新聊天消息录音文件本地地址
            let index=data.index;
            let cid=data.cid;
            let list=state[cid].chatMessageList
            let item=list[index]
            item.url=data.url
            list.splice(index,1,item)
        },
        updateFileProgress(state,data){
            //更新文件上传进度
            let msg=data.msg;
            let chatMessageList=state[msg.group_id].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]

                if (message.file_id==msg.file_id) {
                    if(data.hasOwnProperty('percent')){
                        message.percent=data.percent
                    }
                    if(data.hasOwnProperty('uploadId')){
                        message.uploadId = data.uploadId
                    }
                    if(data.hasOwnProperty('pauseUpload')){
                        message.pauseUpload = data.pauseUpload
                    }
                    if(data.hasOwnProperty('uploadError')){
                        message.uploadFail = data.uploadError
                    }
                    chatMessageList.splice(i,1,message)
                    break;
                }
            }
        },
        setMessageSending(state,data){
            let chatMessageList=state[data.cid].chatMessageList
            let message=chatMessageList[data.index]
            message.sendingTimer=setTimeout(()=>{
                message.sending=true;
            },500)
            message.uploading=false;
            chatMessageList.splice(data.index,1,message)
        },
        updateUploadFail(state,data){
            //设置文件上传失败
            let chatMessageList=state[data.cid].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.file_id==data.file_id) {
                    message.uploadFail=true;
                    message.uploading=false;
                    chatMessageList.splice(i,1,message)
                    break;
                }
            }
        },
        updateGroupSettingLocalUrl(state,data){
            //更新群设置页会诊文件本地地址
            let index=data.index;
            let cid=data.cid;
            let list=state[cid].galleryObj.gallery_list
            let item=list[index]
            item.url_local=data.url_local
            list.splice(index,1,item)
        },
        updateFriendToAttendeeList(state,data){
            //更新所有会话好友信息
            let key='attendee_'+data.id
            for(let item in state){
                let conversation=state[item]
                if(!conversation){
                    continue
                }
                let friend=conversation.attendeeList[key]
                if (friend) {
                    friend.avatar=data.avatar
                    friend.avatar_local=data.avatar_local
                    friend.state=data.state
                    friend.nickname=data.nickname
                    friend.hospital_id=data.hospital_id
                    Vue.set(conversation.attendeeList,key,friend);
                    //更新聊天消息的头像昵称
                    for(let i=0;i<conversation.chatMessageList.length;i++){
                        let message=conversation.chatMessageList[i]
                        if (message.sender_id==data.id) {
                            message.avatar=data.avatar;
                            message.avatar_local=data.avatar_local;
                            message.nickname=data.nickname;
                            conversation.chatMessageList.splice(i,1,message)
                        }
                    }
                }
            }
        },
        pushMoreGroupFile(state,data){
            //群文件加载更多
            let galleryObj=state[data.cid].galleryObj;
            Object.keys(data).forEach(key=>{
                if(key.indexOf('index')>-1){
                    galleryObj[key] = data[key]
                }else if(key.indexOf('list')>-1){
                    galleryObj[key]=galleryObj[key].concat(data[key])
                }
            })
        },
        updateAttendeeLocalUrl(state,data){
            //更新群成员头像本地地址
            let key='attendee_'+data.uid
            let conversation=state[data.cid]
            let friend=conversation.attendeeList[key]
            if (friend) {
                friend.avatar_local=data.avatar_local
                Vue.set(conversation.attendeeList,key,friend)
            }
        },
        addAttendee(state,data){
            //添加群成员
            let cid=data.groupid;
            let key='attendee_'+data.userid;
            let conversation=state[cid];
            data.realtime_voice_state=0;
            data.is_speek=0;
            if(!conversation){
                return
            }
            Vue.set(conversation.attendeeList,key,data)
        },
        deleteAttendee(state, data){
            //删除群成员
            let cid = data.cid;
            let key='attendee_'+data.uid;
            let conversation=state[cid];
            let attendee=conversation.attendeeList[key];
            if(attendee){
                attendee.attendeeState = 0;
                Vue.set(conversation.attendeeList,key,attendee);
            }
        },
        updateSubjectToConversation(state,data){
            //更新群聊名称
            let cid=data.cid;
            let subject=data.subject;
            Vue.set(state[cid],"subject",subject);
        },
        updateIsPublic(state,data){
            //更新群公开属性
            let cid=data.cid;
            let is_public=data.is_public;
            Vue.set(state[cid],"is_public",is_public);
        },
        updateIsLiveRecord(state,data){
            //更新群公开属性
            let cid=data.cid;
            let record_mode=data.record_mode;
            Vue.set(state[cid],"record_mode", record_mode);
        },
        updateGroupOwnerId(state,data){
            //更新群主
            Vue.set(state[data.cid],"creator_id",data.user_id);
        },
        updataVoiceCtrlMode(state,data){
            //更新语音模式
            let cid=data.cid;
            let voice_ctrl_mode=data.voice_ctrl_mode;
            Vue.set(state[cid],"voice_ctrl_mode", voice_ctrl_mode);
        },
        updateProgressByImgId(state,data){
            //更新文件上传进度
            let percent=data.percent
            let chatMessageList=state[data.cid].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.file_id==data.file_id) {
                    message.percent=percent
                    chatMessageList.splice(i,1,message)
                    break;
                }
            }
        },
        updateFriendToConversationList(state,data){
            for(let key in state){
                let conversation=state[key];
                if(!conversation){
                    continue
                }
                if(conversation&&conversation.is_single_chat&&conversation.fid==data.id){
                    conversation.subject=data.nickname;
                }

                let attendee='attendee_' + data.id;
                let user=conversation.attendeeList[attendee]
                if(user && (user.hospital_id != data.hospital_id)){
                    user.hospital_id  = data.hospital_id;
                }
            }
        },
        updateViewMode(state,data){
            Vue.set(state[data.cid],"view_mode",data.value);
        },
        deleteConversationList(state, data){
            if(state[data.cid]){
                delete state[data.cid];
            }
        },
        updateGalleryRealurlToConversation(state, data){
            let imgObj=data.imgObj;
            let list=state[data.cid].galleryObj.gallery_list
            for(let index=0;index<list.length;index++){
                if (imgObj.resource_id==list[index].resource_id) {
                    let item=list[index]
                    item.realUrl=data.realUrl
                    item.loaded=true;
                    list.splice(index,1,item)
                    break;
                }
            }
        },
        updateGalleryVideoSrcToConversation(state, data){
            let list=state[data.cid]&&state[data.cid].galleryObj.gallery_list
            if(!list || (data.index>list.length-1)){
                return
            }
            let item=list[data.index]
            item.mainVideoSrc=data.mainVideoSrc
            item.gestrueVideoSrc=data.gestrueVideoSrc
            item.mainAudioSrc=data.mainAudioSrc
            list.splice(data.index,1,item)
        },
        clearHistory(state,data){
            state[data.cid].chatMessageList=[]
        },
        deleteChatMessagesByGmsgIdList(state,valObj){
            //删除消息
            let cid=valObj.cid;
            let gmsg_id_list=valObj.gmsg_id_list;
            if (gmsg_id_list && cid && state[cid]){
                let chatMessageList=state[cid].chatMessageList;
                for(var i in gmsg_id_list){
                    let gmsg_id = gmsg_id_list[i];
                    for(let j=chatMessageList.length-1; j>=0; j--){
                        if (gmsg_id==chatMessageList[j].gmsg_id){
                            chatMessageList.splice(j,1);
                            // 如果存在引用消息，并且引用消息也在本次撤回列表中，则一并更新引用消息的状态
                            if (chatMessageList[j].quote_message && gmsg_id_list.includes(chatMessageList[j].quote_message.gmsg_id)) {
                                Vue.set(chatMessageList[j].quote_message, 'been_withdrawn', 1);
                            }
                        }
                        if(chatMessageList[j].quote_message && chatMessageList[j].quote_message.gmsg_id==gmsg_id){
                            Vue.set(chatMessageList[j].quote_message,'been_withdrawn',1);
                        }
                    }
                }
            }
        },
        withDrawChatMessagesByGmsgIdList(state,valObj){
            //撤回消息
            let cid=valObj.cid;
            let gmsg_id_list=valObj.gmsg_id_list;
            if (gmsg_id_list && cid && state[cid]){
                let chatMessageList=state[cid].chatMessageList;
                for(var i in gmsg_id_list){
                    let gmsg_id = gmsg_id_list[i];
                    for(let j=chatMessageList.length-1; j>=0; j--){
                        if (gmsg_id==chatMessageList[j].gmsg_id){
                            if(chatMessageList[j].msg_type === window.vm.$store.state.systemConfig.msg_type.COMMENT){
                                window.vm.$store.commit('gallery/deleteCommentByCommentId',{
                                    resource_id:chatMessageList[j].resource_id,
                                    comment_id:chatMessageList[j].comment_id
                                });
                            }
                            Vue.set(chatMessageList[j],'msg_type',20);
                            Vue.set(chatMessageList[j],'been_withdrawn',2);
                            // 如果存在引用消息，并且引用消息也在本次撤回列表中，则一并更新引用消息的状态
                            if (chatMessageList[j].quote_message && gmsg_id_list.includes(chatMessageList[j].quote_message.gmsg_id)) {
                                Vue.set(chatMessageList[j].quote_message, 'been_withdrawn', 2);
                            }
                            window.vm.$store.commit('chatList/setLastMessage',{cid:valObj.cid,message:chatMessageList[j]});
                            // window.mainDB.conversationMessageList.put(chatMessageList[j])
                        }
                        if(chatMessageList[j].quote_message && chatMessageList[j].quote_message.gmsg_id==gmsg_id){
                            Vue.set(chatMessageList[j].quote_message,'been_withdrawn',2);
                        }

                    }
                }
            }
        },
        updateExpirationResourceDataByChatMessages(state,data){
            let cid=data.cid;
            let resource_id=data.resource_id;
            if(resource_id && cid && state[cid]){
                let chatMessageList=state[cid].chatMessageList;
                for(let i=chatMessageList.length-1; i>=0; i--){
                    if(resource_id === chatMessageList[i].resource_id){
                        if(chatMessageList[i].been_withdrawn!=1&&chatMessageList[i].been_withdrawn!=2){
                            console.log(chatMessageList[i])
                            Vue.set(chatMessageList[i],'msg_type',window.vm.$store.state.systemConfig.msg_type.EXPIRATION_RES)
                        }

                    }
                }
            }

        },
        deleteFileToConversation(state,data){
            let cid=data.cid;
            let resource_id=data.resource_id;
            if(resource_id && cid && state[cid]){
                let gallery_list=state[cid].galleryObj.gallery_list;
                // for(let i=gallery_list.length-1; i>=0; i--){
                //     if(resource_id==gallery_list[i].resource_id){
                //         gallery_list.splice(i,1);
                //     }
                // }
                let resourceIdList = []
                gallery_list.forEach(item=>{
                    if(!resourceIdList.includes(item.resource_id)){
                        if(item.resource_id !== null){
                            resourceIdList.push(item.resource_id)
                        }
                    }
                })
                const msgType = window.vm.$store.state.systemConfig.msg_type
                const resourceMsgTypeList = [msgType.Image,msgType.Video,msgType.COMMENT,msgType.TAG,msgType.Frame,msgType.EXAM_IMAGES]
                let chatMessageList=state[cid].chatMessageList;
                for(let i=chatMessageList.length-1; i>=0; i--){
                    let message=chatMessageList[i]
                    if (message.msg_type==msgType.EXAM_IMAGES) {
                        //判断聚合消息图像列表是否包含删除的图片,是则触发重新刷新
                        let resourceList=message.resourceList;
                        for(let index=0;index<resourceList.length;index++){
                            if (resource_id==resourceList[index].id) {
                                Vue.set(message,'imageList',null)
                                break;
                            }
                        }
                    }else if(message.hasOwnProperty('resource_id')&&!resourceIdList.includes(message.resource_id)&&resourceMsgTypeList.includes(message.msg_type)){
                        if(message.been_withdrawn!=1&&message.been_withdrawn!=2){
                            Vue.set(message,'msg_type',msgType.EXPIRATION_RES)
                            // window.mainDB.conversationMessageList.put(chatMessageList[i])
                        }
                    }
                }
            }
        },
        updateAttendeeVideoStatus(state,data){
            //更新群成员头像本地地址
            let key='attendee_'+data.uid
            let conversation=state[data.cid]
            let friend=conversation.attendeeList[key]
            if (friend) {
                friend.video_status=data.video_status
                Vue.set(conversation.attendeeList,key,friend)
            }
        },
        updateAttendeeState(state, data){
            //更新群成员
            let cid = data.cid;
            let key='attendee_'+data.uid;
            let attendeeState = data.attendeeState
            let conversation=state[cid];
            let attendee=conversation.attendeeList[key];
            if(attendee){
                attendee.attendeeState = attendeeState;
                Vue.set(conversation.attendeeList,key,attendee);
            }
        },
        updateAttendeeRole(state, data){
            let cid = data.cid;
            let conversation=state[cid];
            for(let key in data.attendeeList){
                let attendee=conversation.attendeeList[key];
                if(attendee){
                    attendee.role = data.attendeeList[key].role;
                    Vue.set(conversation.attendeeList,key,attendee);
                }
            }
        },
        updateAttendeeAliasName(state, data){
            let cid = data.cid;
            let conversation=state[cid];
            let key='attendee_'+data.uid;

            let attendee=conversation.attendeeList[key];
            if(attendee){
                attendee.alias_name = data.aliasName
                Vue.set(conversation.attendeeList,key,attendee);
            }
        },
        updateAiAnalyzeReport(state, data){
            //更新AI分析结果
            let chatMessageList=state[data.cid].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=cloneDeep(chatMessageList[i])
                let ai_analyze_ids = (message.ai_analyze_list||[]).reduce((h,v)=>{
                    h.push(v.id);return h;
                },[])
                if (message.ai_analyze_id==data.ai_analyze_id||ai_analyze_ids.indexOf(data.ai_analyze_id)>=0) {
                    message.ai_analyze.report=data.report;
                    message.ai_analyze.status = 1
                    message.loaded=false;//可以从新加载图片，描绘描迹
                    message.ai_analyze_list = message.ai_analyze_list||[]
                    if(ai_analyze_ids.indexOf(data.ai_analyze_id)>=0){
                        let ai_analyze_list = (message.ai_analyze_list).reduce((h,v)=>{
                            if(data.ai_analyze_id==v.id){
                                v=cloneDeep({...v,...data,status:1,id:data.ai_analyze_id})
                            }
                            h.push(v);
                            return h;
                        },[])
                        message.ai_analyze_list = ai_analyze_list
                    }else{
                        message.ai_analyze_list = cloneDeep([...message.ai_analyze_list,{...data,status:1,id:data.ai_analyze_id}])
                    }
                    message.ai_analyze_list  = cloneDeep(uniqBy(message.ai_analyze_list, 'id'))
                    message.ai_analyze_report={...data,...data.report}
                    chatMessageList.splice(i,1,message)
                    break;
                }
            }
        },
        updateAnnounce(state,data){
            //更新群说明
            let cid=data.cid;
            Vue.set(state[cid],"announcement",data);
        },
        updateStartVoiceMsg(state,data){
            if(Number(data.cid)===0||!data.cid){
                return
            }
            var chatMessageList=state[data.cid].chatMessageList
            for (var i = chatMessageList.length - 1; i >= 0; i--) {
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.sending) {
                    message.sending=false
                    chatMessageList.splice(i,1,message)
                    break;
                }
            }
        },
        deleteStartVoiceMsg(state,data){
            var chatMessageList=state[data.cid].chatMessageList
            for (var i = chatMessageList.length - 1; i >= 0; i--) {
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.sending) {
                    chatMessageList.splice(i,1)
                    break;
                }
            }
        },
        addConferencePlan(state, data){
            let cid=data.group_id;
            let conference_id = data.conference_id;
            Vue.set(state[cid].conferencePlanList, conference_id, data)
        },
        delConferencePlan(state, data){
            let cid=data.group_id;
            let conference_id = data.conference_id;
            let conferencePlanList = state[cid].conferencePlanList;
            if(conferencePlanList){
                // delete conferencePlanList[conference_id];
                Vue.delete(state[cid].conferencePlanList,conference_id)
            }
        },
        updateChatMessage(state,data){
            let msg=data;
            let chatMessageList=state[msg.group_id].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.gmsg_id==msg.gmsg_id) {
                    for(let key in msg){
                        message[key]=msg[key]
                        if(key=='imageList'){
                            let finshed_ai_analyze_ids = (message.ai_analyze_list||[]).reduce((h,v)=>{
                                if(v.status&&v.id){
                                    h.push(v.id);return h;
                                }else{
                                    return h
                                }
                            },[])
                            let ai_analyze_list = (data.key||[]).reduce((h,v)=>{
                                if(v.ai_analyze_report){
                                    if(finshed_ai_analyze_ids.indexOf(v.ai_analyze_report.ai_analyze_id)>=0){
                                        h.push({...v.ai_analyze_report,id:v.ai_analyze_report.ai_analyze_id,status:1})
                                    }else{
                                        h.push({...v.ai_analyze_report,id:v.ai_analyze_report.ai_analyze_id})
                                    }
                                }
                                return h;
                            },message.ai_analyze_list)
                            // console.error(JSON.stringify(ai_analyze_list.reduce((h,v)=>{h.push({id:v.id,status:v.status});return h;},[])))
                            ai_analyze_list  = uniqBy(ai_analyze_list, 'id')
                            message.ai_analyze_list=ai_analyze_list
                        }
                    }
                    chatMessageList.splice(i,1,message)
                    break;
                }
            }
        },
        updateConversationImage(state,data){
            let msg=data;
            let galleryList=state[msg.group_id].galleryObj.gallery_list
            for(let i=galleryList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=galleryList[i]
                if (message.gmsg_id==msg.gmsg_id) {
                    for(let key in msg){
                        message[key]=msg[key]
                    }
                    galleryList.splice(i,1,message)
                    break;
                }
            }
        },
        updateConversationAvatar(state, data){
            const cid = data.cid
            const conversation = state[cid]
            if(conversation){
                for(const key in data) {
                    conversation[key] = data[key]
                }
            }
        },
        updateMuteToConversation(state,data){
            let preferences=state[data.cid]&&state[data.cid].preferences;
            if (preferences) {
                preferences.is_mute=data.is_mute;
                Vue.set(state[data.cid],'preferences',preferences)
            }
        },
        updateResourceDes(state,data){
            let gallery_list=state[data.cid].galleryObj.gallery_list;
            for(let i=0;i<gallery_list.length;i++){
                let item=gallery_list[i]
                if(data.resource_id == item.resource_id){
                    item.des = data.des;
                    //强制视图更新
                    gallery_list.splice(i,1,item)
                    break;
                }
            }

            let gallery_list_2=state[data.cid].chatMessageList;
            for(let i=0;i<gallery_list_2.length;i++){
                let item=gallery_list_2[i]
                if(data.resource_id == item.resource_id){
                    item.des = data.des;
                    //强制视图更新
                    gallery_list_2.splice(i,1,item)
                    break;
                }
            }
        },
        updateDeviceList(state,data){
            if(data&&data.list){
                let conversation=state[data.cid]
                if(conversation){
                    conversation['device_list']=data.list;
                }

            }
        },
        updateMulticenterList(state,data){
            if(data){
                let conversation=state[data.cid]
                if(conversation){
                    conversation['multicenter_list'] = []
                    let optionList = window.vm.$store.state.multicenter.optionList||{}
                    for(let k in data.list){
                        let v = data.list[k]
                        v.optionList = (v.optionList||[]).reduce((h,option)=>{
                            let value = optionList[option.id] ||{}
                            h.push(value)
                            return h
                        },[])[0]
                        conversation['multicenter_list'].push(v)
                    }
                    this.commit('conversationList/updataIsObQCMulticenter', data.cid)
                }
            }
        },
        updataIsObQCMulticenter(state,cid){
            let types = window.vm.$store.state.multicenter.type
            let conversation=state[cid]
            let multicenter_type = ''
            let multicenter_info = null
            let user_service_types = Object.values(window.vm.$store.state.systemConfig.user_service_type)
            if(conversation && conversation.multicenter_list && conversation.attendeeList){
                for(let v of (conversation.multicenter_list||[])){
                    if(types[v.type]){//'obstetric_qc_multicenter'
                        multicenter_type = types[v.type];
                        multicenter_info = v;
                        let role = 0;
                        const temp = (multicenter_info.memberList||[]).map(u=>{
                            if(u.uid == window.vm.$store.state.user.id){
                                role = u.role
                            }
                        })
                        multicenter_info.userInfo={
                            role:role,
                            uid: window.vm.$store.state.user.id,
                            mc_id: v.id,
                            status:1,
                        }
                        if(conversation.is_single_chat){
                            for(let key in conversation.attendeeList){
                                let user = conversation.attendeeList[key]
                                if( user_service_types.indexOf(user.service_type )>-1){
                                    multicenter_type = ''
                                    multicenter_info = null
                                }
                            }
                        }
                    }
                }
            }
            if(multicenter_type){
                conversation['multicenter_type']=multicenter_type
                let list = window.vm.$store.state.multicenter.list||[]
                if(list.length){
                    for(let k in list){
                        if(list[k].id == multicenter_info.id){
                            multicenter_info.userInfo = list[k].userInfo
                        }
                    }
                }
                conversation['multicenter_info']=multicenter_info
            }else{
                conversation['multicenter_info']=null
                conversation['multicenter_type']=''
            }
        },
        addDeviceToConversation(state,data){
            let conversation=state[data.cid]
            if (conversation&&conversation.device_list) {
                if(!conversation.device_list.find(item=>(item.device_id===data.device.device_id))){
                    conversation.device_list.push(data.device)
                }
            }
        },
        updateChatMessageLiveRecordData(state,data){
            let msg=data;
            if(!state[msg.group_id]){
                return
            }
            let chatMessageList=state[msg.group_id].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.resource_id==msg.resource_id) {
                    Vue.set(state[msg.group_id].chatMessageList[i],'live_record_data',data.live_record_data)
                    Vue.set(state[msg.group_id].chatMessageList[i],'coverUrl',data.coverUrl)
                    break;
                }
            }

            let gallery_list=state[msg.group_id].galleryObj.gallery_list;
            for(let i=0;i<gallery_list.length;i++){
                let item=gallery_list[i]
                if(data.resource_id == item.resource_id){
                    for(let key in msg){
                        item[key]=msg[key]
                    }
                    //强制视图更新
                    gallery_list.splice(i,1,item)
                    break;
                }
            }
            let video_list=state[msg.group_id].galleryObj.video_list; //视频文件
            for(let i=0;i<video_list.length;i++){
                let item=video_list[i]
                if(data.resource_id == item.resource_id){
                    for(let key in msg){
                        item[key]=msg[key]
                    }
                    //强制视图更新
                    video_list.splice(i,1,item)
                    break;
                }
            }
        },
        setFavoriteStatusToConversation(state,data){
            if (!state[data.img.cid]) {
                return ;
            }
            let gallery_list=state[data.img.cid].galleryObj.gallery_list;
            for(let item of gallery_list){
                if (item.resource_id==data.img.resource_id) {
                    window.vm.$set(item,'userFavoriteStatus',data.status)
                    break;
                }
            }
        },
        updateConversation(state,data){
            let cid=data.cid;
            Vue.set(state[cid],data.key, data.value);
        },
        updateMessageListIsLoaded(state,{is_loaded_history_list,cid}){
            if(cid&&state[cid]){
                Vue.set(state[cid],"is_loaded_history_list",is_loaded_history_list);
            }
        },
        updateMessageListNeedReload(state,{is_need_reload,cid}){
            if(cid&&state[cid]){
                Vue.set(state[cid],"is_need_reload",is_need_reload);
            }
        }
    },
    actions: {},
    getters: {}
}
