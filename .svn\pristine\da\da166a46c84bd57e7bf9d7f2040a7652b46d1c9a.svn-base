/////////////////////////////////////////// Molbile移动端交互业务 ////////////////////////////////////////


/////////////////////////////////////////// Molbile移动端交互业务 ////////////////////////////////////////

// function rCMobileBridge(CWorkstationCommunicationMng){

//     const functionNames = [
//         'setStatusBarBackground',// 设置移动端状态栏背景颜色; 参数：JS->APP {color：xxx}
//         'getOSInformation',//获得操作系统信息 JS->APP 参数：{}
//         'NotifyOSInformation',//app返回操作系统信息 app-->js 返回：json {data:{lange:xxx, os:xxxx, version：xxx}, error_code:0, error_message:'success'}
//         'convertLocalFileSystemURL',//将本地URL路径转换成平台绝对路径  JS->APP  参数：{url: xxxxx}
//         'NotifyConvertedLocalFileSystemURL',//将平台绝对路径通知JS  APP->JS 参数：{url: xxxxx}
//         'resolveLocalFileSystemURL',//判断文件是否存在  JS->APP  参数：{url: xxxxx}
//         'NotifyResolvedLocalFileSystemURL',//断文件是否存在结果通知js  APP->JS 参数：{error_code: '数字'}0：表示成功
//         'openFile',//调用第三方程序打开指定的文件
//         'NotifyOpenFile',//调用第三方程序打开指定的文件结果通知JS  APP->JS 参数：{error_code: '数字', error_message:''}0：表示成功,1:表示失败
//         'isApplicationExist',//查询APP是否存在  JS->APP
//         'NotifyIsApplicationExist',//查询APP是否存在结果通知JS  APP->JS 参数：{error_code: '数字'} 0：表示存在,1:表示不存在
//         'unlockOrientation',//解除锁定屏幕方向  JS->APP 参数：{}
//         'lockOrientation',//锁定屏幕方向  JS->APP 参数：
//         'getModel',//获得设备型号 JS->APP  参数：{}
//         'NotifyGetModel',//获得设备型号结果通知JS
//         'setFullscreen',//设置应用是否全屏显示 JS->APP  参数：{isFull: true} //false
//         'saveFileToLocalWithBase64',//存储文件到app  JS->APP
//         'NotifySaveFileToLocalWithBase64',//存储文件到app结果通知JS
//         'getLocalFileWithBase64',//获得文件的Base64编码字符串
//         'NotifyGetLocalFileWithBase64',//APP通知js的base64字符串 APP->JA  参
//         'deviceVibrate',//js通知APP的震动指定时间 JS->APP
//         'gallerySave',//Js通知App保存文件到相册
//         'NotifyGallerySave',//App通知JS保存文件到相册是否成功
//         'createDownload',//s通知App下载文件到本地  JS->APP
//         'NotifyCreateDownload',//App通知JS下载文件是否成功  APP->JS
//         'NotifyCreateDownloadProgress',////App通知JS下载文件是否成功
//         'shareGetService',//Js通知App获取终端支持分享的APP
//         'NotifyShareGetService',//App通知JS支持分享的APP
//         'nativeActionSheet',///Js通知App弹出系统选择按钮框
//         'NotifyNativeActionSheet',//App通知JS弹出系统选择按钮框时，用户点击的那个按钮
//         'setFlash',//Js通知App 打开/关闭手机摄像头闪光灯 J
//         'NotifySetFlash',//App通知JS开启或关闭闪光灯是否成功  APP->JS
//         'scanQRCode',//Js通知App 进行扫码识别
//         'NotifyScanQRCode',//App通知JS 扫码结果
//         'shareLinkToWechat',//Js通知App 微信链接分析
//         'NotifyShareLinkToWechat',//App通知JS 扫码结果

//     ]
//     // forEach
//     // 获得操作系统信息 JS->APP 参数：{}
//     CWorkstationCommunicationMng.getOSInformation = function (params) {
//         console.info('CWorkstationCommunicationMng.getOSInformation', params)
//         CWorkstationCommunicationMng.query('getOSInformation:' + JSON.stringify(params))
//     }
//     //app返回操作系统信息 app-->js 返回：json {data:{lange:xxx, os:xxxx, version：xxx}, error_code:0, error_message:'success'}
//     CWorkstationCommunicationMng.NotifyOSInformation = function(json_str){
//         let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
//         console.info('CWorkstationCommunicationMng.NotifyOSInformation', json)
//         requestManager.handleResponse('NotifyOSInformation', json)
//     }
// }


import moment from 'moment';
import Tool from '@/common/tool'
import requestManager from '@/common/CommunicationMng/requestManager';
let confirm_back_tag = false

export default function CMobileBridge(CWorkstationCommunicationMng){
    CWorkstationCommunicationMng.CLiveConferenceBridgeErrorKey= function () {
        return {
            UlinkerError:{
                'HTTP_URL_IS_CONTRADICT':'HTTP_URL_IS_CONTRADICT',//地址错误与移动端不一致
            },
            CommonError:{
                'OPEN_CONSERVATION_FAIL_ERROR':'OPEN_CONSERVATION_FAIL_ERROR',//会话打开失败
                'NET_WORK_ERROR':'NET_WORK_ERROR',//会话打开失败
                'MAIN_SCREEN_SOCKET_DISCONNECT_ERROR':'MAIN_SCREEN_SOCKET_DISCONNECT_ERROR',//主控制器断开
                'LOGIN_TOKEN_IS_INVALID_ERROR':'LOGIN_TOKEN_IS_INVALID_ERROR',//token无效
                'LOGIN_FAIL_ERROR':'LOGIN_FAIL_ERROR',//shib
                'LIVE_ROOM_IS_INVALID_ERROR':'LIVE_ROOM_IS_INVALID_ERROR',//直播间不存在
                'JOIN_LIVE_ROOM_FAIL_ERROR':'JOIN_LIVE_ROOM_FAIL_ERROR',//加入房间失败
                'PUSH_MAIN_STREAM_FAIL_NO_MAIN_CHANNEL_IN_USE_ERROR':'PUSH_MAIN_STREAM_FAIL_NO_MAIN_CHANNEL_IN_USE_ERROR',//其他人推流中
                'UNKNOWN_ERROR':'UNKNOWN_ERROR',//地token无效
                'ALREADY_JOINED_ANOTHER_CHANNEL':'ALREADY_JOINED_ANOTHER_CHANNEL'
            }
        }
    }
    // 设置移动端状态栏背景颜色; 参数：JS->APP {color：xxx}
    CWorkstationCommunicationMng.setStatusBarBackground = function (params) {
        console.info('CWorkstationCommunicationMng.setStatusBarBackground', params)
        CWorkstationCommunicationMng.query('setStatusBarBackground:' + JSON.stringify(params))
    }

    // 将本地URL路径转换成平台绝对路径  JS->APP  参数：{url: xxxxx}
    CWorkstationCommunicationMng.convertLocalFileSystemURL = function (params) {
        console.info('CWorkstationCommunicationMng.convertLocalFileSystemURL', params)
        CWorkstationCommunicationMng.query('convertLocalFileSystemURL:' + JSON.stringify(params))
    }
    // 将平台绝对路径通知JS  APP->JS 参数：{url: xxxxx}
    CWorkstationCommunicationMng.NotifyConvertedLocalFileSystemURL = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyConvertedLocalFileSystemURL', json)
        requestManager.handleResponse('NotifyConvertedLocalFileSystemURL', json)
    }

    // 判断文件是否存在  JS->APP  参数：{url: xxxxx}
    CWorkstationCommunicationMng.resolveLocalFileSystemURL = function (params) {
        console.info('CWorkstationCommunicationMng.resolveLocalFileSystemURL', params)
        CWorkstationCommunicationMng.query('resolveLocalFileSystemURL:' + JSON.stringify(params))
    }
    // 判断文件是否存在结果通知js  APP->JS 参数：{error_code: '数字'}0：表示成功
    CWorkstationCommunicationMng.NotifyResolvedLocalFileSystemURL = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyResolvedLocalFileSystemURL', json)
        requestManager.handleResponse('NotifyResolvedLocalFileSystemURL', json)
    }

    // 调用第三方程序打开指定的文件   JS->APP  参数：{url: xxxxx,options:{}}
    CWorkstationCommunicationMng.openFile = function (params) {
        console.info('CWorkstationCommunicationMng.openFile', params)
        CWorkstationCommunicationMng.query('openFile:' + JSON.stringify(params))
    }
    // 调用第三方程序打开指定的文件结果通知JS  APP->JS 参数：{error_code: '数字', error_message:''}0：表示成功,1:表示失败
    CWorkstationCommunicationMng.NotifyOpenFile = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyOpenFile', json)
        requestManager.handleResponse('NotifyOpenFile', json)
    }

    // 查询APP是否存在  JS->APP  参数：{pname:'com.tencent.mm',action:'weixin://'}
    CWorkstationCommunicationMng.isApplicationExist = function (params) {
        console.info('CWorkstationCommunicationMng.isApplicationExist', params)
        CWorkstationCommunicationMng.query('isApplicationExist:' + JSON.stringify(params))
    }
    // 查询APP是否存在结果通知JS  APP->JS 参数：{error_code: '数字'} 0：表示存在,1:表示不存在
    CWorkstationCommunicationMng.NotifyIsApplicationExist = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyIsApplicationExist', json)
        requestManager.handleResponse('NotifyIsApplicationExist', json)
    }

    // 解除锁定屏幕方向  JS->APP 参数：{}
    CWorkstationCommunicationMng.unlockOrientation = function (params) {
        console.info('CWorkstationCommunicationMng.unlockOrientation', params)
        CWorkstationCommunicationMng.query('unlockOrientation:' + JSON.stringify(params))
    }
    // 锁定屏幕方向  JS->APP 参数：{action:'portrait-primary'}....
    CWorkstationCommunicationMng.lockOrientation = function (params) {
        console.info('CWorkstationCommunicationMng.lockOrientation', params)
        CWorkstationCommunicationMng.query('lockOrientation:' + JSON.stringify(params))
    }

    //获得设备型号 JS->APP  参数：{}
    CWorkstationCommunicationMng.getModel = function (params) {
        console.info('CWorkstationCommunicationMng.getModel', params)
        CWorkstationCommunicationMng.query('getModel:' + JSON.stringify(params))
    }
    //获得设备型号结果通知JS  APP->JS 参数：{name: ''}
    CWorkstationCommunicationMng.NotifyGetModel = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyGetModel', json)
        requestManager.handleResponse('NotifyGetModel', json)
    }

    //设置应用是否全屏显示 JS->APP  参数：{isFull: true} //false
    CWorkstationCommunicationMng.setFullscreen = function (params) {
        console.info('CWorkstationCommunicationMng.setFullscreen', params)
        CWorkstationCommunicationMng.query('setFullscreen:' + JSON.stringify(params))
    }

    //存储文件到app  JS->APP  参数：{Base64: '',name:'',path:''} ;path为相对路径
    CWorkstationCommunicationMng.saveFileToLocalWithBase64 = function (params) {
        console.info('CWorkstationCommunicationMng.saveFileToLocalWithBase64')
        CWorkstationCommunicationMng.query('saveFileToLocalWithBase64:' + JSON.stringify(params))
    }
    //存储文件到app结果通知JS 参数：{error_code: '数字', error_message:''}
    CWorkstationCommunicationMng.NotifySaveFileToLocalWithBase64 = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifySaveFileToLocalWithBase64', json)
        requestManager.handleResponse('NotifySaveFileToLocalWithBase64', json)
    }

    //获得文件的Base64编码字符串  JS->APP  参数：{url:''};
    CWorkstationCommunicationMng.getLocalFileWithBase64 = function (params) {
        console.info('CWorkstationCommunicationMng.getLocalFileWithBase64', params)
        CWorkstationCommunicationMng.query('getLocalFileWithBase64:' + JSON.stringify(params))
    }
    //APP通知js的base64字符串 APP->JA  参数：{base64_data: '',error_code: '数字', error_message:''}
    CWorkstationCommunicationMng.NotifyGetLocalFileWithBase64 = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyGetLocalFileWithBase64', json)
        requestManager.handleResponse('NotifyGetLocalFileWithBase64', json)
    }

    //获得手机型号  JS->APP  参数：{};
    CWorkstationCommunicationMng.getModel = function (params) {
        console.info('CWorkstationCommunicationMng.getModel', params)
        CWorkstationCommunicationMng.query('getModel:' + JSON.stringify(params))
    }
    //APP通知js的base64字符串 APP->JA  参数：{base64_data: '',error_code: '数字', error_message:''}
    CWorkstationCommunicationMng.NotifyGetModel = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyGetModel', json)
        requestManager.handleResponse('NotifyGetModel', json)
    }

    //js通知APP的震动指定时间 JS->APP  参数：{duration:''}指定震动时间
    CWorkstationCommunicationMng.deviceVibrate = function (params) {
        console.info('CWorkstationCommunicationMng.deviceVibrate', params)
        CWorkstationCommunicationMng.query('deviceVibrate:' + JSON.stringify(params))
    }

    //Js通知App保存文件到相册  JS->APP  参数：{url:''};
    CWorkstationCommunicationMng.gallerySave = function (params) {
        console.info('CWorkstationCommunicationMng.gallerySave', params)
        CWorkstationCommunicationMng.query('gallerySave:' + JSON.stringify(params))
    }
    //App通知JS保存文件到相册是否成功  APP->JS  参数：{url:'',error_code: '数字', error_message:''};
    CWorkstationCommunicationMng.NotifyGallerySave = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyGallerySave', json)
        requestManager.handleResponse('NotifyGallerySave', json)
    }

    //Js通知App下载文件到本地  JS->APP  参数：{url:''，options：{filename:'',timeout:'',retry:'',retryInterval:''}};
    CWorkstationCommunicationMng.createDownload = function (params) {
        console.info('CWorkstationCommunicationMng.createDownload', params)
        CWorkstationCommunicationMng.query('createDownload:' + JSON.stringify(params))
    }

    //App通知JS下载文件是否成功  APP->JS  参数：{url:'',error_code: '数字', error_message:''};
    CWorkstationCommunicationMng.NotifyCreateDownload = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyCreateDownload', json)
        requestManager.handleResponse('NotifyCreateDownload', json)
    }

    //App通知JS下载文件是否成功  APP->JS  参数：{url:'',error_code: '数字', error_message:''};
    CWorkstationCommunicationMng.NotifyCreateDownloadProgress = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyCreateDownloadProgress', json)
        requestManager.handleResponse('NotifyCreateDownloadProgress', json)
    }

    //Js通知App获取终端支持分享的APP  JS->APP  参数：{};
    CWorkstationCommunicationMng.shareGetService = function (params) {
        console.info('CWorkstationCommunicationMng.shareGetService', params)
        CWorkstationCommunicationMng.query('shareGetService:' + JSON.stringify(params))
    }
    //App通知JS支持分享的APP  APP->JS  参数
    // {
    //     list: [{
    //         list：'获得的支持的应用列表',
    //         id: '分享服务标识',
    //         description: '分享服务描述',
    //         authenticated: '是否授权认证',
    //         accessToken: '授权认证信息',
    //         nativeClient: '是否存在对应的分享客户端'
    //     }],
    //     error_code: '数字',
    //     error_message:''
    // }
    CWorkstationCommunicationMng.NotifyShareGetService = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyShareGetService', json)
        requestManager.handleResponse('NotifyShareGetService', json)
    }


    //Js通知App弹出系统选择按钮框 JS->APP  参数：
    // {
    //     title:'标题内容',
    //     cancel:'取消按钮文本内容'，
    //     buttons:[
    //         {title:'按钮文本内容'},
    //         {title:'按钮文本内容'}
    //     ]
    // }
    CWorkstationCommunicationMng.nativeActionSheet = function (params) {
        console.info('CWorkstationCommunicationMng.nativeActionSheet', params)
        CWorkstationCommunicationMng.query('nativeActionSheet:' + JSON.stringify(params))
    }

    //App通知JS弹出系统选择按钮框时，用户点击的那个按钮  APP->JS  参数：{index: '',error_code: '数字', error_message:''}
    CWorkstationCommunicationMng.NotifyNativeActionSheet = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyNativeActionSheet', json)
        requestManager.handleResponse('NotifyNativeActionSheet', json)
    }

    //Js通知App 打开/关闭手机摄像头闪光灯 JS->APP  参数：{status: true/false}
    CWorkstationCommunicationMng.setFlash = function (params) {
        console.info('CWorkstationCommunicationMng.setFlash', params)
        CWorkstationCommunicationMng.query('setFlash:' + JSON.stringify(params))
    }

    //App通知JS开启或关闭闪光灯是否成功  APP->JS  参数：{error_code: '数字', error_message:''}
    CWorkstationCommunicationMng.NotifySetFlash = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifySetFlash', json)
        requestManager.handleResponse('NotifySetFlash', json)
    }

    //Js通知App 进行扫码识别
    CWorkstationCommunicationMng.scanQRCode = function (params) {
        console.info('CWorkstationCommunicationMng.scanQRCode', params)
        CWorkstationCommunicationMng.query('scanQRCode:' + JSON.stringify(params))
    }

    //App通知JS 扫码结果
    CWorkstationCommunicationMng.NotifyScanQRCode = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyScanQRCode', json)
        requestManager.handleResponse('NotifyScanQRCode', json)
    }

    //Js通知App 微信链接分析
    CWorkstationCommunicationMng.shareLinkToWechat = function (params) {
        console.info('CWorkstationCommunicationMng.shareLinkToWechat', params)
        CWorkstationCommunicationMng.query('shareLinkToWechat:' + JSON.stringify(params))
    }

    //App通知JS 扫码结果
    CWorkstationCommunicationMng.NotifyShareLinkToWechat = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyShareLinkToWechat', json)
        requestManager.handleResponse('NotifyShareLinkToWechat', json)
    }

    //Js通知App 压缩文件
    CWorkstationCommunicationMng.compressImage = function (params) {
        console.info('CWorkstationCommunicationMng.compressImage', params)
        CWorkstationCommunicationMng.query('compressImage:' + JSON.stringify(params))
    }


    //App通知JS 压缩结果
    CWorkstationCommunicationMng.NotifyCompressImage = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyCompressImage', json)
        requestManager.handleResponse('NotifyCompressImage', json)
    }

    //Js通知App 压缩文件
    CWorkstationCommunicationMng.openCameraOrPhotoAlbum = function (params) {
        console.info('CWorkstationCommunicationMng.openCameraOrPhotoAlbum', params)
        CWorkstationCommunicationMng.query('openCameraOrPhotoAlbum:' + JSON.stringify(params))
    }


    //App通知JS 压缩结果
    CWorkstationCommunicationMng.NotifyOpenCameraOrPhotoAlbum = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyOpenCameraOrPhotoAlbum')
        requestManager.handleResponse('NotifyOpenCameraOrPhotoAlbum', json)
    }
    //Js通知App 控制登录失败
    CWorkstationCommunicationMng.ULinkerConnectionResult = function (params) {
        CWorkstationCommunicationMng.query('ULinkerConnectionResult:' + JSON.stringify(params))
    }
    //App通知JS 登录
    CWorkstationCommunicationMng.NotifyCloudLoginOrLogout = function (json_str) {
        console.info('CWorkstationCommunicationMng.NotifyCloudLoginOrLogout', json_str)
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str;
        if (json.action === 'Login') {
            if (json.cloudUrl.replace(/\/$/,'')=== window.location.origin.replace(/\/$/,'')) {
                requestManager.handleResponse('NotifyCloudLoginOrLogout', json);
            } else {
                CWorkstationCommunicationMng.ULinkerConnectionResult({ errorcode:1 ,key: CWorkstationCommunicationMng.CLiveConferenceBridgeErrorKey().UlinkerError.HTTP_URL_IS_CONTRADICT});
            }
        } else if (json.action === 'Logout') {
            requestManager.handleResponse('logout');
            window.vm.$router.replace(`/login`);

        }
    }
    //App通知JS 停止或者发起直播
    // Ulinker（带以下四个参数）调用前端接口，开始/结束直播
    // ★ConferenceID = "conferenceid12345";（云++直播群的id）
    // ★mainstreamType="Doppler";
    // ★auxstreamType="Camera";
    // ★action="start";(开始start/离开leave/退出quit)
    CWorkstationCommunicationMng.NotifyStartOrStopConference = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyStartOrStopConference', json)
        requestManager.handleResponse('operatePushStreamByULinker', json)
    }
    //Js通知App 停止或者发起直播的结果
    CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult = function (params) {
        CWorkstationCommunicationMng.query('NotifyStartOrStopConferenceResult:' + JSON.stringify(params))
    }
    //Js通知App get default server
    CWorkstationCommunicationMng.getDefaultServer = function (params) {
        console.info('CWorkstationCommunicationMng.getDefaultServer', params)
        CWorkstationCommunicationMng.query('getDefaultServer:' + JSON.stringify(params))
    }


    //App通知JS get default server
    CWorkstationCommunicationMng.NotifyGetDefaultServer = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyGetDefaultServer', json)
        requestManager.handleResponse('NotifyGetDefaultServer', json)
    }
    //Js通知App 写日志文件
    CWorkstationCommunicationMng.WriteLog = function (data) {
        try {
            CWorkstationCommunicationMng.query('WriteLog:' + Tool.safeStringify(data))
        } catch (error) {
            console.log(error)
        }
    }
    //Js通知App 上传日志文件
    CWorkstationCommunicationMng.uploadLogFile = function () {
        CWorkstationCommunicationMng.query('uploadFile:' + JSON.stringify({}))
    }
    //设置webview地址 : mobile
    CWorkstationCommunicationMng.loadWebview = function(json){
        this.query("loadWebview:" + JSON.stringify(json));
    }
    //设置webview地址 : mobile
    CWorkstationCommunicationMng.reloadWebview = function(json){
        this.query("reloadWebview:" + JSON.stringify(json));
    }

    //询问APP设备参数 js-->app: mobile
    CWorkstationCommunicationMng.getMobileInfo = function(json){
        this.query("getMobileInfo:" + JSON.stringify(json));
    }
    //APP告知设备参数 : mobile
    CWorkstationCommunicationMng.notifySetMobileInfo = function(json){
        console.log('notifySetMobileInfo',json)
        window.vm.$store.commit('device/updateDeviceInfo', {
            isUltraSoundMobile: json.isUltraSoundMobile===true,
            device_id: json.device_id,
            isTEAir:json.isTEAir===true
        })
        requestManager.handleResponse('notifySetMobileInfo',json)
    }
    //Js通知App 设置屏幕亮度
    CWorkstationCommunicationMng.setBrightness = function(json){
        this.query("setBrightness:" + JSON.stringify(json));
    }
    //Js通知App 获取当前屏幕亮度
    CWorkstationCommunicationMng.getBrightness = function () {
        CWorkstationCommunicationMng.query('getBrightness:' + JSON.stringify({}))
    }
    //App通知JS get default server
    CWorkstationCommunicationMng.NotifyGetBrightness = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyGetBrightness', json)
        requestManager.handleResponse('NotifyGetBrightness', json)
    }
    //Js通知App 询问APP用户权限状态  {permission: ['CAMERA/RECORD_AUDIO/WRITE_EXTERNAL_STORAGE/LOCATION']}
    CWorkstationCommunicationMng.queryAppPermissions = function (json) {
        CWorkstationCommunicationMng.query('queryAppPermissions:' + JSON.stringify(json))
    }
    //App通知JS 询问APP用户权限状态结果 app-->js json {error_code:0/-1,data:{permission:['CAMERA/RECORD_AUDIO/WRITE_EXTERNAL_STORAGE/LOCATION']},error_message:'success/no permission'}
    CWorkstationCommunicationMng.NotifyQueryAppPermissions = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyQueryAppPermissions', json)
        requestManager.handleResponse('NotifyQueryAppPermissions', json)
    }
    CWorkstationCommunicationMng.KeyboardChanged = function (json_str) {
        console.log('KeyboardChanged', json_str)
        requestManager.handleResponse('chatWindowKeyboardChanged', json_str)
        requestManager.handleResponse('reviewEditKeyboardChanged', json_str)
        requestManager.handleResponse('registerKeyboardChanged')
    }
    CWorkstationCommunicationMng.PressReturnKeyFromMobile = function (json_str) {
        console.log('PressReturnKeyFromMobile')
        const isUltraSoundMobile = window.vm.$store.state.device.isUltraSoundMobile
        const isTEAir = window.vm.$store.state.device.isTEAir
        const isSpDevice = isUltraSoundMobile||isTEAir
        const rootPath = ['/uLinkerInstructionManual','/init']
        const Toast = window.vm.$root.platformToast
        var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        if(isTEAir){
            requestManager.handleResponse('close_iworks_statistics')
            return
        }
        if (window.vm.$router.currentRoute.name == 'chat_window') {
            window.vm.$router.back()
        } else if (window.vm.$router.currentRoute.path === '/index') {
            if(Tool.checkMobileDialogShow()){
                if(Tool.checkMobileCanCloseDialog()&&Tool.checkMobileCanCloseOnPopstate()){
                    Tool.closeMobileDialog()
                }
                console.error('PressReturnKeyFromMobile closeMobileDialog /index');
                console.error(Tool.checkMobileDialogShow(),Tool.checkMobileCanCloseDialog(),Tool.checkMobileCanCloseOnPopstate());
                return
            }
            requestManager.handleResponse('closeChatWindowFromIndex',(complete)=>{
                if(complete&&!isSpDevice){
                    if (!confirm_back_tag) {
                        Toast(window.vm.$store.state.language.confirm_min_app)
                        confirm_back_tag = true
                        setTimeout(() => {
                            confirm_back_tag = false
                        }, 2000)
                        return
                    }else{
                        window.CWorkstationCommunicationMng.destroyApp()

                    }
                }


            })

        }else if(rootPath.includes(window.vm.$router.currentRoute.path)){
            if(Tool.checkMobileDialogShow()){
                if(Tool.checkMobileCanCloseDialog()&&Tool.checkMobileCanCloseOnPopstate()){
                    Tool.closeMobileDialog()
                }
                console.error('PressReturnKeyFromMobile closeMobileDialog rootPath');
                console.error(Tool.checkMobileDialogShow(),Tool.checkMobileCanCloseDialog(),Tool.checkMobileCanCloseOnPopstate());
                return
            }
            if (!confirm_back_tag) {
                Toast(window.vm.$store.state.language.confirm_min_app)
                confirm_back_tag = true
                setTimeout(() => {
                    confirm_back_tag = false
                }, 2000)
                return
            }else{
                window.CWorkstationCommunicationMng.destroyApp()

            }
        }else if( window.vm.$router.currentRoute.path == '/login'){
            if(Tool.checkMobileDialogShow()){
                if(Tool.checkMobileCanCloseDialog()&&Tool.checkMobileCanCloseOnPopstate()){
                    Tool.closeMobileDialog()
                }
                console.error('PressReturnKeyFromMobile closeMobileDialog /login');
                console.error(Tool.checkMobileDialogShow(),Tool.checkMobileCanCloseDialog(),Tool.checkMobileCanCloseOnPopstate());
                return
            }
            if(!isSpDevice){
                if (!confirm_back_tag) {
                    Toast(window.vm.$store.state.language.confirm_min_app)
                    confirm_back_tag = true
                    setTimeout(() => {
                        confirm_back_tag = false
                    }, 2000)
                    return
                }else{
                    window.CWorkstationCommunicationMng.destroyApp()
                }

            }


        } else if (window.vm.$router.currentRoute.name == 'iworks_statistics') {
            console.log('close_iworks_statistics')
            requestManager.handleResponse('close_iworks_statistics')
            return
        }else{
            window.vm.$router.back()
        }
    }
    //获得文件的Base64编码字符串  JS->APP  参数：{};
    CWorkstationCommunicationMng.getNetworkType = function (params) {
        console.info('CWorkstationCommunicationMng.getNetworkType', params)
        CWorkstationCommunicationMng.query('getNetworkType:' + JSON.stringify(params))
    }
    //获得文件的Base64编码字符串  APP->JS  app-->js json {error_code:0,data:{type:wifi/mobileData/ethernet},error_message:'success'}
    CWorkstationCommunicationMng.NotifyGetNetworkType = function (json_str) {
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        requestManager.handleResponse('NotifyGetNetworkType', json)
    }
    /* 前端JS通知UlinkerAI分析报告
     * 参数：json
     * params={
     *  task_id: str
     *  report: Array
     *  request_params:json
     * }
     */
    CWorkstationCommunicationMng.NotifyImageAnalyzeReport = function(params){
        console.info('CWorkstationCommunicationMng.NotifyImageAnalyzeReport', params)
        CWorkstationCommunicationMng.query('NotifyImageAnalyzeReport:' + JSON.stringify(params))
    }

    /* 前端JS通知Ulinker当前的云++连接状态
     * 参数：json
     * params={
     *  errorcode: 0//非0为异常
     *  errormsg:
     * }
     */
    CWorkstationCommunicationMng.NotifyWebimSocketState = function(params){
        console.info('CWorkstationCommunicationMng.NotifyWebimSocketState', params)
        const isUltraSoundMobile = window.vm.$store.state.device.isUltraSoundMobile
        if(isUltraSoundMobile){
            CWorkstationCommunicationMng.query('NotifyWebimSocketState:' + JSON.stringify(params))
        }else{
            console.log('Is Not Ulinker')
        }
    }

    /* 前端JS通知Ulinker当前的云++主页完成加载
     * 参数：json
     * params={
     *  errorcode: 0//非0为异常
     *  errormsg:
     * }
     */
    CWorkstationCommunicationMng.getWebimPageState = function(){
        console.info('CWorkstationCommunicationMng.getWebimPageState')
        const isUltraSoundMobile = window.vm.$store.state.device.isUltraSoundMobile
        if(isUltraSoundMobile){
            requestManager.handleResponse('checkWebimPageState')
        }else{
            console.log('Is Not Ulinker')
        }
    }
    CWorkstationCommunicationMng.NotifyWebimPageState = function(params){
        console.info('CWorkstationCommunicationMng.NotifyWebimPageState', params)
        const isUltraSoundMobile = window.vm.$store.state.device.isUltraSoundMobile
        if(isUltraSoundMobile){
            CWorkstationCommunicationMng.query('NotifyWebimPageState:' + JSON.stringify(params))
        }else{
            console.log('Is Not Ulinker')
        }
    }

    /* 前端JS通知Ulinker当前用户登录后，数据初始化完成
     * 参数：json
     * params={
     *  errorcode: 0//非0为异常
     *  errormsg:
     * }
     */
    CWorkstationCommunicationMng.NotifyUserState = function(params){
        console.info('CWorkstationCommunicationMng.NotifyUserState', params)
        const isUltraSoundMobile = window.vm.$store.state.device.isUltraSoundMobile
        if(isUltraSoundMobile){
            CWorkstationCommunicationMng.query('NotifyUserState:' + JSON.stringify(params))
        }else{
            console.log('Is Not Ulinker')
        }
    }
    //让原生跳转到网络摄像头设置界面  参数：{};
    CWorkstationCommunicationMng.openNetWorkCameraSetting = function (params) {
        console.info('CWorkstationCommunicationMng.openNetWorkCameraSetting', params)
        CWorkstationCommunicationMng.query('openNetWorkCameraSetting:' + JSON.stringify(params))
    }
    //让原生截图直播画面  参数：{};
    CWorkstationCommunicationMng.captureLiveStream = function (params) {
        console.info('CWorkstationCommunicationMng.captureLiveStream', params)
        CWorkstationCommunicationMng.query('captureLiveStream:' + JSON.stringify(params))
    }

    //通知前端完成数据上传
    CWorkstationCommunicationMng.sendAiResultWithImage = function (json_str) {
        console.info('CWorkstationCommunicationMng.sendAiResultWithImage', json_str)
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        requestManager.handleResponse('saveAiResultWithImage', json)
    }
}
