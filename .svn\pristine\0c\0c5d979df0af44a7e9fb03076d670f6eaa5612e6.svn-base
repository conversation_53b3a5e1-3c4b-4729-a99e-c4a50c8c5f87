<template>
    <div class="multicenter_searchbar" v-loading="loading">
        <mr-query-form>
          <mr-query-form-top-panel>
            <div class="search_bar">
              <el-form ref="form" :inline="true" label-width="70px" :model="queryForm"
                @submit.native.prevent>
                <el-form-item :label="lang.group_by" prop="conversations">
                      <el-select class="group_by" v-model="queryForm.conversations" :placeholder="lang.group_by" multiple>
                          <el-option
                          v-for="(conversation,index) of conversationList"
                          :key="index"
                          :label="conversation.subject"
                          :value="conversation.id" />
                      </el-select>
                </el-form-item>
                <el-form-item :label="lang.upload_datetime">
                  <el-date-picker
                    v-model="queryForm.uploadDateRange"
                    type="daterange"
                    unlink-panels
                    :range-separator="lang.date_to"
                    :start-placeholder="lang.start_date"
                    :end-placeholder="lang.end_date"
                    :picker-options="pickerOptions">
                  </el-date-picker>
                </el-form-item>
                <el-form-item :label="lang.case_status" prop="case_status">
                    <el-select v-model="queryForm.caseStatus">
                        <el-option
                        v-for="caseItem of caseSelectList"
                        :label="caseItem.value"
                        :value="caseItem.id"
                        :key="caseItem.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item :label="lang.patient_sex" prop="patient_sex">
                    <el-select v-model="queryForm.patient_sex" class="gender_select">
                        <el-option :label="lang.exam_status['-1']" :value="-1" />
                        <el-option :label="lang.male" :value="1" />
                        <el-option :label="lang.female" :value="2" />
                        <el-option :label="lang.unknown" :value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item :label="lang.patient_age" prop="patient_start_age">
                    <el-input class="age_range" v-model="queryForm.patient_start_age" @blur="patternAge" />
                </el-form-item>
                <el-form-item :label="lang.date_to" prop="patient_end_age">
                    <el-input class="age_range" v-model="queryForm.patient_end_age" @blur="patternAge" />
                </el-form-item>
                <el-form-item>
                  <el-button
                    icon="el-icon-search"
                    native-type="submit"
                    type="primary"
                    @click="handleQuery"
                  >
                    {{lang.query_btn}}
                  </el-button>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    @click="exportExcel"
                  >
                    {{lang.thyroid.export_excel}}
                  </el-button>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    @click="openExport"
                  >
                    {{lang.export_case}}
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </mr-query-form-top-panel>
        </mr-query-form>
        <status-count ref="statusCount"></status-count>
        <el-dialog
          class="export_confirm_dialog"
          :title="lang.tip_title"
          :visible="isShowConfirm"
          :close-on-click-modal="true"
          width="300px"
          height="300px"
          :append-to-body="true"
          :before-close="closeConfirmDialog">
          <div>
              {{lang.thyroid.export_excel_confirm}}
          </div>
          <div class="checkbox_wrap">
              <el-checkbox v-model="neverNotify">{{lang.never_notify}}</el-checkbox>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="isShowConfirm = false">{{lang.cancel_btn}}</el-button>
            <el-button type="primary" @click="doExportExcel">{{lang.confirm_txt}}</el-button>
          </span>
        </el-dialog>
        <export-dialog
          :examList="examList"
          :queryForm="queryForm"
          :examListTotal="examListTotal"
          :handleCurrentChange="handleCurrentChange"
          :handleSizeChange="handleSizeChange"
          :loadingData="loading"
          ref="exportDialog"></export-dialog>
        <div class="pagelist">
            <table>
                <thead>
                    <tr>
                        <th>{{lang.index_num}}</th>
                        <th>{{lang.case_num}}</th>
                        <th>{{lang.upload_datetime}}</th>
                        <th>{{lang.group_by}}</th>
                        <th>{{lang.patient_name}}</th>
                        <th>{{lang.patient_age}}</th>
                        <th>{{lang.patient_sex}}</th>
                        <th>{{lang.numberOfImages}}</th>
                        <th>{{lang.case_status}}</th>
                        <th>{{lang.operation}}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item,index) of examList" :key="index">
                        <td>{{getRowIndex(index)}}</td>
                        <td>{{item.statusInfo.id}}</td>
                        <td>{{formatTime(item.upload_ts)}}</td>
                        <td>{{item.group_subject[0].group_subject}}</td>
                        <td>{{item.patientInfo.patient_name}}</td>
                        <td>{{item.patientInfo.patient_age}}</td>
                        <td>{{item.patientInfo.patient_sex}}</td>
                        <td>{{item.count}}</td>
                        <td>{{formatCaseStatus(item)}}</td>
                        <td>
                             <el-button  v-loading="item.openState" type="text" @click="handleShow(item)">{{getBtnText(item)}}</el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
            <el-image v-if="examList.length==0" class="md-data-empty" src="static/resource_pc/images/nodata.png"/>
        </div>
        <!-- 病人信息组件 -->
        <!-- <patient-info v-if="viewType === 2" :examList="examList" @toggleExam="toggleExam" @openGallery="openGallery"></patient-info> -->
        <mr-gallery ref="MdGallery" class="md_gallery" :loading="isSubmiting">
            <div class="operation_btns" v-if="viewType === 2 && exam.statusInfo && exam.statusInfo.status == exam_status.submited">
                <el-button type="primary" @click="showReject">{{lang.reject_btn}}</el-button>
                <el-button type="primary" @click="passCase">{{lang.admin_pass}}</el-button>
            </div>
            <div class="case_view_wrap">
                <case-info ref="caseInfo"></case-info>
            </div>
            <el-dialog
                  :close-on-click-modal="false"
                  :visible.sync="isShowReject"
                  width="50%"
                  :modal="false"
                  class="reject_dialog"
                  :before-close="closeReject">
                  <div class="" style="width:100%">
                      <el-input type="textarea" width="100%" :rows="5" :placeholder="lang.reject_reason_title" v-model="rejectContent" :maxlength="200"></el-input>
                      <el-button class="reject_btn" type="primary" @click="rejectExam">{{lang.confirm_txt}}</el-button>
                  </div>
            </el-dialog>
        </mr-gallery>
        <el-pagination
          background
          :current-page="queryForm.pageNo"
          :layout="layout"
          :page-size="queryForm.pageSize"
          :total="examListTotal"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          style="margin-top:20px"
        />
        <a ref="link" style="display:none;"></a>
    </div>
</template>
<script>
import base from '../../../lib/base'
import service from '../../../service/multiCenterService.js'
import { transferPatientInfo , htmlEscape,deDuplicatingImg } from '../../../lib/common_base'
import MrGallery from './MrGallery'
import CaseInfo from '../assignment/caseInfo'
import exportDialog from './exportDialog'
import mrQueryForm from './MrQueryForm'
import statusCount from './statusCount'
import mrQueryFormTopPanel from './MrQueryForm/components/mrQueryFormTopPanel'
import Tool from '@/common/tool'
export default {
    name: 'listView',
    mixins:[base],
    components: {exportDialog, mrQueryForm, mrQueryFormTopPanel, MrGallery, CaseInfo,statusCount},
    props:{
        viewType:{
            type:Number,
            default() {
                return -1
            }
        },
    },
    data() {
        return {
            examList:[],
            loading:false,
            isSubmiting: false,
            queryForm: {
                conversations:[],
                pageNo: 1,
                pageSize: 10,
                caseStatus: -1,
                patient_sex: -1,
                patient_start_age:'',
                patient_end_age:'',
                uploadDateRange:[]
            },
            layout: 'total, sizes, prev, pager, next, jumper',
            caseSelectList:[
                {id:-1,value:''}, //全部
                {id:2,value:''}, //已提交
                {id:3,value:''}, //被驳回
                {id:6,value:''}, //已会诊
            ],
            conversationList:[],
            pickerOptions:{
                shortcuts: [{
                    text: window.vm.$store.state.language.recent_two_week,
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 13);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: window.vm.$store.state.language.recent_one_month,
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth()-1)
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: window.vm.$store.state.language.recent_two_month,
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth()-2)
                        picker.$emit('pick', [start, end]);
                    }
                }],
                disabledDate:(time)=>{
                    return time.getTime()>new Date().getTime()
                }
            },
            examListTotal:0,
            isShowConfirm:false,
            neverNotify:false,
            isShowReject:false,
            rejectContent:'',
            caseData:null,
            exam:{},
        }
    },
    computed: {
        currentMulticenter(){
            return this.$store.state.multicenter.currentMulticenter||{}
        },
        currentConfig(){
            return this.$store.state.multicenter.currentConfig;
        },
        exam_status(){
            return this.currentConfig.exam_status
        },
    },
    mounted(){
        this.init()
    },
    created() {

    },
    methods: {
        init(){
            this.queryForm.caseStatus = this.viewType;
            this.fetchData()
            this.caseSelectList.forEach(item=>{
                item.value=this.lang.exam_status[item.id]
            })
            this.getConversations()
        },
        handleQuery(){
            this.queryForm.pageNo = 1
            this.fetchData()
        },
        getConversations(){
            service.getConversations({
                mcID:this.currentMulticenter.id
            }).then((res)=>{
                if (res.data.error_code==0) {
                    res.data.data.forEach(item=>{
                        if (item.attendeeList) {
                            item.subject=item.attendeeList[0].userInfo.nickname;
                        }
                    })
                    this.conversationList=res.data.data;
                }

            })
        },
        toggleExam(index,caseData){
            this.caseData = caseData
        },
        async handleShow(row) {
            row.openState = true;
            await this.getExamDetail(row);
            console.log('assignment -->> openGallery',this.user.role)
            this.exam = row;
            if (row.caseData == null) {
                row.openState = false;
                this.$refs.MdGallery.openGallery(row.image_list[0], 0, row);
                this.$nextTick(() => {
                    this.$refs.caseInfo.clearAllData();
                });
                return;
            }
            try {
                row.openState = false;
                this.$refs.MdGallery.openGallery(row.image_list[0], 0, row);
                this.$nextTick(() => {
                    this.$refs.caseInfo.updateToView(row.caseData);
                });
            } catch (err) {
                console.error(err);
            }
        },
        openGallery(img,index,exam){
            this.exam=exam
            console.log('assignment -->> openGallery',img,index,exam,this.examObj)
            this.$refs.MdGallery.openGallery(img,index,exam)
            this.$nextTick(() => {
                this.$refs.caseInfo.updateToView(this.exam.caseData)
            })
        },
        async getExamDetail(exam) {
            if (!exam.initImageList) {
                await service
                    .getExamDetail({
                        mcID: this.currentMulticenter.id,
                        protocolGUID: exam.protocol_guid,
                        statusID: exam.statusInfo.id,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            exam.caseData = res.data.data.caseData;
                            exam.image_list = deDuplicatingImg(res.data.data.image_list);
                            exam.process = res.data.data.process;
                            exam.topic = res.data.data.topic;
                        }
                        exam.initImageList = true;
                    });
            }
        },
        formatCaseStatus(row){
            return this.lang.exam_status[row.statusInfo.status]
        },
        fetchData(){
            this.loading=true;
            let start='',end='';
            if(this.queryForm.uploadDateRange&&this.queryForm.uploadDateRange.length>1){
                start = this.queryForm.uploadDateRange[0]
                end = this.queryForm.uploadDateRange[1]
                start = this.formatDate(start)
                end = this.formatDate(end)
            }
            service.getExamList({
                mcID:this.currentMulticenter.id,
                page:this.queryForm.pageNo,
                pageSize:this.queryForm.pageSize,
                condition:{
                    start_time:start,
                    end_time:end,
                    status:this.queryForm.caseStatus,
                    start_age:this.queryForm.patient_start_age,
                    end_age:this.queryForm.patient_end_age,
                    gender:this.queryForm.patient_sex,
                    gidList:this.queryForm.conversations
                }
            }).then(async (res)=>{
                this.loading=false;
                if (res.data.error_code==0) {
                    let examList = res.data.data.data
                    for(let item of examList){
                        item.patientInfo = transferPatientInfo(item)
                        this.$set(item,'openState',false)
                        this.$set(item,'initImageList',false)
                    }
                    this.examList = examList;
                    this.examListTotal=res.data.data.total;
                }
            })
            this.$refs.statusCount.getMultiCenterStatus();
        },
        handleSizeChange(val) {
            this.queryForm.pageSize = val
            this.fetchData()
        },
        handleCurrentChange(val) {
            this.queryForm.pageNo = val
            this.fetchData()
        },
        formatDate(date){
            let year = date.getFullYear()
            let month = '00' + (date.getMonth()+1)
            let day = '00' + date.getDate()
            month = month.substr(-2);
            day = day.substr(-2);
            return `${year}-${month}-${day}`
        },
        getRowIndex(index) {
            index = index + 1
            return this.queryForm.pageNo * this.queryForm.pageSize - (this.queryForm.pageSize - index)
        },
        openExport(){
            if(!Tool.checkAppClient('Cef')){
                this.$message.error(this.lang.use_app_tip)
                return
            }
            this.$refs.exportDialog.init();
        },
        exportExcel() {
            let neverNotify = window.localStorage.getItem('neverNotifyExport');
            this.neverNotify = neverNotify?true:false;
            if (neverNotify || this.queryForm.caseStatus === 6 ) {
                this.doExportExcel();
            }else{
                this.isShowConfirm = true;
            }
        },
        doExportExcel(){
            this.isShowConfirm = false;
            window.localStorage.setItem('neverNotifyExport', this.neverNotify ? 1 : '');
            let start='',end='';
            if(this.queryForm.uploadDateRange&&this.queryForm.uploadDateRange.length>1){
                start = this.queryForm.uploadDateRange[0]
                end = this.queryForm.uploadDateRange[1]
                start = this.formatDate(start)
                end = this.formatDate(end)
            }
            service.exportExcel({
                mcID:this.currentMulticenter.id,
                condition:{
                    start_time:start,
                    end_time:end,
                    status:this.queryForm.caseStatus,
                }
            }).then(async (res)=>{
                let blob = new Blob([res.data],{ type: "application/xlsx" });
                const url = window.URL.createObjectURL(blob); // 设置路径
                const link = this.$refs.link;
                link.href = url;
                link.download = `${this.currentMulticenter.name}.xlsx`; // 设置文件名
                link.click();
                URL.revokeObjectURL(url); // 释放内存
            }).catch(function (error) {
                console.log(error);
            });
        },
        closeConfirmDialog(){
            this.isShowConfirm = false;
        },
        patternAge(){
            let start_age = Number(this.queryForm.patient_start_age);
            let end_age = Number(this.queryForm.patient_end_age);
            if (isNaN(start_age)||!/^\d+$/.test(start_age)) {
                this.queryForm.patient_start_age = '';
                this.$message.error(this.lang.enter_age_wrong)
            }
            if (isNaN(end_age)||!/^\d+$/.test(end_age)||(end_age === '0')) {
                this.queryForm.patient_end_age = '';
                this.$message.error(this.lang.enter_age_wrong)
            }
            if (start_age && end_age && (start_age > end_age)) {
                this.queryForm.patient_start_age = '';
                this.queryForm.patient_end_age = '';
                this.$message.error(this.lang.enter_age_wrong)
            }
        },
        modifyStatus(status){
            this.exam.statusInfo.status=status;
        },
        showReject(){
            this.rejectContent=''
            this.isShowReject=true;
            window.CWorkstationCommunicationMng.hideRealTimeVideo({})
        },
        closeReject(){
            this.isShowReject=false;
            this.$root.eventBus.$emit('regainVideo')
        },
        rejectExam(){
            if(this.rejectContent.trim().length==0){
                this.$message.error(this.lang.reject_reason_no_white);
                return;
            }
            if(!this.rejcetClick){
                this.rejectContent = htmlEscape(this.rejectContent)
                this.rejcetClick=true;
                service.refuteCase({
                    exam_id: this.exam.exam_id,
                    reason:this.rejectContent,
                    mcID:this.currentMulticenter.id,
                }).then(res => {
                    if(res.data.error_code==0){
                        this.$message({
                            message:this.lang.reject_success_tip,
                            type:'success'
                        })
                        this.fetchData()
                        this.modifyStatus(this.exam_status.reject)
                        this.closeReject();
                        this.$refs.MdGallery.destroyGallery()
                    }
                    this.rejcetClick=false;
                })
            }
        },
        passCase() {
            window.CWorkstationCommunicationMng.hideRealTimeVideo({})
            this.$confirm(this.lang.thyroid.confirm_passing_the_case,this.lang.tip_title,{
                confirmButtonText:this.lang.confirm_txt,
                cancelButtonText:this.lang.cancel_btn,
                type:'warning',
                closeOnClickModal:false,
                modal:false,
                customClass:'confirm-assign-exam'
            }).then(()=>{
                console.log(this.exam.exam_id,this.currentMulticenter.id);
                service.passExam({
                    exam_id: this.exam.exam_id || '',
                    mcID:this.currentMulticenter.id,
                }).then(res => {
                    console.log('[pass case]',res)
                    if(res.data.error_code === 0) {
                        this.$message.success(this.lang.operate_success)
                        this.fetchData()
                        this.modifyStatus(this.exam_status.assigned)
                        this.$refs.MdGallery.destroyGallery()
                    }
                    this.$root.eventBus.$emit('regainVideo')
                })
            }).catch(e=>{
                this.$root.eventBus.$emit('regainVideo')
            })
        },
        getBtnText(exam) {
            let role = this.currentMulticenter.userInfo.role
            if(exam.statusInfo.status === this.exam_status.reject) {
                return this.lang.view_btn
            }
            switch(role) {
            case this.currentConfig.role.normal: //普通管理员
            case this.currentConfig.role.admin: //超管
                return this.lang.view_btn
            case this.currentConfig.role.assignment: //审核者
                if(exam.statusInfo.status === this.exam_status.submited) {
                    return this.lang.assign_btn
                }else{
                    return this.lang.view_btn
                }
            default:
                return this.lang.view_btn
            }
        }
    },
}
</script>
<style lang="scss">
.multicenter_searchbar{
    padding: 20px 0;
    overflow-y: auto;
    .search_bar{
      .el-range-separator{
        width:9%;
      }
      .el-form-item__label{
        padding-left: 4px;
        width:auto !important;
      }
      .el-select .el-input{
        width: 120px;
      }
      .el-tag{
        overflow: hidden;
        text-overflow: ellipsis;
    }
      .el-input{
        width: 60px;
      }
    }
}
</style>
