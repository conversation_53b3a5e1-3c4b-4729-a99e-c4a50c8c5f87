<template>
    <transition name="slide">
        <div class="threshold-setting-page third_level_page">
            <mrHeader>
                <template #title> {{ lang.auto_recognition_threshold }} </template>
            </mrHeader>
            <div class="threshold-setting-content">
                <div class="threshold-setting-content-top">
                    <div class="description-text">
                        {{ lang.auto_recognition_threshold_tips }}
                    </div>

                    <van-cell-group class="input-group">
                        <van-field v-model="abdomenThreshold" :label="`${this.lang.abdomen || '腹部'}: ≥`" type="number" :placeholder="lang.please_enter_threshold" @input="handleThresholdInput($event, 'abdomenThreshold')">
                            <template #button>
                                <span class="input-suffix">%</span>
                            </template>
                        </van-field>
                        <!-- <van-field v-model="obstetricsThreshold" :label="`${lang.homework_type4}: ≥`" type="digit" :placeholder="lang.please_enter_threshold">
                            <template #button>
                                <span class="input-suffix">%</span>
                            </template>
                        </van-field> -->
                        <van-field v-model="cardiacThreshold" :label="`${this.lang.cardiac || '心脏'}: ≥`" type="number" :placeholder="lang.please_enter_threshold" @input="handleThresholdInput($event, 'cardiacThreshold')">
                            <template #button>
                                <span class="input-suffix">%</span>
                            </template>
                        </van-field>
                    </van-cell-group>
                </div>
                <div class="button-group">
                    <van-button class="action-button confirm-button" block @click="onConfirm" type="primary">
                        {{ lang.confirm_txt }}
                    </van-button>
                    <van-button class="action-button cancel-button" block @click="onCancel" type="default">
                        {{ lang.cancel_btn }}
                    </van-button>
                </div>
            </div>
        </div>
    </transition>
</template>

<script>
import { Field, CellGroup, Button, Toast } from "vant";
import base from "../lib/base";
import Tool from "@/common/tool"; // 假设 Tool 的路径
import { EXAM_TYPE } from "../lib/constants";


const NATIVE_TYPE_TO_VUE_DATA_MAP = {
    [EXAM_TYPE.ABDOMEN]: "abdomenThreshold", // 腹部 (EXAM_TYPE.ABDOMEN = 4)
    [EXAM_TYPE.CARDIOVASCULAR]: "cardiacThreshold", // 心脏 (EXAM_TYPE.CARDIOVASCULAR = 2)

};

export default {
    mixins: [base],
    name: "AutoRecognitionThreshold",
    components: {
        [Field.name]: Field,
        [CellGroup.name]: CellGroup,
        [Button.name]: Button,
    },
    data() {
        return {
            abdomenThreshold: "",
            obstetricsThreshold: "",
            cardiacThreshold: "",
        };
    },
    mounted() {
        this.loadThresholds();
    },
    methods: {
        async loadThresholds() {
            try {
                Toast.loading({
                    message: this.lang.loading_text || "加载中...",
                    forbidClick: true,
                    duration: 0,
                });
                const res = await Tool.createCWorkstationCommunicationMng({
                    name: "GetAutoRecognitionThreshold",
                    emitName: "NotifyGetAutoRecognitionThreshold",
                    params: {},
                });
                Toast.clear();
                if (res && res.error_code === 0 && res.data) {
                    res.data.forEach((item) => {
                        const vueDataKey = NATIVE_TYPE_TO_VUE_DATA_MAP[item.type];
                        if (vueDataKey && typeof item.lowestScore === 'number') {
                            this[vueDataKey] = (item.lowestScore * 10).toString();
                        }
                    });
                } else {
                    Toast(res.error_message || this.lang.load_fail_text || "加载失败");
                }
            } catch (error) {
                Toast.clear();
                console.error("Load thresholds failed:", error);
                Toast(this.lang.load_fail_text || "加载失败");
            }
        },
        async onConfirm() {
            const thresholdsToSet = [];
            const fieldsToValidate = [
                { key: "abdomenThreshold", name: this.lang.abdomen || '腹部', type: EXAM_TYPE.ABDOMEN },
                { key: "cardiacThreshold", name: this.lang.cardiac || '心脏', type: EXAM_TYPE.CARDIOVASCULAR },
            ];

            for (const field of fieldsToValidate) {
                const valueStr = this[field.key];
                if (valueStr === "" || valueStr === null || typeof valueStr === 'undefined') {
                    // 允许用户清空阈值，如果清空则不提交该类型
                    continue;
                }

                const valueNum = parseFloat(valueStr);

                if (isNaN(valueNum)) {
                    Toast(`${field.name} ${this.lang.please_enter_valid_number || '请输入有效数字'}`);
                    return;
                }
                if (valueNum < 0 || valueNum > 100) {
                    Toast(`${field.name} ${this.lang.threshold_range_error || '阈值必须在0-100之间'}`);
                    return;
                }
                thresholdsToSet.push({
                    type: field.type,
                    lowestScore: valueNum / 10,
                });
            }

            if (thresholdsToSet.length === 0) {
                // 如果用户清空了所有输入框，则可以理解为不设置，或者提示用户至少输入一个
                // 当前行为：如果都清空了，允许提交一个空数组给原生，由原生处理（或者根据具体业务调整）
                // 或者可以提示用户至少设置一项：
                // Toast(this.lang.please_set_at_least_one_threshold || "请至少设置一项阈值");
                // return;
                console.log("没有需要设置的阈值或所有阈值都被清空");
            }

            try {
                Toast.loading({
                    message: this.lang.saving_text || "保存中...",
                    forbidClick: true,
                    duration: 0,
                });
                const res = await Tool.createCWorkstationCommunicationMng({
                    name: "SetAutoRecognitionThreshold",
                    emitName: "NotifySetAutoRecognitionThreshold",
                    params: { threshold: thresholdsToSet },
                });
                Toast.clear();
                if (res && res.error_code === 0) {
                    Toast.success(this.lang.save_success_text || "保存成功");
                    this.back(); // 保存成功后返回上一页
                } else {
                    Toast(res.error_message || this.lang.save_fail_text || "保存失败");
                }
            } catch (error) {
                Toast.clear();
                console.error("Set thresholds failed:", error);
                Toast(this.lang.save_fail_text || "保存失败");
            }
        },
        onCancel() {
            this.back()
        },
        handleThresholdInput(value, fieldName) {
            let valStr = String(value === null || typeof value === 'undefined' ? '' : value);
            let cleanedValue = valStr.replace(/[^\d]/g, '');

            if (cleanedValue === "") {
                this[fieldName] = "";
                return;
            }

            const num = parseInt(cleanedValue, 10);

            if (num < 0) {
                this[fieldName] = '0';
            } else if (num > 100) {
                this[fieldName] = '100';
            } else {
                this[fieldName] = String(num); // 使用 String(num) 以处理 '05' -> '5' 并确保是字符串
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.threshold-setting-page {
    background-color: #f5f7fa;
    .threshold-setting-content {
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 0.96rem;
        .threshold-setting-content-top {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1.2rem;
        }
    }
    .description-text {
        font-size: 0.67rem;
        color: #606266;
        line-height: 1.6;
        padding: 0.72rem;
        background-color: #fff;
        text-align: left;
        box-shadow: 0 0.1rem 0.19rem rgba(0, 0, 0, 0.05);
        border-left: 0.19rem solid #409eff;
    }

    .input-group {
        background-color: #fff;
        margin-bottom: 0;
        border-radius: 0.48rem;
        overflow: hidden;
        box-shadow: 0 0.1rem 0.19rem rgba(0, 0, 0, 0.05);

        .van-cell {
            padding: 0.72rem 0.96rem;
            font-size: 0.77rem;
            position: relative;
            transition: background-color 0.2s ease;

            &:not(:last-child)::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0.96rem;
                right: 0.96rem;
                height: 0.05rem;
                background-color: #ebeef5;
            }

            &:active {
                background-color: #f5f7fa;
            }
        }

        .van-field__label {
            color: #303133;
            font-weight: 500;
            width: auto;
            margin-right: 0.24rem;
        }
    }

    .input-suffix {
        color: #606266;
        font-size: 0.77rem;
        margin-left: 0.24rem;
        font-weight: 400;
    }

    .button-group {
        margin-top: 1.44rem;

        .action-button {
            font-size: 0.82rem;
            font-weight: 500;
            border-radius: 0.38rem;
            height: 2.12rem;

            &.cancel-button {
                margin-top: 0.72rem;
                border: 0.05rem solid #dcdfe6;
            }
        }
    }

    ::v-deep .van-field__control {
        color: #303133;
        font-weight: 500;
        &::placeholder {
            color: #c0c4cc;
            font-weight: normal;
        }
    }
}
</style>
