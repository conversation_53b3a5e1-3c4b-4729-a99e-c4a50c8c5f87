<template>
    <!-- 作答记录区域 -->
    <div class="answer-records" v-if="records && records.length > 0">
        <div class="answer-records-header" @click="toggleRecords">
            <span>作答记录 ({{ records.length }})</span>
            <i :class="[showRecords ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
        </div>
        <div class="answer-records-list" v-show="showRecords">
            <div
                v-for="(record, index) in records"
                :key="index"
                class="answer-record-item"
            >
                <div class="record-time">{{ formatTime(record.submitTime) }}</div>

                <!-- 文本内容记录 -->
                <div v-if="!isImageRecord(record)" class="record-content">{{ record.content }}</div>
                <!-- 图片记录 -->
                <div v-else class="record-images">
                    <div
                        v-for="(image, imgIndex) in record.images"
                        :key="imgIndex"
                        class="record-image-item"
                        @click="viewImage(record.images, imgIndex)"
                    >
                        <img :src="image.url" alt="作答图片" />
                    </div>
                </div>
                <!-- 批改结果显示 -->
                <div v-if="record.isPassed !== undefined" class="record-result">
                    <span class="result-label">批改结果：</span>
                    <span :class="{'result-status': true, 'passed': record.isPassed === true, 'failed': record.isPassed === false}">
                        {{ record.isPassed === true ? '通过' : '不通过' }}
                    </span>
                </div>
                <div v-else-if="record.score !== undefined" class="record-result">
                    <span class="result-label">批改分数：</span>
                    <span class="result-score">{{ record.score }}</span>
                </div>

                <!-- 评语显示 -->
                <div v-if="record.comment" class="record-comment">
                    <div class="comment-label">评语：</div>
                    <div class="comment-content">{{ record.comment }}</div>
                </div>

            </div>
        </div>
        <base-gallery ref="baseGallery"></base-gallery>
    </div>

</template>

<script>
import baseGallery from "../../../MRComponents/baseGallery";

export default {
    name: "AnswerRecords",
    components: {
        baseGallery
    },
    props: {
        records: {
            type: Array,
            default: () => [],
        }
    },
    data() {
        return {
            showRecords: false, // 控制作答记录的显示/隐藏
        };
    },
    methods: {
        // 切换作答记录的显示/隐藏状态
        toggleRecords() {
            this.showRecords = !this.showRecords;
        },

        // 格式化时间显示
        formatTime(timeString) {
            try {
                const date = new Date(timeString);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });
            } catch (e) {
                return timeString;
            }
        },

        // 判断是否为图片记录
        isImageRecord(record) {
            return record.images && record.images.length > 0;
        },

        // 判断是否有批改结果
        hasResult(record) {
            return record.isPassed !== undefined || record.score !== undefined;
        },

        // 判断是否有评语
        hasComment(record) {
            return record.comment && record.comment.trim() !== '';
        },

        // 查看图片
        viewImage(imageList, index) {
            // 确保图片列表格式正确，baseGallery 需要 url 和 fileType 属性
            const files = imageList.map(image => ({
                url: image.url,
                fileType: 'image'
            }));
            this.$refs.baseGallery.openGallery(files, index);
        }
    }
};
</script>

<style lang="scss" scoped>
.answer-records {
    margin-top: 15px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;

    .answer-records-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background-color: #f5f7fa;
        cursor: pointer;
        font-weight: bold;
        color: #606266;

        &:hover {
            background-color: #e6ebf5;
        }

        i {
            font-size: 14px;
            transition: transform 0.3s;
        }
    }

    .answer-records-list {
        background-color: #fff;

        .answer-record-item {
            padding: 12px 15px;
            border-top: 1px solid #ebeef5;

            &:first-child {
                border-top: none;
            }

            .record-time {
                font-size: 12px;
                color: #909399;
                margin-bottom: 5px;
            }

            .record-result {
                margin-top: 5px;
                margin-bottom: 8px;
                display: flex;
                align-items: center;

                .result-label {
                    font-size: 13px;
                    color: #606266;
                    margin-right: 8px;
                    font-weight: bold;
                }

                .result-status {
                    display: inline-block;
                    padding: 2px 8px;
                    border-radius: 4px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 13px;

                    &.passed {
                        background-color: #00c59d;
                        color: #fff;
                    }

                    &.failed {
                        background-color: #f56c6c;
                        color: #fff;
                    }
                }

                .result-score {
                    font-size: 14px;
                    color: #00c59d;
                    font-weight: bold;
                }
            }

            .record-comment {
                margin-top: 5px;
                margin-bottom: 8px;
                background-color: #f8f8f8;
                padding: 8px 10px;
                border-radius: 4px;
                border-left: 3px solid #ff9900;

                .comment-label {
                    font-size: 13px;
                    color: #606266;
                    font-weight: bold;
                    margin-bottom: 4px;
                }

                .comment-content {
                    font-size: 13px;
                    color: #606266;
                    line-height: 1.5;
                    white-space: pre-wrap;
                    word-break: break-word;
                }
            }

            .record-content {
                font-size: 14px;
                color: #606266;
                line-height: 1.5;
                white-space: pre-wrap;
                word-break: break-word;
            }

            .record-images {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-top: 5px;

                .record-image-item {
                    width: 120px;
                    height: 90px;
                    border-radius: 4px;
                    overflow: hidden;
                    cursor: pointer;
                    border: 1px solid #ebeef5;
                    transition: all 0.3s;

                    &:hover {
                        transform: scale(1.05);
                        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
                    }

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }
            }
        }
    }
}
</style>
