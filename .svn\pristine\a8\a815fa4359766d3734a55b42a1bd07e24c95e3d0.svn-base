let RU = {
    "please_enter_threshold": "Введите порог",
    "auto_recognition_threshold_tips": "Установите соответствующие пороговые значения в зависимости от типа проверки, и функция автоматической идентификации после установки будет показывать только результаты выше пороговых значений в зависимости от типа проверки.",
    "auto_recognition_threshold": "Порог автоматической идентификации",
    "topic_summary_no_score": "Вопросы этого класса состоят из {a} подтем",
    "ob_right_ventricular_outflow_tract_view": "Правый разрез люка",
    "ob_right_ventricular_outflow_tract_view_group": "Правый разрез люка",
    "renal_long_axis_section_group": "Длинноосный разрез почек",
    "right_kidney": "Правая почка",
    "left_kidney": "Левая почка",
    "spinal_sagittal_section_group": "Сектор векторного сечения позвоночника",
    "ob_sacral_caudal_segment": "крестцовый хвост",
    "ob_cervicothoracic_junction": "Шеногрудная часть",
    "angle_between_sound_beam_long_axis_femur": "Угол между акустическим пучком и длинной осью бедренной кости составляет 60° - 90°",
    "short_axis_section_of_biventricular_myocardium": "Двухкамерный короткоосевой разрез",
    "ob_weekclear_IVS_display": "Интервал между камерами не совсем ясен.",
    "ob_unclear_IVS_display": "Интервал между камерами не ясен.",
    "ob_unclear_LV_display": "В левом желудочке нет ясности.",
    "ob_weekclear_LV_display": "Левый желудочек не совсем ясен.",
    "ob_weekclear_pulmonary_artery_and_ductus_arteriosus_display": "Лёгочная артерия и артериальные катетеры не ясны.",
    "ob_unkclear_pulmonary_artery_and_ductus_arteriosus_display": "Легочная артерия и артериальные катетеры не ясны.",
    "ob_weekclear_TV_display": "Трехлепестковый дисплей не совсем ясен.",
    "ob_unkclear_TV_display": "Тройной клапан неясен.",
    "ob_weekclear_main_pulmonary_artery_and_ductus_arteriosus_display": "Основные легочные артерии и артериальные катетеры не совсем ясны.",
    "ob_unkclear_main_pulmonary_artery_and_ductus_arteriosus_display": "Недостаточная ясность в артериях и артериях.",
    "ob_unkclear_RV_display": "Не ясно в правом желудочке.",
    "ob_weekclear_RV_display": "Недостаточная ясность в правом желудочке.",
    "ob_weekclear_IVC_display": "Нижняя вена не совсем ясна.",
    "ob_unclear_IVC_display": "Нижняя вена не ясна.",
    "ob_weekclear_LA_display": "Левое предсердие не ясно.",
    "ob_unclear_LA_display": "Левое предсердие не ясно.",
    "ob_weekclear_RA_display": "В правом предсердии нет ясности.",
    "ob_unclear_RA_display": "Не ясно в правом предсердии.",
    "ob_weekclear_head_and_neck_arterial_branch_display": "Разделение артерии головы и шеи не ясно.",
    "ob_unclear_head_and_neck_arterial_branch_display": "Разделение артерий головы и шеи не ясно.",
    "ob_weekclear_ARCH_display": "Оральный лук не совсем ясен.",
    "ob_unclear_ARCH_display": "Артериальный лук не совсем ясен.",
    "ob_unclearx_image_display": "Изображения не ясны",
    "ob_weekclear_superior_vena_cava_display": "В верхней полости вены нет ясности.",
    "ob_unclear_superior_vena_cava_display": "В верхней полости вены нет ясности.",
    "ob_weekclear_DA_display": "Пониженная аорта не ясна.",
    "ob_unclear_DA_display": "Опустить аорту. Не ясно.",
    "ob_weekclear_ASC_display": "Повышенная аорта не совсем ясна.",
    "ob_unclear_ASC_display": "Возникновение аорты не ясно.",
    "ob_weekclear_brach_of_right_pulmonary_artery_display": "Разделение правой легочной артерии не ясно.",
    "右肺动脉分支显示不清晰": "Разделение правой легочной артерии не ясно.",
    "ob_weekclear_rama_arteria_pulmonar_izquierda_display": "Левая ветвь легочной артерии не совсем ясна.",
    "ob_unclear_rama_arteria_pulmonar_izquierda_display": "Левая ветвь легочной артерии не ясна.",
    "ob_weekclear_image_display": "Изображение не совсем ясно",
    "ob_weekclear_gallbladder_display": "Желчный пузырь не совсем ясен.",
    "ob_unclear_spine_display": "Спинной позвоночник не работает.",
    "ob_unclear_liver_display": "Печень не показывает.",
    "ob_unclear_image_display": "Изображение не отображается",
    "ob_unclear_diaphragm_display": "Диафрагма плохо видна.",
    "ob_unclear_magenblase_display": "Желудочные пузыри не видны.",
    "ob_unclear_heart_display": "Сердце не видно.",
    "ob_unclear_lung_display": "Плохое зрение.",
    "head_and_neck_arterial_branch": "Разделы артерий головы и шеи",
    "long_axis_section_aortic_arch_view": "Длинноосный разрез дуги аорты",
    "longitudinal_spine_conus_medullaris": "продольный разрез позвоночника - конус спинного мозга",
    "brach_of_right_pulmonary_artery": "ветвь правой легочной артерии",
    "pulmonary_artery_bifurcation_view": "Перекресток лёгочной артерии",
    "placental_umbilical_cord_entrance_umbilical_cord_entrance": "Входной разрез плаценты - пуповинный вход",
    "blood_flow_map_attachment_site_umbilical_blood_vessels": "Диаграмма кровотока в месте сцепления пуповинных кровеносных сосудов",
    "umbilical_cord_placenta_attachment_site": "Сцепление пуповинной плаценты",
    "cervical_internal_opening_cervix_placenta_attachment_site": "Внутренний разрез шейки матки - мочевой пузырь беременной женщины",
    "cervical_internal_opening_cervix_fetal_head": "Внутренний разрез шейки матки - головка протектора",
    "cervical_internal_opening_cervix_cervical_internal_mouth": "Внутренний разрез шейки матки",
    "cervical_internal_opening_cervix": "Внутренний разрез шейки матки - шейка матки",
    "cervical_internal_opening": "Внутреннее отверстие шейки матки",
    "neck_of_uterus": "Шейка матки",
    "cervical_internal_opening_view": "Внутренний разрез шейки матки",
    "ob_plantar": "подошва ноги",
    "plantar_bilateral_view": "Нижний разрез стопы (двусторонний)",
    "character_7foot": "Семь слов.",
    "longitudinally_cut_foot": "продольная нога",
    "transverse_calf_foot_7foot_bilateral_view": "Длинный разрез голени и стопы (7 слов стопы) (двусторонний)",
    "visible_anthor_tibia_and_fibula": "Большая берцовая и малоберцовая кости видны на другом конце.",
    "visible_one_tibia_and_fibula": "На одном конце голени.",
    "long_axisvsection_tibia_fibula_bilateral_view": "Длинный осевой разрез большеберцовой и малоберцовой костей (двусторонний)",
    "ob_femur": "Бедренная кость",
    "duce_points_angle_between_sound_beam_long": "Дополнительный балл под углом менее 45° между звуковым пучком и длинной осью бедренной кости",
    "visible_metaphysis_anthor_end_femur": "На другом конце бедренной кости.",
    "visible_metaphysis_one_end_femur": "Видно на бедренной кости.",
    "femoral_long_axis_bilateral_view": "Длинный осевой разрез бедренной кости (двусторонний)",
    "vertical_cutting_hand": "Продольная рука",
    "forearm_hand_longitudinal_bilateral_view": "Предплечье и продольный разрез руки (двусторонний)",
    "crown_hand": "коронарный рука",
    "ob_palm": "Руки",
    "coronary_hand_ulna_and_radius_bilateral_view": "коронарный разрез руки (двусторонний)",
    "ulna_and_radius": "Лазерная кость",
    "visible_anthor_radius_and_ulna": "Видна на другом конце лучевой кости.",
    "visible_one_radius_and_ulna": "Видна на одном конце лучевой кости.",
    "long_axis_radius_ulna_bilateral": "Длинный осевой разрез лучевой кости",
    "long_bone_endpoints": "Длинная костная точка",
    "ob_humerus": "Плечевая кость",
    "duce_points_angle_between_sound_beam_long_axis_humerus": "Дополнительный вычет при углу менее 45° между звуковым пучком и длинной осью плечевой кости",
    "angle_sound_beam_and_long_axis_humerus": "Угол между звуковым пучком и длинной осью плечевой кости 60° - 90°",
    "tibia_and_fibula": "Большая берцовая и малоберцовая кости",
    "ob_ductus_arteriosus_arch_view": "дуговой разрез артериального катетера",
    "pulmonary_artery_and_ductus_arteriosus": "Лёгочная артерия и артериальный катетер",
    "ob_upper_inferior_vena_cava_section": "Вверх - нижний венозный разрез",
    "short_axis_great_arteries_heart": "Краткоосный разрез предсердной аорты",
    "visible_metaphysis_other_end_humerus": "На другом конце плечевой кости.",
    "visible_metaphysis_one_end_humerus": "На одном конце плечевой кости.",
    "long_axis_section_humerus_bilateral": "Длинный осевой разрез плечевой кости (двусторонний)",
    "longitudinal_pine_full_view": "Длинный разрез позвоночника - весь путь",
    "longitudinal_pine_mid_view": "Длинный разрез позвоночника - средняя часть",
    "conus_medullaris": "Спинной конус",
    "sacral_vertebra_point": "Крестный позвоночник",
    "caudal_vertebrae": "Хвостовой позвонок",
    "sacral_tail": "крестцовый хвост",
    "longitudinal_spine_coccygeal_segment": "продольный разрез позвоночника - крестцовый хвост",
    "ob_skin": "Кожа",
    "cervical_vertebra": "Шейные позвонки",
    "skin_contour": "Контуры кожи",
    "vertebral_body": "позвонок",
    "vertebral_arch": "Спинолук",
    "longitudinal_spine_cervical_thoracic_segment": "Длинный разрез позвоночника - шейно - грудной сегмент",
    "ob_coronary_kidney_view": "Коронарный разрез почек",
    "ob_sagittal_section_right_kidney": "Правый векторный разрез почек",
    "ob_sagittal_section_left_kidney": "Левый векторный разрез почек",
    "bilateral_renal_pelvis": "Двусторонняя почечная лоханка",
    "bilateral_kidneys": "Двусторонняя почка",
    "horizontal_cross_section_both_kidneys": "Двойной почечный горизонтальный разрез",
    "bladder_double_umbilical_artery_section_umbilical_artery": "Разрез мочевого пузыря - пуповинной артерии",
    "dual_umbilical_artery_section_bladder_bladder": "Диапупочный разрез мочевого пузыря - мочевой пузырь",
    "intersection_of_umbilical_wheel": "Соединение пуповинных колес",
    "ob_ilateral_umbilical_artery_blood_flow": "Двусторонний пуповинный кровоток.",
    "ob_bladder": "Мочевой пузырь",
    "horizontal_bladder_and_bilateral_umbilical_artery_blood_flow_map": "Горизонтальный разрез мочевого пузыря и двухсторонняя пуповинная артерия.",
    "ob_pinal_cone": "Конус позвоночника",
    "ob_sectional_view_of_umbilical_cord_abdominal_wall_entrance": "Входной разрез брюшной стенки пуповины",
    "ob_gallbladder_view": "разрез желчного пузыря",
    "extra_deduction_for_kidney_damage": "Появляется дополнительный почечный вычет.",
    "ob_one_rib_on_each_side": "По левому и правому ребрам.",
    "ob_complete_abdominal_wall": "Полнота брюшной стенки.",
    "descending_aorta_and_inferior_vena_cava": "Снижение аорты, нижняя вена",
    "coronary_section_diaphragm_view": "Диафрагмальный коронарный разрез",
    "sagittal_section_right_diaphragm_view": "векторный разрез правой диафрагмы",
    "ob_diaphragm": "Диафрагма",
    "sagittal_section_left_diaphragm": "Левый диафрагмальный векторный разрез",
    "ob_trachea": "Газопровод",
    "confluence_of_aortic_arch_ductus_arteriosus": "Соединение дуги аорты с артериальным катетером (V - образная структура)",
    "ob_three_vessel_tracheal_view": "Трёхкровеносный трахеостомоз",
    "rama_de_la_arteria_pulmonar_izquierda": "Левая ветвь легочной артерии",
    "main_pulmonary_artery_and_ductus_arteriosus": "Главная легочная артерия и артериальный катетер",
    "ob_three_vessel_view": "Тройной кровеносный разрез",
    "main_pulmonary_artery": "Главная легочная артерия",
    "obcleft_ventricular_outflow_tract_section": "Прорез левого люка",
    "ob_ight_ventricular_outflow_tract_view_2": "Профиль выхода правой камеры 2",
    "ob_ight_ventricular_outflow_tract_view_1": "Профиль выхода правой камеры 1",
    "superior_vena_cava": "Верхняя полость вены",
    "ductus_arteriosus": "Артериальный катетер",
    "main_pulmonary_artery_continuous_with_right_ventricle": "Главная легочная артерия соединена с правым желудочком.",
    "inferior_vena_cava_aorta": "Нижняя аорта",
    "multiple_ribs": "Множественные ребра",
    "single_rib": "Одношпангоут",
    "renal_pelvis": "Почечная лоханка",
    "ob_kidney": "Почки",
    "umbilical_cord_abdominal_wall_entrance": "Вход пуповинной брюшной стенки",
    "umbilical_vein": "Пупочная вена",
    "ob_magenblase": "Желудочный пузырь",
    "ob_bdominal_circumference_view": "брюшной разрез",
    "ob_continuous_ascending_aorta_and_interventricular_septum": "Непрерывность восходящей аорты и межжелудочкового интервала",
    "ob_left_ventricular_outflow_tract": "Левый люк",
    "ob_spine": "Спинной позвоночник",
    "ob_lungs": "Лёгкие",
    "ob_four_chamber_view": "Четырехполостный сечение",
    "ob_extra_deduction_points_left_ventricular_outflow_tract": "Дополнительный балл в левом люке.",
    "ob_bilateral_lungs": "Двойное лёгкое",
    "ob_pinal_vertebrae": "позвоночник",
    "ob_at_least_one_pulmonary_vein_enters_the_left_atrium": "По крайней мере одна легочная вена в левую.",
    "ob_cross_shaped_endocardial_cushion": "Крест эндокардиальной подушки",
    "ob_foramen_ovale_valve": "Яйцевидный лепесток",
    "ob_four_chambers_of_the_heart": "Четыре полости сердца.",
    "ob_mandibular_bone_sagittal_plane": "Нижняя челюсть (векторная поверхность)",
    "ob_maxillary_bone_sagittal_plane": "Верхняя челюсть (векторная поверхность)",
    "ob_lower_jaw_sagittal_plane": "Нижняя челюсть (векторная поверхность)",
    "ob_lower_lip_sagittal_plane": "Нижняя губа (векторная поверхность)",
    "ob_upper_lip_sagittal_plane": "Верхняя губа (векторная поверхность)",
    "ob_frontal_bone_sagittal_plane": "Лобная кость (векторная поверхность)",
    "ob_nasal_bone_sagittal_plane": "Носовая кость (векторная поверхность)",
    "ob_nose_tip_sagittal_plane": "кончик носа (векторная поверхность)",
    "median_sagittal_plane_of_face_view": "Сциферический сечение",
    "ob_mandible": "Нижняя челюсть",
    "ob_chin": "Подбородок",
    "ob_maxilla": "Верхняя челюсть",
    "tip_of_the_nose": "кончик носа",
    "frontal_bone": "Лобная кость",
    "ob_lens": "Хрусталик",
    "ob_nasal_bone_and_maxillary_frontal_process": "Носовые кости и лобные отростки верхней челюсти",
    "eye_frame": "Окно",
    "ob_cross_section_of_binocular_sphere_view": "Биокулярный поперечный разрез",
    "nasal_bone": "Носовые кости",
    "bilateral_crystalline_lenses": "Двусторонний хрусталик",
    "ob_bilateral_eye_sockets": "Двусторонняя глазница",
    "ob_jaw": "Нижняя челюсть",
    "lower_lip": "Нижняя губа",
    "ob_coronal_section_nose_and_lip_view": "носогубный коронарный разрез",
    "upper_lip": "Верхняя губа",
    "ob_bilateral_nostrils": "Двусторонние ноздри",
    "ob_posterior_cranial_fossa_pool": "Задняя черепная ямка",
    "cerebellum": "Мозг",
    "ob_transverse_section_of_cerebellum_view": "поперечный сечение мозжечка",
    "ob_cisterna_magna": "Задняя черепная ямка",
    "ob_verebellar_hemisphere": "Мозговое полушарие",
    "ob_vermis_of_cerebellum": "Мозговые черви",
    "ob_ransverse_section_through_lateral_ventricle_view": "поперечный разрез бокового желудочка",
    "ob_educed_points_with_for_brain_foot": "Появление дополнительных очков на ногах мозга.",
    "ob_anterior_horn_of_lateral_ventricle": "Передний угол бокового желудочка",
    "ob_posterior_horn_of_lateral_ventricle": "Задний угол бокового желудочка",
    "ob_choroid_plexus": "Пульсовое сплетение",
    "ob_transverse_section_through_thalamus_view": "Переталамус",
    "ob_educed_points_with_cerebellum": "Появляется дополнительный отсек мозжечка.",
    "ob_image_quality_and_detail_desc": "Качество изображения (ясность, глубина подходит, структура посередине)",
    "ob_brain_middle": "Средняя линия мозга",
    "ob_skull_ring": "Кольцо черепа",
    "ob_cerebral_peduncle": "Ноги мозга",
    "ob_thalamus": "Таламус",
    "ob_sylvian_fissure": "Внешняя трещина мозга",
    "ob_transparent_compartment": "Прозрачный отсек",
    "statistic": {
        "user_online_avg": "Среднее время работы пользователя в сети",
        "answer_sheet_report": "Работа и оценка",
        "answer_sheet_avg": "Среднее значение операций и оценок",
        "device_report": "Статистика оборудования",
        "content_report": "Статистика содержания",
        "device_overview": "Общий обзор основной информации",
        "large_billboard": "Большой экран",
        "statistic_report": "Статистические отчеты",
        "statistic_data_title": "Статистика содержания",
        "maintenance_range": {
            "0": "Не обслуживается",
            "1": "Месяц",
            "2": "Два месяца",
            "3": "Три месяца",
            "6": "Шесть месяцев",
            "15": "0,5 месяца"
        },
        "deviceDetection": "Время обнаружения / сохранения оборудования",
        "QCStatistics": "Статистика контроля качества",
        "dr_distribution": "DR Контроль распределения мест",
        "adult": "Взрослые",
        "child": "Дети",
        "dr_exam_types": {
            "P07": "Нижние конечности",
            "P06": "таз",
            "P05": "брюшная полость",
            "P04": "Грудь",
            "P03": "Верхние конечности",
            "P02": "Спинной позвоночник",
            "P01": "Голова"
        },
        "technician": "Статистика техников",
        "quality_images": "Общее количество контролируемых изображений",
        "BI_DR_title": "Дистанционный центр обработки данных DR",
        "exam_create_image": "Количество созданных изображений",
        "exam_create_video": "Количество созданных видео",
        "device_search": "Запрос устройства",
        "active_level": "Уровень активности",
        "deviceFailureTip": "Подсказка устройства",
        "examCountOfDevice": "Создание контрольного числа (устройства)",
        "byDevice": "По устройству",
        "video_count": "Всего видео",
        "statistic_device_new_exam": "Создать контрольный отчет",
        "byUploader": "Нажмите на передачу.",
        "byExam": "Проверить по",
        "unbind": "Снять привязку",
        "doppler_user_report": "Статистика новых пользователей U - Link",
        "table_attendee_distribution": "Распределение участников",
        "statistic_paper": "Статистическая ведомость операций и экзаменов",
        "library_accumulated_views": "Совокупное количество просмотров",
        "region_txt": "Регионы",
        "branch_txt": "Филиал",
        "correction_teacher": "Изменить учителя",
        "assignment_name": "Название задания",
        "answer_sheet_report_title": "Статистическая ведомость операций и экзаменов",
        "answerSheetChartTitle": "Распределение персонала, представившего данные",
        "probeUsageNumber": "Количество использованных зондов",
        "owner_departments": "Секция",
        "data_detail_export": "Экспорт деталей",
        "time_range_tip_month": "Выберите меньше месяца",
        "probe": {
            "others": "Прочее",
            "S": "Монокристаллические кристаллы",
            "P": "Фазовая решетка",
            "L": "Линейная матрица",
            "C": "выпуклая решетка"
        },
        "probeUsageRate": "Доля использования зонда (%)",
        "min_tip": "Минимальное значение",
        "max_tip": "Максимальное значение",
        "not_support_map": "Карта не поддерживается.",
        "total": "Всего",
        "summary_user_count": "Количество пользователей",
        "doppler_offline_number": "В автономном режиме",
        "doppler_standby_number": "В режиме ожидания",
        "doppler_working_number": "На работе",
        "device_install_time_chart": "Статистика времени выпуска (станция)",
        "all_exam_types": {
            "0": "OB",
            "1": "Гинекология",
            "2": "Сердце",
            "3": "VAS",
            "4": "брюшная полость",
            "5": "URO",
            "6": "Малый орган",
            "7": "Педиатрия",
            "8": "Молочная железа",
            "9": "Неизвестно",
            "10": "Не определено",
            "11": "EM",
            "12": "Нервы"
        },
        "summary_exam_count_ingroup": "Количество проверок (группа)",
        "table_exam_type_count": "Тип проверки",
        "table_total_working_time": "Время работы",
        "table_total_startup_time": "Время загрузки",
        "group_joined_count": "Число входов в группу",
        "equipment_ownership": "Принадлежность оборудования",
        "using_departments": "Секция эксплуатации",
        "exam_type_count": "Тип проверки",
        "year_range": {
            "0": "Неизвестно",
            "1": "< 1 год",
            "2": "1 - 5 лет",
            "3": "6 - 10 лет",
            "4": "> 10 лет"
        },
        "exam_create": "Создать количество проверок",
        "device_exam_report": "Отчет о проверке оборудования",
        "BI_device_subtitle": "Просмотр оборудования",
        "chart_legend_other": "Прочее",
        "chart_legend_group": "Группа",
        "summary_org_count": "Число учреждений",
        "summary_image_count": "Количество изображений",
        "summary_exam_count": "Количество проверок",
        "statistic_device": "Статистика оборудования",
        "statistic_group": "Статистика данных",
        "examCountOfOrganization": "Создание числа проверок (учреждений)",
        "deviceStatus": "Состояние оборудования",
        "deviceUtilizationRate": "Коэффициент использования оборудования",
        "deviceInstallTime": "Время установки оборудования",
        "BI_title_dr_demo": "Демонстрация - Региональный центр изображений DR",
        "image_count": "Общее количество изображений",
        "minute_text": "Минуты",
        "sex_map": {
            "0": "Мужчины",
            "1": "Женщины",
            "2": "Неизвестно"
        },
        "table_library_like": "похвала",
        "table_library_view": "Количество просмотров",
        "table_library_title": "Заголовок",
        "table_iworks_finish_count": "Число завершенных сечений",
        "table_iworks_standard_count": "Количество сечений iWorks",
        "table_patient_sex": "Пол пациента",
        "table_patient_name": "Имя пациента",
        "table_iworks_name": "Название протокола iWorks",
        "table_send_ts": "Время отправки",
        "table_exam_id": "Проверить ID",
        "group_types_map": {
            "0": "Частная собственность",
            "1": "Открыто"
        },
        "increased_group_total": "Совокупные группы",
        "increased_group_avg": "Средние показатели по новым группам",
        "table_group_create_ts": "Время создания",
        "table_group_admin": "Владелец группы",
        "table_group_creator": "Создатель",
        "table_group_type": "Тип группы",
        "user_online_duration_report": "Длительность времени пользователя в сети",
        "user_online_duration": "Продолжительность работы пользователя в сети",
        "doppler_user_total": "Совокупное число пользователей u - Link",
        "doppler_user_avg": "Среднее число новых пользователей u - Link",
        "table_first_login_ulink": "Первое время использования Ulink",
        "device_active_number": "Количество активных ультразвуковых устройств",
        "device_utilization_rate_report": "Отчет об использовании ультразвукового оборудования",
        "device_utilization_rate": "Коэффициент использования ультразвукового оборудования",
        "device_status_report": "Отчет о состоянии ультразвукового оборудования",
        "device_status": "Состояние ультразвукового оборудования",
        "device_status_map": {
            "0": "В эксплуатации",
            "1": "В автономном режиме",
            "2": "В режиме ожидания",
            "3": "В эксплуатации"
        },
        "table_comment_count": "Количество замечаний",
        "table_tag_count": "Количество меток",
        "table_exam_type": "Тип проверки",
        "table_exam_date": "Дата проверки",
        "table_birthday": "Дата рождения",
        "table_offline_time": "Время выхода из сети",
        "table_standby_duration": "Продолжительность ожидания (минуты)",
        "table_last_standby_time": "Последнее время ожидания",
        "table_online_duration": "Продолжительность онлайн (минуты)",
        "table_last_startup_time": "Последнее время загрузки",
        "table_current_status": "Текущее состояние",
        "device_install_time_report": "Отчет о времени установки ультразвукового оборудования",
        "doppler_number": "Количество ультразвуковых устройств",
        "iworks_statistic_report": "Статистика протокола iWorks",
        "doppler_status": "Состояние оборудования",
        "doppler_utilization_rate": "Коэффициент использования оборудования",
        "doppler_install_time": "Статистика времени установки оборудования",
        "doppler_user": "Количество новых пользователей u - Link",
        "library_statistic_report": "Библиотечная статистика",
        "iworksCompliance": "Соблюдение правил поиска",
        "search_user_name": "Введите полный аккаунт.",
        "group_active_avg": "Средний показатель по активным группам",
        "table_video_count": "Количество отправленных видео",
        "other_exam_type": "Прочее",
        "device_install_time": "Время установки УЗИ",
        "table_install_ts": "Время установки",
        "table_product_type": "Тип продукции",
        "distance_training_number": "Количество учебных курсов",
        "remote_consultation_number": "Количество удаленных консультаций",
        "device_increased_report": "Отчет о дополнительном оборудовании",
        "total_device": "Накопленное оборудование",
        "device_increased_avg": "Средняя стоимость дополнительного оборудования",
        "device_increased": "Дополнительное оборудование",
        "devices_report": "Статистика оборудования",
        "device_types": {
            "doppler": "УЗИ ультразвуковое оборудование",
            "ulinker": "u-Linker",
            "isync": "Рабочая станция iSync"
        },
        "device_type_title": "Тип устройства",
        "breastAI_usage_report": "Отчет об использовании учителя",
        "breastAI_usage": "Учитель использует",
        "user_types": {
            "0": "Пробный пользователь",
            "1": "Официальные пользователи",
            "2": "Заявка на счет отклонена.",
            "3": "Счет запрещен.",
            "4": "Списано"
        },
        "time_txt": "Время",
        "total_user": "Совокупные пользователи",
        "user_active_avg": "Среднее число активных пользователей",
        "user_increased_avg": "Среднее число новых пользователей",
        "time_range_tip": "Выберите время меньше года",
        "table_product_name": "Название продукта",
        "table_series_number": "Серийный номер",
        "table_device_id": "Идентификатор устройства",
        "table_last_login_account": "Последний аккаунт",
        "table_hospital": "Больницы",
        "table_device_name": "Имя",
        "table_mac_addr": "MAC Адрес",
        "table_exam_count": "Количество отправленных проверок",
        "table_last_login_time": "Последнее время регистрации",
        "table_login_times": "Число посещений",
        "table_online_time": "Длительность онлайн (минуты)",
        "table_last_send_time": "Время последнего выпуска",
        "table_fail_count": "Количество неудач",
        "table_success_count": "Количество успехов",
        "table_image_count": "Количество отправленных изображений",
        "table_upload_account": "Счет загрузчика",
        "table_city": "Города",
        "table_province": "Провинции",
        "table_organization": "Учреждения",
        "table_email": "Почтовый ящик",
        "table_user_type": "Тип пользователя",
        "table_register_time": "Регистрация",
        "table_nickname": "Имя пользователя",
        "table_account": "Номер счета",
        "table_review_duration": "Продолжительность просмотра (минуты)",
        "table_review_times": "Количество просмотров",
        "table_live_duration": "Продолжительность прямой трансляции (минуты)",
        "table_attendee_count": "Число участников",
        "table_live_end": "Время окончания прямой трансляции",
        "table_live_start": "Время начала прямой трансляции",
        "table_live_type": "Тип прямой трансляции",
        "table_host": "Ведущий",
        "table_group_subject": "Название группы",
        "table_group_id": "Идентификатор группы",
        "table_subject": "Тема конференции",
        "table_index": "Серийный номер",
        "data_export": "Экспорт данных",
        "data_list": "Список данных",
        "distance_training": "Преподавательская подготовка",
        "remote_consultation": "Дистанционные консультации",
        "live_tip2": "Тип прямой трансляции = дистанционное обучение или отсутствие основного оборудования в прямом эфире (и основное время устройства менее 5 минут)",
        "live_tip1": "Тип прямой трансляции = дистанционная консультация или в прямом эфире с основным оборудованием (более 5 минут)",
        "live_attendee_count": "Количество участников в прямом эфире",
        "live_duration": "Продолжительность прямой трансляции (минуты)",
        "live_times": "Количество прямых трансляций",
        "search_btn": "Поиск",
        "time_map": {
            "1M": "Почти январь",
            "TY": "В этом году",
            "1Y": "Почти год",
            "6M": "Почти июнь",
            "3M": "Почти март"
        },
        "weeks": {
            "0": "Воскресенье",
            "1": "Понед.",
            "2": "Вторник",
            "3": "Среда",
            "4": "Четверг",
            "5": "Пятница",
            "6": "Суббота"
        },
        "ulinkerIncreasedUsers": "Количество новых пользователей U - Link",
        "end_date": "Дата окончания",
        "start_date": "Дата начала",
        "theme": "Тема",
        "online_statistic": "Статистика времени в Интернете",
        "library_statistic": "Библиотечная статистика",
        "iworks_statistic": "Статистика протокола iWorks",
        "exam_increased_report": "Дополнительные проверки",
        "device_manage": "Управление оборудованием",
        "increased_group_report": "Дополнительные групповые отчеты",
        "group_active_report": "Отчеты активных групп",
        "user_active_report": "Отчеты активных пользователей",
        "user_increased_report": "Дополнительные отчеты пользователей",
        "live_report": "Прямая трансляция отчетов",
        "exam_count": "Общее число проверок",
        "exam_distribution": "Общее распределение ультразвуковых исследований",
        "back_btn": "Возвращение",
        "device_distribution": "Распределение оборудования",
        "cancel_btn": "Отменить",
        "confirm_btn": "Определение",
        "device_related": "Оборудование",
        "iworks_protocol": "Протокол iWorks",
        "group_related": "Связь групп / сообществ",
        "content_related": "Содержание",
        "user_related": "Пользователи",
        "selected": "Выбран",
        "months": {
            "1": "Январь",
            "2": "Февраль",
            "3": "Март",
            "4": "Апрель",
            "5": "Май",
            "6": "Июнь",
            "7": "Июль",
            "8": "Август",
            "9": "Сентябрь",
            "10": "Октябрь",
            "11": "Ноябрь",
            "12": "Декабрь"
        },
        "show_type_map": {
            "0": "График",
            "1": "Столбцы"
        },
        "show_type": "Тип презентации",
        "search_group_name": "Введите имя группы.",
        "statistic_time": "Статистическое время",
        "BI_setting_title": "Настройка статистической панели BI",
        "iworksUseTimes": "Регулярное использование",
        "iworksUserCount": "Количество пользователей",
        "library": "Количество просмотров библиотеки",
        "breastAI": "Dr.M",
        "videoIncreased": "Количество дополнительных просмотров видео",
        "imageIncreased": "Количество дополнительных контрольных изображений",
        "examIncreased": "Дополнительные проверки",
        "dopplerTotal": "Общее число ультразвуковых приборов",
        "dopplerIncreased": "Количество новых ультразвуковых приборов",
        "ulinkerTotal": "Общее количество устройств U - Linker",
        "ulinkerIncreased": "Количество новых устройств U - Linker",
        "iSyncTotal": "Общее количество устройств iSync",
        "iSyncIncreased": "Количество новых устройств iSync",
        "liveDuration": "Продолжительность дистанционного консультирования и обучения (минуты)",
        "liveUserTimes": "Число удаленных консультаций и учебных занятий",
        "liveCount": "Количество удаленных консультаций и учебных площадок",
        "groupTotal": "Совокупное число групп",
        "groupIncreased": "Количество новых групп",
        "groupActive": "Количество активных групп",
        "userActive": "Активные пользователи",
        "userTotal": "Общее число пользователей",
        "userIncreased": "Новые пользователи",
        "BI_subtitle": "Просмотр данных",
        "BI_title_demo": "Демонстрация - Центр телеультразвуковой медицины",
        "BI_title": "Дистанционный ультразвуковой центр данных"
    },
    "data_too_old_to_locate": "Данные слишком длинные, чтобы найти исходное сообщение.",
    "unable_locate_original_message": "Невозможно найти исходное сообщение",
    "quoted_content_has_been_withdrawn": "Ссылка отозвана.",
    "quoted_content_has_been_deleted": "Ссылка была удалена.",
    "group_has_joined": "Вы присоединились к этой группе.",
    "scan_qr_code": "двухмерный код на опознавательной карте",
    "locate_original_message": "Оригинальное расположение",
    "share_to_conversation": "Поделиться на сеанс",
    "download_to_local": "Загрузить локально",
    "share_qrcode_to": "Поделиться QR - кодом",
    "quote_title": "Ссылки",
    "temporarily_store": "Временное хранение",
    "total_score": "Итого",
    "improvement_suggestions": "Рекомендации по улучшению",
    "analysis_result": "Результаты анализа",
    "comprehensive_evaluation": "Всеобъемлющая оценка",
    "ulinker_settings": "Параметры источника видео",
    "AI_analysis_results": "Результаты анализа ИИ",
    "answer_not_submitted_next_tips": "Текущая тема не дает ответа, переключается ли следующий вопрос?",
    "re_answer_cleared_tips": "Вы уверены, что хотите снова ответить? Текущие ответы будут пустыми.",
    "answer_not_submitted_leave_tips": "Текущая тема не представила ответа, подтверждается ли отъезд?",
    "evaluating": "Evaluating",
    "please_wait_ai_analysis": "В анализе ИИ, пожалуйста, позже...",
    "ai_analysis_encountered_retry_tips": "Анализ ИИ показал аномалию, попробуйте позже.",
    "answer_automatically_review_tips": "Ваш ответ был успешно представлен, ждите автоматического просмотра системы или нажмите на следующий вопрос, чтобы продолжить практику (продолжение практики не влияет на результаты обзора этого вопроса, результаты анализа можно узнать позже из истории ответов)",
    "please_answer_here": "Пожалуйста, ответьте здесь.",
    "used_time": "Время",
    "other_answers": "Другие ответы",
    "best_answer": "Лучший ответ",
    "my_answer": "Мой ответ.",
    "statistical_analysis": "statistical analysis",
    "answering_requirements": "Ответы на запросы",
    "video_materials": "Видеоматериалы",
    "completed_total": "completed",
    "answer_record": "Запись ответов",
    "professional_imaging_knowledge_tips": "Профессиональные знания изображения, быстрый и точный ответ",
    "you_could_say_tips": "Ты можешь так сказать.",
    "delete_apply_multicenter_tips": "Удалить ли многоцентровую заявку",
    "overdue_assignment_transmit_tip": "Текущая операция просрочена и не может быть передана",
    "password_rule_incorrect": "Правила шифрования неверны.",
    "PacsServerError": "Сервис Pacs недоступен",
    "uploading": "Загрузить...",
    "upload_success": "Удалось загрузить",
    "exam_overdue_creator_tip": "Срок действия текущего задания истек, изменить конфигурацию задания?",
    "student_org": "Студенческие организации",
    "single_chat": "Один разговор.",
    "not_group_chat": "Нет",
    "table_drag_to_reorder": "Таблица поддерживает перетаскивание",
    "edit_collection_group_options_tips": "При настройке группы необходимо одновременно установить требования к набору конфигурации группы, иначе группа будет недействительной.",
    "unsaved_item": "Не сохраненный элемент",
    "image_label": "Метки изображений",
    "homework": {
        "upload": {
            "error": {
                "noOptions": "{type} должен содержать хотя бы один вариант ответа",
                "invalidScore": "Оценка для {type} должна быть больше 0",
                "fieldRequired": "Поле {field} не может быть пустым",
                "maxColumns": "{sheetName} превышает максимальное количество столбцов",
                "maxRows": "{sheetName} превышает максимальное количество строк",
                "general": "Ошибка загрузки",
                "fileFormat": "Пожалуйста, загрузите файл Excel (.xlsx или .xls)",
                "fileSize": "Размер файла не может превышать 2MB"
            },
            "download_excel_template": "Скачать шаблон",
            "upload_excel": "Загрузить файл Excel"
        },
        "detail_requirements": "Подробные требования",
        "exam_not_exist": "Экзамена не существует.",
        "revoke": "Снятие",
        "revoke_exam_confirm": "После отмены, все сотрудники не смогут открыть или отправить это задание, пожалуйста, убедитесь?",
        "operation_min_subtopic": "Операционный вопрос должен содержать хотя бы один подвопрос",
        "progress": {
            "legend": {
                "graded": "Утверждено",
                "ungraded": "Не утверждено",
                "current": "В настоящее время",
                "alreadyDone": "Уже сделано",
                "notDone": "Не выполнено"
            },
            "completionStatus": "Выполнено {done} из {total} вопросов",
            "correctionHeader": "Ход работы по пересмотру",
            "answeringHeader": "Прогресс ответов"
        },
        "confirm_delete_topic": "Подтвердить удаление этой темы?",
        "delete_paper": "Удалить задание",
        "delete_failed ": "Ошибка удаления",
        "delete_success": "Удалить успешно",
        "confirm_delete_paper": "Подтвердить удаление этого документа?",
        "save_failed": "Ошибка сохранения",
        "save_success": "Сохранить успешно",
        "topic_answer_required": "Пожалуйста, укажите ответ на каждый вопрос.",
        "topic_score_required": "Установите баллы по каждой теме.",
        "exam_title_required": "Введите заголовок экзамена.",
        "reference_answer": "Справочный ответ",
        "exam_title_tip": "Название задания",
        "upload_requirements": {
            "item5": "Документы должны содержать не более 20 столбцов.",
            "item4": "Количество строк данных, загружаемых каждый раз, не должно превышать 10000.",
            "item3": "Размер файла не должен превышать 2 Мб",
            "item2": "Не размещайте данные в объединенных ячейках (примечание: в одной ячейке могут находиться несколько вариантов с несколькими темами)",
            "item1": "Расширение XLS или XLSX"
        },
        "preview_exam_content": "Предварительный просмотр вопросов"
    },
    "welcome_to_realm_imaging_intelligence": "Добро пожаловать в мир медицинской мудрости!",
    "leave_ai": "Оставь ИИ",
    "expand_menu": "Развернуть меню",
    "empty_title": "Пустой заголовок",
    "empty_nodes": "Пустой узел",
    "empty_group_options": "Опции пустых групп",
    "collection_requirements": "Требования к сбору",
    "integrity_verified_submit_cases": "Проверка полноты при подаче дела",
    "basic_configuration": "Базовая конфигурация",
    "delete_apply_create_multicenter": "Удалить запись заявки на создание мультицентра",
    "error": {
        "paperAssignmentNoAuth": "Эта операция уже существует в целевой группе. Свяжитесь с администратором группы, чтобы изменить конфигурацию задания,",
        "assignmentNotBelongToUser": "У вас нет разрешения на доступ к этому заданию, так как оно не принадлежит вашей учетной записи.",
        "paperAssignmentHasBeenRevoked": "Операция была отменена.",
        "paperAnswerSheetNotExist": "Операция вышла из строя.",
        "paperAnswerSheetLocked": "Работа проверяется другими.",
        "paperAssignmentDuplication": "Эта группа уже организовала это задание, не повторяйте его",
        "paperSubmitDueTimeError": "Текущая операция не может быть представлена после истечения срока",
        "codeAfsError": "Существуют риски безопасности для текущего компьютера, попробуйте позже",
        "patientExternalSame": "Многоцентровый идентификатор пациента повторяется",
        "timestampError": "Текущее местное время не откалибровано",
        "userIsLiving": "Добавл. прямые трансляции на других устр.",
        "liveStatusError": "Прямая трансляция началась или закончилась и не работает",
        "livingCreating": "Подготовка прямой трансляции, повторите попытку позже",
        "userNeedSafeAuthToken": "Для этой операции требуется аутентификация",
        "userCanNotBindAccount": "Не требуется привязывать мобильный телефон или электронную почту. Войдите в систему и выполните привязку еще раз",
        "codeNotSupportSelfNet": "Внутр. сеть не поддерживает функцию проверочного кода, обратитесь к администратору",
        "paramsError": "Ошибка параметра",
        "userEmailInvalid": "Недейст. эл. почта",
        "codeMustRequired": "Период для входа истек, войдите повторно",
        "userLoginNameInvalidate": "Имя учетной записи может содержать только буквы, цифры и символы подчеркивания и не может состоять только из цифр.",
        "userPasswordLockError": "Введите неверный пароль более 5 раз подряд, и учетная запись заблокирована. Повторите попытку через 5 минут",
        "userReferralCodeError": "Ошибка реферального кода",
        "userEmailError": "Неверный адрес эл. почты",
        "userMobileError": "Неверный номер телефона",
        "userPwdEnhanceError": "Уровень безопасности пароля недостаточен. Он должен содержать как прописные, так и строчные буквы, цифры и специальные символы",
        "userLoginNameRegisted": "Учетная запись зарегистрирована.",
        "userMobileRegisted": "Мобильный телефон зарегистрирован.",
        "userEmailHasRegisted": "Адрес электронной почты зарегистрирован.",
        "codeError": "Ошибка проверочного кода",
        "codeSendError": "Сбой отправки проверочного кода",
        "codeCountLimitError": "Отправленный сегодня проверочный код превысил лимит",
        "codeTimerLimitError": "Проверочный код будет отправлен в течение 1 минуты. Не отправляйте его повторно",
        "imageCodeError": "Ошибка графического проверочного кода",
        "LimitMobileLogin": "Эта функция ограничена механизмом обеспечения безопасности компании и недоступна во внутренней сети компании.",
        "userWaitVerification": "Учетная запись ожидает проверки администратором",
        "userStatusFail": "Учетная запись заблокирована, обратитесь к администратору",
        "userPasswordError": "Неверный пароль или имя учетной записи",
        "userLoginNameError": "Уч. запись не существует",
        "operateFrequenceError": "Операция выполняется слишком часто, повторите попытку позже",
        "userLoginBusy": "Много запр.вход",
        "deviceInfoNotFound": "Свед. об устройстве не найдены",
        "deviceNotFound": "Устройство не найдено",
        "multiCenterExamStatusError": "Текущее состояние не позволяет выполнить эту операцию",
        "multiCenterHadCollector": "Этот многоцентровой проект для единственного заказчика",
        "multiCenterLackReviewer": "Количество проверяющих меньше 2; они не могут быть назначены автоматически"
    },
    "standardization_rate": "Нормативная ставка",
    "integrity_rate": "Коэффициент полноты",
    "jump_external_links_tips": "Предстоящий переход к браузеру для доступа к внешним ссылкам, обратите внимание на распознавание рисков",
    "switch_app_client_to_workstation_tip": "Выберите рабочую станцию, вам нужно подать заявку на код лицензии и установить драйвер карты сбора, будьте осторожны в выборе",
    "share_paper_to": "Поделиться вопросами для:",
    "average_duration": "Среднее время",
    "cardiovascular": "Cardiovascular",
    "human_body_diagram": "Карта человека",
    "welcome_clinical_thinking_exercise_tips": "Добро пожаловать в практику ультразвукового клинического мышления, где вы можете щелкнуть рандомизированные упражнения или выбрать любую часть ниже, чтобы начать свое путешествие по практике.",
    "random_exercise": "Случайные упражнения",
    "total_completion_count": "Total count",
    "re_answer": "Повторить ответ",
    "recent_submission_time": "Дата последнего представления",
    "completion_times": "Количество выполненных работ",
    "topic_types": "Тип темы",
    "exercises_my_completed_title": "Я выполнил упражнение.",
    "clinical_thinking_practice_title": "AI Practice",
    "ai_welcome_tips": "I am DR AI. Nice to meet you!",
    "answering": "Ответ.",
    "thinking": "В мыслях",
    "historical_records": "История",
    "screen_shot": "Снимок экрана",
    "sorry_an_error_occurred_please_try_again": "Извините, ошибка произошла, пожалуйста, попробуйте еще раз.",
    "server_request_exception_tips": "Сервер запрашивает аномалию, попробуйте позже",
    "user_has_terminated_answering": "Пользователь прекратил отвечать.",
    "input_talk_about_tips": "Скажи что - нибудь...",
    "input_talk_about_tips_Shift_Enter": "Профессиональные знания изображения, быстрый и точный ответ (Shift + Enter = Перемещение строк)",
    "new_chat": "Создать чат",
    "deep_thinking": "DeepThink",
    "ai_chat": "Разговор ИИ",
    "select_video_window_to_be_recorded": "Выберите окно видео, которое нужно записать",
    "detailed_mode": "Подробный режим",
    "simplified_mode": "Упрощенная модель",
    "mode_select": "Выбор режима",
    "edit_collection_group": "Группа редактирования",
    "collection_group": "Группа",
    "collection_category": "Категория сбора",
    "please_select_group_title": "Выберите группу",
    "add_collection_group": "Добавить пользовательские группы",
    "formatDateTime": {
        "yyyy-MM-dd HH:mm:ss": "Year-Month-Day-Hour-Minute-Second",
        "yyyy-MM-dd HH:mm": "Year-Month-Day-Hour-Minute",
        "yyyy-MM-dd HH": "Year-Month-Day-Hour",
        "yyyy-MM-dd": "Year-Month-Day",
        "yyyy-MM": "Year-Month",
        "yyyy": "Year"
    },
    "please_fill_field_name_tips": "Заполните имя поля",
    "number_options_greater_2": "Количество вариантов должно быть больше двух.",
    "empty_element_title_tips": "Имена полей, которые в настоящее время пусты, должны быть улучшены или удалены",
    "new_options_default_selection": "Параметры галочки будут выбраны по умолчанию, а без галочки - по умолчанию.",
    "not_required_filed": "Необязательно заполнять",
    "required_filed": "Обязательно",
    "option_name": "Имя опции",
    "edit_options": "Изменить параметры",
    "date_time": "Date time",
    "check_box": "Checkbox",
    "radio": "Radio",
    "supply_exam_image": {
        "err_tip": {
            "select_up_to_number_files": "Выберите до {number} файлов.",
            "unsupported_image_format": "Формат изображения не поддерживается, поддерживается только JPG!",
            "many_tasks_have_failed_tips": " зад. — сбой. Повторите попытку позже",
            "select_up_to_500_files": "Выберите до 500 файлов.",
            "not_exam_sender": "Вы не являетесь отправителем этого исследования, невозможно импортировать изображение в это исследование!",
            "import_exam_image_succ": "Изображение успешно импортировано!",
            "import_exam_image_failed": "Сбой импорта файла!",
            "upload_file_failed": "Сбой выгрузки файла!",
            "invalid_exam_id": "Недопустимый ID исслед.!",
            "invalid_exam_path": "Недопустимый путь к исследованию!",
            "unsupported_file_format": "Формат файла не поддерживается",
            "more_than_one_file": "Только один из трех файлов можно импортировать одномоментно!",
            "no_files": "Выберите хотя бы один файл!"
        }
    },
    "only_training_tips": " (только для обучения, не использовать для диагностики)",
    "dopplerControl": {
        "Disconnect": "Отключить",
        "UPDATE": "Update",
        "SAVEIMG": "Save",
        "CALIPER": "Caliper",
        "MEASURE": "Measure",
        "PWMode": "PW Mode",
        "MMode": "M Mode",
        "BMode": "B Mode",
        "CMode": "C Mode",
        "ImageQuality": "Качество изобр.",
        "Freeze": "Стоп-кадр",
        "DuplexOrTriplex": "Дуплекс/Триплекс",
        "Tint Map": "Карта оттен.",
        "Quick Angle": "Быстрый угол",
        "SV": "SV",
        "Angle": "Угол",
        "Unfreeze": "Отм. с-кадра",
        "SwitchMode": "Перекл. режима",
        "FlowOnly": "FlowOnly",
        "StereoFlowSwitch": "Glazing Flow",
        "WF": "WF",
        "GlazingFlow": "GlazingFlow",
        "Packer Size": "Размер пакета",
        "FineSteer": "Точн.отклон.",
        "Steer": "Отклон.",
        "Scale": "Шкала",
        "Baseline": "Баз. линия",
        "iClear+": "iClear+",
        "iClear": "iClear",
        "HDscope": "HDscope",
        "iBeam": "iBeam",
        "iOneTouch": "iOneTouch",
        "iTouch": "iTouch",
        "Depth": "Глубина",
        "Zoom": "Масшт.",
        "Dyn Ra": "Дин.диап.",
        "Gray Map": "Карта серого",
        "UMA": "UMA",
        "Persistence": "Персистенция",
        "Gain": "Усил.",
        "Smooth": "Гладк."
    },
    "doppler_controlling_device_tips": "(only for training and teaching purposes, do not use for diagnosis)",
    "doppler_controlling_device_name": "Controlling the device of ${name} ",
    "effect_after_restart": "Вступление в силу после перезагрузки",
    "white_board": "Белая доска",
    "whiteboard": {
        "more": "Больше.",
        "large": "Крупные",
        "normal": "Нормально.",
        "small": "Маленький",
        "mini": "Мини",
        "thin": "Тонкая форма",
        "thick": "Толстый",
        "close": "Закрыть",
        "clear": "Очистить",
        "laserPointer": "Лазерный индикатор",
        "shape": "График",
        "eraser": "Ластик",
        "text": "Текст",
        "pencil": "Картина",
        "selector": "Выбор",
        "clicker": "Нажмите"
    },
    "userBindPacsAccountError": "Ошибка проверки учетной записи Pacs",
    "video_type_proportion": "Количество типов видео",
    "image_type_proportion": "Доля типов изображений",
    "relieve": "Снять",
    "unbind_description": "Вы уверены, что хотите удалить учетную запись, связанную с облачным PACS?",
    "relation": "Связь",
    "bind_description": "Текущая учетная запись не привязана к облачной учетной записи PACS и не может быть запущена, сначала свяжите привязанную учетную запись и попробуйте снова",
    "unbind_tip": "Счета не связаны и не могут быть активированы",
    "account_association": "Связанные счета",
    "remove_association": "Удаление связи",
    "information_system": "Информационная система",
    "associated": "Связаны",
    "visit": "Поездки",
    "cloud_platform": "Облачная платформа",
    "my_application": "Мое приложение.",
    "scan_to_download": "Скачать бесплатно",
    "email_login": "Почтовый ящик",
    "mobile_login": "Мобильный вход",
    "ecology_welcome": "Добро пожаловать в Эрин Эко.",
    "scan_login_title": "Скачать MiCo + App",
    "qrcode_login": "Регистрация с помощью QR - кода",
    "password_login": "Пароль для входа",
    "verification_login": "Код проверки",
    "choose_language": "Выберите регион и язык",
    "ecology_title": "Специалист по цифровым услугам медицинских изображений",
    "cardiac_parasternal_short_axis_psax": "Parasternal short-axis View",
    "Cardiac_Smart": "Умное сердце",
    "unsave_tip": "Выход потеряет текущий заполненный контент, будет ли сохранен?",
    "web_live_name_support_tips": "Поддерживаются только буквы, цифры, китайские символы, пробелы, подчеркивания и горизонтальные шесты",
    "LOCAL_TRACK_ERROR": {
        "NOT_SUPPORTED": "Текущий браузер не поддерживает эту операцию",
        "MEDIA_OPTION_INVALID": "Текущее устройство не поддерживает разрешение или частоту кадров",
        "DEVICE_NOT_FOUND": "Если устройство сбора не найдено, проверьте, правильно ли оно подключено или занято другими системными приложениями.",
        "PERMISSION_DENIED": "В настоящее время не предоставлены права на открытие устройства, попробуйте снова после авторизации",
        "MICROPHONE_PERMISSION_DENIED": "В настоящее время разрешения на микрофон не предоставлены, попробуйте снова после авторизации",
        "CAMERA_PERMISSION_DENIED": "В настоящее время нет разрешений на камеры, пожалуйста, попробуйте снова после разрешения",
        "CONSTRAINT_NOT_SATISFIED": "Браузер не поддерживает указанные параметры сбора",
        "SHARE_AUDIO_NOT_ALLOWED": "Поделиться аудио на экране без галочки Поделиться аудио",
        "OTHERS": "Устройство ненормально, попробуйте снова после проверки",
        "NOT_READABLE": "Текущее устройство не открывается и может быть занято"
    },
    "cloud_record": "Запись в облако",
    "account_sync_completed_tips": "Синхронизация счетов завершена",
    "notify_linker_sync_account_tips": "Поскольку текущее устройство привязано к устройству u-Linker, устройство u-Linker будет уведомлено об активном изменении учетной записи",
    "notify_linker_start_live_tips": "Поскольку текущее устройство привязано к устройству u-Linker, устройство u-Linker будет уведомлено о запуске прямой трансляции",
    "cardiac_left_cardiac_ventricle": "LV",
    "cardiac_right_cardiac_ventricle": "RV",
    "cardiac_right_cardiac_atrium": "RA",
    "U-Linker_agree_binding": "u-Linker согласился, привязка удалась",
    "cardiac_left_cardiac_atrium": "LA",
    "microphone_detection_tips": "Примечание: При обнаружении микрофона приостанавливается обмен микрофоном",
    "camera_preview_tips": "Примечание: Предварительный просмотр камеры приостанавливает обмен камерой",
    "cardiac_parasternal_left_ventricular_long_axis": "Parasternal long-axis View",
    "cardiac_parasternal_short_axis_psax_apex": "PSAX-Apex",
    "cardiac_parasternal_short_axis_psax_pm": "PSAX-PM",
    "cardiac_parasternal_short_axis_psax_mv": "PSAX-MV",
    "cardiac_parasternal_short_axis": "Короткая ось у грудины",
    "cardiac_aortic_arch_view": "Aortic Arch Long- Axis View",
    "cardiac_long_axis_inferior_vena_cava_below_xiphoid_process": "Subcostal IVC View",
    "cardiac_cross_four_chambers_below_xiphoid_process": "Subcostal 4 chamber View",
    "cardiac_five_chambers_apex_heart": "Apical 5 chamber View",
    "cardiac_four_chambers_apex_heart": "Apical 4 chamber View",
    "cardiac_three_chambers_apex_heart": "Apical 3 chamber View",
    "cardiac_two_chambers_apex_heart": "Apical 2 chamber View",
    "cardiac_short_axis_major_artery": "Parasternal short-axis aortic valve View",
    "parasternal_left_ventricular_long_axis": "Parasternal long-axis View",
    "cardiac_papillary_muscle": "PM",
    "cardiac_aortic_arch": "ARCH",
    "cardiac_ascending_aorta": "ASC",
    "cardiac_hepatic_vein": "HV",
    "cardiac_inferior_vena_cava": "IVC",
    "cardiac_atrial_septum": "IAS",
    "cardiac_interventricular_septum": "IVS",
    "cardiac_pulmonary_aortic_valve": "PV",
    "cardiac_pulmonary_aorta": "PA",
    "cardiac_tricuspid_valve": "TV",
    "cardiac_right_atrium": "RA",
    "cardiac_descending_aorta": "DA",
    "cardiac_mitral_valve": "MV",
    "cardiac_aorta": "AO",
    "cardiac_left_atrium": "RA",
    "cardiac_left_ventricle": "LV",
    "right_ventricular_outflow_tract": "RVOT",
    "unbind": "Освободите.",
    "fault_description": "Описание неисправности",
    "fault_code": "Код отказа",
    "fault_time": "Время отказа",
    "historical_faults": "Исторический сбой",
    "received_binding_from_pc_tips": "Получен запрос на привязку от устройства PC, согласен ли",
    "bind_ultrasound_device_title": "СВУ ультразвуковое толкающее устройство",
    "binding": "Привязка",
    "instruction_sent": "Команда отправлена.",
    "processed": "Обработано",
    "unprocessed": "Не обработано",
    "exam_duration": "Проверка занимает много времени (секунд)",
    "video_number": "Количество видео",
    "image_mode": "Режим изображения",
    "network_camera_setting": "Параметры источника видео",
    "exam_type_proportion": "Процент видов проверок",
    "latest_reporting_time": "Дата последнего отчета",
    "device_model": "Тип оборудования",
    "groupQrcodeExpire": "Срок действия QR-кода истек.",
    "latest_version": "Последняя версия",
    "desktop_text": "Рабочий стол",
    "upgrade_input_method": "Обновление ввода",
    "reapply_btn": "Повторное заявление",
    "withdrawn": "Снято",
    "existing_items": "Существующий пункт",
    "new_options": "Новые параметры",
    "preview": "Предпросмотр",
    "delete_item_tips": "Удалить подпункт",
    "delete_category_contents_tips": "Удалить эту категорию и все содержимое в ней",
    "adding_child_nodes_tips": "Текущий уровень достиг или превысил уровень 5 и не позволяет продолжать добавлять подузлы.",
    "empty_categories_submitted_tips": "Проверьте, не допускаются пустые категории в представленных данных",
    "add_standard_library_fields": "Добавить поле сбора стандартной библиотеки",
    "add_custom_collection_fields": "Добавить собственное поле сбора",
    "add_subcategories": "Добавить подкатегорию",
    "add_same_level_category": "Добавить эквивалентную категорию",
    "downgrade_action": "Снижение",
    "upgrade_action": "Повышение",
    "move_action": "Переместить",
    "homework_search_key": "Название экзамена или автор",
    "step_tip": "Шаги",
    "score_integer_tip": "Очки должны быть целыми.",
    "deadline_exceeded_tip": "Время истекло.",
    "homework_overdue_tip": "Задание вышло за предельный срок и не может быть просмотрено",
    "homework_correcting_tip": "Работа находится в процессе проверки, проверить ее невозможно",
    "homework_cancheck_tip": "Параметры задания не позволяют просматривать детали и не могут быть просмотрены.",
    "publish_at": "Опубликовано в {location}",
    "homework_type3": "CARD",
    "homework_type1": "ABD",
    "homework_type2": "Superf.",
    "homework_type4": "OB",
    "homework_type5": "Comp.",
    "device_binding_success_and_more_info": "Устройство успешно входит в группу, для просмотра информации о работе устройства откройте переключатель прав доступа в Doppler End - Prevent - Network - Управление устройством",
    "whether_pull_xxx_equipment_into_group": "Добавляет ли устройство '${a}' в групповой разговор:",
    "copy_link": "Копировать ссылку",
    "submit_paper_tip": "Вы завершили тест по пути {a}, в общей сложности тест по пути {b}, подаете ли вы ответ?",
    "correct_paper_tip": "Текущий общий балл {a}, представлены ли результаты проверки?",
    "question_score": "Значение данной темы",
    "correction_progress": "Ход работы по пересмотру",
    "zero_point": "Нулевой",
    "next_question": "Следующий вопрос",
    "prev_question": "Предыдущий вопрос",
    "score_range": "${a}- ${b} мин.",
    "score_less": "${a} ниже",
    "avg_point": "Средний балл",
    "min_point": "Минимум",
    "max_point": "Максимальный балл",
    "point_tip": "Б",
    "grades_distribution": "Распределение успеваемости учащихся",
    "unfinished_number": "Число незавершенных дел",
    "complete_number": "Число выполненных заданий",
    "to_be_complete_number": "Число лиц, подлежащих выполнению",
    "correction_teacher_empty_tip": "Учителя не могут быть пустыми.",
    "deadline_empty_tip": "Крайний срок не может быть пустым",
    "allow_view_correction": "Разрешить просмотр деталей",
    "set_homework": "Настройка заданий",
    "select_homework": "Выбор темы",
    "assign_homework_to": "Установить задание",
    "error_tip": "Ошибка",
    "correct_tip": "Правильно.",
    "submitted_answers": "Представленные ответы",
    "homework_operation_step": "Шаг подсказки: <br/>1, пожалуйста, используйте ультразвуковое устройство, откройте u - Link и сканируйте код для входа в систему. <br/>2、 Нажмите, чтобы начать сбор, сохранить диаграмму по требованию и отправить сохраненное изображение « помощнику по передаче файлов».",
    "stop_collecting": "Остановить сбор",
    "start_collecting": "Начать сбор",
    "correction_time": "Время проверки",
    "correction": "Исправление",
    "student_name": "Имя студента",
    "not_submitted": "Не представлено",
    "corrected": "Исправлено",
    "pending_correction": "Подлежит пересмотру",
    "feedback_time": "Средняя продолжительность обратной связи учащихся",
    "correcting": "Исправлено",
    "assign_homework": "Установка работ",
    "share_paper": "Поделиться темой",
    "submission_time": "Дата представления",
    "arrange_time": "Время установки",
    "assign_people": "Установить людей",
    "enter_correction": "Введите исправление",
    "creation_time": "Время создания",
    "author": "Автор",
    "paper_results": "Достижения",
    "paper_duration": "Рабочее время",
    "paper_statistics": "Статистика",
    "complete_again": "Повторное завершение",
    "release_time": "Время публикации",
    "deadline": "Крайний срок",
    "paper_question_count": "Количество вопросов",
    "paper_total_score": "Общий балл экзаменационных работ",
    "view_homework": "Входящие операции",
    "topic_type": {
        "operation": "Практическая задача",
        "shortAnswer": "Краткий ответ",
        "multiSelect": "Множественные задачи",
        "singleSelect": "Отдельные темы"
    },
    "exam_incomplete": "Ожидающие завершения",
    "sorry_no_post_dat_with_tag": "Прошу прощения, но нет соответствующего содержания.",
    "my_cloud_exam": "Моя облачная работа.",
    "famous_teacher_post": "Expert Lectures",
    "vetus_club_post": "Vetus Club",
    "functional_technology_post": "Technologies",
    "medical_workers_home_post": "Customer Service",
    "explosive_product_post": "Products",
    "operation_guidance": "Operation Guide",
    "clinical_beauty_map_post": "Image Gallery",
    "new_hot_post": "What's New",
    "certificate_agree_tips": "Доступ к текущему сервису может быть ограничен из - за сертификата, следует ли доверять сертификату",
    "live_expired_tips": "Текущая трансляция просрочена, пожалуйста, присоединяйтесь снова",
    "re_analyze_tips": "Повторный анализ будет охватывать первоначальные результаты анализа, пожалуйста, убедитесь, что он продолжается?",
    "re_analyze_title": "Повторный анализ",
    "reason_for_deficiency_text": "Причины недостаточного:",
    "exam_bank": "Моя библиотека.",
    "arranged_exam": "Я всё устроил.",
    "topic_count": "Тема {a}",
    "topic_summary": "Вопросы этого класса состоят из {a} подтем, всего {b}",
    "correcting_exam": "Исправление операций",
    "exam_completed": "Завершено",
    "cloud_exam": "Облачные операции",
    "input_your_name": "Введите ваше имя",
    "image_tag_unique_tip": "Метки изображений могут быть настроены только для одной группы",
    "verify_expired": "Идентификация истекла, перезапустите проверку",
    "multicenter_release_tip": "Определена ли текущая конфигурация публикации?",
    "retry": "Повторить попытку",
    "ai_doppler_layout_list_error": "Ошибка загрузки данных",
    "not_filled": "Не заполнено",
    "apply_time": "Время подачи заявки",
    "apply_name": "Имя заявителя",
    "apply_account": "Номер счета заявителя",
    "enter_age_wrong": "Пожалуйста, введите правильный возраст.",
    "standard_library": "Стандартная библиотека",
    "add_category": "Добавить категорию",
    "unit": "Единицы измерения",
    "filed_name": "Название поля",
    "add_field": "Добавить поле",
    "case_submitting_tip": "После того, как дело будет подано, оно будет включено в многоцентровый список, проверяющий может рассмотреть его, и вы больше не сможете удалить этот случай, пожалуйста, подтвердите, что он был подан?",
    "not_filled_tip": "Следующие поля не заполнены",
    "multicenter_collection_progress": "Ход сбора данных",
    "multicenter_collected": "Собрано",
    "multicenter_reject_tip": "Дополнительные случаи отклонены.",
    "enter_correct_number": "Введите правильное значение.",
    "please_select_end_time": "Выберите время окончания",
    "domain_verify": "Ошибка проверки текущего доменного имени, невозможно подключиться к серверу",
    "setting_management": "Управление конфигурацией",
    "uploader_nickname": "Имя загрузчика",
    "uploader_login_name": "Счет загрузчика",
    "please_input_login_name": "Пользовательский аккаунт",
    "data_detail_export": "Экспорт деталей",
    "Abdomen_smart_abd": "Умный живот",
    "multicenter_approve": "Многоцентровое утверждение",
    "start_ai_live_analyze": "Начать анализ в реальном времени",
    "apply_btn": "Заявления",
    "required_tip": "Обязательное заполнение несовершенно.",
    "multicenter_remarks": "Примечания",
    "multicenter_materials": "Проектно - программные материалы",
    "multicenter_unit_count": "Предполагаемое число участвующих подразделений",
    "multicenter_case_count": "Предполагаемое число собранных случаев",
    "multicenter_project_cycle": "Проектный цикл",
    "PI_unit_name": "Название единицы PI",
    "multicenter_name": "Многоцентровое имя",
    "withdrawal_of_application": "Отзыв заявления",
    "multicenter_release": "Опубликовано",
    "multicenter_config_tip": "Настройка требований к сбору",
    "multicenter_approval": "Утверждение системы",
    "applying_tip": "Создание заявки",
    "pending_approval_tip": "Ваша многоцентровая заявка находится на рассмотрении системного администратора.",
    "to_be_configured": "Настроить",
    "pending_approval": "Подлежит утверждению",
    "myocardial_strain_multicenter": "Многоцентровый стресс сердечной мышцы",
    "create_multicenter": "Создание многоцентровых",
    "view_groupset_statistics_report": "Посмотреть статистику сообщества",
    "view_groupset_bi_data_display": "Показать данные сообщества BI",
    "groupset_admin_abilities_tips": "Администратор сообщества может обладать следующими способностями",
    "my_manage_groupset": "Сообщества, которыми я управляю",
    "my_created_groupset": "Сообщества, которые я создал.",
    "auth_mng": "Полномочия",
    "library": {
        "tag": "Метки",
        "category": "Категория"
    },
    "check_filter_result_of_post": "Просмотреть содержимое ${count}",
    "total_count_of_post": "Всего ${name} Контент",
    "is_optional": "Необязательно",
    "ExamBelongMultiCenterError": "Информация о случаях была передана в несколько центров, и операция удаления невозможна.",
    "batch_cancel_download": "Массовая отмена загрузки",
    "batch_continue_downloading": "Серия продолжает загружаться",
    "pause_download": "Приостановить загрузку",
    "paused": "Приостановлено",
    "delete_message_by_other": "Удалить файловый ресурс",
    "delete_message_by_self": "Вы удалили файловый ресурс",
    "device_pixel_Ratio_change_tips": "Обнаружены изменения в разрешении экрана, могут возникнуть проблемы с адаптацией страницы, будет ли щетка немедленно для нового приложения?",
    "delete_case_fail": "Ошибка удаления случаев",
    "delete_case_success": "Удалить случай успешно",
    "reselect_view_type_anaylze_tips": "Совет: Система будет повторно анализироваться в соответствии с указанным типом сечения и обновлять информацию об оценке сечения.",
    "go_download": "Загрузить",
    "reselect_view_type_tips": "Определить сечение повторного выбора:  ${name}  для повторного анализа?",
    "reselect_view_type_anaylze_success": "Перевыбрать профиль: ${name} Анализ удался, общая оценка обновлена",
    "reselect_view_type_anaylze_fail": "Перевыбрать профиль: ${name} Ошибка анализа",
    "send_by_ctrl_enter": "Нажмите Ctrl+Enter для отправки",
    "delete_exam_tips": "Удалить ли эту проверку и все данные под ней?",
    "multi_center_patient_id_empty": "Многоцентровый идентификатор пациента не может быть пустым.",
    "feedback_success_tips": "Ваш вопрос был получен, большое спасибо за ваши отзывы.",
    "save_txt": "Сохр.",
    "AiAnalyzeFailError": "Ошибка анализа",
    "AiAnalyzeError": "Ошибки анализа.",
    "exam_mode": "Режим исслед.",
    "View_database_tables": "Просмотреть таблицу базы данных",
    "jump_external_browser_tips": "Перейти во внешний браузер для загрузки файла",
    "cancel_tasks_ask": "Отменить незавершенную задачу?",
    "open_directory_failed_tips": "Ошибка открытия каталога",
    "continue_downloading_ask": "Продолжить загрузку незавершенных задач?",
    "cancel_download": "Отменить загрузку",
    "feedback_date_range": "Время обратной связи",
    "feedbacker": "Обратная связь",
    "continue_downloading": "Продолжить загрузку",
    "save_directory": "Сохранить каталог",
    "download_task_manager": "Загрузить диспетчер задач",
    "reselect_view_type": "Перевыбрать тип сечения",
    "please_describe_your_issue": "Опишите свой вопрос:",
    "question_feedback": "Обратная связь",
    "failed": "Неудача",
    "success": "Успех",
    "expiration_date": "Срок годности",
    "delete_case_confirm": "Подтвердить удаление случаев?",
    "delete_case": "Удалить случай",
    "patientExternalEditNotAllowed": "Лица, не являющиеся носителями, владельцами групп или администраторами групп, не имеют права редактировать многоцентровые идентификаторы пациентов",
    "big_files_tips": "Обратите внимание, что текущий документ больше {limitFileSize}M Срок действия истекает через день после загрузки {expiredays}",
    "display_exam_picture_with_struct": "Показать структуру",
    "AiServerConnectError": "Ошибка подключения к серверу AI",
    "multi_center_patient_id_too_long": "Количество идентификаторов пациентов не должно превышать 64.",
    "multi_center_patient_id": "Многоцентровый идентификатор пациента",
    "system_administrator": "Системный администратор",
    "group_view_summary": "Общая оценка группы разрезов",
    "not_friend_tips": "Вы еще не друзья. Пожалуйста, добавьте друзей и сделайте это позже.",
    "ABD_Struct_Convergence_Lower_Cavity": "Нижняя вена",
    "ABD_Struct_Right_Hepatic_Vein": "Правая вена печени",
    "ABD_Struct_Left_Hepatic_Vein": "Левая вена печени",
    "ABD_Struct_Middle_Hepatic_Vein": "Средняя печеночная вена",
    "ABD_Struct_Pancreas": "Поджелудочная железа",
    "ABD_Struct_left_External_Superior_Branch": "Вверхушка левой вены",
    "ABD_Struct_Left_Inferior_External_Branch": "Нижняя левая вена",
    "ABD_Struct_Left_Lateral_Branch": "Левый горизонт воротной вены",
    "ABD_Struct_Left_Medial_Branch": "Внутренняя левая ветвь воротной вены",
    "ABD_Struct_Sagittal_Portal_Vein": "Левый вектор воротной вены",
    "ABD_Struct_Corner": "Вход правый",
    "ABD_Struct_Long_Aaxis_Inferior_Vena_Cava": "Нижняя вена",
    "ABD_Struct_Long_Axis_Abdominal_Aorta": "брюшная аорта",
    "ABD_Struct_Left_Lobe": "Печень",
    "ABD_Struct_Caudal_Lobe_Liver": "Гепатохвост",
    "ABD_Struct_Gallbladder": "Желчный пузырь",
    "ABD_Struct_Right_Lobe": "Печень",
    "ABD_Struct_Kidney": "Почки",
    "ABD_Struct_Portal_Vein": "Входная вена",
    "ABD_UNDEFINED": "Отрицательная выборка",
    "ABD_Liver_2nd_PH": "Подребристый разрез показывает второй разрез.",
    "ABD_Panc_LongAxis": "Длинноосевой разрез поджелудочной железы",
    "ABD_Liver_Lt_Portal_Vein": "Нижний разрез показывает первый разрез печени.",
    "ABD_GB_LongAxis": "Правый ребро через первую дверь печени",
    "ABD_Liver_LHV_IVC": "Левая часть печени через нижнюю вену",
    "ABD_Liver_Lt_Lobe_AAo": "Левая часть печени через брюшную аорту",
    "ABD_Liver_Lt_Lobe_Caudate": "Нижний отросток печени левый и хвостовой продольный разрез",
    "ABD_Liver_1st_PH": "Правый ребро через первую дверь печени",
    "ABD_Liver_Rt_Kidney": "Sag RL-RK",
    "ABD_Portal_Vein": "Правый ребро через первую дверь печени",
    "alternative_images": "Альтернативные изображения",
    "selected_image": "Выберите изображение",
    "edit_report_unenable_tips": "Отчет не поддерживается редактором.",
    "move_down": "Переместить вниз",
    "move_up": "Переместить вверх",
    "dr_ai_analyze_statistics_error": "Получите статистику контроля качества Dr необычно!",
    "select_transfer_image_max": "Максимальная поддержка выбора ${1}",
    "select_image": "Выберите изображение",
    "inspection_overview": "Обзор инспекций",
    "not_started": "Не началось",
    "refresh_status": "Обновить состояние",
    "initiated_live": "Началась прямая трансляция.",
    "join_live_streaming": "Присоединяйтесь к прямой трансляции",
    "ai_search_suggest": "Найти похожие случаи, нажмите, чтобы посмотреть",
    "level_d_count_and_rate": "Уровень D Количество / доля",
    "level_c_count_and_rate": "Уровень C Количество / доля",
    "level_b_count_and_rate": "Уровень B Количество / доля",
    "level_a_count_and_rate": "Количество / доля",
    "operation_doctor": "Оператор.",
    "body_position": "Позиция тела",
    "sub_part": "Подразделение",
    "part": "Место расположения",
    "view_score_display_rule": "При наличии нескольких изображений в одном и том же сечении система автоматически получает максимальный балл.",
    "everyone": "Все.",
    "test_time_tips": "Проверьте разницу во времени.",
    "is_visible_text": "Видно:",
    "after_nickname_modified_in_group": "После изменения прозвища отображается только в этой группе, и все члены группы могут видеть",
    "my_nickname_in_group": "Мое прозвище в группе.",
    "group_modify_nickname": "Изменить групповой псевдоним",
    "group_nick_name": "Групповое прозвище",
    "level_d": "D",
    "level_c": "C",
    "level_b": "B",
    "level_a": "A",
    "not_group_member_live_tips": "Вы в настоящее время не являетесь членом группы, и вы можете связаться с администратором, чтобы присоединиться к групповому разговору и получить полную возможность прямой трансляции.",
    "dr_ai_analyze_statistics": "DR Статистика контроля качества изображений",
    "dr_ai_analyze": "DR AI",
    "display_exam_original_picture": "Показать исходное изображение",
    "box_color": "Цвет рамки",
    "deletion_text": "Отсутствие:",
    "view_deletion": "Отсутствие сечения",
    "structure_evaluation_text": "Структурная оценка:",
    "exam_type_text": "Тип проверки:",
    "pravicy_policy_content": {
        "j": {
            "lead": {
                "d": "Мы рассмотрим запросы как можно скорее и дадим вам ответ в течение 15 дней после подтверждения личности пользователя.",
                "c": " или отправьте по адресу: ",
                "b": " для связи с нами. Кроме того, вы можете отправить свои вопросы по электронной почте ",
                "a": "Если у вас есть другие жалобы, предложения или вопросы, связанные с личными данными несовершеннолетних, посетите веб-сайт "
            },
            "zipcode": "Почтовый индекс: 518057",
            "address": "Центр защиты данных и конфиденциальности, юридический отдел, здание Mindray, парк высоких технологий, район Наньшань, город Шэньчжэнь, провинция Гуандун, КНР.",
            "email": "<EMAIL>",
            "website": "https://consult.mindray.com/kf/",
            "title": "10. Контактные данные"
        },
        "i": {
            "lead": "Данная политика конфиденциальности предназначена для MiСo+. При наличии противоречий или конфликтов между данной политикой и общими правами пользователя и мерами защиты информации компании Mindray ознакомьтесь с данной политикой.",
            "title": "9. Проч."
        },
        "h": {
            "lead": "Мы можем пересмотреть данные указания по мере необходимости. Если условия указаний будут пересмотрены, мы предоставим вам исправленные данные, отправив уведомление, всплывающее окно или другие подходящие формы при входе в MiCo+ или обновлении версии.",
            "title": "8. Обновление политики"
        },
        "g": {
            "lead": "Благодаря непрерывному развитию нашего бизнеса мы можем осуществлять слияние, приобретение и передачу активов. Мы сообщим вам о таких изменениях и продолжим защищать ваши личные данные в соответствии со стандартами, которые требуются в соответствии с законами и нормативными актами, или при использовании нового контроллера данных с целью защиты ваших личных данных.",
            "title": "7. Глобальная передача личных данных пользователей"
        },
        "f": {
            "lead": "Мы придаем огромную важность защите личных данных несовершеннолетних лиц. Поскольку MiCo+ является сетевой платформой для врачей, регистрация и использование возможны только для взрослых старше 18 лет. Если информация о случае, содержащаяся на платформе, относится к пациентам младше 14 лет, мы будем придерживаться более строгой политики конфиденциальности. Например, имя, возраст, рост и пол ребенка скрыты по умолчанию. Если пользователю (врачу) необходимо проверить случай, требуется дополнительный запрос, и система регистрирует эту операцию проверки.",
            "title": "6. Обращение с личными данными детей"
        },
        "e": {
            "content": {
                "f": "5.6 Претензии и сообщения",
                "e": "5.5 Закрытие уч. записи",
                "d": "5.4 Отзыв авторизации",
                "c": "5.3 Удаление личных данных",
                "b": "5.2 Правка личных данных",
                "a": "5.1 Доступ к личным данным"
            },
            "title": "5. Права пользователя"
        },
        "d": {
            "content": {
                "g": "g) ситуации, связанные с сбором личных данных из легально раскрытой информации, например, из юридических новостных отчетов и из государственных данных.",
                "f": "f) Субъект личных данных самостоятельно раскрывает личные данные.",
                "e": "e) ситуации, связанные с защитой субъекта личных данных или со значительными юридическими правами и интересами, такими как жизни и имущество других лиц, в которых трудно получить согласие пользователя.",
                "d": "d) ситуации, непосредственно связанные с уголовным расследованием, судебным преследованием, судебным разбирательством и исполнением решения суда.",
                "c": "в) ситуации, непосредственно связанные с общественной безопасностью, охраной здоровья и основными публичными интересами.",
                "b": "b) ситуации, непосредственно связанные с национальной безопасностью и национальной оборонной безопасностью.",
                "a": "a) ситуации, связанные с исполнением нами обязательств, предусмотренных законами и нормативными актами."
            },
            "lead": "Ваши личные данные будут использоваться только для стандартной работы службы и не будут использоваться для других целей. Чтобы обеспечить безопасность службы и повысить стабильность MiCo+, мы можем записать частоту использования, данные о сбоях, данные о производительности и другие сведения. Мы не будем связывать эти данные с вашими личными данными. Если мы хотим использовать ваши личные данные в целях, не указанных в предыдущем разделе, мы снова проинформируем вас и получим ваше явно выраженное согласие перед использованием. Кроме того, в соответствии с действующими законами и нормативными актами, а также национальными стандартами, мы можем делиться, передавать, и публично раскрывать личные сведения, не получив предварительного разрешения от субъекта личных данных, в следующей ситуации:",
            "title": "4. Правила использования личных данных"
        },
        "c": {
            "content": {
                "g": "3.7 Мы постараемся защитить ваши личные данные. При этом напоминаем, что никакие меры безопасности не являются абсолютными.",
                "f": "3.6 В настоящее время компания MiСo+ завершила регистрацию защиты сети на национальном уровне (уровень 3). Ожидается, что MiСo+ может пройти оценку и получить сертификат уровня безопасности к концу 2021 года. Кроме того, компания Mindray получила официальные сертификаты в области информационной безопасности согласно стандартам ISO/IEC 27001 и ISO/IEC 27701.",
                "e": "3.5 При возникновении проблем с безопасностью, таких как раскрытие личных данных, мы начнем действовать согласно плану для чрезвычайных ситуаций, чтобы предотвратить дальнейшее развитие нарушений безопасности, и сообщим вам об этом, направив уведомление или извещение.",
                "d": "3.4 Мы создаем специальные системы управления, процессы управления и управленческие организации для обеспечения информационной безопасности. Например, мы строго контролируем число сотрудников, имеющих доступ к информации, требуем, чтобы эти сотрудники соблюдали конфиденциальность, а также делаем аудиты их работы.",
                "c": "3.3 Мы будем укреплять безопасность ПО вашего устройства, постоянно совершенствуя технологии, чтобы предотвратить раскрытие личных данных. Например, некоторые данные будут локально зашифрованы на вашем устройстве для безопасной передачи.",
                "b": "3.2 Мы будем использовать различные меры для обеспечения информационной безопасности на адекватном уровне. В частности, мы используем технологию шифрования (например, SSL) и анонимизацию для защиты ваших личных данных.",
                "a": "3.1 Мы стремимся защищать информационную безопасность пользователей, чтобы предотвратить потерю информации, ненадлежащее использование, несанкционированный доступ или раскрытие."
            },
            "title": "3. Информационная безопасность"
        },
        "b": {
            "content": {
                "b": {
                    "content": "Мы сохраняем ваши личные данные только в рамках предоставления наших услуг. Например, если вы отмените свою учетную запись в MiCo+, мы удалим ваши личные данные, включая номер мобильного телефона.",
                    "lead": "2.2 Период хранения: "
                },
                "a": {
                    "content": "в соответствии с законами и нормативными актами личные данные пользователей, собранные в пределах страны, будет храниться в Китае.",
                    "lead": "2.1 Место хранения: "
                }
            },
            "title": "2. Хранение информации"
        },
        "a": {
            "content": {
                "f": "1.6 При добавлении комментариев к изображениям случая ваши комментарии будут храниться на нашем сервере, т. к. это необходимо для данной функции. Мы гарантируем безопасность хранимой информации. Если мы используем стороннюю службу для хранения вашей информации, мы также требуем от сторонней компании предоставить соответствующие сертификаты и удостоверения, подтверждающие обеспечение безопасности данных. Сторонний поставщик услуг хранения данных не может получить доступ к вашей информации или прочитать ее.",
                "e": "1.5 При использовании функции консультации в реальном времени для этой функции мы можем использовать вашу камеру. При включении функции хранения данных во время консультации в реальном времени на сервере будут сохранены аудио- и видеоматериалы всего процесса консультации. Мы гарантируем безопасность хранимой информации. Если мы используем стороннюю службу для хранения вашей информации, мы также требуем от сторонней компании предоставить соответствующие сертификаты и удостоверения, подтверждающие обеспечение безопасности данных. Сторонний поставщик услуг хранения данных не может получить доступ к вашей информации или прочитать ее.",
                "d": "1.4 При использовании услуги консультации отправляемые сведения о случае будут храниться на нашем сервере, т. к. это необходимо для данной функции. Мы гарантируем безопасность хранимой информации. Если мы используем стороннюю службу для хранения вашей информации, мы также требуем от сторонней компании предоставить соответствующие сертификаты и удостоверения, подтверждающие обеспечение безопасности данных. Сторонний поставщик услуг хранения данных не может получить доступ к вашей информации или прочитать ее.",
                "c": "1.3 При использовании функции чата текст, фотографии, видео, файлы, голосовые и другие данные, которые вы отправили, будет храниться на нашем сервере, т. к. это необходимо для этой функции. Мы гарантируем безопасность хранимой информации. Если мы используем стороннюю службу для хранения вашей информации, мы также требуем от сторонней компании предоставить соответствующие сертификаты и удостоверения, подтверждающие обеспечение безопасности данных. Сторонний поставщик услуг хранения данных не может получить доступ к вашей информации или прочитать ее.",
                "b": "1.2 При использовании MiСo+ мы просим указать такую информацию, как модель устройства, операционная система и идентификационный код устройства, для обеспечения нормального использования службы, поддержания оптимальной работы службы, оптимизации обслуживания и обеспечения безопасности учетной записи. Это основная информация для обеспечения качества услуг в рамках MiСo+.",
                "a": "1.1 При регистрации учетной записи в MiCo+ потребуются ваш псевдоним, фотография и номер мобильного телефона, чтобы завершить регистрацию пользователя и обеспечить безопасность вашей учетной записи. Номер мобильного телефона относится к конфиденциальной информации, которая необходима для соблюдения соответствующих законов и нормативных актов, касающихся системы реальных имен в Интернете. Если вы отказываетесь предоставлять такую информацию, вы не сможете зарегистрировать учетную запись. В результате мы не можем предоставить вам услуги. По своему усмотрению вы можете указать такие сведения, как пол и больница."
            },
            "lead": "При использовании MiCo+ мы собираем данные, предоставляемые вами или созданные в ходе использования служб, таким образом, чтобы предоставлять вам услуги, оптимизировать наши услуги и обеспечить безопасность вашей учетной записи.",
            "title": "1. Правила сбора личных данных"
        }
    },
    "pravicy_policy_catalogue": {
        "content": {
            "j": "10. Контактные данные",
            "i": "9. Проч.",
            "h": "8. Обновление политики",
            "g": "7. Глобальная передача личных данных пользователей",
            "f": "6. Обращение с личными данными детей",
            "e": "5. Права пользователя",
            "d": "4. Правила использования личных данных",
            "c": "3. Информационная безопасность",
            "b": "2. Хранение информации",
            "a": "1. Правила сбора личных данных"
        },
        "title": "Каталог",
        "lead": "В частности, получение разрешения для взаимодействия с конфиденциальными данными является необходимым, а не достаточным условием для сбора определенной информации. Если мы получили такое разрешение для взаимодействия с конкретным элементом, это не означает, что мы собираем соответствующие данные о вас. Даже если мы получили разрешения для взаимодействия с конфиденциальными данными, при необходимости мы собираем ваши данные в соответствии с данными указаниями. Для получения более подробной информации ознакомьтесь с соответствующими главами по ссылке:"
    },
    "pravicy_policy_foreword": {
        "content": {
            "g": "g) мы должны запрашивать у вас разрешения на соответствующие действия для сбора ваших данных в рамках данных указаний или для предоставления услуг, оптимизации наших служб и обеспечения безопасности вашей учетной записи. По умолчанию разрешения для взаимодействия с конфиденциальными данными, связанные с хранением, камерой, микрофоном и фотоальбомом, не будут активированы. Мы имеем право доступа только в том случае, если вы предоставили явно выраженное согласие на активацию этих разрешений.",
            "f": "f) Вы можете получить доступ к своим личным данным, исправить и удалить их в соответствии со способами, приведенными в числе данных указаний. Кроме того, вы можете отозвать свое согласие, закрыть свою учетную запись и сообщить о своих жалобах.",
            "e": "e) в настоящее время MiCo+ не получает ваши личные данные от сторонних компаний за пределами Shenzhen Mindray Bio-Medical Electronics Co., Ltd. В будущем, если нам потребуется получить ваши личные данные косвенно от сторонней компании для развития бизнеса, мы предоставим вам источник, тип и область применения ваших личных данных перед получением данных. Если деятельность по обработке личных данных, необходимая компании MiCo+ для ведения бизнеса, выходит за рамки разрешения, предоставленного сторонней компании, мы попросим вас предоставить явно выраженное согласие перед обработкой таких личных данных. Кроме того, мы обязуемся строго соблюдать соответствующие законы и нормативные акты и запрашивать у сторонних компаний гарантию законности в отношении предоставляемых ими данных.",
            "d": "d) в настоящее время MiCo+ не распространяет и не передает ваши личные данные сторонним компаниям за пределами Shenzhen Mindray Bio-Medical Electronics Co., Ltd. В случае возникновения ситуации, когда нам необходимо предоставить или передать ваши личные данные, либо вы просите нас предоставить или передать ваши личные данные сторонней компании за пределами Shenzhen Mindray Bio-Medical Electronics Co., Ltd., мы будем напрямую запрашивать ваше явно выраженное согласие, или мы убедимся, что сторонняя компания получит ваше явно выраженное согласие на выполнение вышеуказанных действий, за исключением защиты прав пользователя или защиты окружающей среды MiCo+. Кроме того, мы будем проводить оценку рисков, когда мы предоставим данные сторонней компании.",
            "c": "c) при использовании некоторых функций мы собираем конфиденциальную информацию о вас или вашем пациенте после получения вашего согласия. Например, при использовании рекомендованной функции контактов мы собираем информацию о контактах с мобильного телефона. При использовании функции консультации по случаю пациента мы получаем информацию о случае пациента из загруженных вами данных. За исключением тех случаев, когда не разрешено собирать некоторые данные в соответствии с действующими законами и нормативными актами, отказ предоставить данные может привести к невозможности использования соответствующих функций. Но вы можете использовать другие функции в MiCo+.",
            "b": "b) мы проиллюстрируем типы персональных данных, собираемых нами, и их соответствующие способы использования, чтобы вы могли понять типы, причины и методы сбора для использования конкретных личных данных.",
            "a": "a) мы понимаем, что для вас важны личные данные, и мы сделаем все возможное, чтобы обеспечить безопасность и надежность ваших личных данных. Мы стремимся поддерживать ваше доверие к нам. Мы придерживаемся следующих принципов для защиты ваших личных данных: баланс прав и обязанностей, целенаправленность, согласие, минимальные требования, безопасность, участие субъекта личных данных и прозрачность. Кроме того, мы будем принимать соответствующие меры безопасности для защиты ваших личных данных в соответствии с развитыми отраслевыми стандартами безопасности. Перед использованием наших продуктов (или услуг) внимательно ознакомьтесь с настоящей политикой защиты личных данных.",
            "brief": " MiCo+ — это сетевая платформа для сообщества врачей, разработанная компанией Shenzhen Mindray Bio-Medical Electronics Co., Ltd. Данная политика демонстрирует важные вопросы, связанные с сбором, использованием и хранением ваших личных данных решением MiCo+, а также вашими правами. Данные представлены следующим образом:"
        },
        "title": "Введение"
    },
    "pravicy_policy_date": {
        "public": "1 ноября 2021 г.",
        "effective": "Дата вступления в силу: 1 ноября 2021 г.",
        "update": "Дата обновления: 1 ноября 2021 г."
    },
    "pravicy_policy_title": "Заявление о политике защиты личных данных MiCo+",
    "pravicy_policy_company_name": "Shenzhen Mindray Bio-Medical Electronics Co., Ltd.",
    "filename_illegal_characters": "Существуют незаконные символы, поддерживающие только: большие и малые буквы, цифры, подчёркивание, точечные, запятые, пробелы, тире (-), знак равенства (=), круглые скобки (((), и символы (&), проценты (%), номер колодца (#), квадратные скобки ([]), номер волны (~), одна кавычка ('), обратные кавычки (\"), китайский символ, китайский двоеточие, китайские скобки, китайские квадратные скобки, китайский номер книги, китайский восклицательный, китайский запятый, китайский вопросительный знак",
    "quick_start_guide": "Краткое руководство",
    "iworks_clip_summary_text": "Необходимо завершить рисование ${total} срез. Количество успешно выгруженных срезов — ${upload_num}, уровень завершения — ${complete_rate}. Для успешно загруженных срезов показатель числа стандартных срезов — ${standard_rate}.",
    "data_loading_failed_tips": "Сбой загрузки данных. Повторите попытку позже",
    "avatar_preview_tips": "Искажение или масштабирование может произойти, если размер отличается от 200х200",
    "files_export_failed": " файл. — сбой",
    "exam_original_picture": "Исходное изображение",
    "exam_picture": "Изобр.исслед.",
    "downloaded_task_failed": "Сбой",
    "downloaded_task_downloaded": "Загружено",
    "download_task_total": "Всего",
    "download_task_completed": "Задача скачивания завершена",
    "avatar_preview": {
        "submit": "Отправить",
        "wait": "Загрузка...",
        "general": "Временный предв. просмотр и сохранение"
    },
    "avatar_preview_title": "Просмотр аватара",
    "data_preparing": "Выполняется подготовка данных",
    "no_ai_result": "Нет результата ИИ",
    "missing_members": "Недостающ. участн.",
    "missing_keys": "Отсутств. ключи",
    "invalid_access": "Доступ не разрешен!",
    "live_disabled": "Функция прям.трансляции отключена",
    "qr_install_app": {
        "unknown_server_type": "Неизвестная версия",
        "add_friend": "Добавить друзей: ",
        "join_group": "Присоед.к групп. чату",
        "allow_clipboard": "(Разрешите приложению считывать содержимое буфера обмена)",
        "first_start_with_var": "При первом запуске приложения автоматически выполняются следующие действия:",
        "start_directly": "Открыть напрямую",
        "has_downloaded": "Уже загружено?",
        "download": "Загрузить"
    },
    "personal_profile_tips": "Можно ввести сведения о своей компании, личные сильные стороны или интересы",
    "personal_profile": "Личный профиль",
    "exam_socre_text": "Оценка исслед.:",
    "discover": "Распозн.",
    "chat_text": "Чат",
    "view_count": "Кол-во проекций",
    "completion_rate": "Скорость выполнения",
    "quality_score": "Оценка качества",
    "jiajia_summary": "Сводка д-ра М.",
    "test_time": "Расход времени",
    "test_time_text": "Расход времени:",
    "iWorks_test": "Проверка iWorks",
    "video_path_full_tips": "Текущий видеоканал заполнен, невозможно продолжить подписку",
    "auto_cancel_video_tips": "Текущий видеоканал заполнен и автоматически отменит одно видео",
    "cookies_privacy_agreement_acknowledge": "Подтвердить",
    "cookies_privacy_agreement_content": "Конфиденциальность пользователей очень ценна. Используя файлы cookie, мы можем улучшить просмотр веб-страниц, понять, как пользователи используют это приложение для повышения качества обслуживания. Продолжение использования данного приложения означает, что вы полностью осведомлены и соглашаетесь хранить и обрабатывать файлы cookie на вашем устройстве.",
    "cookies_privacy_agreement": "Соглашение о конфид. файлов cookie",
    "instruction_manual_version": "Версия руководства по эксплуатации",
    "wechat_unsupported_tip": "Коснитесь ···и , чтобы открыть его в системных браузерах.",
    "userNoAuth": "Нет разрешения на работу",
    "other_side": "Друг. сторона",
    "one_side": "Одна сторона",
    "exam_mode_fetal_heart": "Сердц.плода",
    "exam_mode_obe": "АК2/3",
    "export_live_data": "Экспорт данных прямой трансляции",
    "upload_image_max_text": "Сбой выгрузки. Слишком большой размер изображения",
    "upload_ai_image": "Изображ. ИИ",
    "upload_normal_image": "Нормальное изобр.",
    "supplementary_image_types": "Дополнительные типы изображений",
    "new_exam_switch_image_type_tip": "После смены типа выгружаемого изображения выбранные данные будут потеряны!",
    "submit_ai_image_upload_tip": "${name} отсутствует, выгрузите!",
    "leave_ai_image_upload_tip": "Вы не отправили выбранные данные, они не будут сохранены!",
    "power_image": "Изобр. энер.реж.",
    "color_image": "Цветное изображ.",
    "no_iclear_image": "Нет изображения iClear",
    "b_image": "B-изобр.",
    "cover_image": "Изобр. обложки",
    "power_mode": "Энерг.реж. для пол.изоб.",
    "color_mode": "Режим цветн. изображения",
    "no_color_mode": "Обычный В-режим визуализации",
    "color_type": "Тип цвета",
    "group_apply_expire": "Истекш.",
    "group_apply_pass": "Пройдено",
    "group_apply_success": "Запрос на вступление в группу отправлен",
    "send_files": "Отпр. файл",
    "unit_file": " файл",
    "file_to_be_sent": " — файл для отправки",
    "like_action": "Нрав.",
    "group_join_input_tip": "Владелец группы активировал подтверждение для вступления в группу. Введите проверочное сообщение",
    "group_join_apply_tip": "${1} Заявка на присоединение к групповому чату",
    "group_join_verify_btn": "Заявки групп ",
    "group_join_verify_tip": "После активации участники группы должны получить подтверждение от владельца группы или администратора группы, прежде чем входить в группу",
    "group_join_verify": "Подтверждение заявки на вступление в группу",
    "send_success": "Отправка выполнена успешно.",
    "sender": "Отправит.",
    "total_duration": "Общая длит.",
    "latest_half_year_label": "Посл. 6 месяцев",
    "latest_months_label": " Посл. 3 месяца ",
    "latest_month_label": " Прошл. месяц ",
    "latest_week_label": " Прошл. неделя ",
    "message_type": "Тип сообщения",
    "search_by_specified_memcontent": "Поиск указанного содержимого",
    "search_by_date": "Поиск по дате",
    "search_by_group_member": "Поиск по членам группы",
    "upload_log_file_success": "Файл журнала выгружен успешно.",
    "upload_log_file": "Выгрузить файл журнала",
    "choose_a_chat": "Выбрать чат",
    "chunk_load_error": "Сервер обновлен, и для использования этой функции необходимо перезапустить приложение",
    "remote_control_response_timeout": "Таймаут ответа пульта дистанц. управления",
    "push_stream_not_allow_remote_control": "Клиент прямой трансляции не позволяет выполнять удаленную настройку параметров",
    "search_uploader_name": "Имя загрузчика или пациента",
    "search_patient_name": "Поиск ФИО пациента",
    "confirm_patient_information": "Подтвердите, что введенные сведения о пациенте и изображении совпадают",
    "exam_created_success_tips": "Случай успешно создан. Дождитесь окончания выгрузки сведений об изображении",
    "never_notify": "Никогда не оповещ.",
    "delete_group_manager": "Удалить менеджера",
    "add_group_manager": "Доб. менеджера",
    "group_manager_permissions6": "- Настройка метода присоединения к групповому чату в разделе управления группами и подтверждение присоединения к групповому чату",
    "group_manager_permissions5": "- Просм.свед.о прямой транс.",
    "group_manager_permissions4": "- Удаление участников группы",
    "group_manager_permissions3": "- Завершение прямой трансляции",
    "group_manager_permissions2": "- Опубликовать груп. объявление",
    "group_manager_permissions1": "- Правка данных просмотра и удаление изображений и видеофайлов в группе",
    "group_manager_permissions": "Менеджер группы может иметь следующие возможности",
    "manager": "Диспетчер",
    "group_managers": "Менеджеры группы",
    "group_manage": "Управл. группами",
    "not_allowed_deleted_invited": "Пользователи, которые уже приглашены, не могут быть удалены",
    "exam_number": "Номер исслед.",
    "exam_info_text": "Свед.об исслед.",
    "basic_info_text": "Общие сведения",
    "input_select_tips": "Выбрать",
    "input_enter_tips": "Введите данные",
    "leave_new_exam_tip": "Если вы уходите с этой страницы, убедитесь, что информация была выгружена",
    "create_new_exam": "Создать новый случай исследования",
    "end_time_greater_tips": "Время окончания должно быть позднее времени начала.",
    "transfer_group_error": "Сбой передачи группы. Повторите попытку!",
    "transfer_group_confirm": "Задайте {1} в качестве руководителя группы. Вы больше не будете руководителем группы для этого сеанса. Действительно продолжить?",
    "transfer_group": "Передать группу",
    "clear_text": "Чист.",
    "contacts_select": "Выбор контактов",
    "reselect_chat_send": "Повтор. выбер. отпр. в чат",
    "send_original_chat": "Отправить в исходном чате",
    "export_task_submitted": "Отправлена задача экспорта — отправка сообщений может выполняться с задержками. Подождите",
    "exported_video_use_cover": "Успешная выгрузка, экспортированное видео будет использовать эту обложку",
    "area_cannot_cropped": "Невозможно обрезать эту область",
    "delete_video_clip_not_allowed": "Удаление последнего видеоклипа запрещено",
    "delete_video_track_not_allowed": "Удаление этой дорожки видео запрещено",
    "cloud_resources_processed": "Выполняется обработка облачных ресурсов, повторите попытку позже",
    "server_resource_retried": "Ресурсы сервера генерируются, подождите",
    "instruction_manual": "Руководство по эксплуатации",
    "cannot_edit_multiple_modules": "Одновременное редактирование нескольких модулей невозможно",
    "update_ready_tip": "Новая версия готова, перезапустите, чтобы использовать новейшие функции",
    "no_permission_operate": "У вас нет разрешения на использование этой функции",
    "case_database_fliter": {
        "papillary_carcinoma": "Папиллярная карцинома",
        "mucinous_carcinoma": "Муцинозная карцинома",
        "cephaloma": "Цефалома",
        "infiltrating_lobular_carcinoma": "Инфильтративная дольковая карцинома",
        "infiltrating_ductal_carcinoma": "Инфильтративно-протоковая карцинома",
        "invasive_breast_cancer": "Инвазивный рак молочной железы",
        "lobular_carcinoma_in_situ": "Лобулярная карцинома in situ",
        "ductal_carcinoma_in_situ": "Протоковая карцинома in situ",
        "noninvasive_breast_cancer": "Неинвазивный рак молочной железы",
        "pathological_classification_breast_cancer": "Гистологическая классификация рака молочной железы",
        "bi_rads_6": "Категория 6",
        "bi_rads_5": "Категория 5",
        "bi_rads_4c": "Категория 4c",
        "bi_rads_4b": "Категория 4b",
        "bi_rads_4a": "Категория 4a",
        "bi_rads_3": "Категория 3",
        "bi_rads_2": "Категория 2",
        "bi_rads_1": "Категория 1",
        "bi_rads_type_text": "Классификация BI-RADS:",
        "bi_rads_type": "Классификация BI-RADS",
        "coarse_calcification": "Крупная кальцификация",
        "microcalcification": "Микрокальцинаты",
        "intraductal_calcification": "Кальцификация внутри протоков",
        "calcification_out_mass": "Кальцинаты вне опухоли",
        "calcification_in_mass": "Кальцинаты внутри опухоли",
        "no_calcification": "Без кальцификации",
        "calcification": "Кальцификация",
        "mixed_change": "Смешан.:изменение",
        "acoustic_shadow": "Акустическая тень",
        "echo_enhancement": "Усиление эхо",
        "no_change": "Без изменений",
        "posterior_echo": "Задн. эхо",
        "uneven_echo": "Неравном.эхоген.",
        "mixed_echo": "Смешанные эхо",
        "isoechoic": "Изоэхогенн.",
        "hyperechoic": "Высокоэхоген.",
        "hypoechoic": "Низ. эхо",
        "anechoic": "Анэхоген.",
        "echo_type": "Тип эхо",
        "hairpin_like": "Шпилькообр.",
        "microphylation": "Дифференциальный диагноз",
        "angled": "Под углом",
        "finishing": "Гладк.",
        "vague": "Нечетк.",
        "edge": "Край",
        "unparallel": "Непараллельно",
        "parallel": "Параллельно коже",
        "direction": "Ориентация",
        "irregular": "Неправильн.",
        "circular": "Кругл.",
        "oval": "Овал",
        "shape": "Форма",
        "bi_rads_feature": "Особенности BI-RADS",
        "malignant": "Злокач.",
        "benign": "Доброкач.",
        "benign_or_malignant": "Доброкач. и злокач."
    },
    "obstetric_qc": {
        "obstetric_mid_pregnancy_view_description": "Сочетая существующие решения MiСo+ для контроля качества и планирования, мы сосредоточились на направлении интеллектуального контроля качества изображений, полученных при скрининге, как на ключевом элементе продвижения MiСo+ в системе мед.системах для матери и ребенка.",
        "obstetric_mid_pregnancy_view_name": "Конфигурация контроля качества акушерского режима в середине беременности",
        "title": "Контроль качества при акуш. иссл. с ИИ (результаты идентификации ИИ, только для справки)",
        "nalysis_rconsider_uncompleted": "Результаты повт. рассмотрения не получены",
        "nalysis_uncompleted": "Результаты анализа контроля качества не получены",
        "quality_user": "Контроллер качества",
        "quality_user_leader": "Руководитель контроля качества",
        "quality_control_leader": "Руководитель контроля качества ",
        "preset_data_set": "Конфигурация контроля качества проекции",
        "nalysis_completed": "Анализ контроля качества завершен",
        "result": "Результаты контроля качества",
        "non_view": "Срез, не соотв.контролю качества",
        "base_view": "Базовый вид",
        "optional_view": "Дополнительный вид"
    },
    "verall_evaluation_qc": "Общая оценка контроля качества",
    "obstetric_qc_ai": "Система контроля кач.при акуш. иссл.с ИИ",
    "no_relative_data_find": "Соответствующие данные не найдены",
    "ai_non_standard_view_text": "Количество нестандартных проекций:",
    "ai_recognition_view": "Количество проекций, распознанных ИИ",
    "last_update_time": "Время послед. обновления",
    "uploader": "Загрузчик",
    "organization_name_too_long": "Длина названия учреждения не может превышать 64 символа",
    "orgn_name_too_long": "Длина названия учреждения не может превышать 64 символа",
    "patient_name_too_long": "Длина ФИО пациента не может превышать 64",
    "patient_id_too_long": "Длина ID пациента не может превышать 64",
    "exam_time_interval_too_long": "Максимальный интервал проверки не может превышать один год",
    "data_list": "Список данных",
    "ai_nalysis_no_result_tips": "В настоящее время нет доступных результатов распознавания ИИ",
    "ai_nalysis_result_tips": "Результаты анализа ИИ, только для справки",
    "reconsidered": "Пересмотрено",
    "structure_name": "Название структуры ",
    "structure_evaluation": "Оценка структуры",
    "cancel_rconsider_success": "Повторное рассмотрение вып. успешно",
    "cancel_rconsider_fail": "Сбой отмены повторного рассмотрения",
    "cancel_rconsider_tips": "Действительно отменить повторное рассмотрение?",
    "exam_date": "Дата исслед.",
    "view_type_select_tips": "Выберите тип проекции!",
    "view_type": "Тип проекции",
    "view_type_ai": "Тип проекции (ИИ)",
    "cancel_reconsider": "Отмена повторного рассмотрения",
    "apply_reconsider": "Подать запрос на повт. рассмотрение",
    "institution_id": "ID учреждения",
    "institution_name": "Название учреждения",
    "compliance_rate": "Уровень соответствия",
    "view_compliance_rate": "Уровень соотв. изображения",
    "exam_compliance_rate": "Уровень соотв .исслед.",
    "reason_for_deficiency": "Причина несоотв. качества",
    "go_location_of_file": "Перейти к местоположению файла",
    "check_view_info": "Подробнее",
    "view_quality_text": "Качество сечения:",
    "view_quality": "Качество проекции",
    "view_detail": "Подробнее",
    "group_view_score": "Оценка группы проекций",
    "view_score_text": "Рейтинг сечения:",
    "view_score": "Оценка проекции",
    "view_name_uploader": "Имя проекции(Загрузчик)",
    "view_name_text": "Имя сечения:",
    "view_name": "Имя проекции",
    "latest_exam_time": "Время послед. исслед.",
    "image_number": "Число изображений",
    "non_standard": "Нестандартн.",
    "basic_standard": "Базовый стандарт",
    "standard": "Стандарт",
    "reset_data_tips": "Сбросить все данные конфигурации",
    "last_uploader": "Автор послед. обновл.",
    "search_criteria": "Критерии поиска",
    "ai_score_no_standard": "Балл по шкале ИИ (нестандарт.)",
    "ai_score_basic_standard": "Балл по шкале ИИ (базовый стандарт)",
    "ai_score_standard": "Балл по шкале ИИ (стандарт.)",
    "ai_score": "Оценка ИИ",
    "score_value": "Оценка",
    "total_score_value": "Всего",
    "reconsider_result_new_tips": "Результаты анализа изображения обновлены. Вы подтверждаете согласие с результатами анализа?",
    "overall_evaluation_desc": "Во время этой проверки было выгружено всего {total} изобр., всего определено {finshed} проекц. Качество изображения: стандарт: {standard}, основной стандарт: {basic_standard}, нестандартное: {non_standard}, отсутствует: {deletion}, уровень соответствия: {compliance_rate}%, kоэффициент полноты: {integrity_rate}%， Нормативная ставка: {standardization_rate}.",
    "overall_evaluation": "Общая оценка",
    "view_group_name": "Имя группы проекций",
    "view_group": "Группа проекций",
    "view": "Вид",
    "score_items": "Оценка элементов",
    "full_mark": "Полная метка",
    "proportion_weight_value": "Пропорциональное значение массы",
    "proportion_weight": "Пропорция массы",
    "exam_view_page": "Просм. иссл.",
    "view_view_page": "Проекция среза",
    "detect_image": "Определить изобр.",
    "image_effects": "Эффекты изображения",
    "deletion": "Удаление",
    "display": "Отобр.",
    "status_text": "Состояние",
    "status": "Состояние",
    "obstetric_qc_multicenter": "Многоцентровая платформа для контроля качества АК",
    "ai_error": {
        "invalid": "недопустимый параметр",
        "anaylse_timeout": "Превышено время анализа",
        "report_anaylse_fail": "Сбой анализа отчета",
        "anaylse_fail": "Сбой анализа",
        "connect_fail": "Сбой подключения",
        "disconnect": "Отключить",
        "prohibit_call": "Отключить интерфейс вызова"
    },
    "load_more": "загрузить еще",
    "video_clip_msg": "[видеоклип]",
    "clip_time": "Время обрезки",
    "generated_video_clips": ": созданные видеоклипы",
    "failed_to_enter_editing_mode": "Сбой извлечения данных, невозможно войти в режим редактирования. Повторите попытку позже.",
    "export_video": "Экспорт видео",
    "exit_clip_tips": "Вы собираетесь закрыть окно ролика. Подтвердите, что работа завершена, и экспортируйте данные",
    "pthological_conclusion": "Патологическое заключение",
    "ultrasonic_diagnosis": "УЗ-диагностика",
    "update_confirm_tip": "Новая версия готова. Перезапустить обновление сейчас?",
    "video_clips": "Видеоклипы",
    "local_library": "Альбом",
    "take_picture": "Сделайте снимок",
    "searc_in_case_database_one_picture": "Выберите изображение.",
    "searc_in_case_database_many_picture": "Поиск по изображениям поддерживает только поиск одного изображения",
    "ai_searc_in_case_database": "Распознавание изображения ИИ",
    "searc_in_case_database_result": "При расчетах, выполненных ИИ компании Mindray, на изображении было выявлено {1} подозрительных поражений (результаты ИИ, только для справки)",
    "is_no_case_text": "Сведения о схожих случаях не найдены",
    "picture_is_no_roi": "Выбранное изображение не может быть распознано",
    "get_picture_info_fail": "Сбой чтения сведений об изображении, проверьте сеть",
    "picture_is_only_jpg_jpeg_bmp_png": "Разрешены только изображения в форматах png, jpg, jpeg и bmp",
    "picture_is_too_blurred": "Изображение слишком размытое, сделайте снимок еще раз или выберите изображение",
    "custom_server": "Польз. сервер",
    "default_server": "Сервер по умолчанию",
    "current_server": "Текущий сервер",
    "favorite_confirm_private": "Личный (видно только вам)",
    "favorite_confirm_public": "Общедоступн. (видно только друзьям)",
    "favorite_confirm_tip": "Будет добавл. в личную облачную коллекцию, кто сможет ее просмотреть",
    "favorites_private_tip": "Сделать коллекцию личной (видно только мне)?",
    "favorites_public_tip": "Сделать коллекцию общедоступной (видно только друзьям)?",
    "live_has_no_playback": "В прямой трансляции нет данных для воспр.",
    "rotate_right": "Повернуть вправо",
    "rotate_left": "Повернуть влево",
    "zoom_out": "Уменьшение",
    "zoom_in": "Увеличение",
    "local_camera": "Локальная камера",
    "camera_error": "Камера не работает. Повторите попытку",
    "mic_error": "Микрофон не работает. Повторите попытку",
    "thyroid_multicenter": "Многоцентр. иссл. щитов.жел.",
    "hfr_multicenter": "многоцентр.иссл. с HFR",
    "repeat_supply": "Это исследование было добавлено в другие группы и не может быть отправлено повторно",
    "switch_to_aux_screen": "камера",
    "switch_to_main_screen": "основной экран",
    "server_ended_live": "Сервер завершил прямую трансляцию",
    "image_type_not_compliant": "Тип изображения не соответствует требованиям. Продолжить?",
    "set_to_ordinary_ultrasound": "Задайте как обычное УЗИ",
    "set_to_RCEH": "Задайте как стандартное контрастное УЗИ",
    "set_to_HFRR": "Задайте как УЗИ с контрастированием и HFR",
    "RCEH": "Стандартное УЗИ с контрастированием",
    "HFRR": "Контрастное УЗИ с HFR",
    "ordinary_ultrasound": "Обычное УЗИ",
    "picture_type": "Тип изображения",
    "end_cloud_record": "Заверш.записи в облаке",
    "start_cloud_record": "Пуск записи в облако",
    "only_host_end_cloud_recording": "У вас нет разрешения на завершение записи в облаке",
    "only_host_initiate_cloud_recording": "У вас нет разрешения на запуск записи в облаке",
    "review_detail_title": "Просмотр",
    "live_detail_title": "ПрПер",
    "more_settinngs": "Доп. настройки",
    "share_QR_code": "Поделиться QR-кодом",
    "recording_ended": "Запись завершена",
    "recording_turned_on": "Запись включена",
    "whose_recording_file": ": файл записи",
    "cancel_btn": "Отмена",
    "envTitleMap": {
        "CE": "MiCo+",
        "CN": "MiCo+"
    },
    "recommend_download_app": "Рекомендуется загрузить приложение MiCo+ для улучшения качества просмотра",
    "retract_more_details": "Скрыть доп. сведения",
    "expand_more_details": "Разверните для получения сведений",
    "is_in_conference": " находится на конференции",
    "file_in_progress": "Файл выгружается, подождите",
    "live_setting": "Наст. прям. пер.",
    "someone": "Кто-то",
    "mainstream_is_sharing": " общий доступ к экрану/УЗИ",
    "data_traffic_waring": "Предупр. об исп. данных и ограничение",
    "waiting_for_reconnection": "Ожидание повторного подключения",
    "public_group_tip": "Все польз. могут присоед.",
    "private_group_tip": "Для вступления в группу требуется приглашение от участника группы",
    "not_allow_modify_others_data": "Запрещено изменять данные других пользователей",
    "cancel_favorite": "Отмена избранного",
    "live_broadcast_viewers": "просматр.",
    "live_broadcast_duration": "длительность",
    "live_broadcast_information": "информация",
    "live_broadcast_initiation_time": "Время запуска",
    "live_broadcast_initiator": "Создатель",
    "view_details": "Подробнее",
    "exit_edit": "Выйти из правки?",
    "organization_name": "Организация",
    "not_set_tip": "Не задано",
    "avatar_title": "Фото профиля",
    "initiated_live_broadcast": ": пр.транс. ",
    "teaching_live": "Онлайн-обучение",
    "consultation_live": "Удаленная консультация",
    "universal_live": "Общ.прям. трансл.",
    "got_it": "понятно",
    "select_organization": "Учреждения",
    "enter_organization": "Введите назв.организации",
    "improve_infomation": "Дополнительные личные данные>>",
    "modify_nickname": "Измените псевдоним",
    "body_parts": "Части исслед.",
    "patient_sex": "Пол",
    "patient_age": "Возр",
    "patient_name": "ФИО пациента",
    "apply_description": "Я ${nickname} из группы ${group}",
    "apply_friend_remark": "Пометки",
    "apply_friend_info": "Информация о подтверждении",
    "edit_review_info": "Правка свед.воспроизведения",
    "live_token_has_expired": "Срок действия токена истек, попробуйте повторно войти в прямую трансляцию",
    "live_connection_continue_waiting_tips": "Сеть отключена. Проверьте или подключите повторно.",
    "keep_waiting": "Переподключ.",
    "send_apply_add_friend": "Отправить заявку на добавление друзей",
    "apply_add_friend": "Подать запрос на добав. друзей",
    "live_conference_reconnecting": "Повт. подключение",
    "reload_page_tip": "Ошибка сети. Страница будет перезапущена",
    "resource_being_generated": "Ресурс генерируется, повторите попытку позже",
    "start_live_broadcast": "Пуск прямой трансляции",
    "whether_last_stream_pushing_action": "Инициировать ли автоматически посл. прямую трансляцию автоматически",
    "whether_enable_automatic_recording": "Включить ли запись автоматически",
    "linking": "Подключение",
    "input_device_remark": "Пометка",
    "edit_device_remark": "Пометка",
    "no_device_remark": "Нет пометок",
    "current_device": "Текущее устройство",
    "hospital_name_length_limit_32": "Длина названия больницы не должна превышать 32 символа",
    "hospital_name_length_limit_0": "Поле названия больницы не может быть пустым.",
    "hospital_name_exist": "Больница уже существует в списке",
    "input_hospital_name": "Введите название больницы",
    "add_hospital_name": "Добавить больницы",
    "not_upload_text": "Не выгружено",
    "in_silent_streaming": "Подкл. к ТВ-стене",
    "live_session_in_progress": "Выполняется прямая трансляция",
    "share_link_success": "Ссылка успешно отпр.",
    "searc_in_case_database": "Поиск по изображению",
    "share_email_title": "Отправить по эл.почте",
    "whether_remove_equipment_from_group": "Удалить ли устройство из группы",
    "share_sms": "SMS",
    "share_wechat": "WeChat",
    "exam_images": "Изобр. исслед.",
    "not_connected": "Отключено",
    "piece_tip": " ",
    "exam_images_title": "[Иссл.]",
    "whether_pull_equipment_into_group": "Разрешить ли устройству подключаться к группе",
    "traceless_failed": "Сбой подтверждения, повторите попытку!",
    "traceless_error": "Ошибка, повторите попытку!",
    "traceless_success": "Успешное подтверждение!",
    "traceless_slide": "Проведите пальцем вправо для подтверждения",
    "undefined_name": "Без имени",
    "input_device_name": "Введите имя устройства.",
    "edit_device_name": "Измените имя устройства",
    "device_detail": "Подробн. об устр.",
    "default_push_way": "Метод запуска прямой трансляции по умолчанию",
    "back_to_login": "Возврат ко входу",
    "tv_wall_text": "ТВ-СТЕНА",
    "remote_camera": "Удаленная камера",
    "main_stream_screen": "Основной экран",
    "quick_lauch_live_title": "Быстр. прямая трансляция",
    "start_record": "Пуск записи",
    "weblive_live_not_yet": "В настоящее время никто не делится видео",
    "switch_video_to_main": "Сейчас — камера. Нажмите для перехода к основному видео",
    "switch_video_to_aux": "Сейчас — основное видео. Нажмите, чтобы переключиться на камеру",
    "weblive_download_client_tips": "Для более удобной работы нажмите, чтобы загрузить приложение MiCo+",
    "weblive_download_app_tips": "Для более удобной работы нажмите, чтобы загрузить приложение MiCo+",
    "no_support_browser_live": "Текущий браузер не поддерживает просмотр прямой трансляции. Откройте в другом браузере.",
    "live_des": "Описание прям. пер.",
    "conneting": "Подключение",
    "error_live_address_tip": "Неверный адрес, нет инф. о прямой трансляции",
    "remote_current_mode": "Текущий режим:",
    "storeState": "Сост.хранения",
    "please_enter_code": "Введите проверочный код ",
    "open_system_browser": "Открыть браузер",
    "device_binding_success": "Привязка устр. выполнена успешно",
    "more_device_title": "Еще",
    "device_list": "Устр-ва",
    "device_binding_tip": "Привязка устройства к групповому чату:",
    "authorized_devices": "Авторизованные устройства",
    "device_binding": "Устр-во для привязки",
    "end_live_tips": "Завершите прямую трансляцию; все польз. выйдут",
    "quit_live_tips": "Просто заверш. свою прямую трансл.",
    "end_live": "Зав.пр.пер.",
    "quit_live": "Выйти:пр.транс.",
    "whether_dissolve_live": "Завершить ли прямую трансляцию и уведомить других участников о необходимости выхода",
    "same_label_can_selected": "Можно выбрать только операции с файлами под одной меткой",
    "conference_seeding": "Прямая трансл.",
    "live_not_allow_operation": "Идет прямая трансл., операция запрещена",
    "applying_join_room_error": "Ошибка заявки на присоединение к комнате",
    "open_mic_to_many_tips": "В настоящее время слишком много людей пользуются микрофоном. Повторите попытку после того, как другие пользователи отключат микрофон",
    "processing_wait": "Обработка, подождите",
    "lost_connect_with_server": "Потеряно соединение с сервером",
    "live_ended": "Прямая трансляция завершена",
    "reset_email_success": "Адрес электронной почты успешно задан",
    "reset_email_title": "Сбросить адрес эл. почты",
    "reset_email_tip": "Задать новый адрес эл. почты",
    "auth_by_mobile": "Подтвердите с помощью моб. телефона",
    "auth_by_password": "Подтвердите с помощью пароля",
    "download_tip": "Загрузите MiCo+",
    "userLoginQrCodeError": "Срок действия QR-кода истек, отсканируйте для повторного входа",
    "qrcode_time_out": "Срок действия QR-кода истек, нажмите для обновления!",
    "qrcode_time": "QR-код действителен в течение 120 секунд",
    "time_out_msg": "Срок действия QR-кода истекает через {1} с",
    "cancel_login": "Отмените вход",
    "auto_login_device": "Автовход в устройство",
    "login_to_pc": "Вход в MiCo+",
    "is_force_conference_by_group_owner": "Завершить ли прямую трансляцию принудительно с правами владельца группы",
    "push_stream_setting": "Настр. прямой трансляции",
    "merge_choose_other": "Используйте данные учетной записи ${1}",
    "merge_choose_current": "Используйте данные текущей учетной записи",
    "merge_account_tip": "Данные учетной записи уже существуют в ${1}. Если вы продолжите присоединение, вы можете сохранить только данные одной учетной записи, а данные другой учетной записи могут быть удалены:",
    "verify_bind_description": "Для этой операции требуется привязка мобильного телефона/электронной почты",
    "tap_to_join_conference": "Идет прямая трансл., косн., чтобы присоед.",
    "service_setting": "Настройки службы",
    "network_setting": "Настройка сети",
    "thyroid": {
        "export_excel_confirm": "Можно экспортировать только проверенные данные о состоянии пациента, убедитесь, что они экспортированы?",
        "thyroid_multicenter_form": "Форма многоцентр. иссл. щитовидной железы",
        "export_excel": "Экспорт-Excel",
        "shell_two_hardness_SD": "Твердость оболочки 2,0 мм (SD)",
        "shell_two_hardness_min": "Твердость оболочки 2,0 мм (мин.)",
        "shell_two_hardness_max": "Твердость оболочки 2,0 мм (макс.)",
        "shell_two_hardness_mean": "Твердость оболочки 2,0 мм (сред.)",
        "shell_one_hardness_SD": "Твердость оболочки 1,0 мм (со)",
        "shell_one_hardness_min": "Твердость оболочки 1,0 мм (мин.)",
        "shell_one_hardness_max": "Твердость оболочки 1,0 мм (макс.)",
        "shell_one_hardness_mean": "Твердость оболочки 1,0 мм (сред.)",
        "shell_zero_point_five_hardness_SD": "Твердость оболочки 0,5 мм (SD)",
        "shell_zero_point_five_hardness_min": "Твердость оболочки 0,5 мм (мин.)",
        "shell_zero_point_five_hardness_max": "Твердость оболочки 0,5 мм (макс.)",
        "shell_zero_point_five_hardness_mean": "Твердость оболочки 0,5 мм (сред.)",
        "lesion_hardness_SD": "Плотность поражения (SD)",
        "lesion_hardness_min": "Плотность поражения (мин.)",
        "lesion_hardness_max": "Плотность поражения (макс.)",
        "lesion_hardness_mean": "Плотность поражения (сред.)",
        "high_resolution_shear_wave_elastic_ultrasound": "Эластография сдвиговой волной высокого разрешения",
        "shear_wave_elastic_ultrasound": "Эластография сдвиговой волны",
        "quantitative_longitudinal": "количеств. (продольные срезы)",
        "qualitative_Rago_criteria": "качеств. (критерии Rago)",
        "strain_type_elastic_ultrasound": "Компрессионная эластография",
        "focus_enhancement_direction": "Направление контр. усиления поражения",
        "focus_enhancement_mode": "Режим контр. усиления поражения",
        "enhancement_degree_of_focus_refer_to_thyroid": "Уровень контрастного усиления поражения (по отношению к паренхиме вокруг щитовидной железы)",
        "enhancement_time_of_focus_refer_to_thyroid": "Время контрастного усиления поражения (по отношению к паренхиме вокруг щитовидной железы)",
        "high_frame_rate_contrast_ultrasound": "Контрастное УЗИ с высокой частотой кадров",
        "conventional_contrast_enhanced_ultrasound": "Стандартное УЗИ с контрастированием",
        "focal_blood_flow_CPP": "Парциальный кровоток (ЦПД)",
        "ultrafine_blood_flow_imaging_UMA": "UMA для микровизуализации кровотока",
        "focal_blood_flow_AdlerGrade": "Парциальный кровоток (кат. Adler)",
        "color_Doppler_CDFI": "CDFI: ЦДК",
        "TI_RADS_category": "Классификация TI-RADS",
        "microcalcification_in_lesion": "Микрокальцинаты в поражении",
        "lesion_margin": "Граница поражения",
        "aspect_ratio_of_focus": "Соотношение сторон поражения",
        "focal_echo": "Эхо. поражения",
        "focus_size_diameter_line_2": "Размер поражения: диаметр 2 см",
        "focus_size_diameter_line_1": "Размер поражения: диаметр 1 см",
        "focus_location": "Местополож. поражения",
        "focus_No": "№ поражения",
        "focus_number": "Количество поражений",
        "focus": "Поражение",
        "cervical_lymph_nodes": "Шейные лимфатические узлы",
        "thyroid_parenchyma_echo": "Эхо паренхимы щитов. железы",
        "overview_of_neck_ultrasound": "Обзор УЗИ шеи",
        "followUpReuslt": "Результаты послед. набл.",
        "pathologyResult": "результаты патологии в рамках операции",
        "FNA_result": "Результат FNA",
        "pathology_or_follow_up": "Патология/контроль",
        "thyrotropin_receptor_antibody": "Антитела к рецепторам ТТГ (анти-рТТГ)",
        "anti_thyroid_peroxidase_antibody": "Антитела к тиреопероксидазе (анти-ТПО)",
        "thyroglobulin_antibody": "Антитела к тиреоглобулину (АТ-ТГ)",
        "thyrotropin": "Тиреотропный гормон (ТТГ)",
        "free_triiodothyronine": "Свободный трийодтиронин (T3св.)",
        "free_thyroxine": "Свободный тироксин (T4св.)",
        "tetraiodothyronine": "Тетрайодтиронин (TT4)",
        "triiodothyronine": "Трийодтиронин (T3)",
        "supply_thyroid_function_text": "Функция щитов.железы",
        "supply_choice": {
            "points_5": "5 баллов",
            "points_4": "4 балла",
            "points_3": "3 балла",
            "points_2": "2 балла",
            "mixability": "смешение",
            "centrifugality": "центробежность",
            "centripetal": "центрипетальн.",
            "annular_enhancement": "усиление кольц.",
            "uneven_enhancement": "неравном. контр. усиление",
            "uniform_enhancement": "равномерное усиление",
            "punctate_enhancement": "точечное усиление",
            "high_enhancement": "высок. усиление",
            "Isoenhancement": "усиление",
            "low_enhancement": "низ. усиление",
            "no_enhancement": "нет усиления",
            "fast_rewind": "перемотка назад",
            "sync_enhancements": "улучшения синхронизации",
            "fast_forward": "перемотка вперед",
            "level_III": "уровень III",
            "level_II": "уровень II",
            "level_I": "уровень I",
            "level_0": "уровень 0",
            "category_5": "категория 5",
            "category_4C": "категория 4C",
            "category_4B": "категория 4B",
            "category_4A": "категория 4A",
            "category_3": "категория 3",
            "unsmooth": "неровн.",
            "smooth": "гладк.",
            "no_erect": "не верт.",
            "erect": "прямо",
            "high_echo": "высокоэхоген.",
            "isoechoic": "Изоэхогенн.",
            "low_echo": "низ. эхо",
            "very_low_echo": "оч. низк. эхо",
            "right": "прав.",
            "left": "лев.",
            "isthmus": "перешеек",
            "highEchoGroup": "группа высокоэхоген.",
            "microcalcification": "микрокальцинаты",
            "cysticDegeneration": "кистозная дегенерация",
            "no_suspicious_MT_lymph_nodes": "нет лимф.узлов с подозрением на мтс",
            "suspicious_MT_lymph_nodes": "лимф.узлы с подозрением на мтс",
            "lesion_is_significantly_enlarged": "Значительное увеличение времени контрольной оценки поражения",
            "no_change_of_benign_lesions": "нет изменений в доброк. поражениях при послед. наблюдении",
            "no_lymph_node_metastasis": "отсутствие метастазов в лимф. узлы после операции по поводу злокач. опухоли",
            "lymph_node_metastasis": "метастазы в лимф. узлы после операции по поводу злокач. опухоли",
            "malignant": "злокач.",
            "benign": "доброкач.",
            "ambiguity": "неоднозначн.",
            "not_have": "Нет",
            "have": "Да"
        },
        "confirm_passing_the_case": "Действительно передать сведения о случае?",
        "caseid_not_null": "Введите номер случая"
    },
    "hfr": {
        "HFRR_diagnosis": "Метод диагностической визуализации с HFR",
        "no_same_no_annotate": "Заключение просмотра не противоречиво.",
        "case_info_no_complete": "Неполные сведения о пациенте",
        "pass_review": "Действительно утвердить данные просмотра? После этого заключение нельзя изменить.",
        "search_other_options": {
            "0": "Ввести недост. сведения о пац.",
            "1": "Заключение просмотра является согласованным",
            "2": "Заключение просмотра является согласованным, сведения о пациенте являются полными ",
            "-1": "Все"
        },
        "same_annotation": "Заключение просмотра является согласованным",
        "case_info_complete": "Ввести недост. сведения о пац.",
        "no_assignment": "Нет назначения",
        "assignment_B": "Задание B",
        "assignment_A": "Задание A",
        "approval_btn": "Принятие",
        "topic_text": "Тема",
        "belonging_topic_text": "Тема",
        "end_comment_text": "Заключение просмотра",
        "new_create_comment": "Новое заключение",
        "confirm_to_approve_B": "Действительно принять заключение просмотра данных ангиографии с высокой частотой кадров? Заключение нельзя изменить после принятия.",
        "confirm_to_approve_A": "Действительно принять заключение просмотра данных стандартной ангиографии? Заключение нельзя изменить после принятия.",
        "project_name_title": "Название проекта",
        "only_view_mode_tip": "Режим «Только для чтения», невозможно изменить",
        "review_info_submit_success": "Сведения о просмотре успешно отправлены",
        "review_info_save_success": "Сведения о просмотре успешно сохранены",
        "judge_btn": "Решение",
        "confirm_notice_text": "Это необратимая операция. Вы подтверждаете отправку?",
        "annotation_view_label": "просмотр",
        "annotateCase_option": {
            "HCC": "HCC",
            "equal": "Равно",
            "vague": "Неясно или другое",
            "spoke_like": "Спица",
            "disordered_irregular_shape": "Неровная и неправильная форма",
            "coarse_twisted_shape": "Толстая спираль",
            "branch_like": "Отделение",
            "centrifugal": "Центробежн.",
            "centripetal": "Центрипетальн.",
            "whole_part": "Цел.",
            "inset_part": "Внутренний/центральный отдел",
            "peripheral_part": "Периферическая область",
            "random_distribution": "Случайное распределение",
            "inset_high_strength": "Внутреннее высокое усиление, периферическое равное/низкое усиление",
            "inset_no_strength": "Высокое контр. усиление периферических зон, внутр. области с низким уровнем/без усиления",
            "no_high": "Не высок.",
            "no_early": "Нет,ранн.ст.",
            "early": "Рано"
        },
        "distance_to_body": "Расстояние от передней части поражения до поверхности тела (см):",
        "is_CA_subside": "Происходит ли накопление контрастного вещества в поражении?",
        "diagnosis": "Диагностика методом ангиографии",
        "delay_phase": "Период задержки контр. вещ.",
        "venous_phase": "Фаза ангиографии воротной вены",
        "arterial_phase": "Ангиографическая артериальная фаза",
        "HFRR_delay_phase": "Период задержки контрастирования при HFR",
        "HFRR_venous_phase": "Фаза портальной вены при ангиографии с высокой частотой кадров",
        "HFRR_arterial_phase": "Ангиография артериальной фазы с высокой частотой кадров",
        "delay_lesions_enchance_degree": "Степень контрастного усиления поражения в конце периода задержки:",
        "RCEH_delay_phase": "Поздняя фаза стандартного УЗИ с контрастированием",
        "portal_lesions_enchance_degree": "Степень контрастного усиления в конце фазы воротной вены:",
        "RCEH_portal_phase": "Фаза воротной вены при стандартном УЗИ с контрастированием",
        "vascular_morphology": "Морфология сосудов при контрастном усилении артерий с начальным поражением:",
        "CA_direction": "Направление перфузии контрастного вещества во время контрастного усиления:",
        "first_strength_area": "Область контрастного усиления при первом исследовании:",
        "strength_distribution_features": "Характеристики распределения мощности:",
        "strength_uniform_distribution": "Однородность распределения мощности:",
        "lesions_enchance_degree": "Макс. степень контрастного усиления поражения (по сравнению с тканью вокруг печени):",
        "lesions_enchance_time": "Время контрастного усиления поражения (по сравнению с тканью вокруг печени):",
        "RCEH_arterial_phase": "Артериальная фаза при стандартном УЗИ с контрастированием:",
        "case_date_text": "Дата",
        "assign_succuss_text": "Автоназначение выполнено успешно",
        "confirm_to_assign_exam": "Эта операция автоматически назначит исследование двум экспертам. Продолжить?",
        "data_examine_btn": "Просмотр данных"
    },
    "numberOfImages": "Колич. изображений",
    "HFRR_diagnosis": "Метод диагностической визуализации с HFR",
    "RCEH_diagnosis": "Стандарт. диагностика с контраст.",
    "character_type": {
        "0": "Нет",
        "1": "Обычный польз.",
        "2": "Просм.лицо",
        "3": "Утвержд.лицо",
        "4": "Арбитр",
        "5": "Администратор",
        "6": "Заказчик"
    },
    "exam_status": {
        "1": "Подлежит представлению",
        "2": "Подлежит рассмотрению",
        "3": "Отклонено",
        "4": "Подлежит проверке",
        "5": "Будет определено",
        "6": "Проверено",
        "-1": "Все"
    },
    "sex": {
        "0": "Муж.",
        "1": "Жен.",
        "2": "Неизвестно"
    },
    "no_uniformity": "Неоднородность",
    "uniformity": "равномерн.",
    "export_empty_tip": "Выберите хотя бы один случай.",
    "export_fail_tip": "Номер случая {1} — сбой загрузки изображения. Продолжить экспорт других случаев?",
    "export_case": "Экспорт",
    "number_text": "Серийный номер",
    "please_input_nickname": "Имя пользователя",
    "group_by": "Группа",
    "reject_success_tip": "Случай успешно отклонен!",
    "reject_reason_no_white": "Поле «Причина отклонения» не может быть пустым!",
    "recent_two_month": "Последние два месяца",
    "recent_one_month": "Последний месяц",
    "recent_two_week": "Последние две недели",
    "reject_reason_title": "Введите причину отклонения.",
    "case_view_label": "просм. случая",
    "view_btn": "Вид",
    "reject_btn": "Отклонить",
    "query_btn": "Запрос",
    "is_modify_authorition": "Изменить авторизацию пользователя",
    "authorition": "Авторизация",
    "nickname": "Псевдоним",
    "assign_btn": "Просмотр",
    "exam_type": "Тип исслед.",
    "case_num": "Номер случая",
    "index_num": "Указат.",
    "case_status": "Статус случая",
    "end_date": "дата оконч.",
    "date_to": "до",
    "start_date": "дата начала",
    "upload_datetime": "дата выгрузки",
    "permission_assignment": "Назначение разрешений",
    "exam_view_title": "Просм. иссл.",
    "multicenter_title": "Многоцентров.",
    "select_from_friend_list": "Выберите из списка друзей",
    "select_from_group_list": "Выберите из групп",
    "length_limit_of": "ограничение длины в ",
    "server_disconnect": "сервер отключен",
    "AGORA_MEDIA_DEVICE_STATE_TYPE": {
        "0": "Готово.",
        "1": "_АКТИВН.",
        "2": "_ОТКЛЮЧЕНО",
        "4": "_ОТСУТСТВУЕТ",
        "8": "_ОТСОЕДИНЕНО",
        "16": "_НЕ РЕКОМЕНДОВАНО"
    },
    "AGORA_MEDIA_DEVICE_TYPE": {
        "0": "УСТРОЙСТВО ВОСПРОИЗВ. АУДИО",
        "1": "УСТРОЙСТВО ЗАПИСИ АУДИО",
        "2": "УСТРОЙСТВО ЗАПИСИ АУДИО",
        "3": "УСТРОЙСТВО ЗАХВАТА ВИДЕО",
        "4": "УСТРОЙСТВО ВОСПРОИЗВЕДЕНИЯ АУДИО В ПРИЛОЖЕНИИ",
        "-1": "НЕИЗВЕСТНОЕ АУДИОУСТРОЙСТВО"
    },
    "groupSetNameExists": "Сообщество уже существует",
    "ota_update": "Обновление OTA",
    "reverse_control_fail_another": "Сбой запроса на дистанционное управление. Устройство может находиться под контролем другого пользователя",
    "disconnect_control": "Отключить",
    "reverse_control_gain": "Усил.:",
    "reverse_control_depth": "Глубина:",
    "reverse_control_name": "Управление ультразвуковым аппаратом ${1}",
    "input_collection_name": "Введите имя тега.",
    "group_favorite_total_text": "Всего",
    "personal_favorite_text": "Мое избранное",
    "update_tag_success": "Тег обновлен успешно.",
    "select_at_least_item": "Выберите хотя бы один элемент для операции",
    "change_a_tag": "Изменить тег",
    "new_a_tag": "Новый тег",
    "exit_same_tag_name": "Тег уже существ.",
    "reverse_control_disconnect": "Пульт дистанционного управления отключен",
    "reverse_control_loading": "Ожидание ответа о дистанционном управлении от другой стороны",
    "reverse_control_close": "Отсоединить пульт ДУ?",
    "reverse_control_reject": "Отклонение запроса на удаленный контроль ультразвукового аппарата.",
    "reverse_control_confirm_tip": "Применить ли дистанционное управление УЗ-устройством, используемым пользователем",
    "reverse_control_title": "Дистанц. управление",
    "delete_group_favorite_tag_confirm": "Действительно удалить этот тег?",
    "group_favorite_text": "Коллекция группы",
    "live_replay_second": "с",
    "live_replay_minute": "М",
    "live_replay_hour": "H",
    "private_comment": "Личный комментарий",
    "current_live_status": "Состояние",
    "referral_introduce_A4": "После регистрации введите реферальный код или получите утверждение от администратора, чтобы стать официальным пользователем",
    "referral_introduce_Q4": "В4: Как стать официальным пользователем?",
    "referral_introduce_A3": "Приложение: Я – Предложить регистрацию; окно заявки + Предложить регистрацию",
    "referral_introduce_Q3": "В3: Где указан реферальный код?",
    "referral_introduce_A2": "Обратитесь к другим официальным пользователям MiCo+ за реферальным кодом",
    "referral_introduce_Q2": "В2: Как получить реферальный код?",
    "referral_introduce_A1": "Реферальный код — это сертификат для других официальных пользователей MiCo+, который приглашает вас стать официальным пользователем",
    "referral_introduce_Q1": "В1: Что такое реферальный код",
    "how_to_get_referral": "Как получить",
    "no_referral_code": "Нет реферального кода?",
    "client_only_tip": "Загрузите приложение для использования этой функции",
    "export_start_tip": "Задача скачивания запущена",
    "login_or_register_email": "Войдите в систему с подтверждением по электронной почте, новая эл. почта будет зарегистрирована автоматически",
    "login_or_register_mobile": "Войдите в систему с подтверждением по номеру телефона, новый номер будет зарегистрирован автоматически",
    "live_detail": "Свед.:прям. пер.",
    "iworks_fail_label": "Сбой получения оценки",
    "iworks_score_label": "Оценка:",
    "only_jpg_png": "Разрешить выгрузку изображений только в форматах PNG и JPG",
    "not_support_del_invite_members": "Приглашенные участники не могут быть удалены.",
    "no_permission_to_open_room": "У вас нет разрешения на открытие комнаты прямой трансляции",
    "cover_upload": "Загрузить обложку",
    "live_broadcast_cancel": "Отменено",
    "live_broadcast_end": "Заверш.",
    "live_broadcasting": "Идет прямая трансл.",
    "begin_in_minute": "Сейчас начнем...",
    "waiting": "Ожидание",
    "live_room": "Комн.пр.пер.",
    "remark_text": "Пометка",
    "set_remark": "Правка контактов",
    "quick_entry": "Быстрый ввод",
    "login_need_password": "Учетная запись администратора, войдите в систему с помощью учетной записи и пароля",
    "login_with_account": "Войти через уч. запись",
    "login_with_mobile": " Вход/регистрация с помощью SMS",
    "picture_unit": "Р",
    "search_result_tip": "Результаты поиска для «{1}»",
    "hot_search_tip": "Гор.",
    "delete_all_history_tip": "Действительно удалить всю историю поиска?",
    "delete_history_tip": "Действительно удалить эту историю поиска?",
    "live_invite_status3": "Прямая трансляция, которую запланировал(а) {1}, отменена.",
    "live_invite_status2": "Прямая трансляция, которую выполнил(а) {1}, завершена.",
    "live_invite_status0": "Прямая трансляция, которую запланировал(а) {1}, началась.",
    "live_invite_status4": "Прямая трансляция, которую запланировал(а) {1}, скоро начнется.",
    "live_invite_status1": "{1} запланировал(а) прямую трансляцию.",
    "live_theme": "Тема прям.пер.",
    "send_invitation": "Отправить приглашение",
    "search_invite_member": "поиск друзей/группы/сообществ",
    "describe": "Описать",
    "available_live": "доступная стрим",
    "my_booking_live": "Моя стрим",
    "edit_live": "Ред.пр.транс.",
    "booking_live": "Бронирование прям.транс.",
    "my_live": "Моя прямая трансляция",
    "upload_live_cover": "Выгрузите обложку прямой трансляции",
    "enter_live_des": "Опишите прямую трансляцию",
    "select_live_time": "Выберите время прямой трансляции",
    "enter_live_name": "Введите назв. прямой трансляции",
    "group": "Группа",
    "friend": "Друзья",
    "dissolve_live": "Завершить прямую трансляцию?",
    "live": "ПрПер",
    "moderator": "хост",
    "mine": "Я",
    "reset_mobile_success": "Номер мобильного телефона успешно задан",
    "welcome_tip": "Добро пожаловать в MiСo+",
    "referral_success_tip": "Указан правильный реферальный код. Вы стали официальным пользователем",
    "fill_in_referral_code": "Введите реферальный код",
    "login_directly": "Проб. версия на 7 дней",
    "register_by_mobile": "Зарегистрируйтесь по номеру моб. тел.",
    "reset_password_way": "Выберите способ сброса пароля",
    "not_surpport_forget": "Внутр. сеть не поддерживает получение пароля. Обратитесь к администратору.",
    "read_and_agree": "Сначала ознакомьтесь с политикой конфиденциальности и примите ее",
    "other_register_way": "Другие параметры регистрации",
    "other_login_way": "Другие варианты входа",
    "desensitization_reception": "Скрыть сведения о пациенте",
    "ask_want_to_cancel_apply_speak_permission": "Вы отправили запрос на поднятие руки. Отменить запрос?",
    "ask_want_to_apply_speak_permission": "У вас нет разрешения на речевое сообщение. Отправить запрос?",
    "allows_members_self_mute": "Позволяет участникам отключать микрофон",
    "destroy_replace_text": "Польз. вышел из сист.",
    "account_destroy_tip": "Учетная запись отменена.",
    "unmute_all": "Вкл.звук всех",
    "members_manage_title": "Управл. данными участников",
    "is_agree_all_user_speak": "Включить звук для всех?",
    "is_agree_user_speak": "Разрешить этому пользователю говорить?",
    "all_members_silenced": "Отключ. звук всех и новых участников",
    "more_than_six_mute": "Отключение звука для более 6 участников",
    "no_mute": "Включите микрофон после подключения к прямой трансляции",
    "all_mute": "Отк.звук после подкл.к прямой трансляции",
    "mute_setting_voice_ctrl_mode": "Режим контроля откл.звука",
    "log_off_waring": "При отмене учетной записи UltraCync вы больше не сможете использовать эту учетную запись или извлечь какие-либо материалы или информацию, которые вы добавили или получали (даже если вы используете тот же номер мобильного телефона/адрес электронной почты для повторной регистрации). Действительно продолжить?",
    "admin_cannot_autologin": "Нельзя автоматически войти в учетную запись администратора из-за ограничений безопасности",
    "cancel_account": "Отмена уч. записи",
    "unrecognized_device_type": "Неизвестный тип устройства",
    "device_not_logged_server": "Устройство не авторизовано на сервере и не подключено к той же сети",
    "incorrect_network": "Неверный тип сети. Сеть отключена.",
    "box_not_found": "Панель не найдена",
    "switch_account": "Перекл. уч. запись",
    "account_logged_in": "Выполнен вход в эту учетную запись",
    "phone_no_login": "Вход в систему на телефоне не выполнен",
    "share_risk_content": "При обмене сведениями с другими группами убедитесь, что участники этой группы имеют необходимую квалификацию, и просмотрите сведения. В противном случае это может привести к утечке данных пациента.",
    "codeMustRequired": "Период для входа истек, войдите повторно",
    "userChangePwdNeedLogin": "Пароль изменен. Выполните вход еще раз.",
    "email_verification_code_empty": "Поле для проверочного кода с эл. почты не может быть пустым.",
    "userTokenError": "Период для входа истек, войдите повторно",
    "userOutOfTrail": "Срок действия пробной версии учетной записи истек, обратитесь к администратору",
    "image_code_tip": "Проверочный код изображения",
    "image_code_empty": "Поле проверочного кода изображения не может быть пустым.",
    "verify_send_email": "Отправка эл.почты: ",
    "verify_send_sms": "Отправить SMS: ",
    "verify_description": "Для этой операции требуется аутентификация:",
    "verify_title": "Аутентификация",
    "please_agree_privacy_policy": "Примите политику конфиденциальности MiCo+",
    "please_agree": "Примите условия",
    "email_is_invalid_input_again": "Недействительный адрес эл. почты. Введите еще раз!",
    "verify_with_email": "Подтвердите с эл. почтой",
    "verify_with_mobile": "Подтвердите с помощью номера моб. телефона",
    "forget_password_get_email_code": "Отпр.",
    "email_verification_code": "Проверочный код",
    "register_email": "ЭлПочта",
    "equipment_testing": "Настройки аудио и видео",
    "slide_tip": "Для переключения между изображениями можно двигать мышь влево и вправо. ",
    "case_database_error_code": "Сбой поиска. Ошибка:",
    "case_database_tip": "В настоящее время включены только случаи рака молочной железы, в будущем спектр будет расширен.",
    "private_group_not_share": "Невозможно поделиться личные группами в WeChat.",
    "case_database_id": "ID",
    "image_count": "Изобр.",
    "live_playback": "Воспр. прям. пер.",
    "product_name_value_ce": "MiCo+",
    "product_name_value_cn": "Система удаленной визуализации MiCo+",
    "software_description_ce": "ИТ-решение для визуализации MiCo+ от компании Mindray стремится создать профессиональное сообщество для врачей. С помощью MiCo+ вы можете выполнять удаленный контроль качества и консультирование, а также использовать его для обучения, специализированных собраний и в других медицинских областях. Shenzhen Mindray Bio-Medical Electronics Co., Ltd, 2021 г. Все права защищены",
    "software_description_cn": "Платформа удаленной визуализации Mindray MiCo+ предназначена для создания профессионального сообщества для врачей. С помощью UltraSync можно осуществлять удаленный контроль качества и консультации в реальном времени/в автономном режиме, обучение и учебные задания, управление случаями в сети, а также другие интеллектуальные медицинские возможности. Shenzhen Mindray Bio-Medical Electronics Co., Ltd, 2021 г. Все права защищены.",
    "device_notice_camera_insert": "подключите камеру ",
    "device_notice_camera_pull_out": "Отсоедините камеру",
    "case_require_no_filled": "Поле сведений о пациенте не может быть пустым (элементы с * опциональны, остальные — обязательны)",
    "case_database_title": "Случай мол.жел.",
    "confirm_update_report": "Необходимо обновить отчет. Продолжить?",
    "refute_reason_text": "Причина отклонения:",
    "live_management": "Управл. прямой трансляцией",
    "privacy_welcome_p3": "После входа в систему ознакомьтесь с руководством пользователя",
    "privacy_welcome_p2": "Мы напомним вам во всплывающем окне при первой загрузке приложения или использовании перечисленных выше функций. Вы можете сделать выбор в зависимости от ваших потребностей. Подробнее см. в политике конфиденциальности MiCo+. MiCo+ будет строго защищать ваши личные данные и обеспечивать информационную безопасность. Мы формируем политику конфиденциальности MiCo+ в соответствии с применимым законодательством. Внимательно прочитайте и полностью поймите условия соглашения, прежде чем нажать кнопку согласия со следующей политикой.",
    "privacy_welcome_p1": "Уважаемые пользователи! Благодарим за использование MiCo+. MiCo+ — это сетевая платформа для сообщества врачей, разработанная компанией Shenzhen Mindray Bio-Medical Electronics Co., Ltd. Данная политика конфиденциальности предназначена для информирования вас о том, как решение MiCo+ будет собирать, использовать и хранить ваши личные данные и какими правами обладаете вы в отношении ваших личных данных.",
    "privacy_welcome_title": "Добро пожаловать в MiСo+",
    "ultrasync_privacy_protocol": "Протокол конфиденциальности MiCo+",
    "exit_group_tip": "выйти из группового чата",
    "join_group_tip": "присоедин. к групп. чату",
    "add_btn": "Доб.",
    "replace_btn": "Заменить",
    "autograph": "Подпись:",
    "examining_doctor": "Исслед. врач:",
    "report_time": "Время отчета:",
    "ultrasonic_prompt": "Подсказка УЗИ:",
    "ultrasonic_discovery": "Результаты УЗИ:",
    "exam_position": "Положение исслед.",
    "sending_physician": "Врач приемного отделения:",
    "bed_number": "Номер койки:",
    "inpatient_number": "Номер больницы:",
    "outpatient_number": "Номер клиники:",
    "department_title": "Отделение:",
    "ultrasound_number": "Номер ультразвукового исследования",
    "report_subtitle": "Отчет об ультразвуковом исследовании",
    "report_title": "Отделение УЗ-диагностики в больнице",
    "registration_certificate_no": "Регистрационный номер сертификата",
    "product_model": "Model",
    "product_name": "Название продукта",
    "conversation_not_init": "Диалог не инициализирован. Повторите попытку позже.",
    "no_realtime_tip": "Видео в реальном времени отсутствует. Подождите. ",
    "will_open_conversation": "(Откройте экран диалога ${subject})",
    "enter_live": "Войти:прямая трансляция",
    "enter_gallery": "Войти в галерею",
    "browser_no_support_video": "Ваш браузер не поддерживает следующие теги",
    "browser_no_support_audio": "Ваш браузер не поддерживает следующие теги",
    "file_has_downloaded": "Файл загружен.",
    "save_path": "Путь сохр.:",
    "select_groupset": "Сначала выберите сообщество.",
    "groupset_not_activity": "В этом сообществе нет групповой активности.",
    "select_statistics": "Выберите данные статистики",
    "details": "Подробно",
    "ultrasync_live": "Прямая трансляция MiCo+",
    "conference_invitation": "Приглашение на конференцию:",
    "unsurpport_pdf": "Файлы PDF для предварительного просмотра не поддерживаются в вашей мобильной версии.",
    "file_storage_manage": "Управление кэшем текущей учетной записи",
    "privacy_statement_title": "Заявление о политике защиты личных данных MiCo+",
    "domain_name_tip": "Нет необходимости вводить HTTPS://",
    "other_data": "Прочие данные",
    "file_upload_exception": "Ошибка выгрузки файла",
    "ftp_path_tip": "Советы: формат адреса хранилища — это IP-адрес или имя домена.",
    "reselect_upload_file": "Выберите файл для выгрузки еще раз",
    "copyright": "SHENZHEN MINDRAY BIO-MEDICAL ELECTRONICS CO., LTD. Все права защищены.",
    "no_videocamera_be_detected": "Камера не обнаружена. Сначала подключите камеру.",
    "videocamera_muti_devices": "Обнаружено несколько камер. Выберите одну.",
    "videocamera_title": "Камера",
    "select_micro_device_text": "Обнаружено несколько микрофонов. Выберите подходящее устройство.",
    "watch_supply_case_title": "Просмотр информации",
    "case_exam_status": {
        "judgeSubmit": "Проверено",
        "reviewed": "Будет определено",
        "assigned": "Подлежит проверке",
        "reject": "Отклонено",
        "submited": "Подлежит рассмотрению",
        "saved": "Подлежит представлению",
        "unsubmit": "Подлежит представлению"
    },
    "patient_case_status": "Статус исслед.:",
    "supply_case_close_btn": "Закрыть",
    "privacy_policy": "Политика конфиденц.",
    "file_downloading": "Загрузка...",
    "open_file": "Открыть файл",
    "file_download_progress": "Выполнение загрузки:",
    "less_than_1kb": "Менее 1 КБ",
    "file_size": "Размер файла:",
    "file_name": "Имя файла:",
    "wait_download_file": "Идет загрузка других файлов. Подождите...",
    "tip_file_open_fail": "Невозможно открыть файл.",
    "tip_file_download_success": "Загрузка файла завершена!",
    "tip_file_download_fail": "Сбой загрузки файла!",
    "confirm_delete_file": "Удалить выбранный файл?",
    "image_corruption_text": "Неподдерживаемый формат изображения. Используйте другое изображение.",
    "ban_to_negative_number": "Это значение не может быть отрицательным. ",
    "mobile_number_is_invalid_input_again_cn": "Недопустимый номер телефона. Введите правильный номер!",
    "invite_you_join_group": "Приглашаем вступить в группу:",
    "import_licence": "Импорт. лицензию",
    "no_more_text": "Больше нет сообщений",
    "playing_video_tip": "Вы смотрите прямую трансляцию. Эта функция недоступна.",
    "login_fail_with_lock_tip": "Проверка не пройдена {2} раза подряд в течение {1} мин. При сбое подтверждения {3} раз. подряд, учетная запись будет заблокирована на {4} ч.",
    "enhance_password_tip": "Ненадежный пароль. Измените пароль!",
    "please_choose_topic_text": "Выберите тему",
    "now_topic_text": "Текущая тема:",
    "liver_topic_text": "Тема печени",
    "gall_bladder_text": "Тема желчного пузыря",
    "gall_bladder_no_file_preview": "Элемент не может быть выгружен или просмотрен в рамках темы желчного пузыря.",
    "no_upload_report_text": "Вложения не выгружены. Выгрузите вложение перед предварительным просмотром.",
    "upload_file_error_text": "Сбой выгрузки вложения.",
    "delete_groupset_tip": "Сообщество удалено",
    "back_button": "Назад",
    "ref_res_expired": "Неспособность",
    "init_supply_case_fail": "Сбой инициализации данных случая.",
    "supply_case_fail": "Сбой обновления сведений об исследовании. Обратитесь к администратору.",
    "supply_case_success": "Сведения об исследовании успешно обновлены.",
    "choose_date_time": "Выбрать дату",
    "ultrasound_diagonse_time": "Время УЗ-исследования",
    "cdfi_blood_flow": "Кровоток в режиме ЦДК",
    "echo_text": "Эхо",
    "sizeof_lesions_two": "Размер поражения (диаметр 2см)",
    "sizeof_lesions_one": "Размер поражения (диаметр 1 см)",
    "target_lesions_site": "Локализация целевых поражений",
    "number_of_gallbladder_lesions": "Количество поражений в желчном пузыре",
    "number_of_intrahepatic_lesions": "Количество внутрипеченочных поражений",
    "grey_scale_ultrasound_text": "УЗИ в режиме серой шкалы",
    "pathologic_diagonsis_text": "Патологический диагноз",
    "more_mri_diagonse_text": "Контрольная МРТ (≥ 6 месяцев)",
    "first_time_mri_diagonse_text": "Первая диагностика МРТ",
    "mri_diagonse_text": "Диагностика МРТ",
    "cirrhosis_text": "Цирроз",
    "hepatitis_text": "Гепатит",
    "patient_history_text": "Анамнез пациента",
    "tumor_markers_text": "Опухолевые маркеры",
    "indirect_bilirubin_text": "Непрямой билирубин (IB, мкмоль/л)",
    "direct_bilirubin_text": "Прямой билирубин (DB, мкмоль/л)",
    "total_bilirubin_text": "Общий билирубин (TB, мкмоль/л)",
    "alanine_aminotransferase_text": "Аланинаминотрансфераза (АЛТ, МЕ/л)",
    "aspartate_aminotransferase_text": "Аспартатаминотрансфераза (АСТ, МЕ/л)",
    "biochemical_examination_text": "Биохимическое исследование",
    "anti_hiv_text": "Анти-ВИЧ",
    "treponema_antibody_text": "Антитело к бледной трепонеме",
    "anti_hcv_text": "Анти-ВГС",
    "four_before_operation_text": "4 элемента исследования перед операцией",
    "select_placeholder_text": "Выбрать",
    "no_type": "<span class=\"red\">Не заполнено</span>",
    "no_need_follow_diagonse": "Последующее наблюдение не требуется",
    "mixed_echo": "Смешанные эхо",
    "high_level": "Высок.",
    "middle_level": "Средн.",
    "low_level": "Низ.",
    "mutiple": "Несколько",
    "sigle": "Един.",
    "adenomatous_polyp": "Аденоматозный полип",
    "cholesterol_polyp": "Холестериновый полип",
    "matastatic_carcinoma": "Метастатическая карцинома",
    "poorly_differentiated_hcc": "Низкодифференцированная ГЦК",
    "moderately_poorly_differentiated_hcc": "Умеренно и низкодифференцированный ГЦР",
    "moderately_differentiated_hcc": "Умеренно дифференцированный ГЦР",
    "moderately_well_differentiated_hcc": "Умеренно и высокодифференцированный ГЦР",
    "well_differentiated_hcc": "Высокодифференцированная ГЦК",
    "no_FNH": "Не FNH",
    "negative": "Отриц.",
    "positive": "Положит.",
    "ICC": "ICC",
    "FNH": "FNH",
    "no": "Нет",
    "yes": "Да",
    "hepatitis_five_items_text": "Гепатит B, пять пунктов",
    "blood_cell_text": "Тромбоциты (x10^9/л)",
    "middle_size_cell_rate": "Процентное содержание гранулоцитов (%)",
    "white_cell_text": "Лейкоциты (x10^9/л)",
    "red_cell_text": "Эритроциты (x10^12/л)",
    "supply_patient_age": "Возраст(лет)",
    "supply_patient_name": "ФИО",
    "supply_blood_routine_text": "Обыч. анализ крови",
    "supply_patient_info_text": "Свед. о пациенте",
    "supply_case_title": "Доп. свед.",
    "cannot_share_qrcode_text": "Невозможно поделиться личной группой с помощью QR-кода.",
    "is_recording_text": "Запись...",
    "skip_to_unread_text": "Перейти к непрочит.",
    "consulation_review_text": "Просм.прям. пер.",
    "exam_patient_text": "Пациент:",
    "withdraw_chat_message_fail": "Сбой отзыва. ",
    "withdraw_chat_message_fail_sended_by_others": "Сообщения, отправленные другими пользователями, не могут быть отозваны.",
    "send_recoding_text": "Отпустить для отправки",
    "other_cancel_realtime_voice_text": "Вызов отменен вызывающим абонентом",
    "self_cancel_realtime_voice_text": "Длительность: ",
    "exam_conversation_creator_tag": "Создано:",
    "groupset_manager": "Администратор",
    "groupset_member_title": "Выбр. участников",
    "next_step_text": "Далее",
    "select_audio_device_text": "Обнаружено несколько устройств вывода голоса. Выберите подходящее устройство.",
    "exceeded_max_withdrawal": "Превышено максимальное время отзыва.",
    "re_edit": "Правка",
    "revocation_message_by_other": "Отзыв сообщения",
    "revocation_message_by_self": "Вы отозвали сообщение. ",
    "revocation_message": "Отозвать",
    "get_country_list_fail": "Сбой загрузки списка.",
    "search_groupsets_text": "Поиск сообщества",
    "search_recent_chat_text": "Поиск недавних чатов",
    "choose_country": "Выбрать страну или регион",
    "in_total_text": "Всего",
    "group_has_deleted_text": "Эта группа удалена.",
    "select_hospital": "Выбрать больницу",
    "check_groupset_exist_text": "Сбой поиска сообщества. Проверьте, существует ли сообщество!",
    "search_type_error": "Ошибка типа поиска!",
    "display_all_data_error_txt": "Ошибки поиска!",
    "search_none_text": "Связанное содержимое не найдено. Используйте другое ключевое слово.",
    "real_time_warning_message": "Данные раб. станции не могут быть отпр. при прямой трансляции.",
    "warning_title": "Предупр.",
    "tv_wall_warning_message": "Данные рабочей станции не могут быть отправлены в режиме ТВ-стены. ",
    "device_notice_unusual_exit": "Выход",
    "device_notice_unusual_continue": "Продолжить",
    "device_notice_unusual_message": ". Нажмите [Продолжить] — голос изменится. Нажмите [Выход] — выйти из прямой трансляции. ",
    "device_notice_unusual_title": "Уведомл.об отклонении от нормы",
    "device_notice_now_device_pull_out": "Текущий микрофон отсоединен.",
    "analyze_min_save_time": "Минимальное время сохранения — ",
    "analyze_max_save_time": "Максимальное время сохранения — ",
    "analyze_avarage_save_time": "Среднее время сохранения — ",
    "analyze_min_download_time": "Минимальное время загрузки — ",
    "analyze_max_download_time": "Максимальное время загрузки — ",
    "analyze_avarage_download_time": "Среднее время загрузки — ",
    "analyze_min_text": "Минимум — ",
    "analyze_max_text": "Максимум — ",
    "analyze_avarage_text": "Средний размер — ",
    "analyze_total_download_text": "Всего загружено изображений",
    "charts_sunday_text": "Воскрес.",
    "charts_saturday_text": "Суббота",
    "charts_friday_text": "Пятница",
    "charts_thursday_text": "Четверг",
    "charts_wednesday_text": "Среда",
    "charts_tuesday_text": "Вторник",
    "charts_monday_text": "Понед.",
    "charts_online_device_text": "Количество устройств в сети:",
    "charts_device_unit_text": "-",
    "charts_device_total_text": "Общее число устройств:",
    "parsetime_seconds_text": " с",
    "parsetime_hours_text": " ч",
    "not_online_text": "Автоном.",
    "online_text": "В сети",
    "online_rate": "Онлайн-тариф",
    "unsupported_location_notice": "Позиционирование временно не поддерживается в этой области. Обратитесь к администратору для обновления пакета данных карты!",
    "repeat_client_text": "Эта ссылка открыта в другом месте.",
    "link_connected_fail_text": "Эта ссылка отключена. Откройте окно повторно.",
    "link_expired_text": "Недопустимая ссылка. Откройте окно повторно.",
    "groupset_no_conversation_notice": "Это сообщество не создало диалог! Закройте окно и начните диалог.",
    "device_number_text": "Количество устройств",
    "ultrasonic_today_text": "Статистика контроля качества УЗИ в реальном времени (сегодня)",
    "device_online_rate_text": "Онлайн-част. для устройства",
    "back_text": "Назад",
    "remote_QC_image_quality_text": "Количество изображений в режиме удаленного контроля качества",
    "live_broadcast_volume_text": "Время прямой трансляции (длит.)",
    "accumulation_text": "Кумулятивн.",
    "the_day_text": "День",
    "live_control_statistics_text": "Статистика контроля качества в реальном времени (длительность)",
    "remote_ultrasonic_center_text": "Центр обработки данных для удаленного контроля качества УЗИ",
    "no_support_webrtc_text": "Данный браузер не поддерживает webrtc, а функция прямой трансляции ограничена. Используйте версию приложения.",
    "voice_device_error_text": ". Подключите голосовое устройство и перезапустите приложение.",
    "rt_voice_connect_error_prefix": "Сбой голосового подключения.",
    "webrtc_connect_fail_text": "Сбой подключения к серверу сигнализации WebRTC. Перезапустите сервер.",
    "room_need_close_affix": ". Перезапустите прямую трансляцию",
    "join_room_err_prefix": "Ошибка входа в комнату",
    "no_speak_permission": "Голосовые сообщения запрещены при входе через браузер. \n Установите приложение, если требуются голосовые сообщения.",
    "groupset_text": "Сообщество",
    "no_found_group_text": "Группа не найдена",
    "no_found_user_text": "Пользоват. не найден",
    "more_text": "Еще",
    "recent_chat_text": "Последние чаты",
    "group_chat_text": "Групповые чаты",
    "contact_text": "Контакты",
    "wechat_invite_affix": ": приглашение в группу WeChat",
    "wechat_invite_prefix": "Пройд.",
    "my_groupset": "Мое сообщество",
    "group_set_profile": "Фотография профиля",
    "delete_groupset_text": "Удалить сообщество",
    "already_friend_msg": "Теперь мы друзья. Давайте пообщаемся!",
    "paste": "Вставка",
    "copy": "Копир.",
    "copy_text_success": "Коп. выпол. успешно.",
    "join_group_by_qrcode_tips": ": QR-код",
    "scaned_tip": "Вступите в групповой чат, отсканировав данные ",
    "qrcode_expired": "Срок действия QR-кода истек.",
    "add_group_successful": "Успешно вступил в группу",
    "auto_add_sharer_text": "Автоматически добавлять меня в друзья при вступлении в группу",
    "group_card_apply_btn": "Вступ. в группу",
    "group_visiting_card_title": "QR-код группы",
    "no_description_tip": "Нет описания",
    "groupset_delete_attendee": "Удалить участников",
    "groupset_add_attendee": "Доб. участников",
    "groupset_description": "Описание группы",
    "choose_only_one_file_to_handle": "Выберите только 1 файл для обработки.",
    "group_qrcode_card_notice_text": "Срок действия QR-кода истекает через 7 дней.",
    "group_qrcode_card_text": "QR-код группы",
    "unrecognized_qrcode": "Нераспознанный QR-код",
    "no_group_id": "Ошибка получения ID группы из QR-кода",
    "no_user_id": "Ошибка получения ID пользователя из QR-кода",
    "qrcode_card_notice_text": "Скан. QR-код, чтобы добавить меня",
    "qrcode_card_text": "Мой QR-код",
    "skip_set_password": "Проп.",
    "set_password_first": "Сначала задайте пароль!",
    "filter_hospital": "Поиск больниц",
    "input_info_check_tip": "Введите правильные символы согласно длине.",
    "length_limit_info": "(Длина <=20)",
    "input_info_check_err": "Можно вводить только китайские и английские символы или цифры.",
    "name_null": "Поле имени пустое.",
    "live_name": "Введите имя пр.пер.",
    "input_new_name": "Введите новое имя.",
    "rename": "Переимен.",
    "exam_end_time": "Зав.",
    "exam_start_time": "Пуск",
    "exam_mode_label": "Режим:",
    "exam_patient_sex": "Пол",
    "exam_patient_age": "Возр",
    "exam_patient_name": "ФИО",
    "all_iworks": "Все протоколы iWorks",
    "more_iworks_text": "Еще",
    "exception_to_login_again": "Неизвестная ошибка. Повторите вход!",
    "index_nav_files": "Файл",
    "library_video": "Видео",
    "library_image": "Изобр.",
    "protocol_title": "Сведения о протоколе",
    "view_not_uploaded": "Проекция не выгружена.",
    "click_to_refresh": "Нажмите, чтобы обновить",
    "cancel_download_confirm": "Отменить задачу загрузки?",
    "view_txt": "Вид",
    "checkout_protocol": "Проверьте протокол",
    "export_running": "Некоторые задачи скачивания не выполнены. Загрузка нескольких задач не поддерживается.",
    "media_transfer": {
        "error": {
            "add_task_error_waiting_queue_full": "Невозможно создать задачу, когда очередь заполнена! Повторите попытку позже!",
            "add_task_error": "Сбой создания задачи. Повторите попытку позже."
        },
        "clip_tip_startup": "Пуск обрезки Проверьте ход выполнения в разделе инструменте управления задачами. После завершения обрезки можно сохранять, пересылать и загружать видео.",
        "creating_task": "Создание задачи. Подождите."
    },
    "search_input_key": "Введите ключевые слова.",
    "version": "Версия MiCo+",
    "protocol_version": "Версия протокола",
    "doppler_version": "Версия допплера",
    "ecr_version": "Версия ECR",
    "product_manufacturer": "Изготовитель продукта",
    "product_type": "Тип продукта",
    "hospital_associate": "помощник",
    "hospital_director": "директор",
    "hospital_location": "Местоположение больницы",
    "hospital_address": "Адрес больницы",
    "hospital_name": "Название больницы",
    "mac_addr": "MAC-адрес",
    "loginLoading": "Автовход в систему",
    "task_manager": {
        "media_transfer": {
            "error": {
                "delete_task_error": "Сбой удаления задач!",
                "query_task_error": "Сбой запроса заданий!"
            },
            "status_list": {
                "1": "Ожидание",
                "2": "Обработка",
                "3": "Успешно",
                "4": "Сбой",
                "5": "Отмена"
            },
            "operation_import": "Импорт",
            "progress": "Прогресс",
            "status": "Состояние",
            "image_name": "Имя изображения",
            "image_source": "Источник изобр.",
            "task_id": "ID задачи"
        },
        "title": "Диспетчер задач"
    },
    "add_association": "Доб.",
    "association_hospitals": "Больницы Ассоциации",
    "bi_data_display": "Отображение данных BI",
    "location": "Местопол.",
    "device_has_no_network": "Сбой при сканировании для входа в систему: устройство не подключено к сети",
    "groupset_msg_empty": "Нет изобр.",
    "select_all_group": "Выберите все группы",
    "select_all_friend": "Выберите всех друзей",
    "get_groupset_exam_fail": "Сбой получения списка исследований.",
    "get_groupset_detail_fail": "Сбой получения списка исследований.",
    "groupset_detail_navbar": "Выгруженный список исслед.",
    "groupset_exam_count": "Колич. отправленных исслед.",
    "groupset_video_count": "Количество отправленных видео",
    "groupset_image_count": "Колич. отправленных изображений",
    "groupset_navbar": "Участники группы",
    "delete_group_set_success": "Группа успешно удалена!",
    "delete_group_set_fail": "Сбой удаления группы. Повторите попытку позже.",
    "delete_groupset_confirm": "Действительно удалить это сообщество?",
    "update_group_set_success": "Обновление группы успешно завершено!",
    "update_group_set_fail": "Не удалось обновить группы. ",
    "query_groupset_list_fail": "Сбой получения списка групп. ",
    "create_group_set_success": "Созд. новой группы выполн. успешно!",
    "create_group_set_fail": "Сбой создания группы. Повторите попытку позже.",
    "group_set_name_empty": "Имя сообщества не может быть пустым",
    "edit_group_set": "Правка группы",
    "group_list_empty": "Список групп пуст. Сначала вступите в группу.",
    "group_set_name": "Тема",
    "create_group_set": "Создать сообщество",
    "group_statistics": "Статистика группы",
    "group_set_statistics": "Статистика сообщества",
    "mute_message_number": "сообщения",
    "mute_notifications": "Заглушить уведомления",
    "female": "Жен.",
    "male": "Муж.",
    "exam_manager": {
        "error": {
            "sendto_target_invalid": "Недопустимый адресат пересылки!",
            "sendto_error": "Сбой отправки!",
            "mutiple_selected_unmatch": "{1} изобр. были отправлены другими пользователями, и сведения об исследовании не могут быть заданы! Продолжить настройку доступных изображений?",
            "single_selected_unmatch": "Свед. об иссл. не могут быть заданы для изобр., отправленных другими пользователями!",
            "conversation_not_open": "Соединение не установлено. Повторите попытку позже!",
            "patient_id_repeated": "ID пациента был использован. Введите еще раз!",
            "patient_id_empty": "Поле ID пациента не может быть пустым!",
            "import_exam_image_error": "Сбой импорта изображения в базу данных пациентов!",
            "new_exam_error": "Сбой создания нового пациента!",
            "search_exam_error": "Сбой поиска список облачных исследований!"
        },
        "sendto_success_tip": "Отправка выполнена успешно. Проверьте {1}.",
        "import_exam_image_success_tip": "Импорт выполнен успешно. Проверьте {1}.",
        "import_exam_image_tip": "Импортировать в данные пациента {1}?",
        "ai_image_info": "Сведения об изображении ИИ",
        "image_info": "Сведения об изображении",
        "exam_info": "Свед.об исслед.",
        "import_exam_image_confirm": "Подтверждение импорта",
        "new_exam": "Новый пациент",
        "title": "Выбрать или создать нового пациента"
    },
    "operation": "Операция",
    "exam_time": "Время исслед.",
    "patient_id": "ID пациента",
    "online_time": "время в сети (мин)",
    "user_id": "ID польз.",
    "cellphone": "Номер телефона",
    "time": "Время",
    "resource_id": "ID ресурса",
    "exam_id": "ID исслед. ",
    "series_number": "серийный номер",
    "group_name": "имя группы",
    "user_name": "имя польз.",
    "active": "активн.",
    "optional": "(Дополнительно. Регистрация без утверждения)",
    "users_without_hospital": "Пользователи, не относящиеся ни к одной больнице ",
    "all_users_of_this_hospital": "Все пользователи в данной больнице",
    "hospital_user": "Больница/пользов.:",
    "unread_message_tip": "новые сообщения",
    "someone_mention": "[Кто-то упомянул вас]",
    "mention_dialog_title": "Упоминание",
    "reset_mobile_tip_reset_mobile": "Настройка нового номера мобильного телефона:",
    "verify_password": "Подтверждение пароля:",
    "reset_login_name_tip_reset_login_name": "Задайте новое имя для входа:",
    "reset_login_name_success": "Имя пользователя задано успешно.",
    "reset_login_name": "Сброс имени для входа",
    "group_by_month": "Группировка по месяцам",
    "group_by_week": "Группировка по неделям",
    "group_by_day": "Группировка по дням",
    "sort_by_upload_ts": "Сортировка по времени выгрузки",
    "sort_by_exam_ts": "Сортировка по времени исслед.",
    "exam_group_separation_char": " до ",
    "in_consultation_text": "На консультации",
    "msg_review_tip": "Пометить сообщение как прочит.",
    "review_all": "Пометить все как прочит.",
    "share_link_to_wx": "Отправьте ссылки друзьям WeChat",
    "device_bar_type": {
        "6": "Doppler",
        "7": "Панель MiCo+"
    },
    "query_faq_fail": "Сбой запроса FAQ!",
    "faq_title": "FAQ",
    "delete_conference_tip": "Отмените собрание.",
    "conference_begin_tip": "Собрание начнется через 5 минут",
    "reserved_conference_tip": "Забронируйте собрание.",
    "reserved_success": "Бронирование выпол.успеш.",
    "delete_reserved_tip": "Действительно удалить это бронирование?",
    "reserved_duration_illegal": "Продолжительность конференции должна превышать 5 минут.",
    "reserved_starttime_illegal": "Время запуска не может быть раньше текущего времени",
    "reserved_overtime": "Длительность забронированного собрания превышает 8 часов.",
    "add_reserved_conference": "Доб.",
    "conference_subject_empty": "Введите тему собрания.",
    "end_time": "Время оконч.",
    "start_time": "Время запуска",
    "reserved_conference_time": "Время конференции",
    "reserved_conference_date_tip": "Выбрать дату",
    "reserved_conference_date": "Дата конференции",
    "reserved_conference_subject": "Тема конференции",
    "reserved_conference": "Забронировать собрание",
    "notification_body": {
        "new_message": "У вас новое сообщение.",
        "friend_apply": "У вас есть новый запрос на дружбу.",
        "join_group": "У вас есть новая заявка на группу."
    },
    "notification_reqeust_tip": "Чтобы своевременно получать сообщение, включите разрешение подачи уведомлений о сообщениях.",
    "scan_room_list_none": "Нет доступных ТВ-стен",
    "tv_wall_setting_edit_error": "Сбой настройки ТВ-стены!",
    "scan_room_sort_by_name": "ФИО",
    "scan_room_sort_by_creation_ts": "Период создания",
    "scan_room_sort": "Назнач.",
    "scan_room_list": "Список ТВ-стены (перетащите для изменения порядка)",
    "tv_wall_setting": "Настройки ТВ-стены",
    "sharing_starting": "Запуск общего доступа. Подождите...",
    "loading_module_text": "Загрузка модуля…",
    "loading_fail": "Сбой загрузки. Нажмите «Перезагрузить».",
    "login_sms_verification_code_empty": "SMS с проверочным кодом не может быть пустым.",
    "login_mobile_phone_empty": "Поле номера мобильного телефона не может быть пустым.",
    "edit_txt": "Правка",
    "reset_mobile": "Сбросить номер мобильного телефона",
    "reset_password_fail": "Сбой сброса пароля!",
    "set_admin_error": "Сбой задания {1} в качестве администратора. Повторите попытку!",
    "set_admin_confirm": "Задайте {1} в качестве администратора. Вы больше не будете администратором этой группы. Действительно продолжить?",
    "set_admin": "Настройка администраторов",
    "download_app_tip": "Отсканируйте QR-код, чтобы загрузить UltraSync",
    "file_transfer_assistant": "Передача файлов",
    "service_accounts": "ID службы",
    "referral_code_generate_err": {
        "time_out": "Таймаут. Повторите попытку!",
        "database_err": "Ошибка базы данных. Повторите попытку!",
        "none_referral_code": "Нет реферальных кодов. Повторите попытку!",
        "unknown_err": "Неизвестная ошибка. Повторите попытку!"
    },
    "referral_code_tip_expire": "Срок действия реферального кода истекает после {1}!",
    "referral_code_tip": "Советы: реферальный код можно использовать для регистрации в качестве официальных пользователей. Нет необходимости в утверждении администратором.",
    "referral_code_generate": "Обновите реферальный код",
    "invite_registration": "Предложить регистрацию",
    "search_friend": "Поиск друга",
    "referral_code_is_invalid_input_again": "Реферальный код должен состоять из 6 цифр или быть пустым. Повторите попытку.",
    "referral_code": "Реферальный код",
    "mindray_library": "Библиотека",
    "reupload_frame_tip": "Файл исследования не может быть повторно отправлен автоматически. Выберите и отправьте файл консультации еще раз.",
    "not_login_and_fail_to_start_rt_video": "УЗИ в реальном времени недоступно без входа в систему.",
    "history_tip": "Выше приведена более ранняя информация.",
    "switch_language_to_shoutdown": "Для фактического изменения языка необходимо перезапустить систему. Действительно продолжить?",
    "push_image_mode_set_fail": "Сбой настройки режима изображения MiCo+ Pusher! Сообщение об ошибке: {1}.",
    "push_image_mode_set_succ": "Установка режима изображения MiCo+ Pusher выполнена успешно!",
    "push_image_mode_setting": "Установка режима изображения MiCo+ Pusher. Подождите!",
    "push_image_mode_confirm_to_set": "Настройка режима изображения MiCo+ Pusher. Действительно продолжить?",
    "push_image_mode_fluency": "Частота",
    "push_image_mode_clarity": "Очист.",
    "push_image_mode": "Режим изображения MiCo+ Pusher",
    "realtime_video_review_text": "Воспр. прям. пер.",
    "realtime_video_text": "Видео в реал. времени",
    "report_text": "Отчет",
    "change_voice_device_need_to_close_realtime": "Чтобы изменения вступили в силу, аудиоустройству необходимо перезапустить прямую трансляцию.",
    "can_not_change_voice_device_in_browser": "Не удается изменить аудиоустройство системы по умолчанию с помощью браузера. Измените аудиоустройство по умолчанию в ПРИЛОЖЕНИИ Windows.",
    "history_version_introduce_content": "[Содержание] ",
    "history_version_introduce_time": "[Время] ",
    "history_version_introduce_version": "[Версия] ",
    "history_version_title": "Основн. обновления",
    "query_history_version_fail": "Сбой запроса журнала версий!",
    "history_version_introduce": "Введение в версию",
    "history_version": "Журнал версий",
    "only_creator_can_edit": "Редактировать может только владелец и менеджер группы.",
    "no_service_tip": "Нет описания услуги",
    "no_announcement_tip": "Нет группового объявления",
    "is_save_change": "Сохранить изменения?",
    "semi_public_group_tip": "Речевое сообщение в общедоступной группе невозможно без утверждения. Действительно продолжить?",
    "security_restrictions": "Эта функция ограничена механизмом обеспечения безопасности компании и недоступна во внутренней сети компании.",
    "send_realtime_to_conversation": "УЗИ в реал. вр.",
    "send_uf_to_conversation": "Отправка UF",
    "no_BI_RADS_features_results_tip": "Заключение сводки отсутствует",
    "no_BI_RADS_features": "Нет проанализ. заключения",
    "results_reference_only": "Примечание. Этот результат анализа получен из анализа д-ра М. и используется только для справки.",
    "BI_RADS_features_results_tip": "Заключение сводки: ",
    "BI_RADS_features": {
        "0": {
            "0": "Параллельно коже",
            "1": "не параллельно коже"
        },
        "1": {
            "0": "Овал",
            "1": "Кругл.",
            "2": "Неправильн."
        },
        "2": {
            "0": "Очерчено",
            "1": "Нечеткая граница",
            "2": ", ровная граница",
            "3": ", микролобулярн.",
            "4": ", нечеткая граница"
        },
        "3": {
            "0": "Анэхоген.",
            "1": "Изоэхоген.",
            "2": "Комплекс. кистозно-солид.",
            "3": "Гипоэхоген.",
            "4": "Гетероген."
        },
        "4": {
            "0": "Усиление ",
            "1": "Нет изменений в заднем эхо ",
            "2": "Затен.",
            "3": "Комбинированный паттерн"
        },
        "5": {
            "0": "Нет кальцификации внутри образования",
            "1": "Кальцификация внутри образования"
        },
        "6": {
            "0": "Нет кровотока",
            "1": "Периферический кровоток",
            "2": "Внутренний кровоток"
        }
    },
    "not_exam_picture": "Изобр. не из исслед.",
    "sort_by_time": "Сортировка по времени добавления в избранное",
    "sort_by_exam": "Сортировка по исслед.",
    "sort_by_group": "Сортировка по группам",
    "nls_probationary_expiry_tip": "Пробный период данной учетной записи истекает {1}. Перед повторным использованием учетной записи обратитесь к администратору для ее утверждения.",
    "multi_select": "Множ. выбор",
    "no_new_version": "Нет новой версии",
    "confirm_update_to_new_version": "Действительно обновить до новой версии?",
    "version_update": "Обновление версии",
    "cur_version": "Текущая версия: ",
    "sned_to_analyze_tip": "Изображение было отправлено на обработку.",
    "action_analyze_text": "Обработка изображения",
    "mindray_analyze": "Анализ д-ра М.",
    "ai_analyze": "Д-р М.",
    "click_here": "Нажмите здесь",
    "share_live_addr_tip4": "Способ поделиться 3:",
    "share_live_addr_tip3": "Скопируйте адрес ссылки и откройте в Google Chrome.",
    "share_live_addr_tip2": "Способ поделиться 2: сканирование QR-кода с помощью камеры мобильного телефона и обмен фотографиями",
    "share_live_addr_tip1": "Способ поделиться 1: Откройте страницу прямой трансляции с помощью WeChat Scanning и нажмите в правом верхнем углу, чтобы поделиться.",
    "get_live_addr_error": "Сбой получения адреса прямой трансляции.",
    "set_record_name": "Задать название больницы",
    "live_address": "Адрес прямой трансляции",
    "auto_analyze": "Изображения будут обработаны автоматически. ",
    "from_ai_analyze": "От д-ра М.",
    "share_analyze_result": "Пользователь поделился результатами обработки изображений:",
    "can_not_analyze_video": "Анализ видеофайла не поддерживается.",
    "analyze_error": "Поражения не найдены",
    "analyzing": "Анализ...",
    "see_trace_tip": "Щелкните по изображению для показа контура.",
    "malignant_probability": "Вероятность злокач. новообразования:",
    "bengin_probability": "Вероятн.добр.новообр.:",
    "analyze_fail_tip": "Сбой анализа",
    "analyze_result_tip": "Результат анализа:",
    "close_consultation": "Выйти из консультации",
    "export_comment_fail": "Сбой экспорта комментариев.",
    "export_comment": "Экспорт",
    "multi_audio_device": "Обнаружено несколько аудиоустройств. Сначала выберите нужное устройство",
    "record_no_network": "Запись голосового сообщения не может использоваться, если сеть отключена.",
    "admin_hospital_delete": "Удалить больницу?",
    "admin_hospital_name_empty": "Поле названия больницы не может быть пустым",
    "admin_user_id": "ID польз.",
    "admin_active_user_tip": "Действительно активировать этого пользователя?",
    "admin_forbidden_user_tip": "Действительно деактивировать этого пользователя?",
    "admin_relief_director_tip": "Действительно удалить уровень директора?",
    "admin_setup_director_tip": "Действительно удалить права директора?",
    "admin_revoking_permissions_tip": "Действительно удалить права администратора?",
    "admin_granting_permissions_tip": "Действительно авторизовать права администратора?",
    "admin_pass_empty": "Выберите хотя бы одного пользователя.",
    "admin_add": "Доб.",
    "admin_hospital_name": "Название больницы",
    "admin_revoking_permissions": "Удалить разрешения",
    "admin_granting_permissions": "Предоставьте разрешения",
    "admin_disabled": "Отключ.",
    "admin_activation": "Активировать",
    "admin_activation_and_disabled": "Активировать/Отключить",
    "admin_setting_password": "Настройка пароля",
    "admin_is_director": "Вы директор? ",
    "admin_is_manager": "Вы администратор? ",
    "admin_batch_pass": "Пакет: успешно",
    "admin_pass": "Пройд.",
    "admin_approve": "Утверд.",
    "admin_referee_nickname": "Рекомендовано",
    "admin_approver_nickname": "Утвердил(а)",
    "admin_register_date": "Время регистрации",
    "admin_login_name": "Имя учетной записи",
    "admin_tab_hospital_manage": "Руководство больницы",
    "admin_tab_user_manage": "Управ. пользователями",
    "admin_tab_approve": "Одобрение регистрации",
    "admin_account_not_admin": "Уч. запись без прав администратора",
    "admin_login_title": "Платформа для входа в систему фонового управления",
    "register_workstation_fail": "Сбой регистрации рабочей станции:",
    "register_workstation_success": "Рабочая станция успешно зарегистрирована.",
    "stop_test_microphone": "Остановить проверку микрофона",
    "stop_test_speaker": "Остановить проверку динамиков",
    "microphone_input_strength": "Вх. мощность микрофона",
    "testing_microphone": "После прохождения проверки динамика нажмите здесь, чтобы проверить микрофон. Пожалуйста, говорите в микрофон.",
    "testing_speaker": "Нажмите, чтобы проверить динамик и узнать, слышите ли вы музыку.",
    "no_microphone_be_detected": "Микрофон не обнаружен. Сначала подключите микрофон.",
    "no_speaker_be_detected": "Динамик не обнаружен. Сначала подключите динамик.",
    "microphone_muti_devices": "Обнаружено несколько микрофонов",
    "speaker_muti_devices": "Обнаружено несколько динамиков",
    "microphone_title": "Микрофон",
    "speaker_title": "Динамик",
    "ultrasync_box_mac_empty_tip": "Если поле MAC -адреса решения Pusher MiCo+ пустое, решение не привязано.",
    "workstation_MAC": "Mac рабочей станции",
    "workstation_consulting_room": "Консультационная комната рабочей станции",
    "register_workstation_title": "Регистрация рабочей станции",
    "arrangement_text": "Расположение",
    "enter_conversation": "Вступить в диалог",
    "export_no_select": "Выберите изображение для экспорта.",
    "general_mode": "Общий режим",
    "tv_wall_mode": "Режим ТВ-стены",
    "download_image_success": "Загрузка выполнена успешно!",
    "download_directory_empty": "Выберите папку назначения экспорта.",
    "download_image_space": "Имя изображения не может содержать пробелы.",
    "download_image_name": "Введите имя изображения.",
    "download_exam_num_space": "Номер исследования не может включать пробелы.",
    "download_exam_num_empty": "Введите номер исследования.",
    "download_choose_btn": "Выбрать",
    "download_directory": "Выберите адрес для экспорта: ",
    "download_name": "Имя экспортируемого файла: ",
    "download_exam_num": "Номер исслед.: ",
    "pusher_mac_format_error": "Неверный формат MAC-адреса Pusher. Он должен быть XX-XX-XX-XX-XX-XX.",
    "max_ultrasync_box_length": "Длина имени MiCo+ Pusher должна быть от 4 до 16 символов.",
    "max_scan_room_name_length": "Длина имени комнаты сканирования должна составлять от 4 до 16 символов.",
    "delete_ultrasync_box_text": "Удалить MiCo+ Pusher:",
    "delete_scan_room_text": "Удалить комнату скан.:",
    "other_infomation": "Прочие сведения",
    "device_id": "ID устройства",
    "ultrasync_box_mac": "MAC-адрес MiCo+ Pusher",
    "scan_room_lastest_user": "Недавно вошедш. польз.",
    "scan_room_hospital": "Больница",
    "scan_room_name": "ФИО",
    "ultrasound_device_mng": "Управление ультразвуковым оборудованием",
    "pusher_manage": "Управление MiCo+ Pusher",
    "scan_room_mng": "Управление рабочей станцией",
    "system_info_text": "Сведения о системе",
    "cloud_statistics_iworks_get_token_err": "Сбой получения токена!",
    "cloud_statistics_iworks2": "Статистика iWorks",
    "cloud_statistics_iworks": "iWorks",
    "cloud_statistics_range_empty": "Выберите временной интервал.",
    "cloud_statistics_day_text": "дн",
    "cloud_statistics_hour_text": "ч",
    "minute_text": "мин",
    "cloud_statistics_second_text": "с",
    "cloud_statistics_client_type": "Тип клиента: Панель/рабочая станция /консультация/приложение на телефоне/УЗ-аппарат/браузер",
    "cloud_statistics_tags_count": "Количество тегов",
    "cloud_statistics_comment_count": "Количество аннотаций",
    "cloud_statistics_group_message_count": "Количество речевых сообщ.",
    "cloud_statistics_start_offline_consultation_count": "Количество запущенных консультаций в автономном режиме",
    "cloud_statistics_active_users_count_tip": "Совет: активный пользователь — онлайн-пользователи могут считаться активными",
    "cloud_statistics_active_users_count": "Количество активных пользователей",
    "cloud_statistics_by_the_hour": "По часам",
    "cloud_statistics_by_the_month": "По месяцам",
    "cloud_statistics_by_the_week": "По неделям",
    "cloud_statistics_by_the_day": "По дням",
    "cloud_statistics_last_year": "Прошлый год",
    "cloud_statistics_last_thirty_days": "Последние 30 дней",
    "cloud_statistics_last_seven_days": "Последние 7 дней",
    "cloud_statistics_bar_chart": "Гистограмма",
    "cloud_statistics_line_chart": "Кривая",
    "cloud_statistics_chart_type": "Тип графика:",
    "cloud_statistics_fineness_with_colon": "Зернистость: ",
    "cloud_statistics_client_type_short": "Тип клиент-сервер",
    "cloud_statistics_new_group_number": "Количество новых групп",
    "cloud_statistics_global_group_number": "Общее количество групп",
    "cloud_statistics_active_live_user_count": "Количество активных пользователей прямой трансляции",
    "cloud_statistics_live_user_total_count": "Все пользователи прямой трансляции",
    "cloud_statistics_ulink_user_tip": "Совет: Пользователь u-Link — пользователь, отправивший чек",
    "cloud_statistics_ulink_user": "Польз. u-Link",
    "cloud_statistics_ulink_active_user_count_tip": "Совет: количество активных пользователей u-Link — пользователи, которые использовали u-Linker в течение одного месяца, могут считаться активными",
    "cloud_statistics_ulink_active_user_count": "Количество активных пользователей u-Link",
    "cloud_statistics_ulink_user_count": "Добавленное количество пользователей u-Link",
    "cloud_statistics_ulink_user_total_count": "Общее количество пользователей u-Link",
    "cloud_statistics_total_ultra_device_ulink": "Общее количество ультразвуковых устройств (u-Link)",
    "cloud_statistics_ulink_install_device_total_count": "Общее количество установленных u-Link",
    "cloud_statistics_ulink_install_device_count": "Добавленное количество установл. u-Link",
    "cloud_statistics_live_duration_enter": "Продолжительность приема прямой трансляции (мин)",
    "cloud_statistics_live_duration": "Длит. прямой трансляции (мин)",
    "cloud_statistics_group_total_count": "Всего групп",
    "cloud_statistics_user_total_count": "Всего пользователей",
    "cloud_statistics_active_group_count_tip": "Совет: активная группа — если какой-либо пользователь из группы отправляет сообщение, группа считается активной",
    "cloud_statistics_active_group_count": "Количество активных групп",
    "cloud_statistics_new_group_message_count": "Количество новых групповых сообщений (все типы сообщений чата, кроме системных сообщений)",
    "cloud_statistics_new_realtime_count": "Количество новых прямых трансляций",
    "cloud_statistics_total_exam_video": "Общее количество кинопетель иссл.",
    "cloud_statistics_total_exam_picture": "Общее число изображений исследования",
    "cloud_statistics_total_case_count": "Общее число исследований",
    "cloud_statistics_new_exam_video": "Количество новых кинопетель иссл.",
    "cloud_statistics_new_exam_picture": "Количество новых изображений иссл.",
    "cloud_statistics_new_case_count_for_iworks": "Количество новых исследований с помощью iWorks",
    "cloud_statistics_new_case_count": "Количество новых иссл.",
    "cloud_statistics_new_user_approve_number": "Количество утвержденных нов. пользователей",
    "cloud_statistics_new_user_number": "Колич. новых пользователей",
    "cloud_statistics_receive_real_time_consultation": "Макс.значение полученных консультаций в реальном времени",
    "cloud_statistics_launch_real_time_consultation": "Макс.значение запущ. консультаций в реальном времени",
    "cloud_statistics_active_group_peak": "Макс.количество активных групп",
    "cloud_statistics_online_user_peak": "Макс. значение для пользователей в сети",
    "cloud_statistics_exam_number": "Число исследований",
    "cloud_statistics_chat_messages_other": "Проч.",
    "cloud_statistics_chat_messages_sound": "Звук",
    "cloud_statistics_chat_messages_realtime_video": "Видео",
    "cloud_statistics_chat_messages_image": "Изобр.",
    "cloud_statistics_chat_messages_realtime_review": "Воспр. прям. пер.",
    "cloud_statistics_chat_messages_cine": "Медицинское видео",
    "cloud_statistics_chat_messages_frame": "Медицинские изобр.",
    "cloud_statistics_chat_messages_text": "Текст: ",
    "cloud_statistics_chat_messages_number": "Количество сообщений чата",
    "cloud_statistics_access_realtime_person_time": "Колич.польз. с доступом к голос.сообщ. в реал.вр.",
    "cloud_statistics_access_realtime_total_time": "Длит. доступа к голос.сообщ. в реал.вр.",
    "cloud_statistics_times_of_realtinme_voice": "Кол.исп. голос. сообщ. в реал. вр.",
    "cloud_statistics_total_time_of_realtinme_voice": "Длит.исп. голос. сообщ. в реал.вр.",
    "cloud_statistics_viewing_person_time": "Число пользователей, просматр. консультацию в режиме реального времени",
    "cloud_statistics_viewing_total_time": "Общее вр. просмотра консультаций в режиме реального времени",
    "cloud_statistics_real_time_consultation_times": "Колич. консультаций в реальном времени",
    "cloud_statistics_real_time_consultation_total_time": "Длительность консультации в реальном времени",
    "cloud_statistics_increased_conversation": "Количество новых диалогов",
    "cloud_statistics_total_conversation": "Общее количество сеансов",
    "cloud_statistics_user_total_online_time": "Общая длит. нахождения пользователей в сети",
    "cloud_statistics_online_users": "Пользов. в сети",
    "cloud_statistics_increased_users": "Количество новых пользователей (включая неутвержденную регистрацию)",
    "cloud_statistics_total_users": "Количество ВСЕХ пользователей (включая неутвержденную регистрацию)",
    "cloud_statistics_increased_approved_users": "Количество пользователей на утверждение",
    "cloud_statistics_total_approved_users": "Количество ВСЕХ утвержденных пользователей",
    "cloud_statistics_this_month": " Этот месяце ",
    "cloud_statistics_this_week": " Эта неделя ",
    "cloud_statistics_today": " Сегодня ",
    "cloud_statistics_inquiry": " Запрос ",
    "cloud_statistics_statistical_item_result": "Статистический результат",
    "cloud_statistics_statistical_item": "Статистический элемент",
    "cloud_statistics_diagram": "График",
    "cloud_statistics_interval_table": "Таблица интервалов",
    "cloud_statistics_global_table": "Глобальная таблица",
    "device_mng": "Управление устройствами",
    "cloud_statistics_title": "Статистика по облаку",
    "background_manage_title": "Фоновое управление",
    "enter_purpose_tip": "Введите тему консультации.",
    "fill_in_purpose": "Введите тему консультации.",
    "temp_attendee_text": "Временно",
    "video_cannot_played": "Воспроизведение этого видео невозможно.",
    "exam_conversation_title": "Комната для консультации",
    "add_exam_opinion_success": "Комментарии к консультации успешно отправлены.",
    "add_exam_conclusion_success": "Заключение по консультации успешно отправлено.",
    "exam_conclusion_text": "Заключение по консультации",
    "exam_opinion_text": "Комментарии к консультации: ",
    "cancel_conversation_btn": "Отмена консультации",
    "close_conversation_btn": "Завершить консультацию.",
    "add_exam_conclusion": "Введите заключение по консультации",
    "app_no_speak_permission": "Нет разрешения говорить. Примените в настройках группы и дождитесь утверждения.",
    "applying_join_group": "Запрос разрешения на реч. сообщ.",
    "apply_join_semi_group": "Подать запрос на реч.сообщ.в общедост. группе",
    "toggle_to": "Переключиться на",
    "search_empty_tip": "Введите критерии поиска и нажмите Enter или выберите тип поиска в раскрывающемся меню!",
    "choose_file": "Выбрать файл",
    "submit_btn": "Отправить",
    "download_title": "Загрузить",
    "share_video_limited": "WeChat не поддерживает обмен видео таким образом. Вы можете попытаться загрузить видео в альбомы и использовать WeChat для отправки видео.",
    "history_tag": "История поиска",
    "stop_multi_frame_first": "Сначала остановите сохранение многокадрового изображения!",
    "device_save_video_unit": " ",
    "device_save_video_num": "Видео",
    "device_save_picture_num": "Изобр.: ",
    "device_create_exam": "Новое иссл.",
    "get_repository_list_fail": "Сбой загрузки базы данных.",
    "no_search_data": "Соответствующие результаты не найдены.",
    "load_pdf_error": "Ошибка загрузки PDF: ",
    "scan_to_login": "Скан. для входа",
    "upload_date_text": "Время выгрузки: ",
    "delete_chat_message_warm_sended_by_others": "{1} запис. отправлены другими пользователями, и только данные, отправленные текущим пользователем, могут быть удалены.",
    "delete_chat_message_fail_sended_by_others": "Данные, отправленные другими пользователями, не могут быть удалены.",
    "ultrasync_box_detele_fail_online": "MiCo+ Pusher в сети и не может быть удалено.",
    "ultrasync_box_detele_fail": "Сбой удаления Pusher.",
    "scan_room_detele_fail_online": "Невозможно удалить работающую рабочую станцию. ",
    "scan_room_detele_fail": "Сбой удаления рабочей станции.",
    "delete_chat_message_fail": "Сбой удаления!",
    "auto_join_group": "Общедоступную группу можно добавить после входа в систему.",
    "control_panel": "Панель управления",
    "auth_fail": "Сбой авторизации.",
    "auth_ok": "Авторизация выполнена успешно.",
    "scan_mindray_ultrasync": "Скан. MiCo+",
    "join_group_without_approve": "Добавление групп не требует утверждения.",
    "join_group_with_approve": "Для добавления групп требуется утверждение.",
    "semi_public_group_text": "Полудоступная группа",
    "private_group_text": "Личная группа",
    "public_group_text": "Общедост. группа",
    "group_realtime_review": "Просмотр",
    "group_conversion_file": "Файл консультации",
    "group_all_file": "Все файлы",
    "attendee_member_text": "Участники",
    "to_scan_device": "Устройство для подкл. без входа в систему",
    "send_by_enter": "Нажмите Enter для отправки",
    "chat_history": "История чата",
    "clear_history": "Очист.",
    "more_features": "Другие функции",
    "camera_direct_seeding": "Камера находится в режиме прямой трансляции. ",
    "desktop_direct_seeding": "Прямая трансляция раб. стола",
    "ultrasound_seeding": "Прямая трансляция данных УЗИ",
    "voice_recording": "Запись голоса",
    "export_image": "Экспорт изобр.",
    "upload_file": "Выгрузить вложения",
    "emoticon": "смайлы",
    "search_history_text": "Поиск записи чата",
    "search_file_text": "Поиск файлов",
    "search_group_text": "Поиск группы",
    "search_friend_text": "Поиск друга",
    "version_info": "Информация о версии",
    "uncalibrated_text": "Не откалибровано",
    "calibrater_text": "Калибратор: ",
    "inputPatientOrFtpInfo": "Введите свед. о пациенте или FTP.",
    "shutdown_fail": "Сбой завершения работы.",
    "shutdown_succ": "Заверш. работы — успешно.",
    "save_multi_frame_fail": "Сбой сохранения многокадровых изображений.",
    "save_multi_frame_succ": "Сохранение многокадровых изобр. выполнено успешно.",
    "save_multi_frame_start": "Пуск сохранения многокадровых изображений.",
    "save_single_frame_fail": "Сбой сохранения однокадровых изображений.",
    "save_single_frame_succ": "Сохранение однокадровых изображений выполнено успешно.",
    "update_device_cur_session_fail": "Сбой установки текущего сеанса.",
    "device_offline": "Устройство не в сети.",
    "realtime_video_error": {
        "ultrasync_box_not_binded_switch": "MiCo+ Pusher не привязано, и видео в реальном времени невозможно переключить.",
        "ultrasync_box_offline_switch": "MiCo+ Pusher не в сети, и видео в реальном времени невозможно переключить.",
        "push_error_switch": "Сбой обработки данных. Невозможно переключить видео в реальном времени.",
        "push_camera_desktop_error_switch": "Сбой запроса камеры. Невозможно переключить видео в реальном времени.",
        "push_local_desktop_error_switch": "Сбой общего доступа к локальному рабочему столу. Невозможно переключить видео в реальном времени.",
        "push_error": "Сбой обработки данных. Видео в реальном времени не может быть запущено.",
        "push_camera_desktop_error": "Сбой запроса камеры. Видео в реальном времени не может быть запущено.",
        "push_local_desktop_error": "Сбой общего доступа к локальному рабочему столу. Видео в реальном времени не может быть запущено.",
        "rt_video_started": "Видео в реальном времени запущено. Видео в реальном времени не может быть запущено.",
        "timeout": "Таймаут. Видео в реальном времени не может быть запущено.",
        "rt_video_started_by_others": "Запущено видео рабочей станции в реальном времени. Другие пользователи не могут запускать видео в реальном времени.",
        "camera_error": "Ошибка распознавания камеры. Видео в реальном времени не может быть запущено.",
        "video_transmit_server_not_run": "Сервер пересылки видео не запущен, и видео в реальном времени не может быть запущено.",
        "ultraSync_box_image_reset": "Изображение сброшено, и видео в реальном времени выключается.",
        "ultraSync_box_push_error": "Сбой прямой трансляции. Видео в реальном времени закрыто.",
        "ultrasync_box_no_signal": "У MiCo+ Pusher нет сигнала, и видео в реальном времени будет закрыто.",
        "unbind_scan_room_user": "Рабочая станция находится в автономном режиме, видео в реальном времени отключено.",
        "ultrasync_box_disabled": "MiCo+ Pusher используется другими рабочими станциями, и видео в реальном времени будет закрыто.",
        "ultrasync_box_reset": "Повторная привязка MiCo+ Pusher, видео в реальном времени будет закрыто.",
        "ultrasync_box_offline": "MiCo+ Pusher не в сети, и видео в реальном времени будет закрыто.",
        "unknown_error": "Неизвестная ошибка. Видео в реальном времени будет закрыто."
    },
    "set_catch_option_error": {
        "ultrasync_box_pushing": "Идет прямая трансл.",
        "ultrasync_box_setting": "Настройки не завершены.",
        "ultrasync_box_offline": "MiCo+ Pusher не в сети.",
        "ultrasync_box_not_binded": "MiCo+ Pusher не привязано.",
        "timeout": "Таймаут"
    },
    "pusher_enable_error": {
        "ultrasync_box_no_signal": "У MiCo+ Pusher нет сигнала, и запускается видео в реальном времени.",
        "ultrasync_box_disabled": "MiCo+ Pusher используется другими рабочими станциями, и видео в реальном времени не может быть запущено.",
        "ultrasync_box_offline": "MiCo+ Pusher не в сети, и видео в реальном времени не может быть запущено.",
        "ultrasync_box_not_binded": "MiCo+ Pusher не привязано, и видео в реальном времени не может быть запущено.",
        "unknown_error": "Неизвестная ошибка. Видео в реальном времени не может быть запущено."
    },
    "ultrasync_box_disabled": "MiCo+ Pusher используется другими рабочими станциями, и УЗИ в реальном времени не может быть запущено.",
    "starting_rt_video": "Запуск видео в реальном времени. Подождите!",
    "stop_device_ultrasound_desktop": "Остан. реал.вр.",
    "start_device_ultrasound_desktop": "Пуск видео в реал. вр.",
    "shutdown_confirm": "Выключить?",
    "save_multi_frame_stop": "Остановить сохранение многокадрового изобр.",
    "save_multi_frame": "Сохранение многокадровых изображений",
    "save_single_frame": "Сохранение однокадровых изобр.",
    "please_enter_ftp_path": "Введите путь FTP.",
    "please_enter_ftp_password": "Введите пароль FTP.",
    "please_enter_ftp_account": "Введите данные уч. записи FTP.",
    "ftp_port": "FTP-порт: ",
    "ftp_path": "Путь сохр.: ",
    "ftp_password": "Пароль FTP: ",
    "ftp_account": "Уч. запись FTP: ",
    "ftp_anonymous": "Аноним.",
    "ftp_info": "Данные хранения ",
    "forbid_input_quotation": "Кавычки запрещены.",
    "please_enter_patien_id": "Введите ID пациента.",
    "requestTimeout": "Время ожид. запроса истекло. Повторите попытку.",
    "modify_btn_text": "Изменить",
    "current_conversation": "Текущий диалог: ",
    "toggle_conversation_to": "Переключ.на",
    "ultrasound_device_title": "Мое устр.",
    "recent_consultation": "Последняя консультация",
    "recent_images": "Последний файл",
    "camera_ex_enter_by_other_client": "Другие клиенты добавили двойные камеры, повторное добавление невозможно.",
    "request_done_by_other_clients": "Запрос был обработан этой учетной записью на другом устройстве для входа в систему и не может быть обработан повторно.",
    "user_passive_exit_group": "Вы были удалены из группы",
    "user_exit_group_fail": "Сбой при выходе из группы.",
    "user_exit_group_succ": "Выход из группы успешно выполнен. ",
    "user_exit_group_tip": "После выхода все данные будут удалены. Операция необратима. Действительно продолжить?",
    "creator_user_exit_group_tip": "Поскольку вы — создатель группы, при вашем выходе вся группа будет удалена, а все данные стерты. Операция необратима. Действительно продолжить?",
    "exit_group": "Выйти из группы",
    "delete_group": "Удалить группу",
    "i_o_error": "Ошибка чтения/записи: ",
    "send_message_error": "Сбой отправки сообщения.",
    "audio_not_support": "Браузер не поддерживает запись.",
    "invalid_qrcode": "Недопустимый QR-код!",
    "init_not_ready": "Инициализация не завершена. Повторите попытку позже.",
    "file_does_not_exist": "Файл не существует.",
    "webrtc_transfer_close": "Сервер голос. сообщ. не запущен. ",
    "speak_panel": "Панель динамиков",
    "start_conversation_error": "Сбой открытия сеанса.",
    "banned_this_moment": "Эта функция недоступна.",
    "copy_text_not_support": "Текущий мобильный телефон не поддерживает копирование текста.",
    "copy_text_fail": "Сбой копирования.",
    "text_has_copied": "Текст скопирован в буфер обмена",
    "tag_emoji_err": "Пользовательские теги не могут содержать смайлы",
    "system_tag_top": "Системные теги",
    "custom_tag_top": "Общие теги ",
    "tag_repeat_add": "Повторное добавление меток",
    "update_failed_text": "Сбой изменения",
    "update_success_text": "Измен. выполнено успешно",
    "exam_types": {
        "0": "OB",
        "1": "ГИН",
        "2": "КАРДИО",
        "3": "VAS",
        "4": "Абд.",
        "5": "УРОЛ",
        "6": "SMP",
        "7": "Дети",
        "8": "Мол. жел.",
        "9": "Неизвестно",
        "10": "Неизвестно",
        "-1": "Все"
    },
    "no_more_data": "Больше нет данных",
    "exam_view_mode": "Просм. иссл.",
    "normal_view_mode": "Нормальный режим",
    "freedom_voice_ctrl_mode": "Можно говорить",
    "group_setting_voice_ctrl_mode": "Режим голосового управления",
    "group_setting_view_mode": "Режим просм.",
    "search_key": "КлючСлово",
    "searching": "поиск",
    "search": "Поиск",
    "exam_flesh_btn": "Обновить",
    "confirm_min_app": "Нажмите еще раз для выхода.",
    "delete_tag_msg_text": "Удалить тег",
    "add_tag_msg_text": "Новый тег",
    "add_comment_msg_text": "Нов. комментарий",
    "no_net_no_open_img": "Большое изобр. некорректно открывается в автономном режиме. ",
    "no_net_no_open_video": "Невозможно открыть видео в автономном режиме. ",
    "exam_date_more_tip": "Время запуска не может быть позже времени окончания.",
    "exam_date_less_tip": "Время окончания не может быть раньше времени начала.",
    "search_text": "Фильтр",
    "save_not_support_tip": "Этот тип файла не поддерживается для сохранения в фотоальбом",
    "save_support_fail_tip": "Сбой сохранения. Устройство не поддерживает этот формат файла!",
    "save_null_fail_tip": "Подготовка файлов. Повторите попытку позже!",
    "has_download_tip": "Файл сохранен в альбом.",
    "gallery_dowloading_tip": "Загрузка. Подождите.",
    "confirm_create_group": "Действительно создать групповой чат: ${1}?",
    "patient_birthday_text": "ДР",
    "sender_nickname": "Отпр-ль:",
    "exam_browse_text": "Просмотр исслед.",
    "transfer_helper_text": "Помощник переноса",
    "use_app_tip": "Установите приложение для использования этой функции.",
    "share_to_wechat_failed": "Сбой отправки!",
    "share_to_wechat_succeed": "Вы успешно поделились!",
    "network_error_tip": "Сбой сетевого подключения. Проверьте настройки сети и попробуйте снова.",
    "machine_transmit_tip": "Команда пересылки отправлена.",
    "machine_local_confirm": "Действительно сохранить выбранное изображение локально?",
    "machine_destroy_confirm": "Действительно удалить выбранное изображение?",
    "machine_local_btn": "Локал.",
    "machine_destroy_btn": "Удалить",
    "cancel_select_all": "Отм.выбор всего",
    "select_all": "Выбрат.все",
    "machine_info_serial": "Серийный номер машины",
    "machine_info_type": "Тип устройства",
    "machine_info_name": "Назв. устройства",
    "disconnet_machine_btn": "Отключить",
    "machine_info": "Сведения об устройстве",
    "ultrasound_machine_title": "Передача ультразвуковых изображений",
    "password_error_text": "Неверный пароль. Повторите попытку!",
    "modify_photo_success": "Фотография профиля успешно изменена!",
    "card_request_fail": "Сбой добавления друга.",
    "card_request_sended": "Запрос на добавление в друзья отправлен.",
    "card_applying_tip": "Добавление друзей...",
    "card_chat_btn": "Отправить сообщ. ",
    "visiting_card_title": "Личный профиль",
    "tag_text_has_newline": "Символ разрыва строки не может быть введен для пользовательских тегов.",
    "tag_text_null": "Пользовательские теги не могут быть пустыми",
    "comment_text_null": "Поле коммент. не может быть пустым.",
    "close_realtime_video_tip": "Закрыть видео в реальном времени.",
    "request_realtime_video_tip": "Запрос видео в реальном времени",
    "close_voice_system_tip": "Закрыть чат голос.сообщ.",
    "request_voice_system_tip": "Запрос чата голос. сообщ.",
    "search_join_tip": "Вступите в групповой чат путем поиска",
    "remove_group_tip2": "из группового чата",
    "active_exit_group_tip": "Инициир. выход из группового чата",
    "remove_group_tip1": "удал.",
    "invited_join_group_tip2": "к групповому чату",
    "invited_join_group_tip1": "приглаш.",
    "cannot_delete_tag": "Теги, созданные др.пользователями, не могут быть удалены.",
    "age_unit_month": "мес",
    "age_unit_year": "лет",
    "unknow_text": "Неизвестно",
    "action_clip_text": "Клип",
    "action_set_exam_info_text": "Импорт в базу данных пациентов",
    "action_favorite_text": "Избранное",
    "action_delete_text": "Удалить",
    "transmit_fail_tip": "Сбой отправки!",
    "transmiting_tip": "Пересланный файл отправлен.",
    "transmit_less_tip": "Выберите хотя бы один файл не в формате JSON",
    "transmit_comfirm_msg": "Переслать:",
    "transmit_title": "Вперед",
    "add_favorites_fail": "Сбой добавления в избранное.",
    "add_favorites_success": "Успешно добавлено в избранное",
    "operating_text": "Обработка",
    "share_to_wechat_moment": "Поделиться моментом в WeChat",
    "share_to_wechat_friends": "Поделиться с друзьями в WeChat",
    "share_to_wechat": "Поделиться в WeChat",
    "attendee_join_group_already": "Уже в группе",
    "invite_wechat_user_to_group": "Приглашаем друзей",
    "invite_wechat_user": "Пригласить друзей WeChat",
    "wechat_no_wechat_installed": "WeChat не установлен",
    "wechat_is_not_supported": "Не поддерживается WeChat",
    "send_failed": "Сбой отправки.",
    "wechat_user_tap_cancel_and_back": "Нажмите «Отмена» и выберите «Назад».",
    "wechat_regular_wrong_type": "Стандартный тип ошибки",
    "wechat_get_user_info_failed": "Сбой получения сведений о пользователе WeChat.",
    "wechat_get_access_token_failed": "Сбой получения токена доступа WeChat.",
    "wechat_create_group": "Контакты WeChat",
    "remote_ultrasonic_consultation_system": "MiCo+",
    "authentication_authorization_failed": "Сбой авторизации аутентификации",
    "invalid_sharing_service": "Недопустимая служба общего доступа",
    "mindray_final_mission": "Миссия: продвижение медицинских технологий, которые делают здравоохранение более доступным",
    "account_format_tip": "Имя пользователя состоит из букв/цифр и символов, начинается с буквы/цифры, не может состоять только из цифр.",
    "enhanced_password_format_tip": "Пароль должен состоять из 8-16 символов и нескольких букв, цифр и/или специальных символов (не менее двух).",
    "password_format_tip": "Пароль состоит из 8-16 цифр/букв/специальных символов.",
    "nickname_cannot_contain_special_character": "Псевдоним не может содержать специальный символ.",
    "single_picture_sharing": " Поделиться",
    "multi_picture_sharing": "Обмен несколькими изображ.",
    "choose_transmit_tip": "выберите изображение",
    "choose_favorites_tip": "Выберите изображение для добавления в избранное. ",
    "realtime_review_msg": "[Воспр. прям. транс.]",
    "unknown_error": "Неизв. ошибка",
    "one_mobile_register_multiple_user_forbid": "Не разрешается использовать один и тот же мобильный телефон для регистрации нескольких пользователей.",
    "SMS_validation_is_not_supported": "Проверка SMS не поддерживается, обратитесь к администратору",
    "userNotExist": "Пользователь не существует",
    "data_base_error_contact_admin": "Ошибка базы данных. Обратитесь к администратору.",
    "retrieve_sms_verification_code": "Отправить снова",
    "sms_verification_code_failed": "Сбой проверки проверочного кода из SMS",
    "sms_verification_code": "SMS-код",
    "submit_sms_verification_code": "Отправить проверочный код в SMS",
    "user_reset_password_button": "Сбросить пароль",
    "mobile_number_is_invalid_input_again": "Недопустимый номер мобильного телефона. Введите еще раз!",
    "nickname_should_not_be_empty": "Поле псевдонима не может быть пустым.",
    "modify_password_fail_database_err": "Ошибка базы данных, обратитесь к администраторам!",
    "modify_password_fail_password_same": "Старый и новый пароли совпадают, сбой изменения пароля.",
    "modify_password_fail_password_incorrect": "Неверный первоначальный пароль.",
    "modify_password_fail_account_incorrect": "Учетная запись неверна.",
    "personal_information_fail_mobile_phone_repeated": "Мобильный телефон зарегистрирован.",
    "personal_information_fail_nickname_repeated": "Псевдоним зарегистрирован.",
    "personal_information_fail_email_repeated": "Адрес электронной почты зарегистрирован.",
    "personal_information_fail_name_pure_numbers": "Имя пользователя не может состоять только из цифр.",
    "personal_information_fail_name_repeated": "Имя пользователя зарегистрировано.",
    "modify_basic_info_success": "Личные данные успешно изменены.",
    "modify_password_success": "Пароль изменен успешно.",
    "confirm_password_and_new_password_not_match": "Пароль для подтверждения не совпадает",
    "confirm_password_and_password_not_same": "Пароль для подтверждения не совпадает с паролем.",
    "confirm_password_length_not_correct": "Длина пароля для подтверждения должна составлять от 8 до 16 символов",
    "password_length_not_correct": "Длина пароля должна составлять от 8 до 16 символов",
    "login_name_length_not_correct": "Длина имени пользователя должна быть от 4 до 16 символов.",
    "voice_manager_text": "Инициатор",
    "close_realtime_video": "[Закрыть видео в реал. врем.]",
    "close_voice_chat": "[Закрыть чат голос. сообщ.]",
    "request_realtime_video": "[Запрос видео в реальном времени]",
    "request_voice_chat": "[Запрос чата голос. сообщ.]",
    "group_modify_subject_title": "Изменить имя группы",
    "confirm_txt": "да",
    "group_add_attendee_title": "Доб. участников",
    "group_all_attendee_title": "Все участники",
    "group_all_file_title": "Все файлы консультации",
    "toggle_unlive_record_tip": "Остановить запись в реальном времени?",
    "toggle_live_record_tip": "Начать запись в реальном времени?",
    "group_setting_whether_live_record": "Пуск записи",
    "group_setting_is_live_record": "Начать запись в реальном времени?",
    "group_setting_is_need_to_approve": "Проверять ли голосовые сообщения пользователей, которые вступили в группу путем поиска",
    "group_setting_is_public": "Это общедоступная группа?",
    "group_setting_announce": "Объявление группы",
    "more_attendee_text": "Посмотреть др. участников группы",
    "more_file_text": "Проверьте другие группы файлов исследований",
    "creator_tag_text": "Владелец группы",
    "group_setting_title": "Свед. о группе",
    "yesterday_text": "Вчера",
    "cancel_button_text": "Нет",
    "confirm_button_text": "Да",
    "disable_speak_comfirm": "Это действие не даст участнику говорить. Действительно продолжить?",
    "apply_speak_disagree_comfirm": "Это действие отклонит запрос этого участника на реч. сообщ. Действительно продолжить?",
    "apply_speak_agree_comfirm": "Это действие позволит этому участнику говорить. Действительно продолжить?",
    "all_forbidden_speak": "ОтклЗвукВсех",
    "authorize_speak_text": "Разрешено говорить",
    "attendee_text": "Участники:",
    "modify_confirm_password_input": "Введите новый пароль еще раз",
    "modify_new_password_input": "Введите новый пароль",
    "modify_old_password_input": "Введите стар. пароль.",
    "modify_new_password_label": "Новый пароль",
    "modify_old_password_label": "Старый пароль",
    "browser_no_support_recording": "Браузер не поддерживает запись!",
    "no_audio_input_warn": "Устройство голосового ввода не подключено!",
    "loaded_all_message_no_net": "Автоном. Больше нет сообщений.",
    "loaded_all_message": "Др.анамнез отсутств.",
    "image_all_loaded": "Все изображения загружены",
    "has_chosen_text": "Выбрано",
    "not_choose_text": "Не выбрано",
    "upload_net_error": "Текущая сеть работает с отклонениями и не поддерживает отправку крупных файлов. Настройте сеть и повторите попытку!",
    "upload_forbidden_file_type_text": "Формат файла не поддерживается и не может быть выгружен!",
    "upload_min_text": "Сбой выгрузки. Размер файла — ",
    "upload_max_text": "Сбой выгрузки. Слишком большой размер файла",
    "record_short_text": "Слишком короткое время записи",
    "cancel_recording_text": "Отпустите палец, чтобы отменить отправку",
    "recording_sound_text": "Пров. пальцем вверх для отмены отправки.",
    "chat_upload_text": "Изобр.",
    "login_another": "Ваша учетная запись зарегистрирована в другом месте.",
    "tip_title": "Советы",
    "create_group_text": "Группа успешно создана.",
    "unsupport_video_text": "Браузер не поддерживает воспроизведение видео",
    "gallery_navbar_patient": "Свед. о пациенте",
    "label_txt": "Теги",
    "gallery_navbar_comment": "Комментарии",
    "gallery_add_tags_btn": "Добав. теги",
    "gallery_add_comment_btn": "Доб. комментарии",
    "unsupport_msg_type": "Неподдерживаемый тип сообщения",
    "choose_shared_image": "Выберите изображение, чтобы поделиться.",
    "max_transfer_image": "можно выбрать до 20 изображений или видео.",
    "max_share_image": "можно выбрать до 99 изображений или видео.",
    "msg_type_sound_label": "Голос",
    "msg_type_image_string_label": "Текст",
    "msg_type_consultation_file_label": "Файл исследования",
    "msg_type_iworks_protocol_label": "Протокол iWorks",
    "msg_type_consultation_file": "[Файл исследования]",
    "msg_type_iworks_protocol": "[Протокол iWorks]",
    "msg_type_file": "[Файл]",
    "msg_type_video": "[Видео]",
    "msg_type_sound": "[Голос]",
    "msg_type_image": "[Изобр.]",
    "loading_complete": "Загрузка завершена.",
    "bottom_loading_text": "Загрузка...",
    "click_more_text": "Нажмите для доп. загрузки",
    "bottom_drop_text": "Отпустите, чтобы загрузить еще",
    "bottom_pull_text": "Потяните вверх, чтобы загр.еще.",
    "top_pull_text": "Потяните вниз, чтобы загрузить еще",
    "scanner_text": "Скан.",
    "hold_to_talk": "Удерж. для ответа",
    "send_message_txt": "Отпр.",
    "init_conversation_err": "Сбой открытия диалога.",
    "no_data_txt": "Нет данных",
    "applying_txt": "Подача запр.",
    "tmp_joined_txt": "Временно присоединился",
    "joined_txt": "Вступил",
    "subject_empty_tip": "Введите имя группы.",
    "create_group_empty_list": "Список друзей пуст. Сначала добавьте друга.",
    "group_chat_params": "Параметры группы",
    "group_create_steps": {
        "choose_friends": "Выбр. участников",
        "edit_params": "Настройки",
        "finish": "Готово",
        "group_create_progress_100": "Готово",
        "group_create_progress_50": "Почти готово"
    },
    "group_chat_name": "Имя группы",
    "search_group_title": "Общедоступн. группы",
    "create_group_title": "Создать группы",
    "response_disaccept_friend": "${1} отклонил(а) запрос вашего друга.",
    "response_accept_friend": "${1} принял(а) запрос вашего друга.",
    "added_txt": "Добавл.",
    "no_user_result": "Данные не найдены",
    "search_friend_tip": "Введите ключевое слово.",
    "search_err": "Сбой поиска.",
    "search_friend_txt": "Введите псевдоним/уч. запись/номер телефона/адрес эл. почты.",
    "disagree_txt": "Отклонить",
    "agree_txt": "Согл.",
    "apply_group_txt": "Подать запрос на вступ.в группу",
    "apply_friend_txt": "Нажм., чтобы доб. себя",
    "operate_success": "Операция выполнена успешно.",
    "operate_err": "Сбой операции.",
    "logout": "Выход",
    "my_groups": "Мои группы",
    "new_apply": "Новые друзья",
    "friends_title": "Мои друзья",
    "page_loading_tip": "Загрузка...",
    "international_title": "Язык",
    "modify_basic_info_text": "Изменение основных сведений",
    "system_setting_text": "Параметры системы",
    "change_language_success": "Язык успешно изменен",
    "into_tv_wall_after_login": "После успешного входа в систему перейдите на ТВ-стену",
    "auto_push_stream_for": "Автоматически запускать прямую трансляцию с помощью:",
    "auto_push_stream": "Автоматически запускать прямую трансляцию",
    "auto_download_attachment": "Автоматически загрузить вложение",
    "auto_forwarding_for": "Автоотправка в:",
    "auto_forwarding": "Автоотправка",
    "other_setting_text": "Проч. настройки",
    "modify_photo_text": "Изменить фотографию профиля",
    "modify_password_text": "Смена пароля",
    "personal_setting_title": "Безопасность уч. записи",
    "software_version_number": "Версия ПО",
    "app_and_server_build_version": "Версия UPG",
    "about_ultrasync": "О MiСo+",
    "cloud_favorites": "Избранное в облаке",
    "local_patients": "Локальная база данных пациентов",
    "setting_title": "Настройки",
    "add_friend_text": "Добавить контакты",
    "create_group_btn": "Создать",
    "files": "Список изобр.",
    "contacts": "Контакты",
    "index_nav_mine": "Я",
    "index_nav_chat": "Чат",
    "forget_password_getcode": "Отпр.",
    "forget_password_title": "Забыли пароль",
    "register_reset_btn": "Сброс",
    "register_sex": "Пол",
    "register_mobile": "Телефон",
    "register_confirm_password": "Подтверд.",
    "register_password": "Пароль",
    "register_account": "Уч. запись",
    "register_account_or_mobile": "Уч. запись/Моб. тел./Эл. почта",
    "register_title": "Регистрация",
    "to_forget_password": "Забыли пароль",
    "to_register": "Зарегистр.",
    "download_mobile_app": "Загрузить моб. приложение",
    "download_window": "Загрузите MiCo+ для Windows",
    "auto_login": "Автовход",
    "register_success": "Регистрация выполнена успешно",
    "login_password_empty": "Поле пароля не должно быть пустым.",
    "login_account_empty": "Учетная запись не может быть пустой",
    "login_password": "Пароль для входа",
    "login_account": "Введите данные уч. записи.",
    "login_title": "Вход",
    "network_unavailable": "Сеть недоступна. Проверьте сеть.",
    "new_version": "Новая версия: ",
    "app_name": "MiCo+",
    "RemoteTeaching": "Удаленное обучение",
    "RemoteTeaching not available": "Удаленное обучение недоступно",
    "AutoStart": "Автозапуск",
    "CARD": "КАРДИО",
    "AppWorkstation": "Рабочая станция",
    "AppClient": "Окончание консультации",
    "unknown": "Проч.",
    "StopTest": "Остан. тест",
    "TestMicrophone": "Проверка микрофона"
}

export default RU