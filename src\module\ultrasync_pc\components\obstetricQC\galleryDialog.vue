<!-- 会话下画廊或统计下画廊 ai信息弹框 -->
<template>
    <!-- <el-dialog
      :title="title"
      class=""
      :visible.sync="isShow"
      :close-on-click-modal="false"
      width="90%"
      height="100%"
      :append-to-body="true"
      :before-close="closeDialog"
      :modal="false"> -->
    <div class="obstetric_ac_garrey_dialog" v-show="isShow">
        <div class="content" :class="classNameStr">
            <!-- 已申请复议 -->
            <!-- <template v-if="view.isReconsider">
                    <template v-if="view.reconsider_ai_report">
                        <template v-if="view.reconsider_ai_report.finshed">
                            <div class="reconsider_result_new_tips" v-if="!currentImage.resource_copied_from&& user.uid && user.uid == view.senderId &&false">
                                <span class="icon iconfont iconsearch_up"></span>
                                <div class="exclamation_mark ">！</div>
                                {{lang.reconsider_result_new_tips}}
                                <div class='button_agree'>
                                    <div @click="reconsiderAgree(true)">{{lang.agree_txt}}</div>
                                    <div @click="reconsiderAgree(false)">{{lang.disagree_txt}}</div>
                                </div>
                            </div>
                            <div class='reconsider' @click="popUpReconsider('cancel_reconsider')" v-else>{{lang.reconsidered}}</div>
                        </template>
                        <template v-else>
                            <div class='reconsider' @click="popUpReconsider('cancel_reconsider')">
                            {{lang.obstetric_qc.nalysis_rconsider_uncompleted}}</div>
                        </template>
                    </template>
                    <template v-else>
                        <div class='reconsider' @click="popUpReconsider('cancel_reconsider')">{{lang.reconsidered}}</div>
                    </template>
                </template> -->
            <template
                v-if="
                    currentFile &&
                    currentFile.msg_type != systemConfig.msg_type.EXPIRATION_RES &&
                    (view.viewName || (!view.viewName && isMcData))
                "
            >
                <!-- 病人信息 -->
                <h4>{{ lang.verall_evaluation_qc }}</h4>
                <div class="row">
                    <div class="label">{{ lang.view_name }}:</div>
                    <div class="value">
                        {{ view.viewName ? getItemName(view) : lang.obstetric_qc.nalysis_uncompleted }}
                        <div
                            :title="lang.reselect_view_type"
                            v-if="
                                !currentImage.resource_copied_from &&
                                user.uid &&
                                user.uid == view.senderId &&
                                isShowReconsider
                            "
                        >
                            <span
                                class="icon iconfont iconwenhao icon_reconsider"
                                @click="popUpReconsider('reselect_view_type')"
                            >
                            </span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="label">{{ lang.view_score }}:</div>
                    <div class="value">
                        {{
                            view.viewName
                                ? view.viewScore + "(" + lang.full_mark + "100)"
                                : lang.obstetric_qc.nalysis_uncompleted
                        }}
                    </div>
                </div>
                <div class="row">
                    <div class="label">{{ lang.view_quality }}:</div>
                    <div class="value">
                        {{ view.viewName ? view.viewQuality : lang.obstetric_qc.nalysis_uncompleted }}
                    </div>
                </div>
                <div class="row">
                    <div class="label">{{ lang.reason_for_deficiency }}:</div>
                    <div class="value">
                        {{ view.viewName ? view.viewReason : lang.obstetric_qc.nalysis_uncompleted }}
                    </div>
                </div>

                <!-- 切面结构评价 -->
                <template>
                    <h4>{{ lang.structure_evaluation }}</h4>
                    <table v-if="view && view.viewItemList && view.viewItemList.length > 0">
                        <thead></thead>
                        <tbody>
                            <tr>
                                <td>{{ lang.score_items }}</td>
                                <td>{{ lang.score_value }}</td>
                                <td>{{ lang.proportion_weight }}</td>
                                <td>{{ lang.box_color }}</td>
                                <td class="operation_col" v-if="view.haveItemStruc">
                                    <el-checkbox
                                        class="el-checkbox__input"
                                        :class="{
                                            'is-indeterminate':
                                                isStructImage &&
                                                Object.values(colorsCheck).filter((v) => !v.isChecked).length > 0,
                                        }"
                                        v-model="isStructImage"
                                        @change="checkedAllItem"
                                        :title="lang.select_all + '/' + lang.cancel_select_all"
                                    >
                                    </el-checkbox>
                                </td>
                                <!-- <td>{{lang.display}}</td> -->
                                <!-- <td>{{lang.proportion_weight}}</td> -->
                            </tr>
                        </tbody>
                        <tbody>
                            <tr v-for="(item, id, index) of view.viewItemList" :key="index">
                                <td>{{ getItemName(item[0]) }}</td>
                                <td :class="{ error: !item[0].isHave }">
                                    {{ item[0].isHave ? item[0].score : lang.deletion }}
                                </td>
                                <td>{{ item[0].rate }}</td>
                                <td>
                                    <span
                                        class="arear"
                                        :style="{ background: item[0].color }"
                                        v-if="item[0].existStruct"
                                    >
                                    </span>
                                </td>
                                <td v-if="view.haveItemStruc && item[0].rows" :rowspan="item[0].rows">
                                    <el-checkbox
                                        v-model="colorsCheck[item[0].color].isChecked"
                                        @change="checkedOneItem()"
                                        v-if="item[0].existStruct"
                                    ></el-checkbox>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="tips" v-else>{{ lang.obstetric_qc.nalysis_uncompleted }}</div>
                </template>
                <template v-if="true || view.haveItemStruc">
                    <div class="title">{{ lang.exam_picture }}</div>
                    <div class="row">
                        <div class="label">{{ lang.exam_original_picture }}</div>
                        <div class="value">
                            <!-- <el-checkbox v-model="isStructImage" @change="reloadImage()"></el-checkbox> -->
                            <el-switch v-model="isDisplayBColor" @change="displayBColorImage()"> </el-switch>
                        </div>
                    </div>
                </template>
                <!-- 切面组  结构评价 -->
                <template
                    v-if="
                        isInMc &&
                        view.groupViewId &&
                        currentFile &&
                        currentFile.group_mc_resource_map &&
                        currentFile.group_mc_resource_map[view.groupViewId] &&
                        currentFile.group_mc_resource_map[view.groupViewId].types.indexOf(view.viewId) > -1
                    "
                >
                    <div
                        style="margin-top: 100px; margin-bottom: 20px; border-bottom: 1px solid rgb(197, 199, 199)"
                    ></div>
                    <div class="title">{{ lang.group_view_summary }}</div>
                    <div class="row">
                        <div class="label">{{ lang.view_group_name }}:</div>
                        <div class="value">{{ getItemName(this.view.groupView) }}</div>
                    </div>
                    <div class="row">
                        <div class="label">{{ lang.group_view_score }}:</div>
                        <div
                            class="value"
                            v-if="
                                currentFile &&
                                currentFile.group_mc_resource_map &&
                                currentFile.group_mc_resource_map[view.groupViewId] &&
                                currentFile.group_mc_resource_map[view.groupViewId].score
                            "
                        >
                            {{
                                toFixedNumber(currentFile.group_mc_resource_map[view.groupViewId].score * 100) +
                                "(" +
                                lang.full_mark +
                                "100)"
                            }}
                        </div>
                        <div class="value" v-else>--</div>
                    </div>
                    <div class="title">{{ lang.view_group }}-{{ lang.structure_evaluation }}</div>
                    <table
                        v-if="
                            currentFile.group_mc_resource_map[view.groupViewId] &&
                            currentFile.group_mc_resource_map[view.groupViewId].ai_report &&
                            currentFile.group_mc_resource_map[view.groupViewId].ai_report.report &&
                            currentFile.group_mc_resource_map[view.groupViewId].ai_report.report.group_item
                        "
                    >
                        <thead></thead>
                        <tbody>
                            <tr>
                                <td>{{ lang.score_items }}</td>
                                <td>{{ lang.score_value }}</td>
                                <td>{{ lang.proportion_weight }}</td>
                            </tr>
                        </tbody>
                        <tbody>
                            <tr
                                v-for="(item, index) of currentFile.group_mc_resource_map[view.groupViewId].ai_report
                                    .report.group_item"
                                :key="index"
                            >
                                <td>{{ getItemName(item) }}</td>
                                <td :class="{ error: item.score <= 0 }">
                                    {{ item.score <= 0 ? lang.deletion : toFixedNumber(item.score * 100) }}
                                </td>
                                <td>{{ item.rate }}</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="tips" v-else>{{ lang.obstetric_qc.nalysis_uncompleted }}</div>
                </template>
            </template>
            <template v-else>
                <div class="no-ai-result">
                    {{ lang.no_ai_result }}
                </div>
            </template>
        </div>
        <div class="bottom">
            <div class="tips" v-show="isSuccess">{{ lang[tips] }}</div>
            <div>
                <el-button size="small" class="" @click="closeDialog()" v-if="isShowBackButton">{{
                    lang.back_button
                }}</el-button>
                <template
                    v-if="
                        isShowQuestion &&
                        !currentImage.resource_copied_from &&
                        view.senderId &&
                        view.senderId == user.uid &&
                        mc_resource_map
                    "
                >
                    <el-button size="small" class="" @click="popUpReconsider('question_feedback')">{{
                        lang.question_feedback
                    }}</el-button>
                </template>
            </div>
        </div>
        <el-dialog
            :title="lang[reconsiderForm.type]"
            :visible.sync="isPopReconsider"
            :class="'obstetric_ac_reconsider_dialog'"
            modal-append-to-body
            @closed="resetReconsiderForm"
            append-to-body
            width="30%"
        >
            <div class="reconsider_content">
                <div class="center_value">
                    <template v-if="reconsiderForm.type == 'question_feedback'">
                        <div class="row">
                            <div class="label">{{ lang.please_describe_your_issue }}</div>
                            <div class="value">
                                <template>
                                    <el-input
                                        type="textarea"
                                        :size="'mini'"
                                        :rows="10"
                                        v-model="reconsiderForm.question"
                                    >
                                    </el-input>
                                </template>
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="row">
                            <div class="label">{{ lang.view_type_ai }} :</div>
                            <div class="value">
                                {{ view.viewName ? getItemName(view) : lang.obstetric_qc.nalysis_uncompleted }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="label require">{{ lang.reselect_view_type }} :</div>
                            <div class="value">
                                <template>
                                    <el-select filterable clearable v-model="reconsiderForm.reconsiderType" size="mini">
                                        <el-option
                                            v-for="item in sectionTypeOptions"
                                            :key="item.id"
                                            :label="getItemName(item)"
                                            :value="item.id"
                                        >
                                        </el-option>
                                    </el-select>
                                </template>
                            </div>
                        </div>
                        <div class="row">
                            <div class="label">{{ lang.remark_text }} :</div>
                            <div class="value">
                                <template>
                                    <el-input
                                        type="textarea"
                                        :size="'mini'"
                                        :rows="5"
                                        v-model="reconsiderForm.reconsiderInfo"
                                    >
                                    </el-input>
                                </template>
                            </div>
                        </div>
                        <div class="row">
                            <div class="label"></div>
                            <div class="value">
                                {{ lang.reselect_view_type_anaylze_tips }}
                            </div>
                        </div>
                    </template>
                </div>
                <div
                    class="center_button"
                    v-if="!currentImage.resource_copied_from && user.uid && user.uid == view.senderId"
                >
                    <el-button @click="isPopReconsider = false">{{ lang.cancel_btn }}</el-button>
                    <el-button type="primary" @click="applyReconsider()">{{ lang.confirm_txt }}</el-button>
                </div>

                <div></div>
            </div>
        </el-dialog>
    </div>
    <!-- </el-dialog> -->
</template>
<script>
import base from "../../lib/base";
import moment from "moment";
import examServer from "../../service/multiCenterService.js";
import { cloneDeep, sortBy } from "lodash";
import obstetricTool from "../../lib/obstetricTool";
import service from "../../service/service.js";
import { transferPatientInfo, toFixedNumber } from "../../lib/common_base";
export default {
    mixins: [base, obstetricTool],
    name: "obstetricQCGarreyDialog",
    components: {},
    props: {
        currentFile: {
            type: Object,
            default: () => {
                return {};
            },
        },
        //索引
        index: {
            type: Number,
            default: -1,
        },
        //是否显示
        isShowDialog: {
            type: Boolean,
            default: false,
        },
        //是否显示返回按钮
        isShowBackButton: {
            type: Boolean,
            default: true,
        },
        isShowImageNumber: {
            type: Boolean,
            default: false,
        },
        className: {
            type: String,
            default: "",
        },

        //是否显示问题反馈
        isMcData: {
            type: Boolean,
            default: true,
        },
        //是否显示问题反馈
        isShowQuestion: {
            type: Boolean,
            default: true,
        },
        //是否显示Reconsider
        isShowReconsider: {
            type: Boolean,
            default: true,
        },
        //是否显示Reconsider
        tips: {
            type: String,
            default: "ai_nalysis_result_tips",
        },
        isAiAnalyzeType: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            toFixedNumber,
            isShow: false,
            isPopReconsider: false,
            title: "",
            view: {},
            defaultView: {
                viewId: "",
                viewName: "",
                name: "",
                key: "",
                groupViewId: "",
                viewScore: "",
                groupViewName: "",
                groupView: {
                    name: "",
                    key: "",
                    id: "",
                },
                groupViewScore: "",
                viewQuality: "",
                viewReason: "--",
                viewstructList: {},
                haveStruc: false,
                haveItemStruc: false,
                viewItemList: [],
                groupItemList: [],
                isReconsider: false,
                reconsiderInfo: "",
                question: "",
                reconsiderType: "",
                senderId: 0,
            },
            patient: {},
            defaultPatient: {
                id: "",
                name: "",
                organization_name: "",
                examDate: "",
            },
            reconsiderForm: {
                type: "",
                question: "",
                reconsiderInfo: "",
                reconsiderType: "",
            },
            isSuccess: false,
            colors: this.$store.state.aiPresetData.colors,
            currentImage: {},
            isStructImage: true,
            itemIds: { ids: [], positions: [], noCheckedIds: [] },
            colorsCheck: {},
            useColors: [],
            defaultColorsCheck: this.$store.state.aiPresetData.colors.reduce((h, v) => {
                h[v] = { isChecked: true, items: [], color: v };
                return h;
            }, {}),

            isLoading: false,
            isDisplayBColor: false,
            mc_resource_map: {},
        };
    },
    computed: {
        isInMc() {
            return this.$route.fullPath.indexOf("multicenter") > -1;
        },
        cid() {
            return this.$route.query.cid || this.$route.params.cid || this.currentFile.group_id;
        },
        conversation() {
            return this.conversationList[this.cid] || null;
        },
        mcOptionsObj() {
            return this.$store.state.multicenter.optionList;
        },
        classNameStr() {
            return this.className;
        },

        sectionTypeOptions() {
            let mc_option = null;
            if (this.currentImage && this.currentImage.mc_resource_map) {
                let mc_option_id = this.currentImage.mc_resource_map.mc_op_id;
                if (mc_option_id && this.mcOptionsObj) {
                    mc_option = this.mcOptionsObj[mc_option_id].more_details;
                }
            }
            if (mc_option) {
                let list = Object.values(mc_option.listObj).filter((v) => {
                    return v;
                });
                let options = [];
                list.map((v) => {
                    if (!this.view.viewName || (this.view.viewName && this.view.viewName != v.name)) {
                        options.push({ id: v.id, name: v.name, key: v.key });
                    }
                });
                return options;
            } else {
                return [];
            }
        },
        isAiAnalyze() {
            return this.conversation.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze;
        },
    },
    created() {
        setTimeout(async () => {
            await this.setDefault();
            this.currentImage = cloneDeep(this.currentFile);
            this.initData();
        }, 0);
        this.colorsCheck = cloneDeep(this.defaultColorsCheck);
    },
    activated() {
        setTimeout(async () => {
            await this.setDefault();
            this.currentImage = cloneDeep(this.currentFile);
            // this.initData()
        }, 0);
    },
    mounted() {},
    beforeDestroy() {},
    watch: {
        isShowDialog: {
            handler(newValue, oldValue) {
                if (newValue) {
                    this.isShow = newValue;
                    this.view = cloneDeep(this.defaultView);
                    this.patient = cloneDeep(this.defaultPatient);
                    this.itemIds = { ids: [], positions: [], noCheckedIds: [] };
                    this.initData();
                }
            },
            immediate: true,
        },
        isAiAnalyzeType: {
            handler(newValue, oldValue) {
                this.view = cloneDeep(this.defaultView);
                this.patient = cloneDeep(this.defaultPatient);
                this.itemIds = { ids: [], positions: [], noCheckedIds: [] };
                this.initData();
            },
            immediate: true,
        },

        currentFile: {
            handler(newValue, oldValue) {
                this.currentImage = this.currentFile;
            },
            immediate: true,
            deep: true,
            // this.$emit('change')
        },
        currentImage: {
            handler(newValue, oldValue) {
                if (newValue) {
                    if (
                        newValue &&
                        oldValue &&
                        newValue.resource_id &&
                        oldValue.resource_id &&
                        newValue.resource_id != oldValue.resource_id
                    ) {
                        this.view = cloneDeep(this.defaultView);
                        this.patient = cloneDeep(this.defaultPatient);
                        this.isStructImage = true;
                        this.isDisplayBColor = false;
                        this.itemIds = { ids: [], positions: [], noCheckedIds: [] };
                        this.colorsCheck = cloneDeep(this.defaultColorsCheck);
                    } else {
                    }
                    this.initData();
                    this.isCheckedAll();
                }
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        getItemName(item) {
            if (item.key && item.key in this.lang) {
                return this.lang[item.key];
            }
            return item.name;
        },
        isCheckedAll() {
            let isStructImage = false;
            for (let color in this.colorsCheck) {
                isStructImage = isStructImage || this.colorsCheck[color].isChecked;
            }
            isStructImage = isStructImage;
        },
        popUpReconsider(type) {
            if (
                this.currentImage &&
                !this.currentImage.resource_copied_from &&
                this.view.senderId &&
                this.view.senderId == this.user.uid &&
                this.currentImage.mc_resource_map
            ) {
                this.isPopReconsider = true;
                this.reconsiderForm.type = type;
                this.reconsiderForm.reconsiderType = null;
            }
        },
        displayBColorImage() {
            this.checkedAllItem(true);
        },
        checkedAllItem(checked) {
            this.isStructImage = checked;
            for (let color in this.colorsCheck) {
                this.colorsCheck[color].isChecked = this.isStructImage;
            }
            this.reloadImage();
        },
        getReconsiderName() {
            let list = this.sectionTypeOptions.filter((v) => v.id == this.reconsiderForm.reconsiderType);
            if (list.length > 0) {
                return this.getItemName(list[0]);
            }
            return "";
        },
        checkedOneItem(item) {
            this.isStructImage = false;
            for (let color in this.colorsCheck) {
                if (this.colorsCheck[color].isChecked && this.useColors.indexOf(color) >= 0) {
                    this.isStructImage = true;
                    break;
                }
            }
            this.reloadImage();
        },
        resetReconsiderForm() {
            let { question } = this.view;
            this.reconsiderForm = { type: "", question, reconsiderInfo: "", reconsiderType: "" };
        },
        async setDefault() {
            this.title = this.lang.obstetric_qc.result;
            this.view = cloneDeep(this.defaultView);
            this.patient = cloneDeep(this.defaultPatient);
        },
        initData() {
            if (!this.currentImage) {
                return;
            }
            this.useColors = [];
            console.log("initData", this.currentImage);
            this.mc_resource_map = this.getMcResourceMap(this.currentImage, this.isAiAnalyzeType);
            this.patient.patient_name = this.currentImage.patient_name || this.lang.not_upload_text;
            this.patient.patient_id = this.currentImage.patient_id || "";
            this.patient.patient_series_datetime = this.currentImage.patient_series_datetime
                ? this.formatTime(this.currentImage.patient_series_datetime)
                : "";
            this.patient.patientInfo = this.currentImage.patientInfo
                ? this.currentImage.patientInfo
                : transferPatientInfo(this.currentImage);
            this.patient.exam_type = this.currentImage.exam_type;
            this.patient.image_list = this.currentImage.image_list || [];
            this.patient.image_num = this.currentImage.image_num || 0;
            this.patient.organization_name = this.currentImage.exam_custom_info
                ? this.currentImage.exam_custom_info.organization || ""
                : "";
            this.patient.organization_name = this.patient.organization_name || this.lang.not_upload_text;
            this.view.isReconsider = false;
            this.isSuccess = false;
            this.view.senderId = this.currentImage.sender_id || this.currentImage.creator_id;
            this.view.haveItemStruc = false;
            if (this.mc_resource_map) {
                let mc_resource_map = cloneDeep(this.mc_resource_map);
                let mc_option = mc_resource_map.mc_option;
                if (!mc_option && this.mcOptionsObj && mc_resource_map.mc_op_id) {
                    mc_option = this.mcOptionsObj[mc_resource_map.mc_op_id].more_details;
                }
                let ai_report = mc_resource_map.ai_report;

                this.view.question = mc_resource_map.question;
                this.reconsiderForm.question = mc_resource_map.question;

                // this.view.reconsiderInfo = mc_resource_map.reconsider_info
                // this.view.reconsiderType = mc_resource_map.reconsider_type

                // this.reconsiderForm.reconsiderInfo =  mc_resource_map.reconsider_info
                // this.reconsiderForm.reconsiderType =  mc_resource_map.reconsider_type

                this.view.isReconsider = mc_resource_map.is_reconsider;
                // console.log('ai_report',JSON.stringify(ai_report))
                if (ai_report && ai_report.report && ai_report.report.isSuccess) {
                    if (ai_report.report && ai_report.report.isSuccess) {
                        this.isSuccess = true;
                    }

                    let ai_result = ai_report.report;
                    this.view.viewReason =
                        (ai_result.view_desc || ["--"]).map((v) => (v in this.lang ? this.lang[v] : v)).join(", ") ||
                        "--";
                    if (mc_option && mc_option.listObj) {
                        let list = Object.values(mc_option.listObj).filter((v) => {
                            return v;
                        });
                        for (let i = list.length - 1; i >= 0; i--) {
                            let viewObj = list[i];
                            this.view.viewId = viewObj.id;
                            // console.error('ai_result:',ai_result)
                            if (ai_result && ai_result.isSuccess && viewObj.id == ai_result.view_type) {
                                this.view.viewScore = this.toFixedNumber(parseFloat(ai_result.view_score) * 100);
                                // console.error('viewObj.name:',viewObj.name)
                                this.view.viewName = viewObj.name;
                                this.view.name = viewObj.name;
                                this.view.key = viewObj.key;
                                if (
                                    viewObj.ai_lower.lowest <= this.view.viewScore &&
                                    viewObj.ai_lower.highest >= this.view.viewScore
                                ) {
                                    this.view.viewQuality = this.lang.non_standard;
                                }
                                if (
                                    viewObj.ai_middle.lowest < this.view.viewScore &&
                                    viewObj.ai_middle.highest >= this.view.viewScore
                                ) {
                                    this.view.viewQuality = this.lang.basic_standard;
                                }
                                if (
                                    viewObj.ai_height.lowest < this.view.viewScore &&
                                    viewObj.ai_height.highest >= this.view.viewScore
                                ) {
                                    this.view.viewQuality = this.lang.standard;
                                }
                                // this.view.viewstructList = ai_report.structure
                                let items = ai_result.item || [];
                                let haveSetItem = items.reduce((h, v) => {
                                    h[v.type] = h[v.type] || [];
                                    h[v.type].push(v);
                                    return h;
                                }, {});
                                let colors = this.$store.state.aiPresetData.colors;
                                this.view.viewItemList = [];
                                let colorsObj = {};
                                let i = 0;
                                viewObj.item.forEach((item) => {
                                    let detail_ids = item.detail_ids;
                                    if (detail_ids && detail_ids.length > 0) {
                                        let color = colors[i];
                                        let old_color = "";
                                        detail_ids.forEach((detail_id) => {
                                            if (colorsObj[detail_id]) {
                                                old_color = colorsObj[detail_id];
                                            }
                                        });
                                        if (!old_color) {
                                            i = i + 1;
                                        }
                                        detail_ids.forEach((detail_id) => {
                                            if (old_color) {
                                                colorsObj[detail_id] = old_color;
                                            } else {
                                                colorsObj[detail_id] = color;
                                            }
                                        });
                                    }
                                });

                                for (let k in viewObj.item) {
                                    let item = viewObj.item[k];

                                    this.view.viewItemList[item.id] = this.view.viewItemList[item.id] || [];
                                    if (haveSetItem[item.id] && haveSetItem[item.id].length > 0) {
                                        haveSetItem[item.id].forEach((struc) => {
                                            if (struc.type == item.id) {
                                                this.view.viewItemList[item.id].push({
                                                    id: item.id,
                                                    name: item.name,
                                                    key: item.key,
                                                    rate: item.rate,
                                                    score: this.toFixedNumber(parseFloat(struc.score) * 100),
                                                    detail_ids: item.detail_ids,
                                                    isHave: true,
                                                    color: colorsObj[item.detail_ids[0]],
                                                    rows: 1,
                                                    existStruct: false,
                                                    isChecked: false,
                                                    position: [],
                                                });
                                                if (this.colorsCheck[colorsObj[item.detail_ids[0]]]) {
                                                    this.colorsCheck[colorsObj[item.detail_ids[0]]].items.push(item.id);
                                                }
                                                // console.error(struc.score)
                                                // console.error(this.toFixedNumber(parseFloat(struc.score)*100))
                                            }
                                        });
                                    } else {
                                        this.view.viewItemList[item.id].push({
                                            id: item.id,
                                            name: item.name,
                                            rate: item.rate,
                                            score: 0,
                                            isHave: false,
                                            color: "",
                                            rows: 1,
                                            existStruct: false,
                                            isChecked: false,
                                            position: [],
                                        });
                                    }
                                }
                                let structure = ai_result.structure;
                                let haveSetStruc = structure.reduce((h, v) => {
                                    h[v.type] = h[v.type] || [];
                                    h[v.type].push(v);
                                    return h;
                                }, {});
                                this.view.viewstructList = {};
                                for (let k = viewObj.detail.length - 1; k >= 0; k--) {
                                    let detail = viewObj.detail[k];
                                    this.view.viewstructList[detail.id] = this.view.viewstructList[detail.id] || [];
                                    if (haveSetStruc[detail.id] && haveSetStruc[detail.id].length > 0) {
                                        haveSetStruc[detail.id].forEach((struc) => {
                                            if (struc.type == detail.id) {
                                                this.view.haveStruc = true;
                                                this.view.viewstructList[detail.id].push({
                                                    id: detail.id,
                                                    name: detail.name,
                                                    rate: 0,
                                                    score: 0,
                                                    isHave: true,
                                                    existStruct: true,
                                                });
                                                for (let j in this.view.viewItemList) {
                                                    let item = this.view.viewItemList[j][0];
                                                    if (item.detail_ids && item.detail_ids.indexOf(detail.id) > -1) {
                                                        this.view.viewItemList[j][0].existStruct = true;
                                                        this.view.haveItemStruc = true;
                                                        let is_need_item =
                                                            this.itemIds.ids.indexOf(this.view.viewItemList[j][0].id) >
                                                            -1;
                                                        let is_in = this.itemIds.ids.length > 0 ? is_need_item : true;
                                                        this.view.viewItemList[j][0].isChecked = this.isStructImage
                                                            ? is_in
                                                            : false;
                                                        this.useColors.push(this.view.viewItemList[j][0].color);
                                                    }
                                                }
                                            }
                                        });
                                    } else {
                                        this.view.viewstructList[detail.id].push({
                                            id: detail.id,
                                            name: detail.name,
                                            rate: 0,
                                            score: 0,
                                            isHave: false,
                                            existStruct: false,
                                        });
                                    }
                                }

                                this.view.viewItemList = sortBy(this.view.viewItemList, (v) => {
                                    return !v[0].existStruct;
                                });
                                this.view.viewItemList = sortBy(this.view.viewItemList, (v) => {
                                    return !v[0].color;
                                });
                                this.view.viewItemList = this.view.viewItemList.map((v) => {
                                    if (this.colorsCheck[v[0].color]) {
                                        v[0].rows = this.colorsCheck[v[0].color].items.length || 0;
                                        this.colorsCheck[v[0].color].items = [];
                                        return v;
                                    } else {
                                        return v;
                                    }
                                });
                                //上色
                                this.getArearcolor();

                                if (viewObj.group_view_id) {
                                    this.view.groupViewScore = this.toFixedNumber(
                                        parseFloat(ai_result.group_view_score) * 100
                                    );
                                    this.view.groupViewId = viewObj.group_view_id;
                                    let groupView = mc_option.groupObj[viewObj.group_view_id + ""];
                                    this.view.groupViewName = groupView.name;
                                    this.view.groupView = {
                                        name: groupView.name,
                                        key: groupView.key,
                                        id: groupView.id,
                                    };
                                    let group_items = [];
                                    if (ai_result.group_item && ai_result.group_item.length > 0) {
                                        group_items = ai_result.group_item[0].score || [];
                                    }
                                    if (!Array.isArray(group_items)) {
                                        group_items = [group_items];
                                    }
                                    let haveSetGroupView = (group_items || []).reduce((h, v, i) => {
                                        h[i] = h[i] || [];
                                        h[i].push({ type: i, score: v });
                                        return h;
                                    }, {});
                                    // console.error('haveSetGroupView',haveSetGroupView)
                                    // console.error('groupView.item',groupView.item)
                                    this.view.groupItemList = [];
                                    for (let k = groupView.item.length - 1; k >= 0; k--) {
                                        let item = groupView.item[k];
                                        this.view.groupItemList[item.id] = this.view.groupItemList[item.id] || [];
                                        if (haveSetGroupView[item.id] && haveSetGroupView[item.id].length > 0) {
                                            haveSetGroupView[item.id].forEach((struc, i) => {
                                                if (struc.type == item.id) {
                                                    this.view.groupItemList[item.id].push({
                                                        name: item.name,
                                                        key: item.key,
                                                        rate: item.rate,
                                                        score: this.toFixedNumber(parseFloat(struc.score) * 100),
                                                        isHave: true,
                                                    });
                                                }
                                            });
                                        } else {
                                            this.view.groupItemList[item.id].push({
                                                name: item.name,
                                                rate: item.rate,
                                                score: 0,
                                                isHave: false,
                                            });
                                        }
                                    }
                                }
                                break;
                            }
                        }
                        // console.error('this.view:',this.view)
                    }
                    // console.error(this.view)
                } else {
                    //数据未获得
                }
                // console.error(this.view)
                // debugger
            }
        },
        isHalfChecked(item) {
            let same = [];
            let detail_ids = item[0].detail_ids || [];
            if (item[0].isChecked && detail_ids.length > 1) {
                detail_ids.map((id) => {
                    if (this.itemIds.noCheckedIds.indexOf(id) > -1) {
                        same.push(id);
                    }
                });
                return same.length > 0;
            } else {
                return false;
            }
        },
        reloadImage() {
            let colors = Object.values(this.colorsCheck)
                .filter((v) => v.isChecked)
                .map((v) => {
                    return v.color;
                });
            this.$emit("reloadImage", this.isStructImage, colors, this.isDisplayBColor);
        },

        // reloadImage(){
        //     this.itemIds = {ids:[],positions:[]}
        //     let detailIdCheck=[]
        //     let detailIdSame=[]
        //     // console.error('this.view.viewItemList',this.view.viewItemList)
        //     this.view.viewItemList.map(v=>{
        //         (v[0].detail_ids||[]).map(type=>{
        //             detailIdSame[type] = detailIdSame[type]||0
        //             ++detailIdSame[type]
        //         })
        //     })
        //     let tt = []
        //     this.view.viewItemList.map(v=>{
        //         if(v &&v.length>0&&v[0].isChecked&&v[0].existStruct){
        //             this.itemIds.ids.push(v[0].id);
        //             let r = (v[0].detail_ids||[]).map(type=>{
        //                 detailIdCheck[type]=detailIdCheck[type]||0
        //                 let s = this.view.viewstructList[type+'']
        //                 if(detailIdSame[type]>1){
        //                     if(s&&s.length>=detailIdCheck[type]+1&&s[detailIdCheck[type]]&&s[detailIdCheck[type]].position&&s[detailIdCheck[type]].position.length>0){
        //                         this.itemIds.positions.push(v[0].id+'_'+s[detailIdCheck[type]].position.join('_'))
        //                     }
        //                 }else{
        //                     if(s&&s.length){
        //                         s.map(d=>{
        //                             if(d&&d.position&&d.position.length>=4){
        //                                 this.itemIds.positions.push(v[0].id+'_'+d.position.join('_'))
        //                                 this.itemIds.positions.push(v[0].id+'_'+d.position.join('_'))
        //                             }
        //                         })
        //                     }

        //                 }
        //                 detailIdCheck[type] = detailIdCheck[type] + 1
        //             })
        //         }
        //         return null
        //     })
        //     console.error(this.isStructImage,this.itemIds.ids,this.itemIds.positions)
        //     this.$emit('reloadImage',this.isStructImage,this.itemIds.ids,this.itemIds.positions)
        // },

        closeDialog() {
            this.view = cloneDeep(this.defaultView);
            this.patient = cloneDeep(this.defaultPatient);
            this.isSuccess = false;
            this.isShow = false;
            this.$emit("change");
        },
        getArearcolor() {
            let n = 0;
            let structure = this.mc_resource_map.ai_report.report.structure || [];
            let colorStruc = [];
            for (let k = 0; k < structure.length; k++) {
                let v = structure[k];
                if (colorStruc.indexOf(v.type) > -1) {
                    if (this.view.viewstructList[v.type + ""]) {
                        this.view.viewstructList[v.type + ""][1].color = this.colors[k];
                        this.view.viewstructList[v.type + ""][1].position = v.position;
                    }
                } else {
                    if (this.view.viewstructList[v.type + ""]) {
                        this.view.viewstructList[v.type + ""][0].color = this.colors[k];
                        this.view.viewstructList[v.type + ""][0].position = v.position;
                    }
                }
                colorStruc.push(v.type);
            }
        },
        // 申请复议
        applyReconsider() {
            let that = this;
            let msg = cloneDeep(that.currentImage);
            let param = {
                mcID: msg.mc_resource_map.mc_id,
                id: msg.mc_resource_map.id,
                field: {},
            };
            if (that.view.senderId && that.view.senderId != that.user.uid) {
                return;
            }
            if (this.reconsiderForm.type == "question_feedback") {
                //问题反馈
                param.field["question"] = (this.reconsiderForm.question || "").replace(/^\s+|\s+$/g, "");
                if (
                    (msg.mc_resource_map.question || that.reconsiderForm.question) &&
                    msg.mc_resource_map.question !== that.reconsiderForm.question
                ) {
                    that.isPopReconsider = false;
                    examServer.updateResourceMap(param).then((res) => {
                        if (res.data.error_code == 0) {
                            that.$message.success(that.lang.feedback_success_tips);
                            msg.mc_resource_map.question = that.reconsiderForm.question;
                            that.updateToStore(msg);
                        } else {
                            that.$message.error(that.lang.operate_err);
                            // that.reconsiderForm.question= that.view.question
                        }
                    });
                } else {
                    that.isPopReconsider = false;
                }
            } else {
                //重新分析
                if (that.reconsiderForm.reconsiderType != 0 && !that.reconsiderForm.reconsiderType) {
                    that.$message.info(that.lang.view_type_select_tips);
                    return;
                }
                let param = {
                    mcID: msg.mc_resource_map.mc_id,
                    id: msg.mc_resource_map.id,
                    field: {
                        is_reconsider: true,
                        reconsider_info: that.reconsiderForm.reconsiderInfo,
                        reconsider_type: that.reconsiderForm.reconsiderType,
                        reconsider_ai_report: null,
                    },
                };
                // that.isPopReconsider = false
                let reconsiderName = that.getReconsiderName();
                that.$MessageBox
                    .confirm(that.lang.reselect_view_type_tips.replace("${name}", reconsiderName))
                    .then((action) => {
                        that.view.isReconsider = param.field["is_reconsider"];
                        that.view.reconsiderInfo = param.field["reconsider_info"];
                        that.view.reconsiderType = param.field["reconsider_type"];

                        msg.mc_resource_map.is_reconsider = that.view.isReconsider;
                        msg.mc_resource_map.reconsider_info = that.view.reconsiderInfo;
                        msg.mc_resource_map.reconsider_type = that.view.reconsiderType;
                        that.updateToStore(msg);
                        let file_list = [
                            {
                                url: msg.url.replace("/thumbnail.jpg", ".ai"),
                                img_id: msg.resource_id,
                                view_id: msg.mc_resource_map.reconsider_type,
                            },
                        ];
                        if (that.view.isReconsider) {
                            that.isLoading = true;
                            that.$emit("updataLoadingStatus", that.isLoading);
                            let more_details = msg.mc_resource_map;
                            //"0": "产科",
                            service
                                .requestStartAiAnalyze({
                                    type: "0",
                                    file_list,
                                    more_details: { action: "reconsider", mc_resource_map_id: msg.mc_resource_map.id },
                                })
                                .then(async (res) => {
                                    // console.log('requestStartAiAnalyze:',res)
                                    that.isLoading = false;
                                    that.$emit("updataLoadingStatus", that.isLoading);
                                    if (
                                        res.data &&
                                        !res.data.error_code &&
                                        res.data.data &&
                                        res.data.data.mc_resource_map &&
                                        res.data.data.mc_resource_map.type != null &&
                                        res.data.data.mc_resource_map.type == that.view.reconsiderType
                                    ) {
                                        !that.isShow ||
                                            that.$message.success(
                                                that.lang.reselect_view_type_anaylze_success.replace(
                                                    "${name}",
                                                    reconsiderName
                                                )
                                            );
                                        msg.mc_resource_map = res.data.data.mc_resource_map;
                                        that.updateToStore(msg);
                                        setTimeout(() => {
                                            that.reloadImage();
                                        }, 200);
                                    } else {
                                        !that.isShow ||
                                            that.$message.error(
                                                that.lang.reselect_view_type_anaylze_fail.replace(
                                                    "${name}",
                                                    reconsiderName
                                                )
                                            );
                                    }
                                });
                            return;
                        }
                    });
                that.isPopReconsider = false;
            }
        },
        reconsiderAgree(isAgree) {},
        updateToStore(msg) {
            let that = this;
            that.$store.commit("conversationList/updateMessageMCAiReport", msg);
            that.$store.commit("conversationList/updateGalleryObjMCAiReport", msg);
            //更新store数
            //exmalist
            that.$store.commit("examList/updateExamListMCAiReport", msg);
            //gallerylist
            that.$store.commit("gallery/updateGalleryMCAiReport", msg);
            that.$emit("updataMCResourceMapRconsider", msg.mc_resource_map);
        },
    },
};
</script>
<style lang="scss">
.obstetric_ac_garrey_dialog {
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #ecf6f6;
    overflow: auto;
    .content {
        padding: 10px 10px;
        display: flex;
        flex-direction: column;
        overflow-y: scroll;
        flex: 2;
        color: #537378;
        .title {
            font-family: "Arial Negreta", "Arial Normal", "Arial";
            font-weight: 700;
            font-style: normal;
            font-size: 16px;
            margin-top: 5px;
            margin-bottom: 5px;
            color: #537378;
        }
        .row {
            display: flex;
            flex-direction: row;
            font-size: 14px;
            flex-wrap: nowrap;
            padding: 5px 0 5px 0;
            .label {
                width: 120px;
            }
            .value {
                flex: 2;
                display: flex;
                justify-content: space-between;
                flex-wrap: nowrap;
                .icon_reconsider {
                    margin-top: -4px;
                    display: none;
                }
                &:hover {
                    .icon_reconsider {
                        margin-top: -4px;
                        display: block;
                    }
                }
            }
        }
        .tips {
            font-family: "Arial Normal", "Arial";
            font-weight: 400;
            font-style: normal;
            font-size: 13px;
            line-height: normal;
            color: #537378;
        }
        .reconsider {
            color: #789796;
            font-size: 15px;
            font-family: "Arial Negreta", "Arial Normal", "Arial";
            font-weight: 700;
            font-style: normal;
            text-align: center;
        }
        th,
        td {
            border: 1px solid #bbb;
            padding: 6px;
            text-align: center;
            font-family: "Arial Normal", "Arial";
            font-weight: 400;
            font-style: normal;
            font-size: 13px;
            text-align: center;
            line-height: normal;
            vertical-align: middle;
            color: #537378;
        }
        .operation_col {
            width: 55px;
        }
        table {
            color: #333;
            border: 1px solid #bbb;
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            .name {
                color: #0000cc;
            }
            .error {
                color: red;
            }
            .arear {
                display: inline-block;
                height: 13px;
                width: 25px;
                border: 0px solid;
                box-shadow: 2px 4px 10px 5px #99999954;
            }
        }
    }
    .content::-webkit-scrollbar-track {
        background-color: rgba(0, 0, 0, 0);
    }
    .content::-webkit-scrollbar-thumb {
        display: none;
        height: 10px !important;
    }
    .content:hover::-webkit-scrollbar-thumb {
        display: block;
        height: 10px;
    }
    .content::scrollbar-thumb {
        display: block;
        height: 10px;
    }
    .content_all {
        margin-top: 0px;
    }
    .no-ai-result {
        font-family: "Arial Normal", "Arial";
        font-weight: 400;
        font-style: normal;
        font-size: 15px;
        line-height: normal;
        text-align: center;
        color: #537378;
    }
    .bottom {
        height: 70px;
        background: #ecf6f6;
        display: flex;
        justify-content: flex-end;
        flex-direction: column;
        padding-left: 10px;
        .tips {
            font-family: "Arial Normal", "Arial";
            font-weight: 400;
            font-style: normal;
            font-size: 13px;
            line-height: normal;
            color: #537378;
        }
        div {
            margin-bottom: 8px;
        }
    }
    .reconsider_result_new_tips {
        font-weight: 400;
        font-style: normal;
        font-size: 14px;
        color: #333333;
        line-height: normal;
        margin: 0px 0 8px 0;
        background: #f1f0c2;
        border-radius: 10px;
        padding: 5px 8px 5px 8px;
        text-align: center;
        .button_agree {
            margin-top: 5px;
            padding: 0 20% 0 20%;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            justify-content: space-around;
            div {
                background: white;
                padding: 4px;
                border-radius: 5px;
                width: 80px;
                background: #dcdfe6;

                white-space: nowrap;
                cursor: pointer;
                background: #fff;
                border: 1px solid #dcdfe6;
                color: #606266;
                -webkit-appearance: none;
                text-align: center;
                box-sizing: border-box;
                outline: 0;
                margin: 0;
                -webkit-transition: 0.1s;
                transition: 0.1s;
                font-weight: 500;
                -moz-user-select: none;
                font-size: 14px;
                border-radius: 4px;
            }
        }
        .exclamation_mark {
            color: yellow;
            font-weight: 700;
            display: inline-block;
            text-align: center;
            margin-left: -15px;
        }
    }
}
.obstetric_ac_reconsider_dialog {
    .reconsider_content {
        height: 100%;
        display: flex;
        flex-direction: column;
        font-family: "Arial Normal", "Arial";
        font-weight: 400;
        font-style: normal;
        font-size: 13px;
        color: #333333;
        line-height: normal;
        .center_button {
            display: flex;
            justify-content: center !important;
            flex-direction: row;
            flex-wrap: nowrap;
            bottom: 10px;
            width: calc(100% - 28px);
            text-align: center;
            height: 42px;
        }
        .center_value {
            height: calc(100% - 42px);
            overflow: auto;
            .row {
                display: flex;
                padding: 6px 2px 10px 2px;
                flex-direction: row;
                flex-wrap: nowrap;
                .label {
                    width: 120px;
                }
                .require:before {
                    content: "*";
                    color: #f56c6c;
                    margin-right: 4px;
                }
                .value {
                    flex: 2;
                }
            }
        }
    }
    .el-dialog__header {
        text-align: center;
    }
    .el-dialog {
        height: 400px !important;
    }
}
</style>
