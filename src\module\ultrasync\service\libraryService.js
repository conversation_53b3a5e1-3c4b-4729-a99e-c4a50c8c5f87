import webimSservice from "./service.js";
import axios from 'axios'
import { Toast } from "vant";
import https from 'https' ;
import Tool from '@/common/tool'
import store from "../store";
const getBaseUrl = () => {
    let library_server = store.state.systemConfig.serverInfo.library_server;
    let ajaxServer = library_server.protocol+library_server.addr+':' + library_server.port +"/library/"
    // ajaxServer = "http://************:8081/library/"; //systemConfig.server_type.protocol+systemConfig.server_type.host+systemConfig.server_type.port;
    // console.error('ajaxServer',ajaxServer)
    return ajaxServer;
};
const interfaceList = {
    getCategories: {
        name: "categories",
        suffix_url: "wp-json/wp/v2",
    },
    getTags: {
        name: "tags",
        suffix_url: "wp-json/wp/v2",
    },
    getPosts: {
        name: "posts",
        suffix_url: "wp-json/wp/v2",
        embed:true
    },
    getUser: {
        name: "users",
        suffix_url: "wp-json/wp/v2",
        embed:true
    },
    getPost: {
        name: "posts",
        suffix_url: "wp-json/wp/v2",
        embed:true
    },
    searchPosts: {
        name: "posts/search",
        suffix_url: "wp-json/m_wp/v1",
    },
    setPostViews: {
        name: "posts/views",
        suffix_url: "wp-json/m_wp/v1",
    },
    setPostLikes: {
        name: "posts/likes",
        suffix_url: "wp-json/m_wp/v1",
    },
    getPostSlideImages: {
        name: "posts/get/slide_image",
        suffix_url: "wp-json/m_wp/v1",
    },
    getLibraryToken:{
        name: "auth/get_token",
        suffix_url: "wp-json/m_wp/v1",
    },
    getPostViewStatical:{
        name: "post/m_post_post_statistical",
        suffix_url: "wp-json/m_wp/v1",
    },
    getPostComments:{//get
        name: "comments",
        suffix_url: "wp-json/wp/v2",
    },
    deletePostComment:{//delete
        name: "comments/own",
        suffix_url: "wp-json/m_wp/v1",
    },
    getPostCommentTreeByCommentId:{//get
        name: "comments/tree",
        suffix_url: "wp-json/m_wp/v1",
    },
    //    {
    //   "post": 36,
    //   "parent": 0,
    //   "content": "e18t99",
    //   "custom_fields": {
    //     "ultrasync_user_avart": "999" // 注意字段名拼写是否一致
    //   }
    // }
    createPostComment:{ //post
        name: "comments",
        suffix_url: "wp-json/wp/v2",  
    },
    likePostCommentTreeByCommentId:{ //post
        name: "comments/like",
        suffix_url: "wp-json/m_wp/v1",  
    }
};
/**
 * {
 *  request_type: '1', //请求类型
 *  suffix_url: '1', //url追加
 *  per_page: this.filters.per_page,
 *  page: this.filters.page
 * };
 *
 *
 *
 * **/


const api=axios.create({
    headers:{
        'Content-Type':'application/json'
    },
    httpsAgent: new https.Agent({
        rejectUnauthorized: false // 忽略证书验证
    }),
    timeout:30000,
    transformRequest:[(data)=>{
        if(!data){
            return {error:1};
        }
        return JSON.stringify(data.data)
    }],
    transformResponse:[(data)=>{
        if(data){
            try{
                data=JSON.parse(data)
            }catch(e){
                console.log('请求失败')
            }
        }
        return data;
    }]
})
api.interceptors.request.use((config)=>{
    let token = store.state.libraryData.library_token;
    if(token){
        token = 'Bearer ' + store.state.libraryData.library_token;
        config.headers['Authorization'] = token
    }
    delete config.headers.token
    return config
})
const interfaceFactory = (name, data) => {
    const url = getBaseUrl();
    let r = interfaceList[name];
    let method = r.suffix_url ? `${r.suffix_url}/${r.name}` : r.name;
    if (data && data.suffix_url) {
        method = method + "/" + data.suffix_url;
        delete data.suffix_url;
    }
    if (data && data.suffix_query) {
        method = method + "/" + data.suffix_query;
        delete data.suffix_query;
    }
    let headers = {
        'Content-Type':'application/json'
    }
    let new_data = {}
    let address = url + method;
    let request_type = "";
    if ((data && data.embed)|| r.embed) {
        // data['_embed'] = 'wp:featuredmedia'
        // method = method + "/embed/"
    }
    if (data && data.request_type) {
        request_type = data.request_type
        delete data.request_type;
        new_data = {data}

    }else{
        request_type = "get";
        data.xy_name = store.state.user.username
        new_data = {params:data}
    }
    if (address.indexOf('auth/get_token') > -1) {
        headers = {
            'Content-Type':'application/x-www-form-urlencoded'
        }
        const external_token = data.external_token
        new_data = {
            data:{
                'username': store.state.user.username,
                external_token
            }
        }
    }
    return api[request_type](address, new_data, {headers} ).then(
        (res) => {
            // console.info(method, res);
            return res;
        },
        (error) => {
            console.error(method, error);
            throw error;
        }
    );
};
const service = {getBaseUrl};
Object.keys(interfaceList).forEach((name) => {
    service[name] = (data) => {
        return interfaceFactory(`${name}`, data);
    };
});

let refeshLibrartToken = async (err) =>{
    let res = await webimSservice.getExternalToken({type:'library'});
    if (res.data.error_code===0) {
        let  external_token= res.data.data.external_token;
        res =await service.getLibraryToken({request_type:'post', external_token: external_token});
        if(res.data &&res.data.token){
            store.commit('libraryData/updateLibraryData',{library_token: res.data.token})
        }
        return api.request(err.data.more_detail.config)
    }else{
        return err
    }
}
api.interceptors.response.use(
    (response)=>{
        return response;
    },
    (err)=>{
        console.error('error:',err)
        if(err&&err.data&&err.data.more_detail && err.data.more_detail.response  && err.data.more_detail.response.data && err.data.more_detail.response.data.code=="Token invalid"){
            return refeshLibrartToken(err);
        }
    }
)
export default service;
