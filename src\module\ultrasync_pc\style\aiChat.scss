// AI聊天主题色
$ai-theme-gradient: linear-gradient(134deg, #6082E0 0%, #0BB8B9 96%);

// 滚动条样式占位符
%ai-scrollbar-style {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(96, 130, 224, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(134deg, #6082E0 0%, #0BB8B9 96%);
    border-radius: 3px;

    &:hover {
      background: linear-gradient(134deg, #4a6bc4 0%, #0a9a9b 96%);
    }
  }
}

// AI主题背景样式
.ai-theme-background {
  background: $ai-theme-gradient;
  border: none !important;
  color: #fff;
}
.ai-operation-link {
    color: #007bff;
    text-decoration: underline;
    cursor: pointer;
}
// AI主题滚动条样式
// 通用div滚动条
div {
  @extend %ai-scrollbar-style;
}

// el-table__body-wrapper滚动条
.el-table__body-wrapper {
  @extend %ai-scrollbar-style;
}

// 导出变量供其他组件使用
:export {
  aiThemeGradient: $ai-theme-gradient;
}
:deep(.el-button--primary) {
    background: $ai-theme-gradient;
    border: none;
    color: #fff;
    &:hover,
    &:focus {
        background: linear-gradient(134deg, #7795e6 0%, #27c4c5 96%);
    }
    &.is-disabled,
    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        &:hover,
        &:focus {
            background: $ai-theme-gradient;
        }
    }
}
// 通用标签页样式
.ai-chat-tabs {
    flex: 1;

    :deep(.el-tabs__header) {
        margin-bottom: 0;
        border-bottom: none;

        .el-tabs__nav-wrap {
            &::after {
                display: none;
            }
        }

        .el-tabs__nav {
            border: none;
        }

        .el-tabs__item {
            // 通用item样式
            color: #000;
            border: none;
            box-sizing: border-box;
            &.is-active {
                font-weight: bold;
            }
        }

        .el-tabs__active-bar {
            // 通用active-bar样式
            background: $ai-theme-gradient;
            border-radius: 3px;
        }
    }

    // 大尺寸 Tab
    &.ai-chat-tabs--large {
        :deep(.el-tabs__item) {
            height: 80px;
            line-height: 80px;
            font-size: 20px;
        }
        :deep(.el-tabs__active-bar) {
            height: 4px;
        }
    }

    // 小尺寸 Tab
    &.ai-chat-tabs--small {
        :deep(.el-tabs__item) {
            height: 50px;
            line-height: 50px;
            font-size: 14px;
        }
        :deep(.el-tabs__active-bar) {
            height: 3px; // 比大尺寸的细一点
        }
    }
    :deep(.el-tabs__content) {
        margin-top: 20px;
        .pagination-container {
            margin-top: 20px;
            text-align: right;
        }
    }
}

// el-pagination 样式
:deep(.el-pagination) {
    // 激活的页码
    .el-pager li.active {
        background: $ai-theme-gradient;
        color: #fff !important; // 使用 !important 确保覆盖 Element UI 的默认样式
        border: none;
        border-radius: 2px;
        &:hover {
            color: #fff !important; // 保持激活态的hover文字颜色
        }
    }

    // 页码悬浮状态 (非激活、非禁用)
    .el-pager li:not(.disabled):not(.active):hover {
        color: #6082E0 !important; // 主题渐变起始色, 使用 !important 确保覆盖
    }

    // 上一页/下一页按钮悬浮状态 (非禁用)
    .btn-prev:not([disabled]):hover,
    .btn-next:not([disabled]):hover {
        color: #6082E0 !important; // 主题渐变起始色, 使用 !important 确保覆盖
    }
}