import { Toast } from 'vant';
import axios from "axios";
import Tool from "@/common/tool.js";
import { uniq } from "lodash";
import {uploadFile,pauseUpload,resumeUpload,cancelUpload,getOssOptions} from '@/common/oss/index'
import {
    parseImageListToLocal,
    htmlUnescape,
    formatString,
    getTimestamp,
    pushImageToList,
    getFileIcon,
    transferPatientInfo,
    getResourceTempState
} from "../lib/common_base";
/*
 * chatWindow、index页面
 * 发送文本、发送文件模块代码
 */
export default {
    data() {
        return {
            sendFailTimerObj: {},
            sendResourceByTEAirTask:{}
        };
    },
    computed:{
        supportFileTypeStrings(){
            let fileTypeStrings = ''
            if(this.systemConfig.serverInfo.SupportFileType){
                this.systemConfig.serverInfo.SupportFileType.forEach(item=>{
                    fileTypeStrings =fileTypeStrings +  `.${item},`
                })
            }
            return fileTypeStrings
        },
    },
    methods: {
        sendMessage(quote_message) {
            if(!Tool.checkConversationConnect(this.cid)){
                Toast(this.lang.network_error_tip)
                return
            }
            if (!Tool.checkSpeakPermission(this.cid, this.user.uid)) {
                Toast(this.lang.app_no_speak_permission);
                return;
            }
            //发送消息按钮
            if (!this.isShowEmoji && !this.isIOS) {
                this.$refs.message_text.focus();
            }
            var text = this.messageText;

            text = text.replace(/<div>/g, "\n");
            text = text.replace(/<br>/g, "");
            text = text.replace(/<\/div>/g, "");
            text = text.trim();
            text = text.replace(/&nbsp;/g, " ");
            if (text == "") {
                this.messageText = "";
                this.messageTextOld = this.messageText;
                return;
            }
            text = htmlUnescape(text);
            this.messageText = "";
            this.messageTextOld = this.messageText;

            // 判断是否有引用消息
            if (quote_message) {
                // 使用引用消息发送
                this.sendTextMessage(text, quote_message);
            } else {
                // 正常发送
                this.sendTextMessage(text);
            }

            this.manualSending = true
            this.atUser = []; // 清空@用户列表
            this.shouldScrollBottom(true,0);
            setTimeout(()=>{
                this.manualSending = false
            },300)

        },
        sendTextMessage(text, quote_message) {
            // 原有逻辑 - 发送普通文本消息
            var content_array = this.sliceContent(text, quote_message);
            var that = this;
            var controller = this.conversation.socket;
            controller.emit("send_messages", content_array, function (is_succ, data) {
                setTimeout(()=>{
                    that.sendMessageCallback(is_succ, data,controller.cid);
                },400)

            });
            this.setSendingMessage(content_array);
        },
        setSendingMessage(content_array) {
            //将准备发送消息推送到消息记录
            let that = this;
            var arr = [];
            for (let i = 0; i < content_array.length; i++) {
                let content = Object.assign({}, content_array[i]);
                content.sender_id = this.user.uid; //推回聊天记录时前端匹配头像和昵称用
                content.group_id = this.cid;
                // content.sendingTimer = setTimeout(() => {
                //     content.sending = true;
                // }, 500);
                content.sending=true;
                content.sendFail=false;
                let copy_msg_body = content.msg_body; //保存到本地的Body不转义表情
                content.msg_body = this.parseMessageBody(content.msg_body);
                const attendee = this.conversationList[this.cid]?.attendeeList['attendee_'+content.sender_id]
                content.nickname =  (attendee.alias_name ? attendee.alias_name : attendee.nickname) || this.user.nickname;
                if (!window.main_screen.gateway.check) {
                    content.nickname = this.user.nickname;
                    content.avatar = this.user.avatar;
                    content.avatar_local = this.user.avatar_local;
                }
                that.sendFailTimerObj[content.tmp_gmsg_id] = setTimeout(function () {
                    //60s后没有收到确认则发送失败
                    that.$store.commit("conversationList/setSendFail", {
                        cid: that.cid,
                        tmp_gmsg_id: content.tmp_gmsg_id,
                    });
                }, that.systemConfig.serverInfo.client_send_chat_message_retry_timeout);
                //设置到chatList最后一条消息
                this.$store.commit("chatList/addMessage", content);
                arr.push(content);
            }
            let obj = {
                list: arr,
                cid: this.cid,
                type: "append",
            };
            this.$store.commit("conversationList/setChatMessage", obj);
        },
        sliceContent(content, quote_message = null) {
            //将待发消息体切割成小块
            var start = 0;
            var length = 1024;
            var count = 0;
            var content_array = [];
            while (start < content.length) {
                var sub_content = content.substr(start, length);
                start += length;
                count += 1;
                var timestamp = getTimestamp();
                let mentionList = []
                if(Array.isArray(this.atUser)&&this.atUser.length>0){
                    this.atUser.forEach(item=> {
                        mentionList.push(item.uid)
                    });
                }
                content_array.push({
                    msg_type: this.systemConfig.msg_type.Text,
                    msg_body: sub_content,
                    timestamp: timestamp.time,
                    tmp_gmsg_id: timestamp.timestamp + "_" + count,
                    mentionList,
                    quote_message
                });
            }
            return content_array;
        },

        /*发送文件*/
        async uploadPicture() {
            if (!Tool.checkSpeakPermission(this.cid, this.user.uid)) {
                Toast(this.lang.app_no_speak_permission);
                this.file_tag = new Date().valueOf();
                return;
            }
            await Tool.queryAppPermissions(['CAMERA'])
            if (false == this.systemConfig.serverInfo.enable_file_transfer_function) {
                Toast(this.lang.banned_this_moment);
                return false;
            }
            document.getElementById("upload_picture").click();
        },
        async uploadPictureStart(input_files) {
            let ping = true
            console.error(Tool.checkAppClient('Huawei'))
            if(Tool.checkAppClient('Huawei')){ //华为手机在选择相册的时候 APP会被断网，导致选择完相册回来，socket中断，这里需要等待，不直接ping
                ping = false
            }

            await Tool.handleAfterMainScreenCreated(ping)
            let files = document.getElementById("upload_picture").files;
            if(input_files){
                files = input_files
            }
            this.file_tag = new Date().valueOf();
            let index = 0; //同时发生可能时间戳一样导致UI更新异常
            const limitFileSize = getOssOptions().bigFileSize
            for (let file of files) {
                index++;
                this.hideOperatePanel();
                console.log("uploadPictureStart", file.size, file.type, file.name);
                if (file.size > 20*1024 * 1024 * 1024) {
                    Toast(`${this.lang.upload_max_text}20GB`);
                    return;
                }
                if (file.size == 0) {
                    if (window.browse_type == "MUI_IOS") {
                        window.CWorkstationCommunicationMng.getAppPhotoLibraryAccess();
                    }
                    Toast(`${this.lang.upload_min_text}0M`);
                    this.file_tag = new Date().valueOf();
                    return;
                }
                if(!Tool.validateIllegalCharacters(file.name)){
                    Toast(this.lang.filename_illegal_characters);
                    return;
                }
                if (!this.isSupportFileType(file.name)) {
                    Toast(this.lang.upload_forbidden_file_type_text);
                    return;
                }

                var is_w_db = 0;
                if (/image\/\w+/.test(file.type)) {
                    is_w_db = 1;
                    if (file.size > 20 * 1024 * 1024) {
                        Toast(`${this.lang.upload_image_max_text}20M`);
                        return;
                    }
                } else {
                    if (!window.main_screen.gateway.check) {
                        Toast(this.lang.upload_net_error);
                        return;
                    }
                }
                if((file.size >= limitFileSize)&&(window.vm.$store.state.systemConfig.serverInfo.network_environment===0)){
                    const expireDays = getOssOptions().expireDays
                    let tipsStr = this.lang.big_files_tips.replace("{limitFileSize}", limitFileSize/1024/1024 ).replace("{expireDays}",expireDays)
                    Tool.openMobileDialog({
                        message: tipsStr,
                        showRejectButton:true,
                        confirm:()=>{
                            this.prepareFileMessage(file,index).then(msg => {
                                if(!msg){
                                    return
                                }
                                if(msg.error) { // msg.error为真，表示图片损坏，停止发送
                                    return
                                }
                                this.setSendingFile(msg)
                                this.uploadAttachment(msg, file, is_w_db)
                            })
                        }
                    })
                    console.log('>500M')
                    return
                }else{
                    this.prepareFileMessage(file,index).then(msg => {
                        if(!msg){
                            return
                        }
                        if(msg.error) { // msg.error为真，表示图片损坏，停止发送
                            return
                        }
                        this.setSendingFile(msg)
                        this.uploadAttachment(msg, file, is_w_db)
                    })
                }
            }
        },
        sendResourceByTEAir(fileList, cid) {
            return new Promise((async (resolve,reject)=>{
                this.cid = cid;
                let index = 0; //同时发生可能时间戳一样导致UI更新异常
                this.$set(this, 'sendResourceByTEAirTask', {});
                let uploadFns = []
                for (let file of fileList) {
                    index++;
                    let taskId = Tool.genID()
                    this.$set(this.sendResourceByTEAirTask, taskId, {

                    });
                    this.$set(this.sendResourceByTEAirTask, taskId, {
                        file,
                        isUploaded: false,
                        isError: false,
                    });
                    uploadFns.push(new Promise(async (resolve,reject)=>{
                        try {
                            const msg = await this.prepareFileMessage(file, index)
                            if(!msg){
                                return
                            }
                            if(msg.error){
                                return;
                            }
                            const message = this.setSendingFileByQuickSelect(msg);
                            const progressData = await this.uploadAction(msg, file);
                            console.error('progress',progressData)
                            await this.serviceSendMessage(progressData,message)
                            this.$set(this.sendResourceByTEAirTask[taskId], 'isUploaded', true);
                            resolve(true)
                        } catch (error) {
                            this.$set(this.sendResourceByTEAirTask[taskId], 'isError', true);
                            console.error(error)
                            resolve(false)
                        }

                    }))

                }
                Promise.all(uploadFns).then((result) => {
                    console.log(result)
                    console.log(this.sendResourceByTEAirTask,Object.values(this.sendResourceByTEAirTask).filter(item=>item.isUploaded).length)
                    // this.$set(this, 'sendResourceByTEAirTask', {});
                    resolve(result)

                })

            }))
        },
        compressImage(file) {
            if(file.is_network_resource){
                return file.oss_url
            }
            return new Promise((resolve,reject)=>{
                const reader = new FileReader();

                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        const maxWidth = 300; // 设置最大宽度
                        const maxHeight = 300; // 设置最大高度

                        let width = img.width;
                        let height = img.height;

                        if (width > height) {
                            if (width > maxWidth) {
                                height *= maxWidth / width;
                                width = maxWidth;
                            }
                        } else {
                            if (height > maxHeight) {
                                width *= maxHeight / height;
                                height = maxHeight;
                            }
                        }

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);

                        canvas.toBlob((blob) => {
                            const compressedFile = new File([blob], file.name, { type: file.type });
                            const compressedImageUrl = URL.createObjectURL(compressedFile);
                            resolve(compressedImageUrl)
                        }, file.type);
                    };
                    img.onerror = (err) => {
                        console.log("图片加载错误：", err);
                        reject(err);
                    };

                    img.src = e.target.result;

                };

                reader.readAsDataURL(file);
            })

        },

        prepareFileMessage: async function (file, index) {
            if(!Tool.checkMainScreenConnected()&&!file.is_network_resource){
                Toast(this.lang.network_error_tip);
                return false
            }
            var msg = {
                file_id: this.cid + "-" + this.user.uid + "-" + new Date().getTime() + "_" + index,
                group_id: this.cid,
                resource_file_size:file.size,
                is_network_resource:file.is_network_resource||false,
                network_resource: file.is_network_resource? file : ''
            };

            let file_type=Tool.getFileType(file.name);
            let URL = window.URL || window.webkitURL;
            const limitFileSize = getOssOptions().bigFileSize
            const isBigFile = file.size >= limitFileSize;
            if(isBigFile){
                //文件
                msg.msg_type = this.systemConfig.msg_type.File;
                msg.file_name=file.name
            }else{
                if (/image\/\w+/.test(file.type)) {
                    //图片
                    try {
                        const url = await this.compressImage(file)
                        msg.msg_type = this.systemConfig.msg_type.Image;
                        msg.url = url;
                        msg.url_local = url;
                        msg.file_name = file.name;
                        if(file.ai_image_info){
                            msg.ai_image_info = file.ai_image_info
                        }
                    } catch (error) {
                        // 图片损坏，加载失败
                        msg.error = true;
                        URL.revokeObjectURL(msg.url);
                        Toast(this.lang.image_corruption_text);
                    }
                } else if(this.isVideoFile(file.type)){
                    //视频
                    msg.url = getFileIcon(file.name);
                    msg.url_local = msg.url;
                    msg.msg_type = this.systemConfig.msg_type.Video;
                    msg.file_name = "video." + file_type;


                }else{
                //文件
                    msg.msg_type = this.systemConfig.msg_type.File;
                    msg.file_name = "file." + file_type;

                }
            }
            msg.file_name = Tool.encodeSpecialChars(msg.file_name)
            return msg;
        },
        uploadAction(msg, file) {
            var that = this;
            return new Promise(async(resolve, reject) => {
                //OSS
                /////上传到OSS的视频文件，由于截图的功能问题，不保持原有文件名
                var attachment = false;
                if (this.systemConfig.msg_type.Video == msg.msg_type) {
                    msg.original_file_name = msg.file_name;
                    msg.file_name = "video." + msg.file_name.substring(msg.file_name.lastIndexOf(".") + 1);
                    msg.thumb = "_thumb.jpg";
                    msg.poster = "_poster.jpg";
                } else if (this.systemConfig.msg_type.Image == msg.msg_type) {
                    msg.original_file_name = msg.file_name;
                    // msg.file_name = "image." + msg.file_name.substring(msg.file_name.lastIndexOf(".") + 1);
                    msg.thumb = "_thumb.jpg";
                } else {
                    attachment = true;
                }
                let msg_body = {
                    file_id: msg.file_id,
                    file_name: msg.file_name,
                    original_file_name: msg.original_file_name,
                    attachment_storage_type: that.systemConfig.serverInfo.attachment_storage_type,
                    thumb: msg.thumb,
                };
                if (this.systemConfig.msg_type.Video == msg.msg_type) {
                    msg_body["poster"] = msg.poster;
                }
                msg.msg_body = JSON.stringify(msg_body);
                try {
                    const bucket = that.systemConfig.serverInfo.oss_attachment_server.bucket
                    const filePath = that.systemConfig.serverInfo.oss_attachment_server.sub_dir +
                        "/" +
                        that.cid +
                        "/" +
                        that.user.uid +
                        "/" +
                        msg.file_id +
                        "/" +
                        msg.file_name
                    uploadFile({
                        bucket,
                        filePath,
                        file,
                        timeout:10000,
                        callback:(event, data)=>{
                            var progressData = {
                                file_id: msg.file_id,
                                cid: msg.group_id,
                            };
                            if ("progress" == event) {
                                progressData.progress = data - 1;
                            } else if ("complete" == event) {
                                progressData.msg_body = msg.msg_body;
                                progressData.msg_type = msg.msg_type;
                                progressData.progress = 100;
                                resolve(progressData);
                            } else if ("error" == event) {
                                progressData.error = true;
                                reject(progressData);
                                //上传失败文件记录到本地缓存
                            }
                        }
                    })
                } catch (error) {
                    reject(error)
                }
            });
        },
        handleUploadCallback(event, data,uploadId,msg){
            console.log("uploadFile", event, data,uploadId);
            if(event === 'getOssClient'){
                console.error('client',data)
            }
            if ("progress" == event) {
                const params = {
                    msg,
                    percent:data,
                    uploadId:uploadId,
                    pauseUpload:false,
                    uploadError:false
                }
                this.$store.commit('conversationList/updateFileProgress',params)

            } else if ("complete" == event) {
                const params = {
                    ...msg,
                    percent:100,
                    progress:100,
                    cid:msg.group_id,
                    pauseUpload:false,
                    uploadError:false
                }
                this.updateUploadProgress(params)
            } else if ("error" == event) {
                const params = {
                    msg,
                    uploadId:uploadId,
                    uploadError:true,
                    pauseUpload:true,
                }
                if(data.name ==='pause'){
                    params.uploadError = false
                }
                this.$store.commit('conversationList/updateFileProgress',params)
                if(data.name=='tokenError'){
                    this.$store.commit("conversationList/deleteUploadChatMessage", {
                        cid: msg.group_id,
                        file_id:msg.file_id
                    });
                }
            }

            // this.$root.eventBus.$emit("updateProgressOSS", progressData);
        },
        async uploadAttachment(msg, file, is_w_db,) {
            console.log('uploadAttachment')
            var that = this;
            //OSS
            /////上传到OSS的视频文件，由于截图的功能问题，不保持原有文件名
            var attachment = false;
            if (this.systemConfig.msg_type.Video == msg.msg_type) {
                msg.original_file_name = msg.file_name;
                msg.file_name = "video." + msg.file_name.substring(msg.file_name.lastIndexOf(".") + 1);
                msg.thumb = "_thumb.jpg";
                msg.poster = "_poster.jpg";
            } else if (this.systemConfig.msg_type.Image == msg.msg_type) {
                msg.original_file_name = msg.file_name;
                // msg.file_name = "image." + msg.file_name.substring(msg.file_name.lastIndexOf(".") + 1);
                msg.thumb = "_thumb.jpg";
            } else {
                attachment = true;
            }
            let msg_body = {
                file_id: msg.file_id,
                file_name: msg.file_name,
                original_file_name: msg.original_file_name,
                attachment_storage_type: that.systemConfig.serverInfo.attachment_storage_type,
                thumb: msg.thumb,
            };
            if (this.systemConfig.msg_type.Video == msg.msg_type) {
                msg_body["poster"] = msg.poster;
            }
            msg.msg_body = JSON.stringify(msg_body);
            try {
                const bucket = that.systemConfig.serverInfo.oss_attachment_server.bucket
                let filePath = that.systemConfig.serverInfo.oss_attachment_server.sub_dir +
                        "/" +
                        that.cid +
                        "/" +
                        that.user.uid +
                        "/" +
                        msg.file_id +
                        "/" +
                        msg.file_name
                uploadFile({
                    bucket,
                    filePath,
                    file,
                    copyOssPath:file.copy_oss_path||'',
                    callback: (event, data, uploadId) => {
                        this.handleUploadCallback(event, data,uploadId,msg)
                    }
                })

            } catch (error) {
                console.error(error)
            }
        },
        isVideoFile(type) {
            type = type.substring(type.lastIndexOf("/") + 1).toLowerCase();
            var ret = false;
            for (let key of this.systemConfig.VideoType) {
                if (key == type) {
                    ret = true;
                    break;
                }
            }
            return ret;
        },
        isSupportFileType(name) {
            const fileType = Tool.getFileType(name)
            const supportFileType = this.systemConfig.serverInfo.SupportFileType
            return supportFileType.includes(fileType)
        },
        setSendingFileByQuickSelect(msg){
            msg.sender_id = this.user.uid; //推回聊天记录时前端匹配头像和昵称用
            msg.group_id = this.cid;
            return msg
        },
        setSendingFile(msg, doNotScroll) {
            let that = this;
            msg.sender_id = this.user.uid; //推回聊天记录时前端匹配头像和昵称用
            msg.group_id = this.cid;
            msg.sending = false;
            msg.sendFail = false;
            msg.uploading = true;
            msg.uploadFail = false;
            msg.percent = 0;
            msg.url_local = msg.url_local ? msg.url_local : msg.url;
            msg.tmp_gmsg_id = getTimestamp().timestamp+Math.floor(Math.random() * 100000);
            msg.timestamp = getTimestamp().time;
            const attendee = this.conversationList[this.cid]?.attendeeList['attendee_'+this.user.uid]
            msg.nickname =  (attendee.alias_name ? attendee.alias_name : attendee.nickname) || this.user.nickname;
            //设置到chatList最后一条消息
            this.$store.commit("chatList/addMessage", {...msg});
            let obj = {
                list: [msg],
                cid: this.cid,
                type: "append",
            };
            this.$store.commit("conversationList/setChatMessage", obj);
            this.manualSending = true
            if(!doNotScroll){
                this.shouldScrollBottom(true,0)
            }
            setTimeout(()=>{
                this.manualSending = false
            },300)

            return msg
        },
        updateSendingMessage(cid, file_id) {
            //上传完文件更新为发送消息中
            let chatMessageList = [];
            chatMessageList = this.$store.state.conversationList[cid].chatMessageList;
            let result = null;
            // console.log('updateSendingMessage',chatMessageList,cid,file_id)
            for (var i = chatMessageList.length - 1; i >= 0; i--) {
                //从聊天记录尾部遍历，提高效率
                let message = chatMessageList[i];
                if (message.file_id == file_id) {
                    this.$store.commit("conversationList/setMessageSending", {
                        cid: cid,
                        index: i,
                    });
                    result = message;
                    break;
                }
            }
            return result;
        },
        sendMessageCallback(is_succ, data,cid) {
            var that = this;
            //发送消息成功回调
            if (is_succ) {
                for (let message of data) {
                    console.log("sendMessageCallback", message);
                    parseImageListToLocal([message], "url");
                    let local_text = message.msg_body;
                    message.msg_body = this.parseMessageBody(message.msg_body);
                    message.patientInfo = transferPatientInfo(message);
                    if (message.sender_id != this.user.uid) {
                        //发送者改变了，用于AI分析上传图片的流程
                        message.avatar =
                            this.conversationList[message.group_id].attendeeList[
                                "attendee_" + message.sender_id
                            ].avatar;
                        message.avatar_local =
                            this.conversationList[message.group_id].attendeeList[
                                "attendee_" + message.sender_id
                            ].avatar_local;
                        message.nickname =
                            this.conversationList[message.group_id].attendeeList[
                                "attendee_" + message.sender_id
                            ].nickname;
                    }
                    let tmp_gmsg_id = message.tmp_gmsg_id;
                    if (
                        message.msg_type == this.systemConfig.msg_type.Image ||
                            message.msg_type == this.systemConfig.msg_type.Frame ||
                            message.msg_type == this.systemConfig.msg_type.OBAI  ||
                            message.msg_type == this.systemConfig.msg_type.Sound ||
                            message.msg_type == this.systemConfig.msg_type.Video ||
                            message.msg_type == this.systemConfig.msg_type.Cine
                    ) {
                        tmp_gmsg_id = message.file_id;
                    } else if (message.msg_type == this.systemConfig.msg_type.AI_ANALYZE) {
                        tmp_gmsg_id = message.file_id;
                        parseImageListToLocal(message.ai_analyze.messages, "url");
                        let ignoreConsultationImages = false;
                        let cid = message.group_id;
                        if (
                            this.systemConfig.ServiceConfig.type.AiAnalyze ==
                                this.conversationList[cid].service_type
                        ) {
                            //AI分析图片不放入总图像列表里
                            //放入
                            //ignoreConsultationImages = true;
                        }
                        for (let item of message.ai_analyze.messages) {
                            // item.url_local=this.getLocalImgUrl(item.url||'');
                            let obj = Object.assign({}, item, true);
                            pushImageToList(obj, ignoreConsultationImages);
                        }
                    } else {
                    }
                    clearTimeout(that.sendFailTimerObj[message.tmp_gmsg_id]);
                    that.$store.commit("conversationList/sendAck", {
                        cid: cid,
                        message: message,
                    });
                    if (
                        message.msg_type == that.systemConfig.msg_type.Image ||
                            message.msg_type == that.systemConfig.msg_type.OBAI ||
                            message.msg_type == that.systemConfig.msg_type.Frame
                    ) {
                    } else if (
                        message.msg_type == that.systemConfig.msg_type.Sound ||
                            message.msg_type == that.systemConfig.msg_type.Video ||
                            message.msg_type == that.systemConfig.msg_type.Cine
                    ) {
                    } else {
                    }
                    that.$root.eventBus.$emit("updateExamImageListIfNeed", message);
                    pushImageToList(message);
                    this.$nextTick(()=>{
                        this.shouldScrollBottom&&this.shouldScrollBottom(false,0)
                    })
                }
            } else {
                setTimeout(()=>{
                    for(let message of data){
                        that.$store.commit('conversationList/setSendFail',{cid:message.group_id,tmp_gmsg_id:message.tmp_gmsg_id})
                    }
                },600)
                Toast(this.lang.send_message_error);
            }
        },
        handleUploadStatus(message){
            console.error(message.uploading,message.uploadId,message)
            if(!message.uploading){
                return
            }
            if(message.pauseUpload){
                this.handleReUpload(message)
            }else{
                this.handlePauseUpload(message.uploadId)
            }
        },
        handleCancelUpload(message){
            if(!message.uploading){
                return
            }
            if(message.uploadId){
                cancelUpload(message.uploadId)
            }
            this.$store.commit("conversationList/deleteUploadChatMessage", {
                cid: this.cid,
                file_id:message.file_id
            });
        },
        handleReUpload(message){
            const uploadId = message.uploadId
            console.log('handleReUpload')
            if(!Tool.checkConversationConnect(this.cid)){
                Toast(this.lang.network_error_tip)
                return
            }
            let msg = null
            for(let i = this.chatMessageList.length - 1; i >= 0; i--){
                if(this.chatMessageList[i].uploadId == uploadId){
                    msg = this.chatMessageList[i]
                }
            }
            if(uploadId){
                resumeUpload({uploadId})
            }else{
                //此时还未上传
                if(message.is_network_resource){
                    this.uploadAttachment(message, message.network_resource, false)
                }
            }
            // resumeUpload({uploadId})
        },
        handlePauseUpload(uploadId){
            pauseUpload(uploadId)
            console.log('handlePauseUpload')
        },
        resend(index) {
            //点击重发消息
            let message = this.chatMessageList[index];
            clearTimeout(this.sendFailTimerObj[message.tmp_gmsg_id]);
            this.$store.commit("conversationList/deleteChatMessage", {
                cid: this.cid,
                index: index,
            });

            var that = this;
            var controller = this.conversation.socket;
            controller.emit("send_messages", [message], function (is_succ, data) {
                that.sendMessageCallback(is_succ, data,controller.cid);
            });
            this.setSendingMessage([message]);
        },
        sendTransmitMessage(cid) {
            let that = this;
            let conversation = this.conversationList[cid];
            let queue = this.$root.transmitQueue[cid];
            let isToAI = conversation.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze;
            let hasVideo = false;
            var arr = [];
            let ifAiMsg = false;
            for (let msg of queue) {
                if (msg.msg_type == this.systemConfig.msg_type.AI_ANALYZE) {
                    arr.push({
                        type: this.systemConfig.msg_type.AI_ANALYZE,
                        id: msg.ai_analyze.ai_analyze_id,
                    });
                    ifAiMsg = true;
                } else if (msg.msg_type == this.systemConfig.msg_type.EXAM_IMAGES) {
                    let temp = [];
                    let effResourceList = msg.resourceList.filter(item=>{
                        if(msg.imageList){
                            return msg.imageList.some(item2=>{
                                if(item2.resource_id==item.id){
                                    if(getResourceTempState(item2.resource_id) === 1){
                                        return true
                                    }

                                }
                            })
                        }else{
                            if(getResourceTempState(item.id) === 1){
                                return true
                            }
                        }
                    })
                    for (let resource of effResourceList) {
                        if (isToAI && resource.msg_type == this.systemConfig.msg_type.Cine) {
                            //忽略发送给AI的视频
                            hasVideo = true;
                            continue;
                        } else {
                            temp.unshift(resource.id);
                        }
                    }
                    arr = arr.concat(temp);
                } else if (
                    msg.msg_type == this.systemConfig.msg_type.Video ||
                    msg.msg_type == this.systemConfig.msg_type.Cine ||
                    msg.msg_type == this.systemConfig.msg_type.RealTimeVideoReview ||
                    msg.msg_type == this.systemConfig.msg_type.VIDEO_CLIP
                ) {
                    if (isToAI) {
                        //忽略发送给AI的视频
                        hasVideo = true;
                        continue;
                    } else {
                        arr.push(msg.resource_id);
                    }
                } else {
                    arr.push(msg.resource_id);
                }
            }
            arr = uniq(arr);
            delete this.$root.transmitQueue[cid];
            if (hasVideo) {
                Toast(this.lang.can_not_analyze_video);
                return;
            }
            if (arr.length == 0) {
                Toast(this.lang.transmit_fail_tip);
                return;
            }
            let share_group = 0;
            if (!ifAiMsg && isToAI) {
                //图像处理
                for (let item of queue) {
                    share_group = item.group_id;
                }
                this.analyzeAction(cid, arr, share_group);
            } else {
                //转发消息
                var controller = conversation.socket;
                controller.emit("sendto", arr, function (is_succ, data) {
                    if (is_succ) {
                    } else {
                        Toast(that.lang.transmit_fail_tip);
                    }
                });
                Toast(this.lang.transmiting_tip);
            }
        },
        initMachineTransfer(json_str) {
            let image = Object.assign({}, this.$root.transferExamCidObj[json_str.ImgId]);
            this.$root.transferExamCidObj[json_str.ImgId].exam_id = json_str.ExamId;
            window.vm.$store.state.systemConfig.serverInfo.consultation_file_storage_type =
                json_str.ConsultationFileStorageType;
            image.exam_id = json_str.ExamId;
            let cid = image.cid;
            let uid = this.$store.state.user.uid;
            let msg = {
                file_id: image.file_id,
                img_id: json_str.ImgId,
                exam_id: json_str.ExamId,
                group_id: cid,
                url: "data:image/png;base64," + json_str.Src,
                url_local: "data:image/png;base64," + json_str.Src,
                msg_type: json_str.ImgType == 0 ? 3 : 4,
                sender_id: uid,
                patient_name: json_str.PatientName,
                patient_sex: json_str.PatientGender,
                patient_age: json_str.PatientAge,
                patient_age_unit: json_str.PatientAgeUnit,
                sending: false,
                sendFail: false,
                uploading: true,
                uploadFail: false,
                percent: 0,
                tmp_gmsg_id: getTimestamp().timestamp,
                timestamp: getTimestamp().timestamp,
                photometricInterpretation: image.photometricInterpretation,
            };
            console.log("initMachineTransfer json_str%%%%%%%%%%%%%%%%%%", msg);
            //设置到chatList最后一条消息
            window.vm.$store.commit("chatList/addMessage", msg);
            let obj = {
                list: [msg],
                cid: cid,
                type: "append",
            };
            window.vm.$store.commit("conversationList/setChatMessage", obj);
        },
        updateMachineTransfer(json_str) {
            var image = this.$root.transferExamCidObj[json_str.ImgId];
            json_str.cid = image.cid;
            json_str.file_id = image.file_id;
            this.$store.commit("conversationList/updateProgressByImgId", json_str);
            console.log("UpdateProgress json_str%%%%%%%%%%%%%%%%%%", json_str);
        },
        finishMachineTransfer(json_str) {
            var image = Object.assign({}, this.$root.transferExamCidObj[json_str.ImgId]);
            json_str.cid = image.cid;
            json_str.file_id = image.file_id;
            image.progress = 100;
            image.img_id = json_str.ImgId;
            this.$store.commit("conversationList/updateProgressByImgId", json_str);
            console.log("finishMachineTransfer image%%%%%%%%%%%%%%%%%%", image);
            this.updateUploadProgress(image);
            //文件上传完成后删除队列
            delete this.$root.transferExamCidObj[json_str.ImgId];
        },
        dataURLtoFile(dataurl, filename) {
            var arr = dataurl.split(",");
            var mime = arr[0].match(/:(.*?);/)[1];
            var bstr = window.atob(arr[1]);
            var n = bstr.length;
            var u8arr = new Uint8Array(n);
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n);
            }
            //return new Blob([u8arr], {type:mime});
            return new File([u8arr], filename, { type: mime });
        },
        dropOrientation(file) {
            var that = this;
            return new Promise(function (resolve) {
                if (!document.createElement("canvas").getContext) {
                    console.log("浏览器不支持canvas");
                    resolve(file);
                }
                if (!/image\/\w+/.test(file.type)) {
                    resolve(file);
                }
                var content = {};
                window.URL = window.URL || window.webkitURL;
                content.img = new Image();
                content.blob = window.URL.createObjectURL(file);
                content.canvas = document.createElement("canvas");
                content.img.onload = function () {
                    that._getNewFile(file, content).then(function (newFile) {
                        resolve(newFile);
                    });
                };
                content.img.src = content.blob;
            });
        },
        _getNewFile(file, content) {
            var that = this;
            return new Promise(function (resolve) {
                try {
                    window.EXIF.getData(file, function () {
                        content.orientation = window.EXIF.getTag(this, "Orientation");
                        if (!content.orientation) {
                            resolve(file);
                        }
                        content.resize = that._getResize(content);
                        content.ctx = content.canvas.getContext("2d");

                        content.canvas.width = content.resize.width;
                        content.canvas.height = content.resize.height;

                        // 设置为白色背景，jpg是不支持透明的，所以会被默认为canvas默认的黑色背景。
                        content.ctx.fillStyle = "#fff";
                        content.ctx.fillRect(0, 0, content.canvas.width, content.canvas.height);

                        var resize = content.resize,
                            img = content.img,
                            canvas = content.canvas,
                            ctx = content.ctx,
                            orientation = content.orientation;
                        // 调整为正确方向
                        switch (orientation) {
                        case 3:
                            ctx.rotate((180 * Math.PI) / 180);
                            ctx.drawImage(img, -resize.width, -resize.height, resize.width, resize.height);
                            break;
                        case 6:
                            ctx.rotate((90 * Math.PI) / 180);
                            ctx.drawImage(img, 0, -resize.width, resize.height, resize.width);
                            break;
                        case 8:
                            ctx.rotate((270 * Math.PI) / 180);
                            ctx.drawImage(img, -resize.height, 0, resize.height, resize.width);
                            break;

                        case 2:
                            ctx.translate(resize.width, 0);
                            ctx.scale(-1, 1);
                            ctx.drawImage(img, 0, 0, resize.width, resize.height);
                            break;
                        case 4:
                            ctx.translate(resize.width, 0);
                            ctx.scale(-1, 1);
                            ctx.rotate((180 * Math.PI) / 180);
                            ctx.drawImage(img, -resize.width, -resize.height, resize.width, resize.height);
                            break;
                        case 5:
                            ctx.translate(resize.width, 0);
                            ctx.scale(-1, 1);
                            ctx.rotate((90 * Math.PI) / 180);
                            ctx.drawImage(img, 0, -resize.width, resize.height, resize.width);
                            break;
                        case 7:
                            ctx.translate(resize.width, 0);
                            ctx.scale(-1, 1);
                            ctx.rotate((270 * Math.PI) / 180);
                            ctx.drawImage(img, -resize.height, 0, resize.height, resize.width);
                            break;

                        default:
                            ctx.drawImage(img, 0, 0, resize.width, resize.height);
                        }
                        resolve(
                            that.dataURLtoFile(
                                canvas.toDataURL("image/jpeg", 1),
                                "dorpOrientationImage-" + new Date().valueOf() + ".jpg"
                            )
                        );
                    });
                } catch (err) {
                    // 这样能解决低内存设备闪退的问题吗？
                    throw new Error(err);
                }
            });
        },
        _getResize(content) {
            var img = content.img,
                orientation = content.orientation;
            var ret = {
                width: img.width,
                height: img.height,
            };
            if ("5678".indexOf(orientation) > -1) {
                ret.width = img.height;
                ret.height = img.width;
            }
            // 超过这个值base64无法生成，在IOS上
            while (ret.width >= 3264 || ret.height >= 2448) {
                ret.width *= 0.8;
                ret.height *= 0.8;
            }
            return ret;
        },
        _saveFileToLocal(msg, file, is_w_db) {
            var that = this;
            var fileName = file.name;
            var filePath =
                "_documents/Ruitong/" + this.systemConfig.server_type.host + "/" + this.user.uid + "/images/Upload";
            var newFile = filePath + "/" + fileName;

            var reader = new FileReader();
            if (is_w_db && is_w_db == 1) {
                // console.error('****************send_message-_saveFileToLocal',this.osName)
                if (true) {
                    reader.onload = function () {
                        var reader_t = this;
                        var base64Data = reader_t.result.replace(/^data:image\/\w+;base64,/, "");
                        console.error("保存kaishi");
                        try {
                            Tool.createCWorkstationCommunicationMng({
                                name: "convertLocalFileSystemURL",
                                emitName: "NotifyConvertedLocalFileSystemURL",
                                params: { url: newFile },
                                timeout: 1500,
                            }).then((res) => {
                                if (res.error_code == "0") {
                                    let abs_url = res.url;
                                    console.log(JSON.stringify(res));
                                    console.log(JSON.stringify(abs_url));
                                    try {
                                        Tool.createCWorkstationCommunicationMng({
                                            name: "saveFileToLocalWithBase64",
                                            emitName: "NotifySaveFileToLocalWithBase64",
                                            params: { base64_data: base64Data, url: abs_url, overwrite: "true" },
                                            timeout: null,
                                        }).then((resp) => {
                                            if (resp.error_code == "1") {
                                                console.error("保存失败", { url: abs_url, overwrite: true });
                                                console.log("保存失败");
                                            } else {
                                                console.log("创建成功", { url: abs_url, overwrite: true });
                                            }
                                        });
                                    } catch (error) {
                                        Toast(error);
                                    }
                                } else {
                                    console.error("convertLocalFileSystemURL fail!");
                                }
                            });
                        } catch (error) {
                            Toast(error);
                        }
                    };
                    reader.readAsDataURL(file);
                }
            }
        },
        transferBodyIfAnalyze(tempMsg) {
            if (
                this.functionsStatus.breastAI&&this.$store.state.conversationList[tempMsg.group_id].service_type ==
                    this.systemConfig.ServiceConfig.type.AiAnalyze &&
                tempMsg.msg_type == this.systemConfig.msg_type.Image &&
                (
                    !tempMsg.ai_image_info||(tempMsg.ai_image_info&&tempMsg.ai_image_info&&tempMsg.report)
                )
            ) {
                //对AI分析发送图片时，转化为AI分析消息
                Toast(this.lang.auto_analyze);
                tempMsg.sender_id = this.$store.state.conversationList[tempMsg.group_id].fid;
                tempMsg.msg_type = this.systemConfig.msg_type.AI_ANALYZE;
                tempMsg.messages = [];
                tempMsg.messages.push({
                    msg_type: this.systemConfig.msg_type.Image,
                    msg_body: tempMsg.msg_body,
                });
            }
        },
        setStartVoiceMsg() {
            this.setVoiceMsgCommon(this.systemConfig.msg_type.SYS_START_RT_VOICE);
        },
        setStopVoiceMsg() {
            this.setVoiceMsgCommon(this.systemConfig.msg_type.SYS_STOP_RT_VOICE);
        },
        updateStartVoiceMsg() {
            this.$store.commit("conversationList/updateStartVoiceMsg", { cid: this.cid });
        },
        deleteStartVoiceMsg() {
            this.$store.commit("conversationList/deleteStartVoiceMsg", { cid: this.cid });
        },
        setVoiceMsgCommon(type) {
            var timestamp = getTimestamp();
            let sending = type == this.systemConfig.msg_type.SYS_START_RT_VOICE;
            let msg = {
                msg_type: type,
                msg_body: "",
                timestamp: timestamp.time,
                tmp_gmsg_id: timestamp.timestamp,
                sender_id: this.user.uid,
                sending: sending,
                group_id: this.cid,
            };
            const attendee = this.conversationList[this.cid]?.attendeeList['attendee_'+this.user.uid]
            msg.nickname =  (attendee.alias_name ? attendee.alias_name : attendee.nickname) || this.user.nickname;
            let obj = {
                list: [msg],
                cid: this.cid,
                type: "append",
            };
            this.$store.commit("conversationList/setChatMessage", obj);
            this.$store.commit("chatList/addMessage", msg);
        },
        uploadConsultationFile(option, cb) {
            //OSS
            uploadFile({
                bucket:this.systemConfig.serverInfo.oss_consultation_file_storage_server.bucket,
                filePath:option.filePath,
                file:option.file,
                callback:(event, data)=>{
                    console.log("uploadFile", event, data);
                    if ("progress" == event) {
                        cb &&
                            cb(null, {
                                type: "progress",
                                percent: data > 99 ? 99 : data,
                            });
                    } else if ("complete" == event) {
                        cb &&
                            cb(null, {
                                type: "progress",
                                percent: 100,
                            });
                    } else if ("error" == event) {
                        cb &&
                            cb(null, {
                                type: "progress",
                                percent: -1,
                            });
                    }
                }
            })
        },
        serviceSendMessage(data,message){
            var that = this;
            return new Promise((resolve, reject) => {
                try {
                    let cid = data.cid;
                    let controller = this.$store.state.conversationList[cid].socket;
                    console.log("before prepare step 1%%%%%%%%%%%%%%%%%%", message, data);
                    data.msg_type = data.msg_type || message.msg_type;
                    data.msg_body = data.msg_body || message.msg_body;
                    if (data.msg_type == this.systemConfig.msg_type.File) {
                        //其他文件类型直接发送消息
                        message.msg_type = data.msg_type;
                        message.msg_body = data.msg_body;
                        controller.emit("send_messages", [message], function (is_succ, data) {
                            // data.cid = controller.cid;
                            // that.sendMessageCallback(is_succ, data);
                        });

                        message.timeout = setTimeout(function () {
                            reject('send_messages1 time out')
                        }, that.systemConfig.serverInfo.client_send_chat_message_retry_timeout);
                    } else {
                        let request_option = {};
                        let target = "";
                        //深拷贝消息体，不向服务器发送Url
                        let tempMsg = Object.assign({}, message);
                        tempMsg.nickname = "";
                        console.log("before prepare step 2%%%%%%%%%%%%%%%%%%", tempMsg);
                        if (
                            data.msg_type == this.systemConfig.msg_type.Frame ||
                            data.msg_type == this.systemConfig.msg_type.OBAI  ||
                                data.msg_type == this.systemConfig.msg_type.Cine
                        ) {
                            request_option = {
                                img_id: data.img_id,
                                exam_id: data.exam_id,
                                msg_type: data.msg_type,
                                consultation_file_storage_type:
                                        window.vm.$store.state.systemConfig.serverInfo.consultation_file_storage_type,
                            };
                            target = "request_generate_consultation_file_thumbnail";
                            tempMsg.msg_body = JSON.stringify({
                                img_id: tempMsg.img_id,
                                exam_id: tempMsg.exam_id,
                                timestamp: tempMsg.timestamp,
                                patient_name: tempMsg.patient_name,
                                patient_sex: tempMsg.patient_sex,
                                patient_age: tempMsg.patient_age,
                                patient_age_unit: tempMsg.patient_age_unit,
                                photometricInterpretation: tempMsg.photometricInterpretation,
                            });
                            tempMsg.url = "";
                            tempMsg.url_local = "";
                        } else if (
                            data.msg_type == this.systemConfig.msg_type.Video ||
                                data.msg_type == this.systemConfig.msg_type.Image
                        ) {
                            request_option = {
                                type: data.msg_type,
                                body: JSON.parse(data.msg_body),
                            };
                            target = "request_generate_thumbnail";
                            tempMsg.msg_body = data.msg_body;
                            tempMsg.url = "";
                            tempMsg.url_local = "";
                        }
                        //图片或视频生成缩略图
                        controller.emit(target, request_option, function (is_succ) {
                            //图片上传完发送消息
                            if (is_succ) {
                                clearTimeout(message.timeout);
                                message.timeout = null
                                console.log("say message body%%%%%%%%%%%%%", tempMsg);
                                that.transferBodyIfAnalyze(tempMsg);
                                controller.emit("send_messages", [tempMsg], function (is_succ, data) {
                                    if (is_succ) {
                                        clearTimeout(message.timeout);
                                        message.timeout = null
                                        resolve(is_succ)
                                    }
                                });
                                message.timeout = setTimeout(function () {
                                    //60s后没有收到确认则发送失败
                                    reject('send_messages2 time out')
                                }, that.systemConfig.serverInfo.client_send_chat_message_retry_timeout);

                            } else {
                                //截图失败
                                Toast("request_generate_thumbnail error");
                                reject(data)
                            }
                        });
                        message.timeout = setTimeout(function () {
                            //60s后没有收到确认则发送失败
                            reject('request_generate_thumbnail time out')
                        }, 10000);

                    }
                } catch (error) {
                    console.error(error)
                    reject(false)
                }

            });
        },
        updateUploadProgress(data) {
            var that = this;
            return new Promise((resolve, reject) => {
                try {
                    let cid = data.cid;
                    if (data.progress == 100) {
                        let controller = this.$store.state.conversationList[cid].socket;
                        let message = that.updateSendingMessage(cid, data.file_id);
                        if (!message) {
                            //上传过程中取消上传，找不到message
                            return;
                        }
                        console.log("before prepare step 1%%%%%%%%%%%%%%%%%%", message, data);
                        data.msg_type = data.msg_type || message.msg_type;
                        data.msg_body = data.msg_body || message.msg_body;
                        if (data.msg_type == this.systemConfig.msg_type.File) {
                            //其他文件类型直接发送消息
                            message.msg_type = data.msg_type;
                            message.msg_body = data.msg_body;
                            controller.emit("send_messages", [message], function (is_succ, data) {
                                that.sendMessageCallback(is_succ, data,controller.cid);
                            });

                            message.timeout = setTimeout(function () {
                                //60s后没有收到确认则发送失败
                                that.$store.commit("conversationList/setSendFail", {
                                    cid: cid,
                                    tmp_gmsg_id: message.tmp_gmsg_id,
                                });
                                reject('send_messages1 time out')
                            }, that.systemConfig.serverInfo.client_send_chat_message_retry_timeout);
                        } else {
                            let request_option = {};
                            let target = "";
                            //深拷贝消息体，不向服务器发送Url
                            let tempMsg = Object.assign({}, message);
                            tempMsg.nickname = "";
                            console.log("before prepare step 2%%%%%%%%%%%%%%%%%%", tempMsg);
                            if (
                                data.msg_type == this.systemConfig.msg_type.Frame ||
                                data.msg_type == this.systemConfig.msg_type.OBAI ||
                                data.msg_type == this.systemConfig.msg_type.Cine
                            ) {
                                request_option = {
                                    img_id: data.img_id,
                                    exam_id: data.exam_id,
                                    msg_type: data.msg_type,
                                    consultation_file_storage_type:
                                        window.vm.$store.state.systemConfig.serverInfo.consultation_file_storage_type,
                                };
                                target = "request_generate_consultation_file_thumbnail";
                                tempMsg.msg_body = JSON.stringify({
                                    img_id: tempMsg.img_id,
                                    exam_id: tempMsg.exam_id,
                                    timestamp: tempMsg.timestamp,
                                    patient_name: tempMsg.patient_name,
                                    patient_sex: tempMsg.patient_sex,
                                    patient_age: tempMsg.patient_age,
                                    patient_age_unit: tempMsg.patient_age_unit,
                                    photometricInterpretation: tempMsg.photometricInterpretation,
                                });
                                tempMsg.url = "";
                                tempMsg.url_local = "";
                            } else if (
                                data.msg_type == this.systemConfig.msg_type.Video ||
                                data.msg_type == this.systemConfig.msg_type.Image
                            ) {
                                request_option = {
                                    type: data.msg_type,
                                    body: JSON.parse(data.msg_body),
                                };
                                target = "request_generate_thumbnail";
                                tempMsg.msg_body = data.msg_body;
                                tempMsg.url = "";
                                tempMsg.url_local = "";
                            }
                            //图片或视频生成缩略图
                            controller.emit(target, request_option, function (is_succ) {
                                //图片上传完发送消息
                                if (is_succ) {
                                    clearTimeout(message.timeout);
                                    message.timeout = null
                                    console.log("say message body%%%%%%%%%%%%%", tempMsg);
                                    that.transferBodyIfAnalyze(tempMsg);
                                    controller.emit("send_messages", [tempMsg], function (is_succ, data) {

                                        if (is_succ) {
                                            clearTimeout(message.timeout);
                                            message.timeout = null
                                            resolve(is_succ)
                                        }
                                        that.sendMessageCallback(is_succ, data,controller.cid);
                                    });
                                    message.timeout = setTimeout(function () {
                                        //60s后没有收到确认则发送失败
                                        reject('send_messages2 time out')
                                    }, that.systemConfig.serverInfo.client_send_chat_message_retry_timeout);

                                } else {
                                    //截图失败
                                    Toast("request_generate_thumbnail error");
                                    reject(data)
                                }
                            });
                            message.timeout = setTimeout(function () {
                                //60s后没有收到确认则发送失败
                                reject('request_generate_thumbnail time out')
                            }, 10000);
                        }
                    } else if (data.progress == -1) {
                        //上传失败
                        that.$store.commit("conversationList/updateUploadFail", {
                            cid: cid,
                            file_id: data.file_id,
                        });
                    } else {
                        //更新上传进度
                        that.$store.commit("conversationList/updateFileProgress", {
                            msg: {
                                group_id: data.file_id.split("-")[0],
                                file_id: data.file_id,
                            },
                            percent: data.progress,
                        });
                    }
                } catch (error) {
                    console.error(error)
                    reject(false)
                }

            });
        },
    },
};
