import Vue from 'vue'
import VueRouter from 'vue-router';
import Root from '../pages/root.vue'
const WebLive = resolve => require(['../pages/webLive.vue'], resolve)
const ExternalLink = resolve => require(['../pages/externalLink.vue'], resolve)
const GalleryController = resolve => require(['../pages/galleryController.vue'], resolve)
const qrInstallApp = resolve => require(['../pages/qrInstallApp.vue'], resolve)
// const TEAirAppDownload = resolve => require(['../pages/TEAirAppDownload.vue'], resolve)
const pravicyPolicy = resolve => require(['../pages/pravicyPolicy.vue'], resolve)
const versionHistory = resolve => require(['../pages/versionHistory.vue'], resolve)
const unifiedPlatformLogin = resolve => require(['../pages/unifiedPlatform/login.vue'], resolve)
const unifiedPlatformIndex = resolve => require(['../pages/unifiedPlatform/index.vue'], resolve)

Vue.use(VueRouter);
const router=new VueRouter({
    routes:[
        {
            path: '/',
            name: 'Root',
            component: Root,
        },
        {
            path:'/externalLink',
            name:'externalLink',
            component: ExternalLink,
            children: [
                {
                    path: 'gallery',
                    name: 'unlogin_gallery',
                    component: GalleryController,
                },
            ],
        },
        {
            path: '/webLive/:id',
            name: 'WebLive',
            component: WebLive
        },

        {
            path: '/qr_install_app',
            name: 'qrInstallApp',
            component: qrInstallApp,
        },
        // {
        //     path: '/TE_Air_App',
        //     name: 'TEAirAppDownload',
        //     component: TEAirAppDownload
        // },
        {
            path: '/policy',
            name: 'pravicyPolicy',
            component: pravicyPolicy
        },
        {
            path: '/versionHistory',
            name: 'versionHistory',
            component: versionHistory
        },
        {
            path: '/unifiedPlatformLogin',
            name: 'unifiedPlatformLogin',
            component: unifiedPlatformLogin
        },
        {
            path: '/unifiedPlatformIndex',
            name: 'unifiedPlatformIndex',
            component: unifiedPlatformIndex
        },
        { path: '*', redirect: '/', hidden: true }
    ]
})

//解决vue-router3.0重复访问同一路由导致的报错问题
let originalPush = VueRouter.prototype.push
let originalReplace  = VueRouter.prototype.replace

VueRouter.prototype.push = function push(location){
    return originalPush.call(this,location).catch(e=>e)
}
VueRouter.prototype.replace = function push(location){
    return originalReplace.call(this,location).catch(e=>e)
}
export default router;
