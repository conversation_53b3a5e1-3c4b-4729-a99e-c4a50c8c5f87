<template>
    <div id="live_room_container" class="live_room_container">
        <div v-if="isConferenceJoining" class="full_loading_spinner starting_rt_video_cover van_loading_spinner">
            <van-loading color="#00c59d" />
            <span class="tip">{{ lang.starting_rt_video }}</span>
        </div>
        <div v-if="isReconnectChannel" class="full_loading_spinner starting_rt_video_cover van_loading_spinner">
            <van-loading color="#00c59d" />
            <span class="tip">{{ lang.live_conference_reconnecting }}</span>
        </div>
        <ManageVideoMember
            :show.sync="speechPanelVisible"
            :cid="cid"
        ></ManageVideoMember>
        <moreConferenceOperation
            :show.sync="moreConferenceVisible"
            :currentSubject="groupTitle"
            :cid="cid"
        ></moreConferenceOperation>
        <div class="live_room" :class="[isFullScreenByManual ? 'fullscreen' : '']" v-if="isConferenceAuxOnline">
            <div class="live_header">
                <div class="left_area">
                    <div class="fullscreen_btn" @click="fullScreen">
                        <i class="iconfont icon-fullscreen-exit" v-show="isFullScreen"></i>
                        <i class="iconfont icon-fullscreen" v-show="!isFullScreen"></i>
                    </div>
                </div>
                <div class="header_title">
                    <p class="header_title_text textEllipsis">{{ groupTitle }}</p>
                    <IntervalTime :diffTime="living_time" class="inter_time"></IntervalTime>
                </div>

                <div class="right_area">
                    <van-icon name="close" color="#fa1919" class="close_btn" @click="clickLeaveChannel" />
                </div>
            </div>
            <div class="live_player_container">
                <div class="player-box">
                    <div id="player-box-1">
                        <CustomPlayer
                            :isOnline="!!currentSubscribeMain"
                            :hasMuteBtn="false"
                            :offlineTips="lang.weblive_live_not_yet"
                        >
                            <template>
                                <div id="live_player_main"></div>
                            </template>
                        </CustomPlayer>
                    </div>
                    <div id="player-box-2" v-draggable @tap="swapVideoContent('player-box-2')" v-show="findVideoStreamDomShow('live_player_aux_1','player-box-2')">
                        <CustomPlayer
                            :isOnline="true"
                            :hasMuteBtn="false"
                            :offlineTips="lang.weblive_live_not_yet"
                        >
                            <template>
                                <div id="live_player_aux_1"></div>
                            </template>
                        </CustomPlayer>
                    </div>
                    <div id="player-box-3" v-draggable @tap="swapVideoContent('player-box-3')" v-show="findVideoStreamDomShow('live_player_aux_2','player-box-3')">
                        <CustomPlayer
                            :isOnline="true"
                            :hasMuteBtn="false"
                            :offlineTips="lang.weblive_live_not_yet"
                        >
                            <template>
                                <div id="live_player_aux_2"></div>
                            </template>
                        </CustomPlayer>
                    </div>
                    <div id="player-box-4" v-draggable @tap="swapVideoContent('player-box-3')" v-show="findVideoStreamDomShow('live_player_aux_3','player-box-4')">
                        <CustomPlayer
                            :isOnline="true"
                            :hasMuteBtn="false"
                            :offlineTips="lang.weblive_live_not_yet"
                        >
                            <template>
                                <div id="live_player_aux_3"></div>
                            </template>
                        </CustomPlayer>
                    </div>
                </div>
            </div>
        </div>
        <LiveOperation
            :cid="cid"
            :liveRoom="liveRoom"
            @openSpeechPanelVisible="openSpeechPanelVisible"
            @openMoreConferenceOperationVisible="openMoreConferenceOperationVisible"
        ></LiveOperation>
    </div>
</template>
<script>
import base from "../../lib/base";
import Tool from "@/common/tool";
import { joinRoomWeb, resetRequestJoining } from "../../lib/liveConference";
import service from "../../service/service";
import CLiveRoomWeb from "@/common/CLiveConferenceWeb/CLiveRoomWeb";
import CustomPlayer from "../../MRComponents/customPlayer.vue";
import IntervalTime from "../../MRComponents/intervalTime.vue";
import { Checkbox, Icon, Loading,Popup } from "vant";
import { getLiveRoomObj } from "../../lib/common_base";
import ManageVideoMember from "./manageVideoMember.vue";
import MoreConferenceOperation from "./moreConferenceOperation";
import LiveOperation from "./liveOperation"
import {cloneDeep} from 'lodash'
export default {
    mixins: [base],
    name: "LiveRoomWebComponent",
    props: {
        groupTitle: {
            type: String,
            default: "",
        }
    },
    components: {
        CustomPlayer,
        VanIcon: Icon,
        IntervalTime,
        VanLoading: Loading,
        ManageVideoMember,
        MoreConferenceOperation,
        LiveOperation
    },
    computed: {
        checkShowSmallPlayerBox() {
            if (this.currentLayout[1] === "aux") {
                if (this.currentSubscribeAux && this.currentSubscribeAux.length > 0) {
                    this.resetPlayer2Position();
                    return true;
                }
            } else {
                if (this.currentSubscribeMain) {
                    this.resetPlayer2Position();
                    return true;
                }
            }
            return false;
        },
        isTEAir() {
            return this.$store.state.device.isTEAir;
        },
        checkShowVideoOperateBtn() {
            return this.systemConfig.clientType === 5;
        },
        conversation() {
            return this.conversationList[this.cid] || {};
        },
        LiveConferenceData() {
            return (
                (this.$store.state.liveConference[this.cid] &&
                    this.$store.state.liveConference[this.cid].LiveConferenceData) ||
                {}
            );
        },
    },
    data() {
        return {
            title: "",
            loading: false,
            showMainDialog: false,
            showAuxDialog: false,
            cid: 0,
            remoteUsers: {},
            currentSubscribeUidList: [], //从服务器请求的 当前应当订阅的Uidlist
            agoraOptions: {
                appid: "",
                token: "",
                channel: "",
                uid: null,
            },
            living_time: 0,
            isConferenceJoining: false,
            currentScreen: "main",
            currentSubscribeAux: [],
            currentSubscribeMain: 0,
            liveRoom: null,
            currentLayout: ["main", "aux"],
            isFullScreen: false,
            isFullScreenByManual: false,
            isReconnectChannel: false,
            isConferenceMainOnline: 0, //主流是否在房间
            isConferenceAuxOnline: 0, //是否直播中（辅流在线代表在线）
            isConferenceVideoStream: 0, //是否直播开启视频
            isConferenceAudioStream: 0, //是否直播开启声音
            speechPanelVisible: false, //发言面板
            moreConferenceVisible:false,
            currentShowAuxPlayerList:new Set()
        };
    },
    activated() {},
    async mounted() {},
    methods: {
        async initLiveRoomObj(cid) {
            await Tool.handleAfterConversationCreated(cid);
            if (window.CLiveRoomWeb) {
                if (!window.CLiveRoomWeb[cid]) {
                    window.CLiveRoomWeb[cid] = new CLiveRoomWeb({
                        main_dom: "live_player_main",
                        aux_dom_list: ["live_player_aux_1","live_player_aux_2","live_player_aux_3"],
                        cid,
                    });
                }
            } else {
                window.CLiveRoomWeb = {};
                window.CLiveRoomWeb[cid] = new CLiveRoomWeb({
                    main_dom: "live_player_main",
                    aux_dom_list: ["live_player_aux_1","live_player_aux_2","live_player_aux_3"],
                    cid,
                });
            }
            this.liveRoom = getLiveRoomObj(cid);
            // 监听 liveRoom.data 的变化
            this.observeDataChanges();
            this.liveRoom.event.off("HandleNotifyLeaveChannelAux");
            this.liveRoom.event.on("HandleNotifyLeaveChannelAux", this.HandleNotifyLeaveChannelAux);

            this.liveRoom.event.off("HandleNotifyJoinChannelAux");
            this.liveRoom.event.on("HandleNotifyJoinChannelAux", this.HandleNotifyJoinChannelAux);

            this.liveRoom.event.off("HandleDisconnectAux");
            this.liveRoom.event.on("HandleDisconnectAux", this.HandleDisconnectAux);

            this.liveRoom.event.off("clickLeaveChannel");
            this.liveRoom.event.on("clickLeaveChannel", this.clickLeaveChannel);
            this.$root.eventBus.$off("startJoinRoom").$on("startJoinRoom", this.startJoinRoom);
        },
        initAgoraObj(cid) {},
        beforeCloseMain() {
            this.showMainDialog = false;
            this.$emit("change", false);
            this.Leave();
        },
        beforeCloseAux() {
            this.showAuxDialog = false;
        },
        startJoinRoom:Tool.debounce(async function ({ main = 0, aux = 0, isSender = 0, cid = 0 ,from = "chatWindow",}, callback) {
            this.isConferenceJoining = true;
            this.cid = cid;
            try {
                await this.initLiveRoomObj(cid);
                await joinRoomWeb({
                    main,
                    aux,
                    isSender,
                });
                this.isConferenceJoining = false;
                callback && callback(true);
            } catch (error) {
                console.error(error);
                this.isConferenceJoining = false;
                callback && callback(false, error);
            }
        },1000,true),
        toggleChangeScreen(type) {
            this.currentScreen = type;
        },
        swapArrayElements(arr) {
            [arr[0], arr[1]] = [arr[1], arr[0]];
        },
        swapVideoContent(playerDom) {
            const node1 = document.getElementById("player-box-1");
            const node2 = document.getElementById(playerDom);
            Tool.swapChildren(node1, node2);
            this.swapArrayElements(this.currentLayout);
            console.log(this.currentLayout);
        },
        fullScreen() {
            console.log('是小程序环境吗：',Tool.checkAppClient('MiniProgram'));
            if (this.isFullScreen) {
                //退出全屏
                this.isFullScreen = false;
                if (document.exitFullscreen) {
                    //标准写法
                    document.exitFullscreen();
                } else if (document.webkitCancelFullScreen) {
                    //webkit 内核浏览器	谷歌 Safari
                    document.webkitCancelFullScreen();
                } else if (document.mozCancelFullScreen) {
                    //moz  	内核浏览器  火狐
                    document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    //ms  	ie浏览器  取消全屏是Exit  不是Cancel
                    document.msExitFullscreen();
                } else {
                    this.isFullScreenByManual = false;
                }
            } else {
                //进入全屏
                this.isFullScreen = true;
                // const dom = document.querySelector(".live_room_container");
                const dom = document.getElementById("live_room_container");
                if (dom.requestFullScreen) {
                    //标准写法
                    dom.requestFullScreen();
                    console.log('标准写法');
                } else if (dom.webkitRequestFullScreen) {
                    //webkit 内核浏览器	谷歌 Safari
                    dom.webkitRequestFullScreen();
                    console.log('webkit 内核浏览器	谷歌 Safari');
                } else if (dom.mozRequestFullScreen) {
                    //moz  	内核浏览器	火狐
                    dom.mozRequestFullScreen();
                    console.log('moz  	内核浏览器	火狐');
                } else if (dom.msRequestFullscreen) {
                    //ms  	ie浏览器	 RequestFullscreen中 Screen中的s ie浏览器需要小写
                    dom.msRequestFullscreen();
                    console.log('ms  	ie浏览器	 RequestFullscreen中 Screen中的s ie浏览器需要小写');
                }else if(Tool.checkAppClient('MiniProgram')){
                    dom.requestFullScreen();
                    console.log('小程序环境');
                } else {
                    this.isFullScreenByManual = true;
                    console.log('其他浏览器');
                }
            }
        },
        resetPlayer2Position(playerDom) {
            const node2 = document.getElementById(playerDom);
            if(this.currentShowAuxPlayerList.size === 0){
                node2.style.right = 0;
                node2.style.top = 0;
                node2.style.left = "auto";
                node2.style.bottom = "auto";
            }else if(this.currentShowAuxPlayerList.size === 1){
                node2.style.right = 0;
                node2.style.bottom = 0;
                node2.style.left = "auto";
                node2.style.top = "auto";
            }else if(this.currentShowAuxPlayerList.size === 2){
                node2.style.left = 0;
                node2.style.top = 0;
                node2.style.right = "auto";
                node2.style.bottom = "auto";
            }

            console.log(node2.style);
        },
        clickLeaveChannel() {
            console.info("clickLeaveChannel");
            let liveRoom = this.liveRoom;
            if (!liveRoom) {
                return;
            }
            const conversation = this.$store.state.conversationList[this.cid];
            const is_single_chat = conversation.is_single_chat;
            if (liveRoom.checkManagerAuth() && !is_single_chat) {
                if (this.isTEAir) {
                    liveRoom.LeaveChannelAux("sender");
                } else {
                    Tool.openMobileDialog({
                        message: this.lang.whether_dissolve_live,
                        showRejectButton: true,
                        confirmButtonText: this.lang.end_live,
                        rejectButtonText: this.lang.quit_live,
                        confirm: () => {
                            liveRoom.LeaveChannelAux("sender");
                        },
                        reject: () => {
                            liveRoom.LeaveChannelAux("normal");
                        },
                        close: () => {},
                    });
                }
            } else {
                liveRoom.LeaveChannelAux("normal");
            }
        },
        HandleNotifyJoinChannelAux() {
            console.log("HandleNotifyJoinChannelAux");
            this.$emit("joinChannelAux");
        },
        HandleNotifyLeaveChannelAux({}) {
            console.log("HandleNotifyLeaveChannelAux");
            Tool.closeAllDialog()
            this.speechPanelVisible = false;
            this.moreConferenceVisible = false;
            this.$emit("leaveChannelAux");
            setTimeout(()=>{
                this.unbindDataChanges()
            },0)
        },
        HandleDisconnectAux() {
            this.isConferenceJoining = false;
            resetRequestJoining();
        },
        openSpeechPanelVisible() {
            this.speechPanelVisible = !this.speechPanelVisible;
        },
        openMoreConferenceOperationVisible() {
            this.moreConferenceVisible = true;
        },
        observeDataChanges() {
            // 防抖函数用于批量替换 roomUserMap
            const debouncedUpdateRoomUserMap = Tool.debounce(() => {
                // 防抖函数内部执行深拷贝，确保拷贝最新的 roomUserMap
                // const newRoomUserMap = cloneDeep(this.liveRoom.data.roomUserMap);
                this.$store.commit("liveConference/setRoomUserMapData", {
                    cid: this.cid,
                    data: { roomUserMap: this.liveRoom.data.roomUserMap }
                });
            }, 50); // 设置防抖时间为 50ms，可以根据需求调整
            // 代理 liveRoom.data 对象
            const originData = cloneDeep(this.liveRoom.data);
            this.liveRoom.data = Tool.deepReactive(originData, (target, key, value, action, path) => {
                // 同步数据到 Store
                if(path.includes('roomUserMap')){
                    if(action === 'set'||action === 'delete'){
                        debouncedUpdateRoomUserMap()
                    }

                }else{
                    if (action === 'set') {
                        this.$store.commit('liveConference/setLiveRoomData', { cid: this.cid, data: { path, value } });
                    } else if (action === 'delete') {
                    // 处理删除操作
                        this.$store.commit('liveConference/deleteLiveRoomData', { cid: this.cid, data: { path } });
                    }
                }
                if(this.liveRoom.data){
                    this.living_time = this.liveRoom.data.living_time;
                    this.currentSubscribeAux = this.liveRoom.data.currentSubscribeAux;
                    this.currentSubscribeMain = this.liveRoom.data.currentSubscribeMain;
                    if (this.liveRoom.data.joinedAux && this.liveRoom.data.losing_connect_server) {
                        this.isReconnectChannel = true;
                    } else {
                        this.isReconnectChannel = false;
                    }
                    if (this.liveRoom.data.joinedMain) {
                        this.isConferenceMainOnline = 1;
                    } else {
                        this.isConferenceMainOnline = 0;
                    }
                    if (this.liveRoom.data.joinedAux) {
                        this.isConferenceAuxOnline = 1;
                    } else {
                        this.isConferenceAuxOnline = 0;
                    }
                    this.isConferenceVideoStream = this.liveRoom.data.localVideoStream > 0 ? 1 : 0;

                    this.isConferenceAudioStream = this.liveRoom.data.localAudioStream > 0 ? 1 : 0;
                    if (this.liveRoom.data.joinedAux && this.liveRoom.data.losing_connect_server) {
                        this.isReconnectChannel = true;
                    } else {
                        this.isReconnectChannel = false;
                    }
                    if (!this.liveRoom.data.joinedAux) {
                        this.speechPanelVisible = false;
                    }
                }
            });
            // 初始同步一次数据
            this.$store.commit("liveConference/replaceLiveRoomData", { cid: this.cid, data: cloneDeep(this.liveRoom.data) });
        },
        unbindDataChanges(){
            this.liveRoom.data = cloneDeep(this.liveRoom.data);
        },
        findVideoStreamDomShow(videoDom,playerDom){
            // 查找 showVideoDomList 中 id 匹配的对象及其索引
            const index = this.LiveConferenceData.showAuxVideoDomList.findIndex(item => item.dom === videoDom);
            if (index !== -1) {
                if (!this.currentShowAuxPlayerList.has(playerDom)) {
                    this.resetPlayer2Position(playerDom);
                    this.currentShowAuxPlayerList.add(playerDom);

                }
                console.error('findVideoStreamDomShow',videoDom)
                return true;
            }
            // 如果 `playerDom` 存在于 `Set` 中，则移除
            if (this.currentShowAuxPlayerList.has(playerDom)) {
                this.currentShowAuxPlayerList.delete(playerDom);
            }

            return false;
        }
    },
};
</script>
<style lang="scss">
.van_loading_spinner {
    display: flex;
    justify-content: center;
    align-items: center;

    .van-loading__spinner {
        width: 2.8rem;
        height: 2.8rem;
    }
}
.starting_rt_video_cover {
    background-color: rgba(128, 128, 128, 0.8);
    z-index: 999;
    .tip {
        margin-top: 3rem;
        width: 80%;
        text-align: center;
    }
}
.live_room_container {
    width: 100%;
    .live_room {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: #000;
        height: 250px;
        &.fullscreen {
            position: fixed !important;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
        }
        .live_header {
            position: relative;
            .header_title {
                color: #fff;
                text-align: center;
                font-size: 0.8rem;
                height: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                .header_title_text {
                    width: 70%;
                }
            }
            .left_area {
                position: absolute;
                left: 0.3rem;
                top: 50%;
                transform: translateY(-50%);
                color: #fff;
                i {
                    font-size: 1.2rem;
                }
            }
            .right_area {
                position: absolute;
                right: 0.3rem;
                top: 50%;
                transform: translateY(-50%);
                .close_btn {
                    font-size: 1.5rem;
                }
            }
        }
        .live_player_container {
            flex: 1;
            overflow: hidden;
            position: relative;
            .player-box {
                width: 100%;
                height: 100%;
                #player-box-1 {
                    width: 100%;
                    height: 100%;
                    background: #0c7bea;
                    position: absolute;
                    left: 0;
                    top: 0;
                }
                #player-box-2,#player-box-3,#player-box-4 {
                    width: 6rem;
                    height: 6rem;
                    position: absolute;
                    right: 0;
                    top: 0;
                    background: #c4a20a;
                }
                #live_player_main,
                #live_player_aux_1,#live_player_aux_2,#live_player_aux_3 {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
}
</style>
