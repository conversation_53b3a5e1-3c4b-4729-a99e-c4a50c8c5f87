<template>
    <transition name="slide">
        <div class="case_database_page second_level_page">
            <mrHeader @click-left="exitCaseDatabase">
                <template #title>
                    {{ lang.case_database_title }}
                </template>
                <template #right>
                    <span class="condition_icon" @click="toggleCondition">
                        <div class="red_point" v-if="isSetCondition"></div>
                        <i class="icon iconfont icon-shaixuan icon_shaixuan_"></i>
                    </span>
                </template>
            </mrHeader>
            <div class="container" v-loading="isSearchLoading">
                <div class="color_frame">
                    <div v-for="index of 5" :key="index" :class="'color_item_' + index"></div>
                </div>
                <div class="scroll_box" ref="scrollArea">
                    <div class="search_wrap">
                        <span class="key_btn">{{ lang.search }}</span>
                        <input
                            class="search_btn"
                            type="text"
                            v-model="searchInfo.searchInputKey"
                            maxlength="12"
                            @focus="clickInput"
                            @keyup.enter="blurInput"
                        />
                        <i class="iconfont svg_icon_camera icon-camera_frame" @click="selectPicture"></i>
                        <input
                            type="file"
                            class="select_picture"
                            ref="selectPictureRef"
                            accept="image/png,image/jpg,image/jpeg,image/bmp"
                            @change="uploadPicture({})"
                        />
                        <i class="iconfont svg_icon_search icon-magnifier" @click="startTextSearch()"></i>
                    </div>
                    <div v-show="history.showList.length > 0 && isShowHot" class="search_key">
                        <p>{{ lang.history_tag }}</p>
                        <i
                            class="iconfont icon-down-end"
                            v-show="history.isShowCollapse && history.isCollapseTag"
                            @tap="toggleCollapseTag(false)"
                        ></i>
                        <i
                            class="iconfont icon-up-start"
                            v-show="history.isShowCollapse && !history.isCollapseTag"
                            @tap="toggleCollapseTag(true)"
                        ></i>
                        <i class="iconfont icon-delete" @click="clearTag"></i>
                        <v-touch
                            v-for="tag in history.showList"
                            :key="'caseTag' + tag"
                            @tap="clickSearch(tag)"
                            @press="deleteTag(tag)"
                            class="case_tag"
                        >
                            {{ tag }}
                        </v-touch>
                        <!-- <p>{{lang.case_database_tip}}</p> -->
                    </div>
                    <div class="case_list_container" v-show="galleryState == 0">
                        <!-- <div class="search_loading" ></div> -->
                        <p class="latest_examlist_tip" v-show="isShowHot">{{ lang.hot_search_tip }}</p>
                        <div v-show="searchInfo.imageSrc" class="examlist_image_tip_container">
                            <div class="examlist_image_button_wrap">
                                <img
                                    class="examlist_image_button"
                                    :src="imageDraw || searchInfo.imageSrc"
                                    @click="showUploadImage"
                                />
                            </div>
                            <div class="examlist_image_close">
                                <div class="left">
                                    <i class="icon iconfont icon-shuaxin" @click="refreshlImageSearch"></i>
                                </div>
                                <div class="right">
                                    <i class="icon iconfont icon-close" @click="clearImageSearch"></i>
                                </div>
                            </div>
                        </div>
                        <p v-show="searchInfo.searchInputKey != '' && !isShowHot" class="examlist_tip">
                            {{ fliterTip }}
                        </p>
                        <div class="no_data" v-show="resultTip != ''">{{ resultTip }}</div>
                        <van-list
                            ref="loadmore"
                            v-model="caseIsLoading"
                            @load="loadCase"
                            :finished="exams.endCase >= exams.ids.length || caseIsFinished"
                            :loading-text="lang.bottom_loading_text"
                            offset="0"
                        >
                            <div
                                class="case_item"
                                v-for="(item, index) of exams.currentExamList.slice(0, exams.endCase)"
                                :key="index"
                            >
                                <div class="image_list_first clearfix" @click="setPatientImage(item)">
                                    <div class="image_item" @click="showGallery(item)">
                                        <span class="images_count"
                                            >{{ item.image_list.length }}{{ lang.picture_unit }}</span
                                        >
                                        <div class="image_item_container">
                                            <img class="image" :src="prefUrl + item.image_list[0]" />
                                        </div>
                                    </div>
                                </div>
                                <div class="patient_info">
                                    <div class="patient_info_title">
                                        <p>{{ item.postoperative_pathology }}:{{ item.benign_malignant }}</p>
                                    </div>
                                    <div class="patient_info_item">
                                        <p>{{ lang.case_database_id }}: {{ item.collection_id }}</p>
                                    </div>
                                    <div class="patient_info_item">
                                        <p>{{ lang.exam_patient_sex }}: {{ item.patient_sex }}</p>
                                        <p>{{ lang.exam_patient_age }}: {{ item.patient_age }}</p>
                                    </div>
                                    <!-- <div class="patient_info_item" >
                                        <p>similarity: {{item.similarity? (item.similarity+'').substr(0,5):'unknow'}}</p>
                                    </div> -->
                                </div>
                            </div>
                        </van-list>
                    </div>
                </div>
            </div>
            <van-action-sheet
                v-model="isShowSelectType"
                :actions="selectTypeActions"
                @select="onSelectActions"
                close-on-click-action
                :cancel-text="lang.cancel_btn"
                @cancel="isShowSelectType = false"
            />
            <case-fliter
                ref="case_fliter"
                :condition="filters.condition"
                :isShowCondition="filters.isShowCondition"
                @handleback="handleback()"
            ></case-fliter>
            <caseGallery
                :pathologyInfo="exams.pathologyInfo"
                :show.sync="showSearchImage"
                @close="showImageEvent"
                :isCaseImage="true"
            ></caseGallery>
            <caseGallery
                :imageObj="uploadImageObj"
                :show.sync="showCaseImage"
                :isCaseImage="false"
                @close="showSearchImageEvent"
            ></caseGallery>
        </div>
    </transition>
</template>
<script>
import base from "../lib/base";
import Tool from "@/common/tool.js";
import caseGallery from "@/module/ultrasync/components/caseGallery.vue";
import caseFliter from "@/module/ultrasync/components/caseFliter.vue";
import service from "../service/service.js";
import { ActionSheet } from "vant";
import { Toast, List } from "vant";
import { getRealUrl } from "../lib/common_base";
import { cloneDeep, trim } from "lodash";
import { uploadFile, cancelUpload } from "@/common/oss/index";
export default {
    mixins: [base],
    name: "case_database",
    components: {
        caseGallery: caseGallery,
        VanActionSheet: ActionSheet,
        caseFliter,
        VanList: List,
    },
    data() {
        return {
            filters: {
                isShowCondition: false, //是否显示过滤条件
                condition: {},
            },
            isShowSelectType: false, //是否显示弹出相机或本地相册
            selectTypeActions: [], //相机或者照片弹出
            caseIsLoading: false, // case库是否在处于loading状态
            caseIsFinished: false, // case库是否在处于finished状态
            exams: {
                endCase: 20, //默认20条
                // isNoData: false,//无数据
                sliceCount: 20, //每次增20条
                currentExamList: [], //当前显示列表
                ids: [], //ids
                oldIds: [], //过滤后还原
                pathologyInfo: {}, //当前病例
            },
            history: {
                showList: [], //展示的搜索历史
                list: [], //展示的搜索历史
                isShowCollapse: false, //是否显示收起历史
                isCollapseTag: false, //是否收起搜索历史
                collapseNum: 10,
            },
            isShowHot: false, //是否显示热搜
            isSearchLoading: false, //是否加载中
            defaultSearchInfo: {
                defaltSearchInputKey: "回声",
                searchInputKey: "", //搜索文本内容
                imageSrc: "", //用于显示到前端
                file: "", //文件
                name: "", //文件名字
                msg: "", //文件名字
                selectRoi: 0, //选中的roi，默认第一个
                roi: [], //本次搜索roi
                // oldRoi:[], //上次搜索roi
                hotcount: 20, //热搜获取的数据
                isUrl: false, //是否未网络资源
                url: "", //图片云端地址
            },
            imageDraw: "",
            uploadImageObj: {}, //用于画廊显示
            searchInfo: {},
            galleryState: 0, //是否显示画廊0默认，1病例，2搜索图片
            end_time: 0,
            start_time: 0,
            colors: ["#FF0000", "#00FF00", "#0000FF", "#8000FF", "#FFFFFF", "#FFFF00"],
            case_database_fliter: {
                benign_or_malignant: "良恶性",
                benign: "良性",
                malignant: "恶性",
                bi_rads_feature: "BI - RADS特征",
                shape: "形状",
                oval: "卵圆形",
                circular: "圆形",
                irregular: "不规则形",
                direction: "方向",
                parallel: "与皮肤平行",
                unparallel: "不平行",
                edge: "边缘",
                vague: "模糊",
                finishing: "光整",
                angled: "成角",
                microphylation: "微分叶",
                hairpin_like: "毛刺状",
                echo_type: "回声类型",
                anechoic: "无回声",
                hypoechoic: "低回声",
                hyperechoic: "高回声",
                isoechoic: "等回声",
                mixed_echo: "混合回声",
                uneven_echo: "不均回声",
                posterior_echo: "后方回声",
                no_change: "无改变",
                echo_enhancement: "回声增强",
                acoustic_shadow: "声影",
                mixed_change: "混合性改变",
                calcification: "钙化",
                no_calcification: "无钙化",
                calcification_in_mass: "肿块内钙化",
                calcification_out_mass: "肿块外钙化",
                intraductal_calcification: "导管内钙化",
                microcalcification: "微钙化",
                coarse_calcification: "粗大钙化",
                bi_rads_type: "BI - RADS分类",
                bi_rads_1: "1类",
                bi_rads_2: "2类",
                bi_rads_3: "3类",
                bi_rads_4a: "4a类",
                bi_rads_4b: "4b类",
                bi_rads_4c: "4c类",
                bi_rads_5: "5类",
                bi_rads_6: "6类",
                pathological_classification_breast_cancer: "乳腺癌病理分类",
                noninvasive_breast_cancer: "非浸润性乳腺癌",
                ductal_carcinoma_in_situ: "导管原位癌",
                lobular_carcinoma_in_situ: "小叶原位癌",
                invasive_breast_cancer: "浸润性乳腺癌",
                infiltrating_ductal_carcinoma: "浸润性导管癌",
                infiltrating_lobular_carcinoma: "浸润性小叶癌",
                cephaloma: "髓样癌",
                mucinous_carcinoma: "粘液癌",
                papillary_carcinoma: "乳头状癌",
            },
            isInit: false,
            showSearchImage: false,
            showCaseImage: false,
        };
    },
    computed: {
        prefUrl() {
            return this.systemConfig.serverInfo.oss_attachment_server.playback_https_addr + "/" + "AiSearch" + "/";
        },
        condition() {
            return this.$store.state.caseDatabase.defaultCondition;
        },

        fliterTip() {
            return this.searchInfo.searchInputKey
                ? this.lang.search_result_tip.replace("{1}", this.searchInfo.searchInputKey)
                : "";
        },
        resultTip() {
            if (this.isSearchLoading) {
                return this.lang.searching;
            }
            if (this.searchInfo.msg != "") {
                return this.searchInfo.msg;
            }
            if (this.exams.ids.length < 1) {
                return this.lang.is_no_case_text;
            }

            return "";
        },
        searchParams() {
            return this.$store.state.caseDatabase.searchParams || { type: "text", content: "" };
        },
        isSetCondition() {
            for (let key in this.filters.condition) {
                let item = this.filters.condition[key];
                if (this.isArrary(item)) {
                    let list = item;
                    if (list.length > 0) {
                        return true;
                    }
                } else {
                    for (let key_ in item) {
                        let list = item[key_];
                        if (list.length > 0) {
                            return true;
                        }
                    }
                }
            }
            return false;
        },
    },
    created() {
        this.initFn();
    },
    activated() {
        this.initFn();
    },
    mounted() {},
    watch: {
        searchInfo: {
            handler(newVal, oldVal) {
                if (newVal && newVal.imageSrc && newVal.roi.length > 0) {
                    this.LoadImage();
                }
            },
            deep: true,
        },
    },
    methods: {
        initFn() {
            if (this.isInit) {
                return;
            }
            this.isInit = true;
            this.start_time = new Date().getTime();
            this.searchInfo = cloneDeep(this.defaultSearchInfo);
            this.imageDraw = "";
            this.clearFilterCondition();
            //相机弹窗
            this.selectTypeActions = [
                { name: this.lang.take_picture, key: 0 },
                { name: this.lang.local_library, key: 1 },
            ];
            this.initTagHistory();
            this.setCurrentList();
            setTimeout(() => {
                this.isInit = false;
            }, 300);
        },
        serverUrl(method) {
            let ai_searcher_server = this.$store.state.systemConfig.serverInfo.ai_searcher_server;
            let ajaxServer =
                ai_searcher_server.protocol +
                ai_searcher_server.addr +
                ":" +
                ai_searcher_server.port +
                "/" +
                trim(trim(ai_searcher_server.api, "/"), "\\");
            return trim(trim(ajaxServer, "/"), "\\") + "/" + trim(trim(method, "/"), "\\");
        },
        drawCanvasToImage(realImage) {
            let that = this;
            //左右[x1,y1,x2,y2]
            var canvas = document.getElementById("ai_canvas");
            var context = canvas.getContext("2d");
            canvas.width = realImage.width;
            canvas.height = realImage.height;
            context.drawImage(realImage, 0, 0);
            context.strokeStyle = "#f00";
            context.lineWidth = 1;
            let roi = that.searchInfo.roi[that.searchInfo.selectRoi];
            if (roi) {
                context.beginPath();
                context.moveTo(roi[0], roi[1]); //把画笔移动到指定的坐标
                context.lineTo(roi[2], roi[1]); //绘制一条从当前位置到指定坐标(200, 50)的直线.
                context.lineTo(roi[2], roi[3]);
                context.lineTo(roi[0], roi[3]);
                //闭合路径。会拉一条从当前点到path起始点的直线。如果当前点与起始点重合，则什么都不做
                context.closePath();
                context.strokeStyle = that.colors[that.searchInfo.selectRoi];
                context.lineWidth = 8;
                // context.fillStyle = this.colors[i];
                // context.fill();
                context.stroke(); //绘制路径。
            }
            let base64 = canvas.toDataURL("image/jpeg");
            return base64;
        },
        LoadImage() {
            let that = this;
            console.log("that.searchInfo.isUrl:", that.searchInfo.isUrl);
            if (!that.searchInfo.isUrl) {
                //base64
                let reader = new FileReader();
                reader.onload = function (e) {
                    let data = e.target.result;
                    let image = new Image();
                    image.src = data;
                    image.onload = function () {
                        that.imageDraw = that.drawCanvasToImage(image);
                    };
                };
                console.error("that.searchInfo.file:", that.searchInfo.file);
                reader.readAsDataURL(that.searchInfo.file);
            } else {
                //url
                let image = new Image();
                image.onerror = function (e) {
                    console.log("preloadError", e);
                };
                image.onload = function () {
                    that.imageDraw = that.drawCanvasToImage(image);
                };
                if (that.systemConfig.serverInfo.network_environment === 1) {
                    that.searchInfo.file = Tool.replaceInternalNetworkEnvImageHost(that.searchInfo.file);
                }
                image.setAttribute("crossOrigin", "anonymous");
                image.src = `${that.searchInfo.file}?temp=${Date.now()}`;
            }
        },
        exitCaseDatabase() {
            if (this.isShowHot) {
                this.searchInfo.searchInputKey = "";
                this.isShowHot = false;
            } else {
                this.back();
            }
        },
        clearFilterCondition() {
            this.filters.condition = cloneDeep(this.condition);
        },
        //触发文本搜索
        startTextSearch() {
            this.clearFilterCondition();
            let params = {
                type: "text", //text,url,file
                content: this.searchInfo.searchInputKey,
            };
            this.sortAndSaveTag(this.searchInfo.searchInputKey);
            this.$store.commit("caseDatabase/updateSearchParams", params);
            this.setCurrentList(params);
        },
        // 点击搜索历史
        clickSearch(keyword) {
            this.searchInfo.searchInputKey = keyword;
            this.startTextSearch();
        },
        //设置当前列表
        async setCurrentList(params = null) {
            this.isSearchLoading = true;
            this.isShowHot = false;
            this.exams.endCase = 20;
            this.searchInfo.msg = "";
            this.exams.currentExamList = [];
            this.exams.ids = [];
            this.exams.oldIds = [];

            this.searchInfo = cloneDeep(this.defaultSearchInfo);
            this.imageDraw = "";
            this.clearFilterCondition();
            let { type, content } = params == null ? this.searchParams : params;
            console.log("setCurrentList:", type);
            /* eslint-disable */
            switch (type) {
                case "text":
                    let op = { type: "text", text: content };
                    this.searchInfo.searchInputKey = content;
                    let datas = await this.searchAction("find_by_keyword", op);
                    if (datas && !datas.error) {
                        this.exams.currentExamList = datas.data.list;
                        this.exams.ids = datas.data.ids;
                        this.exams.oldIds = cloneDeep(datas.data.ids);
                    }
                    break;
                case "file":
                    await this.uploadPicture(content);
                    break;
                case "url":
                    // this.debounceSearch = Tool.debounce(this.startSearch,400);
                    await this.pictureUrlDeal(content);
                    break;
                default:
                    break;
            }
            this.isSearchLoading = false;
        },
        //清空搜索
        clearImageSearch() {
            this.isShowHot = false;
            this.searchInfo = cloneDeep(this.defaultSearchInfo);
            this.imageDraw = "";
            this.exams.endCase = 20;
            this.exams.currentExamList = [];
            this.exams.ids = [];
            this.exams.oldIds = [];
            this.clearFilterCondition();
            this.startTextSearch();
        },
        //显示搜索图片
        showUploadImage() {
            this.uploadImageObj = {
                file: this.searchInfo.imageSrc,
                roi: this.searchInfo.roi,
                selectRoi: this.searchInfo.selectRoi,
                isHttpResoucel: this.searchInfo.isUrl,
            };
            this.galleryState = 2;
            this.showCaseImage = true;
        },

        //刷新图片搜索
        async refreshlImageSearch() {
            this.isShowHot = false;
            let that = this;
            that.isSearchLoading = true;
            that.exams.endCase = 20;
            that.exams.currentExamList = [];
            that.searchInfo.msg = "";
            that.exams.ids = [];
            that.exams.oldIds = [];
            that.clearFilterCondition();
            // console.log("refreshlImageSearch:",that.searchInfo)
            if (that.searchInfo.msg == "" || that.searchInfo.msg == this.lang.is_no_case_text) {
                // || (that.searchInfo.msg == this.lang.picture_is_no_roi)
                that.searchInfo.msg = "";
                if (that.searchInfo.isUrl) {
                    let op = {
                        realUrl: that.searchInfo.url,
                    };
                    if (that.searchInfo.roi.length > 0) {
                        op.roi = that.searchInfo.roi[that.searchInfo.selectRoi];
                    }
                    await this.pictureUrlDeal(op);
                } else {
                    let op = {
                        file: that.searchInfo.file,
                    };
                    if (that.searchInfo.roi.length > 0) {
                        op.roi = that.searchInfo.roi[that.searchInfo.selectRoi];
                    }
                    await this.uploadPicture(op);
                }

                that.isSearchLoading = false;
            } else {
                that.isSearchLoading = false;
                Toast(that.searchInfo.msg);
            }
        },
        //执行搜索
        async searchAction(name, options, numb = 30000) {
            let timer = null;
            let that = this;
            // console.log('searchAction function',options)
            return new Promise(async (resolve, reject) => {
                let result = { error: false, msg: "", data: { list: [], roi: [], ids: [] } };
                timer = setTimeout(() => {
                    result.error = true;
                    result.msg = "timeout";
                    Toast(that.lang.requestTimeout);
                    that.isSearchLoading = false;
                    reject(result);
                    return;
                }, numb);
                that.$once("hook:beforeDestroy", function () {
                    timer && clearTimeout(timer);
                });
                let start_req_time = new Date().getTime();
                let op = {
                    pageNo: options.pageNo || 1,
                    pageSize: options.pageSize || that.exams.sliceCount,
                    type: "BREASTSEARCH",
                    sender_id: this.user.id,
                };
                this.caseIsFinished = false;
                if (name == "find_by_keyword") {
                    op.keyword = options.text;
                }
                if (name == "find_by_image") {
                    op.url = options.FILE.url;
                }
                if (name == "find_by_ids") {
                    op.ids = options.ids;
                }
                if (name == "fliter_by_condition") {
                    this.caseIsFinished = true; // 筛选时限制加载
                    if (options.ids && options.ids.length > 0) {
                        op.ids = options.ids;
                    }
                }
                if (options && options.condition) {
                    op.fliterCondition = options.condition;
                }
                if (options && options.FILE && options.FILE.roi && options.FILE.roi.length > 0) {
                    op.roi = options.roi;
                }
                service.requestAiAnalyzeWithUrl({ method: name, condition: op }).then(async (res) => {
                    let data = res.data.data;
                    if (res.data.error) {
                        data.error = true;
                    } else {
                        data.error = false;
                    }
                    console.log(data);
                    this.end_time = new Date().getTime();
                    console.log("time:", this.end_time - this.start_time);
                    if (!data.error) {
                        timer && clearTimeout(timer);
                        let end_req_time = new Date().getTime();
                        if (data && data.roi) {
                            result.data.roi = [];
                            for (let key in data.roi) {
                                if (key && data.roi[key].length > 0) {
                                    result.data.roi.push(data.roi[key]);
                                }
                                if (options && options.FILE && options.FILE.roi) {
                                    result.data.roi = cloneDeep(that.searchInfo.roi);
                                } else {
                                    that.searchInfo.roi = cloneDeep(result.data.roi);
                                }
                            }
                        }
                        // if(result.data.roi.length<1&&result.data.list.length<1&&options.type=='image'){
                        //     that.searchInfo.msg = that.lang.picture_is_no_roi
                        // }
                        if (name == "fliter_by_condition") {
                            // 如果筛选出来之后存在list
                            if (data.ids.length) {
                                // 第一次筛选：
                                // 先清空 currentExamList
                                // 滚动条回滚到最上面，不然其自动触发 loadmore
                                if (this.caseIsFinished) {
                                    this.exams.currentExamList = [];
                                    this.$refs.scrollArea.scrollTop = 0;
                                }
                            }
                        }
                        console.log("data.list", data.list);
                        result.data.ids = data.ids || [];
                        result.data.list = data.list || [];
                        that.isSearchLoading = false;
                        resolve(result);
                    } else if (data.error == 1) {
                        let msg = this.transErrorMsg(data.error_code);
                        result.error = true;
                        result.msg = msg;
                        resolve(result);
                        return;
                    }
                });
            }).catch(() => {});
        },
        //提示错误
        transErrorMsg(error_code) {
            let msg = error_code;
            switch (error_code) {
                case "E01":
                    msg = this.lang.unknown_error;
                    break;
                case "E02":
                    msg = this.lang.ai_error["prohibit_call"];
                    break;
                case "E03":
                    msg = this.lang.ai_error["disconnect"];
                    break;
                case "E04":
                    msg = this.lang.ai_error["connect_fail"];
                    break;
                case "E05":
                    msg = this.lang.ai_error["anaylse_fail"];
                    break;
                case "E06":
                    msg = this.lang.ai_error["report_anaylse_fail"];
                    break;
                case "E07":
                    msg = this.lang.ai_error["anaylse_timeout"];
                    break;
                case "E08":
                    msg = this.lang.ai_error["invalid"];
                    break;
                default:
                    msg = error_code;
            }
            Toast(this.lang.case_database_error_code + msg);
            return msg;
        },
        //显示过滤
        toggleCondition() {
            if (!this.isSearchLoading) {
                this.filters.isShowCondition = !this.filters.isShowCondition;
            }
        },
        //筛选条件
        async handleback(newCondition) {
            this.filters.isShowCondition = false;
            this.filters.condition = cloneDeep(newCondition);
            this.$store.commit("caseDatabase/updateCondition", newCondition);
            // if(this.isSetCondition){
            this.exams.endCase = 20;
            this.isSearchLoading = true;
            await this.startFliter();
            this.isSearchLoading = false;
        },
        //检查查找
        async startFliter() {
            // if(this.isSetCondition){
            this.exams.endCase = 20;
            let content = this.searchInfo.searchInputKey;
            if (this.exams.oldIds.length < 1 && (this.searchInfo.searchInputKey || this.searchInfo.imageSrc)) {
                return;
            }
            let op = {
                ids: this.exams.oldIds,
                text: content,
                pageNo: 1,
                pageSize: this.exams.sliceCount,
                condition: this.filters.condition,
            };
            let datas = await this.searchAction("fliter_by_condition", op);
            if (datas && !datas.error) {
                this.exams.currentExamList = datas.data.list;
                this.exams.ids = datas.data.ids;
                if (this.exams.currentExamList.length < this.exams.ids.length) {
                    // 当当前设置的词条比筛选出来的多，就让其还能继续滑动获取数据
                    this.caseIsFinished = false;
                } else {
                    this.caseIsFinished = true;
                }
            }
        },

        isArrary(a) {
            return a instanceof Array;
        },
        //聚焦
        async clickInput() {
            this.isShowHot = true;
            let oldText = this.searchInfo.searchInputKey;
            this.searchInfo = cloneDeep(this.defaultSearchInfo);
            this.imageDraw = "";
            this.searchInfo.searchInputKey = oldText;
            this.exams.endCase = 20;
            this.exams.msg = "";
            this.exams.currentExamList = [];
            this.exams.ids = [];
            this.exams.oldIds = [];
            this.clearFilterCondition();
            let op = {
                text: "回声",
            };
            this.isSearchLoading = true;
            let datas = await this.searchAction("find_by_keyword", op);
            if (datas && !datas.error) {
                this.exams.currentExamList = datas.data.list;
                this.exams.ids = datas.data.list.map((v) => {
                    return v.collection_id;
                });
                this.exams.oldIds = cloneDeep(this.exams.ids);
            }
            this.isSearchLoading = false;
        },
        // 失焦事件
        blurInput() {
            this.startTextSearch();
            //this.sortAndSaveTag(this.searchInfo.searchInputKey)
        },
        //相机打开
        async onSelectActions(item) {
            this.isShowHot = false;
            let action = "";
            if (item.key === 0) {
                //拍照
                action = "camera";
            } else if (item.key === 1) {
                //图库
                action = "photoalbum";
            } else {
            }
            if (action && action.length > 0) {
                // 打开文件
                try {
                    await Tool.queryAppPermissions(["CAMERA"]);
                    Tool.createCWorkstationCommunicationMng({
                        name: "openCameraOrPhotoAlbum",
                        emitName: "NotifyOpenCameraOrPhotoAlbum",
                        params: { action: action },
                        timeout: null,
                    }).then((res) => {
                        // console.log(res)
                        if (res.error_code == "0") {
                            this.searchInfo = cloneDeep(this.defaultSearchInfo);
                            this.imageDraw = "";
                            let base64_data = res.base64_data;
                            let file_type = "image/" + res.name.replace(/.+\./, "");
                            let size = res.size; //Tool.showSize(base64_data)
                            console.log("size:", size);
                            let blobFile = Tool.dataURLtoBlob(base64_data, file_type);
                            let file = Tool.blobToFile(blobFile, res.name);
                            let base64_obj = "data:" + file_type + ";base64," + base64_data;
                            let fileObj = {
                                file: file,
                                size: size,
                                file_type: file_type,
                                name: res.name,
                                base64_obj: base64_obj,
                                type: "file", //text,url,file
                            };
                            this.$store.commit("caseDatabase/updateSearchParams", fileObj);
                            this.uploadPicture(fileObj);
                        }
                    });
                } catch (error) {
                    Toast(error);
                }
            }
        },
        //弹出相机或者本地相册
        selectPicture() {
            this.isShowHot = false;
            this.searchInfo.searchInputKey = "";
            this.$store.commit("caseDatabase/updateSearchParams", {
                type: "text", //text,url,file
                content: "",
            });
            this.isShowSelectType = true;
            //模拟本地
            // this.$refs.selectPictureRef.click()
        },
        //设置显示病例
        setPatientImage(item) {
            this.exams.pathologyInfo = item;
        },
        setGalleryState(val) {
            if (val === 0) {
                this.galleryState = 0;
                this.showSearchImage = false;
                this.showCaseImage = false;
            }
            if (val === 1) {
                this.galleryState = 1;
                this.showSearchImage = true;
                this.showCaseImage = false;
            }
            if (val === 2) {
                this.galleryState = 2;
                this.showCaseImage = true;
                this.showSearchImage = false;
            }
        },
        //隐藏病例图片
        showImageEvent(flag) {
            this.exams.pathologyInfo = {};
            this.setGalleryState(0);
        },
        //显示病例图片
        showGallery(item) {
            const collectionID = item.collection_id;
            // window.main_screen.sendAnalyzeClick({
            //     collectionID
            // })
            service
                .requestAiAnalyzeWithUrl({
                    method: "update.click." + collectionID,
                    condition: { type: "BREASTSEARCH" },
                })
                .then(async (res) => {
                    console.error(res);
                });
            this.setGalleryState(1);
        },
        //加载病例
        async loadCase() {
            // if(this.exams.endCase >= this.exams.ids.length){
            //     this.$refs.loadmore.onBottomLoaded()
            // }else{
            //     this.exams.endCase += this.exams.sliceCount;
            //     this.$refs.loadmore.onBottomLoaded()
            // }
            let pageNo = Math.ceil(this.exams.endCase / 20) + 1;
            let ids = this.exams.ids.slice(this.exams.endCase, this.exams.endCase + 20);
            let op = {
                ids: ids,
                pageNo: pageNo,
                pageSize: this.exams.sliceCount,
            };
            this.isSearchLoading = true;
            let datas = await this.searchAction("find_by_ids", op);
            if (datas && !datas.error) {
                this.exams.currentExamList = this.exams.currentExamList.concat(datas.data.list);
                // that.exams.ids = datas.data.ids
                this.exams.endCase += 20;
                this.caseIsLoading = false;
            }
            this.isSearchLoading = false;
        },
        //上传文件
        async uploadPicture(options = {}) {
            // console.log('uploadPicture function',options)
            let roiObj = {
                roi: cloneDeep(this.searchInfo.roi),
                selectRoi: this.searchInfo.selectRoi,
            };
            this.searchInfo = cloneDeep(this.defaultSearchInfo);
            this.imageDraw = "";
            this.exams.endCase = 20;
            this.clearFilterCondition();
            this.exams.currentExamList = [];
            this.exams.ids = [];
            this.exams.oldIds = [];
            this.isSearchLoading = true;
            let that = this;
            let timer = null;
            let client = null;
            return new Promise((resolve, reject) => {
                let result = { error: false, msg: "", data: {} };
                let time_start = new Date().getTime();
                timer = setTimeout(() => {
                    result.msg = "timeout";
                    Toast(that.lang.requestTimeout);
                    that.isSearchLoading = false;
                    console.error("client:", client);
                    client && cancelUpload(client);
                    reject(result);
                    return;
                }, 30 * 1000);
                that.$once("hook:beforeDestroy", function () {
                    timer && clearTimeout(timer);
                });
                let roi = options.roi || [];
                let files = [];
                let URL = window.URL || window.webkitURL;
                if (options && options.file && options.file != null) {
                    files = that.isArrary(options.file) ? options.file : [options.file];
                } else {
                    files = that.$refs.selectPictureRef.files;
                }
                let base64_obj = options.base64_obj;
                // that.user = that.$store.state.user

                if (files && files.length > 0) {
                    let file = files[0];
                    if (that.$refs.selectPictureRef && that.$refs.selectPictureRef.value) {
                        that.$refs.selectPictureRef.value = [];
                    }
                    let file_type = file.name.replace(/.+\./, "");
                    that.searchInfo.name = file.name;
                    console.log("file_type:", file_type);
                    console.log("name:", file.name);
                    if (that.searchInfo.name.length > 20) {
                        that.searchInfo.name = that.searchInfo.name.substr(0, 15) + "...";
                    }
                    let is_legal_format = ["png", "jpg", "jpeg", "bmp"].indexOf(file_type.toLowerCase()) > -1;
                    if (!is_legal_format) {
                        Toast(that.lang.picture_is_only_jpg_jpeg_bmp_png);
                        timer && clearTimeout(timer);
                        result = { error: true, msg: that.lang.picture_is_only_jpg_jpeg_bmp_png, data: {} };
                        that.isSearchLoading = false;
                        that.searchInfo.msg = that.lang.picture_is_only_jpg_jpeg_bmp_png;
                        reject(result);
                        return;
                    }
                    let is_legal_size = file.size <= 1024 * 1024 * 10; ///不能大于10M
                    if (options.size && options.size != null) {
                        is_legal_size = parseFloat(options.size) <= 1024 * 1024 * 10; ///不能大于10M
                    }
                    let reader = new FileReader();
                    reader.onload = function (e) {
                        let data = e.target.result;
                        let image = new Image();
                        image.onload = async function () {
                            console.log("onload:", 1);
                            let width = image.width;
                            let height = image.height;
                            let is_legal_resolution_ratio = image.width > 0 && image.height > 0;
                            let tempUrl = URL.createObjectURL(file);
                            that.searchInfo.imageSrc = tempUrl;
                            that.searchInfo.file = file;
                            if (is_legal_format && is_legal_size && is_legal_resolution_ratio) {
                                let file_id = that.user.uid + that.user.client_uuid + new Date().getTime();
                                let destination =
                                    that.systemConfig.serverInfo.oss_attachment_server.sub_dir +
                                    "/" +
                                    "AiSearch" +
                                    "/" +
                                    that.user.uid +
                                    "/" +
                                    file_id +
                                    "." +
                                    file_type;
                                that.searchInfo.url =
                                    that.systemConfig.serverInfo.oss_attachment_server.playback_https_addr +
                                    "/" +
                                    destination;
                                let ping = true;
                                await Tool.handleAfterMainScreenCreated(ping);
                                client = await uploadFile({
                                    bucket: that.systemConfig.serverInfo.oss_attachment_server.bucket,
                                    filePath: destination,
                                    file,
                                    callback: async (event, data) => {
                                        if ("error" == event) {
                                            //上传失败文件记录到本地缓存
                                            timer && clearTimeout(timer);
                                            console.error("FileReader error:", e);
                                            result = { error: true, msg: that.lang.file_upload_exception, data: {} };
                                            Toast(that.lang.file_upload_exception);
                                            that.isSearchLoading = false;
                                            reject(result);
                                            return;
                                        } else {
                                            if (event && event == "complete") {
                                                timer && clearTimeout(timer);
                                                let time_end = new Date().getTime();
                                                let num = 30000 - time_end + time_start;
                                                let param = {
                                                    type: "image",
                                                    CLASSIFICATION: "BREAST",
                                                    FILE: {
                                                        filename: that.searchInfo.name,
                                                        url: that.searchInfo.url,
                                                    },
                                                    splitCount: "",
                                                };
                                                if (roi && roi.length > 0) {
                                                    param.FILE.roi = roi;
                                                    that.searchInfo.roi = roiObj.roi;
                                                    that.searchInfo.selectRoi = roiObj.selectRoi;
                                                }
                                                let datas = await that.searchAction("find_by_image", param, num);
                                                if (datas && !datas.error) {
                                                    that.exams.currentExamList = datas.data.list;
                                                    that.exams.ids = datas.data.ids;
                                                    that.exams.oldIds = cloneDeep(datas.data.ids);
                                                    if (that.exams.currentExamList < 1) {
                                                        that.searchInfo.msg = that.lang.is_no_case_text;
                                                        if (datas.data.roi.length < 1) {
                                                            // that.searchInfo.msg = that.lang.picture_is_no_roi
                                                        }
                                                    }
                                                }
                                                timer && clearTimeout(timer);
                                                that.isSearchLoading = false;
                                                resolve(datas);
                                                return;
                                            }
                                        }
                                    },
                                });
                            } else {
                                timer && clearTimeout(timer);
                                console.log("Please choose an leagal image file.");
                                let msg = "";
                                result = { error: true, msg: msg, data: {} };
                                if (!is_legal_size) {
                                    msg = that.lang.upload_max_text + "10M";
                                }
                                if (!is_legal_resolution_ratio) {
                                    msg = that.lang.picture_is_too_blurred;
                                }
                                if (!is_legal_format) {
                                    msg = that.lang.picture_is_only_jpg_jpeg_bmp_png;
                                }
                                that.searchInfo.msg = msg;
                                result.msg = msg;
                                that.isSearchLoading = false;
                                reject(result);
                                return;
                            }
                        };
                        image.onerror = function (e) {
                            timer && clearTimeout(timer);
                            result = { error: true, msg: this.lang.get_picture_info_fail, data: {} };
                            console.error("new Image error:", e);
                            Toast(that.lang.get_picture_info_fail);
                            that.isSearchLoading = false;
                            reject(result);
                            return;
                        };
                        image.src = data;
                    };
                    reader.onerror = function (e) {
                        timer && clearTimeout(timer);
                        result = { error: true, msg: this.lang.get_picture_info_fail, data: {} };
                        console.error("FileReader error:", e);
                        Toast(that.lang.get_picture_info_fail);
                        that.isSearchLoading = false;
                        reject(result);
                        return;
                    };
                    reader.readAsDataURL(file);
                } else {
                    timer && clearTimeout(timer);
                    console.log("Please choose an image file.");
                    // that.isLatestExamlist = true
                    result = { error: true, msg: "Please choose an image file.", data: {} };
                    Toast(that.lang.reselect_upload_file);
                    that.isSearchLoading = false;
                    reject(result);
                    return;
                }
            }).catch(() => {});
        },
        // 获取最新病例
        async pictureUrlDeal(options) {
            let roiObj = {
                roi: cloneDeep(this.searchInfo.roi),
                selectRoi: this.searchInfo.selectRoi,
            };
            this.searchInfo = cloneDeep(this.defaultSearchInfo);
            this.imageDraw = "";
            this.exams.endCase = 20;
            this.exams.msg = "";
            this.searchInfo.msg = "";
            this.clearFilterCondition();
            this.exams.currentExamList = [];
            this.exams.ids = [];
            this.exams.oldIds = [];
            let that = this;
            let timer = null;
            return new Promise((resolve, reject) => {
                let time_start = new Date().getTime();
                let result = { error: false, msg: "", data: {} };
                timer = setTimeout(() => {
                    result.msg = "timeout";
                    Toast(that.lang.requestTimeout);
                    that.isSearchLoading = false;
                    reject(result);
                    return;
                }, 30 * 1000);
                that.$once("hook:beforeDestroy", function () {
                    timer && clearTimeout(timer);
                });
                let roi = options.roi || [];
                let image_url = options.realUrl || getRealUrl(options).serverRealUrl;
                if (image_url == "" || image_url == undefined || image_url == null) {
                    image_url = options.url;
                }
                that.searchInfo.url = image_url;
                that.searchInfo.imageSrc = image_url;
                that.searchInfo.file = that.searchInfo.url;
                that.searchInfo.isUrl = true;
                let URL = window.URL || window.webkitURL;
                let file_type = that.searchInfo.url.replace(/.+\./, "");
                that.searchInfo.name = that.searchInfo.url.split("/").pop();
                if (that.searchInfo.name.length > 20) {
                    that.searchInfo.name = that.searchInfo.name.substr(0, 15) + "...";
                }
                if (that.searchInfo.url && that.searchInfo.name) {
                    let is_legal_format = ["png", "jpg", "jpeg", "bmp"].indexOf(file_type.toLowerCase()) > -1;
                    if (!is_legal_format) {
                        Toast(that.lang.picture_is_only_jpg_jpeg_bmp_png);
                        result = { error: true, msg: that.lang.picture_is_only_jpg_jpeg_bmp_png, data: {} };
                        that.isSearchLoading = false;
                        that.searchInfo.msg = that.lang.picture_is_only_jpg_jpeg_bmp_png;
                        timer && clearTimeout(timer);
                        reject(result);
                        return;
                    }

                    let realImage = new Image();
                    realImage.onerror = function (e) {
                        timer && clearTimeout(timer);
                        result = { error: true, msg: this.lang.get_picture_info_fail, data: {} };
                        console.error("new Image error:", e);
                        Toast(that.lang.get_picture_info_fail);
                        that.isSearchLoading = false;
                        reject(result);
                        return;
                    };
                    realImage.onload = function () {
                        var xhr = new XMLHttpRequest();
                        xhr.open("HEAD", that.searchInfo.url, true);
                        xhr.onreadystatechange = async function () {
                            if (xhr.readyState == 4) {
                                if (xhr.status == 200) {
                                    timer && clearTimeout(timer);
                                    let time_end = new Date().getTime();
                                    let num = 30000 - time_end + time_start;
                                    let size = xhr.getResponseHeader("Content-Length");
                                    let is_legal_size = size <= 1024 * 1024 * 10; ///不能大于10M
                                    let is_legal_resolution_ratio = realImage.width > 0 && realImage.height > 0;
                                    if (
                                        is_legal_format &&
                                        is_legal_size &&
                                        is_legal_resolution_ratio &&
                                        !timer._destroyed
                                    ) {
                                        let param = {
                                            type: "image",
                                            CLASSIFICATION: "BREAST",
                                            FILE: {
                                                filename: that.searchInfo.name,
                                                url: that.searchInfo.url,
                                            },
                                            splitCount: "",
                                        };
                                        if (roi && roi.length > 0) {
                                            param.FILE.roi = roi;
                                            that.searchInfo.roi = roiObj.roi;
                                            that.searchInfo.selectRoi = roiObj.selectRoi;
                                        }
                                        let datas = await that.searchAction("find_by_image", param, num);
                                        if (datas && !datas.error) {
                                            that.exams.currentExamList = datas.data.list;
                                            that.exams.ids = datas.data.ids;
                                            that.exams.oldIds = cloneDeep(datas.data.ids);
                                            if (that.exams.currentExamList < 1) {
                                                that.searchInfo.msg = that.lang.is_no_case_text;
                                                if (datas.data.roi.length < 1) {
                                                    // that.searchInfo.msg = that.lang.picture_is_no_roi
                                                }
                                            }
                                        }
                                        timer && clearTimeout(timer);
                                        that.isSearchLoading = false;
                                        resolve(datas);
                                        return;
                                    } else {
                                        timer && clearTimeout(timer);
                                        console.log("Please choose an leagal image file.");
                                        let msg = "";
                                        if (!is_legal_size) {
                                            msg = that.lang.upload_max_text + "10M";
                                        }
                                        if (!is_legal_resolution_ratio) {
                                            msg = that.lang.picture_is_too_blurred;
                                        }
                                        if (!is_legal_format) {
                                            msg = that.lang.picture_is_only_jpg_jpeg_bmp_png;
                                        }
                                        that.searchInfo.msg = msg;
                                        result.msg = msg;
                                        that.isSearchLoading = false;
                                        reject(result);
                                        return;
                                    }
                                } else {
                                    timer && clearTimeout(timer);
                                    result = { error: true, msg: that.lang.get_picture_info_fail, data: {} };
                                    Toast(that.lang.get_picture_info_fail);
                                    that.isSearchLoading = false;
                                    reject(result);
                                    return;
                                }
                            }
                        };
                        xhr.send(null);
                    };
                    if (this.systemConfig.serverInfo.network_environment === 1) {
                        that.searchInfo.url = Tool.replaceInternalNetworkEnvImageHost(that.searchInfo.url);
                    }
                    realImage.setAttribute("crossOrigin", "anonymous");
                    realImage.src = `${that.searchInfo.url}?temp=${Date.now()}`;
                } else {
                    timer && clearTimeout(timer);
                    console.log("Please choose an image file.");
                    // that.isLatestExamlist = true
                    result = { error: true, msg: "Please choose an image file.", data: {} };
                    Toast(that.lang.reselect_upload_file);
                    that.isSearchLoading = false;
                    reject(result);
                    return;
                }
            });
        },
        //隐藏搜图原图片
        async showSearchImageEvent(param) {
            let that = this;
            this.setGalleryState(0);
            that.clearFilterCondition();
            if (param.flag) {
                //用户改变了选中roi
                that.isSearchLoading = true;
                let roi =
                    that.searchInfo && that.searchInfo.roi && that.searchInfo.roi[param.roiIndex]
                        ? that.searchInfo.roi[param.roiIndex]
                        : "";
                that.searchInfo.msg = "";
                that.searchInfo.selectRoi = param.roiIndex;
                that.searchInfo.endCase = 0;
                that.exams.currentExamList = [];
                that.exams.ids = [];
                that.exams.endCase = 20;
                let op = {
                    FILE: {
                        url: that.searchInfo.url,
                        roi: roi,
                    },
                };
                let datas = await that.searchAction("find_by_image", op);
                if (datas && !datas.error) {
                    that.exams.currentExamList = datas.data.list;
                    that.exams.ids = datas.data.ids;
                    that.exams.oldIds = cloneDeep(datas.data.ids);
                }
                that.isSearchLoading = false;
            } else {
                // that.exams.currentExamList = cloneDeep(that.exams.ids)
            }
        },
        //搜索历史
        initTagHistory() {
            const localTag = window.localStorage.getItem("caseTagHistoryList") || "";
            if (localTag == "") {
                return;
            }
            this.history.list = localTag.split(",");
            this.history.showList = this.history.list.slice(0, this.history.collapseNum);
            if (this.history.list.length > this.history.collapseNum) {
                this.history.isShowCollapse = true;
                this.history.isCollapseTag = true;
            }
        },
        //排序历史记录
        sortAndSaveTag(keyword) {
            //重排和保存tag
            if (keyword && keyword.length > 0) {
                const index = this.history.list.indexOf(keyword);
                if (index > -1) {
                    this.history.list.splice(index, 1);
                    this.history.list.unshift(keyword);
                } else {
                    if (this.history.list.length >= this.maxNum) {
                        this.history.list.pop();
                    }
                    this.history.list.unshift(keyword);
                }
                if (this.history.list.length > this.history.collapseNum) {
                    this.history.isShowCollapse = true;
                    this.history.isCollapseTag = true;
                }
                this.collapseTag();
                window.localStorage.setItem("caseTagHistoryList", this.history.list.join(","));
            }
        },
        //历史记录折叠与展开
        toggleCollapseTag(isCollapseTag) {
            this.history.isCollapseTag = isCollapseTag;
            this.collapseTag();
        },
        collapseTag() {
            if (this.history.isCollapseTag) {
                this.history.showList = this.history.list.slice(0, this.history.collapseNum);
            } else {
                this.history.showList = this.history.list;
            }
        },
        //历史记录清空
        clearTag() {
            Tool.openMobileDialog({
                message: this.lang.delete_all_history_tip,
                showRejectButton: true,
                confirm: () => {
                    this.history.showList = [];
                    this.history.list = [];
                    this.history.isShowCollapse = false;
                    this.history.isCollapseTag = false;
                    window.localStorage.setItem("caseTagHistoryList", "");
                },
            });
        },
        //删除历史记录
        deleteTag(tag) {
            Tool.openMobileDialog({
                message: this.lang.delete_history_tip,
                showRejectButton: true,
                confirm: () => {
                    const index = this.history.list.indexOf(tag);
                    this.history.list.splice(index, 1);
                    if (this.history.list.length <= this.history.collapseNum) {
                        this.history.isShowCollapse = false;
                        this.history.isCollapseTag = false;
                    }
                    this.collapseTag();
                    window.localStorage.setItem("caseTagHistoryList", this.history.list.join(","));
                },
            });
        },
    },
    destroyed() {
        this.$store.commit("caseDatabase/updateSearchParams", {
            type: "text", //text,url,file
            content: "",
        });
    },
};
</script>
<style lang="scss">
.case_database_page {
    .color_frame {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 0.3rem;
        z-index: 0;
        .color_item_1 {
            height: 20%;
            background-color: rgb(255, 103, 92);
        }
        .color_item_2 {
            height: 20%;
            background-color: #00c59d;
        }
        .color_item_3 {
            height: 20%;
            background-color: rgb(255, 177, 68);
        }
        .color_item_4 {
            height: 20%;
            background-color: rgb(86, 200, 255);
        }
        .color_item_5 {
            height: 20%;
            background-color: rgb(116, 128, 234);
        }
    }
    .container {
        overflow: auto;
        position: relative;
        display: flex;
        flex-direction: column;
        .no_data {
            margin-top: 40%;
            text-align: center;
            font-size: 0.8rem;
            color: #666;
        }

        .search_loading {
            z-index: 10;
            width: 100%;
            height: 6rem;
        }
        .scroll_box {
            flex: 1;
            overflow: auto;
            .search_wrap {
                position: relative;
                background-color: #c9ede6;
                z-index: 2;
                display: flex;
                align-items: center;
                width: 100%;
                .key_btn {
                    color: #666;
                    font-size: 0.8rem;
                    font-weight: 500;
                    height: 1rem;
                    margin-left: 0.25rem;
                    padding: 0 0.5rem;
                    border-right-style: solid;
                    border-right-color: #b5adad;
                    border-right-width: 1px;
                    white-space: nowrap;
                }
                .search_btn {
                    background-color: #c9ede6;
                    border: none;
                    line-height: 1.9rem;
                    font-size: 0.8rem;
                    text-align: left;
                    color: #605757;
                    flex: 1;
                    padding-right: 2rem;
                    padding-left: 0.5rem;
                    min-width: 0;
                }
                span,
                i {
                    vertical-align: middle;
                }
                .svg_icon_search {
                    font-size: 0.95rem;
                    position: absolute;
                    right: 0.725rem;
                    // width: 1.9rem;
                    // height: 0.9rem;
                    fill: rgb(4, 167, 134);
                    color: rgb(4, 167, 134);
                    z-index: 2;
                }
                .svg_icon_camera {
                    font-size: 1.2rem;
                    // border-right: solid 1px #b5adad;
                    position: absolute;
                    right: 2.95rem;
                    // width: 2rem;
                    // height: 1rem;
                    fill: rgb(4, 167, 134);
                    color: rgb(4, 167, 134);
                    z-index: 2;
                }
                .select_picture {
                    display: none;
                }
            }
            .search_key {
                margin: 0.45rem;
                display: flex;
                flex-wrap: wrap;
                position: relative;
                .case_tag {
                    background-color: #e4e4e4;
                    color: #444;
                    padding: 0.2rem 0.4rem;
                    border-radius: 0.2rem;
                    font-size: 0.7rem;
                    margin: 0.3rem;
                }
                & > p {
                    width: 100%;
                    font-size: 0.8rem;
                    color: #000;
                    padding: 0.5rem 0.3rem;
                }
                .icon-down-end,
                .icon-up-start {
                    position: absolute;
                    right: 0;
                    bottom: 0;
                }
                .icon-delete {
                    position: absolute;
                    right: 0;
                    top: 0.5rem;
                    color: #666;
                }
            }
            .case_list_container {
                display: flex;
                flex-direction: column;
                z-index: 1;
                padding: 0rem 0.8rem 0 0;
                margin-top: 0.5rem;
                margin-bottom: 0.5rem;
                position: relative;
                box-sizing: border-box;
                margin-left: 0.8rem;
                & > p {
                    width: 100%;
                    font-size: 0.7rem;
                    padding: 1rem 0 0.7rem 0;
                    color: #6d7271;
                    border-bottom-width: 1px;
                    border-bottom-style: solid;
                    border-bottom-color: #00c59d;
                }
                .latest_examlist_tip {
                    color: #252626;
                    font-weight: border;
                    font-size: 0.8rem;
                }
                .examlist_image_tip_container {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    width: 100%;
                    font-size: 0.7rem;
                    color: #6d7271;
                    border-bottom-width: 1px;
                    border-bottom-style: solid;
                    border-bottom-color: #00c59d;
                    line-height: 30px;
                    vertical-align: middle;
                    padding: 2px;
                }
                .examlist_image_tip {
                    width: 30px;
                    height: 30px;
                    vertical-align: middle;
                }
                .examlist_image_text {
                    display: inline;
                    vertical-align: middle;
                    line-height: 30px;
                    flex: 1 auto;
                }
                .examlist_image_button_wrap {
                    width: 100%;
                    height: 100%;
                    text-align: center;
                    object-fit: contain;
                }
                .examlist_image_button {
                    color: #00c59d;
                    width: 30%;
                    height: 30%;
                    border-radius: 9px;
                    -webkit-border-radius: 9px;
                    -moz-border-radius: 9px;
                }

                .examlist_image_close {
                    display: flex;
                    color: #00c59d;
                    height: 25px;
                    margin-top: -25px;
                    width: 100%;
                    .left {
                        width: 50%;
                        text-align: left;
                        float: left;
                    }
                    .right {
                        width: 50%;
                        text-align: right;
                        float: right;
                    }
                }

                .case_item {
                    width: 100%;
                    padding-bottom: 0.5rem;
                    position: relative;
                    overflow: hidden;
                    border-bottom-width: 1px;
                    border-bottom-style: solid;
                    border-bottom-color: #00c59d;
                    display: inline-flex;
                    .patient_info {
                        width: 45%;
                        padding-top: 0.5rem;
                        padding-left: 0.7rem;
                        display: flex;
                        flex-direction: column;
                        justify-content: flex-start;
                        .patient_info_title {
                            font-size: 0.8rem;
                            margin-bottom: 0.3rem;
                        }
                        .patient_info_item {
                            position: relative;
                            display: flex;
                            justify-content: center;
                            font-size: 0.65rem;
                            color: rgb(101, 109, 112);
                            line-height: 1.5;
                            text-align: left;
                            p {
                                flex: 1;
                            }
                            &:nth-of-child(2)::after {
                                content: "";
                                position: absolute;
                                height: 1px;
                                background-color: rgb(171, 181, 186);
                                transform: scaleY(0.5);
                                left: 0;
                                right: 0;
                                bottom: 0;
                            }
                        }
                        .patient_info_result {
                            line-height: 1.5;
                            font-size: 0.65rem;
                            color: #0c9176;
                        }
                    }

                    .image_list_first {
                        width: 55%;
                        .image_item,
                        .folder_item {
                            float: left;
                            width: 100%;
                            height: 100%;
                            box-sizing: border-box;
                            position: relative;
                            padding-right: 0.15rem;
                            padding-bottom: 0.25rem;
                            overflow: hidden;
                            display: flex;
                            flex-direction: column;
                            align-content: center;
                            justify-items: center;
                            .images_count {
                                position: absolute;
                                right: 0.6rem;
                                top: 0.6rem;
                                text-align: center;
                                background-color: #6d7271;
                                color: #c9cfce;
                                border-radius: 0.4rem;
                                width: 1.8rem;
                                line-height: 1rem;
                                letter-spacing: 0.1rem;
                                font-size: 0.65rem;
                                font-style: normal;
                                z-index: 1;
                            }
                            .image_item_container {
                                position: relative;
                                padding-top: 75%;
                                height: 0;
                                background-color: #000;
                                overflow: hidden;
                                border-radius: 0.2rem;
                                .image {
                                    max-width: 100%;
                                    max-height: 100%;
                                    position: absolute;
                                    top: 50%;
                                    left: 50%;
                                    transform: translate(-50%, -50%);
                                }
                            }
                        }
                        .folder_item {
                            float: right;
                            .image_item_container {
                                background-color: transparent;
                                .exam_down,
                                .exam_up {
                                    fill: gray;
                                    color: gray;
                                    position: absolute;
                                    top: 50%;
                                    left: 50%;
                                    transform: translate(-50%, -50%);
                                    width: 1rem;
                                    height: 1rem;
                                }
                            }
                        }
                    }
                }
                .icon-eye,
                .icon-eye-close {
                    position: absolute;
                    right: 0.8rem;
                    top: 1rem;
                }
            }
        }
    }
    .condition_icon {
        position: relative;
        .red_point {
            width: 0.4rem;
            height: 0.4rem;
            border-radius: 0.4rem;
            content: "";
            background-color: red;
            position: absolute;
            top: 0;
            left: 0.8rem;
        }
        .icon_shaixuan_ {
            font-size: 1rem;
            fill: #9ca8ad;
        }
    }
    .icon_back_ {
        color: black;
    }
}
</style>
