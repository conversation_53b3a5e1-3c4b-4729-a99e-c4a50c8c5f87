<template>
    <div>
        <el-dialog
            :close-on-click-modal="false"
            :visible.sync="isShowGallery"
            width="90%"
            append-to-body
            custom-class="multicenter_gallery_dialog"
            v-loading="loading"
            :before-close="destroyGallery"
        >
            <div class="left-gallery" ref="leftGallery">
                <div class="gallery-top" id="gallery-box">
                    <pdf-reader ref="fileReader" :url="readingFile.url" v-if="isShowFileReader"></pdf-reader>
                    <div class="main_swiper mui-slider" id="my_slider" ref="topSwiper">
                        <div class="mui-slider-group">
                            <div v-for="(file, f_index) in imageList" class="mui-slider-item" :key="f_index">
                                <template v-if="file.msg_type == msg_type.Image || file.msg_type == msg_type.OBAI || file.msg_type == msg_type.Frame">
                                    <div class="">
                                        <!-- <div class="loading_span" v-loading="!file.loaded"></div> -->
                                        <img :src="file.error_image||file.realUrl" class="preview" draggable="false" @error="setErrorImage(file)"/>
                                    </div>
                                </template>
                                <template v-else-if="file.msg_type == msg_type.Cine || file.msg_type == msg_type.Video">
                                    <!-- <div class="loading_span" v-loading="!file.loaded"></div> -->
                                    <video
                                        v-if="!isCef && f_index == currentSliderIndex"
                                        class="main_video"
                                        :poster="file.loaded ? file.realUrl : file.url_local"
                                        :src="file.mainVideoSrc"
                                        controls
                                        @error="playVideoError(f_index)"
                                    ></video>
                                    <video
                                        v-if="!isCef && file.img_has_gesture_video"
                                        :poster="file.loaded ? file.realUrl : file.url_local"
                                        :src="file.gestrueVideoSrc"
                                        class="gesture_video"
                                        controls
                                    ></video>
                                </template>
                                <template v-else>
                                    <img
                                        src="static/resource_pc/images/poster2.jpg"
                                        class="preview"
                                        draggable="false"
                                    />
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="thumb_wrap">
                    <div class="thumb_loading" v-show="currentSliderIndex == -1" v-loading="true"></div>
                    <div ref="thumb_scroll_wrap" class="thumb_scroll_wrap">
                        <vue-slide :key="slideKey" class="thumb_slide" ref="thumb_slide" :ops="ops">
                            <div @mousewheel.prevent.stop class="clearfix" style="display: flex">
                                <div
                                    v-for="(file, f_index) in imageList"
                                    class="thumb_item"
                                    :class="{ current_thumb: f_index == currentSliderIndex }"
                                    @mousedown="mousedownThumb($event, f_index)"
                                    @mouseup="mouseupThumb($event, f_index)"
                                    :key="f_index"
                                >
                                    <!-- <span v-if="file.from_repository&&!file.reviewed" class="unread_tip"></span> -->
                                    <template v-if="file.msg_type == msg_type.Image || file.msg_type == msg_type.OBAI || file.msg_type == msg_type.Frame">
                                        <img v-if="file.url" :src="file.error_image||file.url" class="preview" draggable="false" @error="setErrorImage(file)"/>
                                        <div v-else class="empty_thump"></div>
                                        <i class="icon iconfont iconpicture"></i>
                                    </template>
                                    <template
                                        v-else-if="file.msg_type == msg_type.Cine || file.msg_type == msg_type.Video"
                                    >
                                        <img :src="file.url" class="preview" draggable="false" />
                                        <i class="icon iconfont iconvideo_fill_light"></i>
                                    </template>
                                    <template v-else>
                                        <img
                                            src="static/resource_pc/images/poster2.jpg"
                                            class="preview"
                                            draggable="false"
                                        />
                                    </template>
                                    <span v-if="file.imageType" class="image_tag" :class="'tag_' + file.imageType">{{
                                        imageTagType[file.imageType]
                                    }}</span>
                                    <p v-if="file.protocol_view_name" class="view_name">
                                        {{ file.protocol_view_name }}
                                    </p>
                                </div>
                            </div>
                        </vue-slide>
                    </div>
                    <i @click="lastPage" class="icon iconfont iconsanjiaoxing last_page"></i>
                    <i @click="nextPage" class="icon iconfont iconsanjiaoxing next_page"></i>
                </div>
            </div>
            <div class="right-gallery" ref="rightGallery" :style="{ zIndex: onlyleft ? '-1' : '10' }">
                <slot></slot>
            </div>
            <template #footer> </template>
        </el-dialog>
    </div>
</template>

<script>
import base from "../../../../lib/base";
import service from "../../../../service/multiCenterService.js";
import { mapGetters } from "vuex";
import {
    initVideoPage,
    initDcm,
    destroyGallery,
    getRealtimeConsultationSize,
} from "../../../../lib/common_realtimeVideo";
import iworksTool from "../../../../lib/iworksTool";
import { checkResourceType,getResourceTempStatus,getRealUrl, deDuplicatingImg } from "../../../../lib/common_base";
import vueSlide from "vuescroll";
import { cloneDeep } from "lodash";

export default {
    name: "MrGallery",
    mixins: [base,iworksTool],
    props: {
        loading: {
            type: Boolean,
            default: false,
        },
        onlyleft: {
            type: Boolean,
            default: false,
        },
    },
    components: { vueSlide,pdfReader: () => import(/* webpackPrefetch: true */ '../../../../components/pdfReader')   },
    watch: {
        galleryList(cur, prev) {
            this.$nextTick(() => {
                this.updateSwiper();
            });
        },
    },
    computed: {
        currentConfig() {
            return this.$store.state.multicenter.currentConfig;
        },
        msg_type() {
            return this.systemConfig.msg_type;
        },
    },
    data() {
        return {
            checkResourceType,
            getResourceTempStatus,
            isShowGallery: false,
            currentSliderIndex: -1,
            swiperTop: null,
            mousedownThumpPoint: null,
            exam: null,
            isShowReject: false,
            rejectContent: "",
            rejcetClick: false,
            imageList: [],
            ops: {
                vuescroll: {
                    mode: "slide",
                    sizeStrategy: "percent",
                    detectResize: true,
                    /** 锁定一种滚动方向， 锁定的方向为水平或者垂直方向上滑动距离较大的那个方向 */
                    locking: true,
                },
                scrollPanel: {
                    scrollingY: false,
                },
            },
            playRealtimeVideo: false, //是否正在播放实时视频
            currentFile: null,
            isExpand: false,
            isPlayVideo: false,
            imageTag: [],
            readingFile: {},
            isShowFileReader: false,
            slideKey:0,
        };
    },
    mounted() {
        this.$root.eventBus.$off("expandRightWidth").$on("expandRightWidth", this.expandRightWidth);
        this.$root.eventBus.$off("closeVideo").$on("closeVideo", this.closeVideo);
        this.$root.eventBus.$off("openBeforeVideo").$on("openBeforeVideo", this.openBeforeVideo);
        this.$root.eventBus.$off("regainVideo").$on("regainVideo", this.regainVideo);
        document.addEventListener("visibilitychange", () => {
            var isHidden = document.hidden;
            if (isHidden) {
                this.destroyGallery();
            }
        });
    },
    beforeDestroy() {
        this.destroyGallery();
    },
    methods: {
        openGallery(image, index, exam) {
            this.isShowGallery = true;
            this.isShowFileReader = false;
            this.exam = exam;
            this.slideKey = Date.now();
            console.log("openGallery", exam);
            exam.imageList = exam.image_list
            if (exam.iworks_protocol) {
                this.setIworksProtocol(exam.iworks_protocol)
            }
            if (exam.iworks_protocol_execution) {
                this.setIworksProtocol(exam.iworks_protocol_execution)
            }
            this.parseIworksImage(exam);
            this.imageList = exam.iworksImages.concat(exam.noneIworksImages);
            this.imageList.map((item) => {
                item.realUrl = getRealUrl(item, this.msg_type);
                item.mainVideoSrc = this.getMainVideoSrc(item);
            });
            this.$nextTick(() => {
                this.initSwiper(image, index);
                if (this.onlyleft) {
                    this.$refs.leftGallery.style.width = "100%";
                }
            });
        },
        destroyGallery() {
            this.isShowGallery = false;
            this.isShowFileReader = false;
            destroyGallery();
            if (!this.isCef) {
                //浏览器关闭画廊停止播放视频
                let videos = document.querySelectorAll("video");
                for (let video of videos) {
                    video.pause();
                }
            }
        },
        closeVideo() {
            destroyGallery();
        },
        openBeforeVideo() {
            this.changeHandler(this.currentSliderIndex);
        },
        initSwiper(image, index) {
            var that = this;
            document.getElementById("my_slider").addEventListener("slide", that.swiperTopChange);

            var gallery = window.mui(".mui-slider");
            this.swiperTop = gallery.slider();

            this.positionToIndex(index);
            this.changeHandler(index);
        },
        positionToIndex(index) {
            this.slideTop(index);
        },
        slideTop(index) {
            this.currentSliderIndex = index;
            if (!this.swiperTop) {
                var gallery = window.mui(".mui-slider");
                this.swiperTop = gallery.slider();
            }
            this.$nextTick(() => {
                this.swiperTop.gotoItem(index, 0);
                this.changeHandler(index);
            });
        },
        swiperTopChange(event) {
            this.currentSliderIndex = event.detail.slideNumber;
            this.slideThumb(this.currentSliderIndex);
            this.changeHandler(event.detail.slideNumber);
        },
        changeHandler(index) {
            this.currentFile = this.imageList[index];
            let domRect = getRealtimeConsultationSize(this.$refs.topSwiper);
            initVideoPage({ file: this.currentFile, domRect });
            initDcm(this.currentFile);
            this.initFileReaderIfNeed(this.currentFile);
            let systemConfig = this.systemConfig;
            if (
                this.currentFile.msg_type == systemConfig.msg_type.Video ||
                this.currentFile.msg_type == systemConfig.msg_type.Cine ||
                this.currentFile.msg_type == systemConfig.msg_type.RealTimeVideoReview ||
                this.currentFile.msg_type == systemConfig.msg_type.VIDEO_CLIP
            ) {
                this.isPlayVideo = true;
            } else {
                this.isPlayVideo = false;
            }
            if (this.isExpand) {
                this.expandRightWidth("60%");
            }
        },
        getMainVideoSrc(imageObj){
            var that=this;
            let mainVideoSrc='';
            if(this.checkResourceType(imageObj)==='video'||
            this.checkResourceType(imageObj) === 'review_video'
            ){
                if (imageObj.msg_type==this.systemConfig.msg_type.Video||imageObj.img_type_ex==this.systemConfig.msg_type.Video) {
                    mainVideoSrc=imageObj.url.replace(imageObj.thumb,"")
                }else if(imageObj.msg_type==this.systemConfig.msg_type.Cine||imageObj.img_type_ex==this.systemConfig.msg_type.Cine){
                    if (this.getResourceTempStatus(imageObj.resource_id,'mainVideoSrc')) {
                        //存在mainVideoSrc直接播放该地址
                        mainVideoSrc=this.getResourceTempStatus(imageObj.resource_id,'mainVideoSrc')
                    }else if(imageObj.mainVideoSrc){
                        mainVideoSrc = imageObj.mainVideoSrc
                    }else{
                        mainVideoSrc=imageObj.url.replace('thumbnail.jpg','DeviceVideo.');
                        mainVideoSrc+=imageObj.img_encode_type
                    }
                }
            }
            return mainVideoSrc;
        },
        parsePdf(file) {
            let obj = {
                file_type: "pdf",
            };
            let arr = file.url.split("/");
            arr.pop();
            obj.file_name = arr.pop();
            obj.url = file.url.replace("thumbnail.jpg", "SingleFrame.pdf");
            return obj;
        },
        initFileReaderIfNeed(file) {
            console.log(file);
            this.isShowFileReader = false;
            this.$nextTick(()=>{
                if (file.msg_type == this.systemConfig.msg_type.File) {
                    this.isShowFileReader = true;
                    this.readingFile = file;
                    // this.$nextTick(() => {
                    //     this.$refs.fileReader.initPage();
                    // });
                } else if (
                    file.msg_type == this.systemConfig.msg_type.Frame&&file.img_encode_type &&
                file.img_encode_type.toUpperCase() == "PDF"
                ) {
                    this.isShowFileReader = true;
                    const pdfFile = this.parsePdf(file);
                    this.readingFile = pdfFile;
                    // this.$nextTick(() => {
                    //     this.$refs.fileReader.initPage();
                    // });
                }
            })

        },
        updateSwiper() {
            if (this.swiperTop) {
                //图片张数改变时更新画廊
                console.log("updateSwiper");
                this.swiperTop.refresh();
                window.gallery = this;
                setTimeout(() => {
                    this.slideThumb(this.currentSliderIndex);
                }, 300);
            }
        },
        slideThumb(index) {
            let thumb_slide = this.$refs.thumb_slide;
            let thumb_scroll_wrap = this.$refs.thumb_scroll_wrap;
            let scroll_width = thumb_scroll_wrap.clientWidth;
            let left = index * 157 - scroll_width / 2 + 78;
            thumb_slide && thumb_slide.scrollTo({ x: left }, 100);
        },
        mousedownThumb(event, index) {
            console.log(index);
            this.mousedownThumpPoint = {
                x: event.x,
                y: event.y,
            };
        },
        mouseupThumb(event, index) {
            let offsetX = this.mousedownThumpPoint.x - event.x;
            let offsetY = this.mousedownThumpPoint.y - event.y;
            if (Math.abs(offsetX) < 20 && Math.abs(offsetY) < 20) {
                if (index == this.currentSliderIndex) {
                    this.initDcmIfNeed(this.currentFile);
                    return;
                }
                this.slideThumb(index);
                this.slideTop(index);
            }
        },
        initDcmIfNeed(file) {
            if (file && file.msg_type == this.msg_type.Frame&& file.img_encode_type && file.img_encode_type.toUpperCase()===this.systemConfig.file_type.DCM) {
                //打开dicom播放器
                var dcm_url = file.url.replace("thumbnail.jpg", `SingleFrame.${file.img_encode_type}`);
                window.CWorkstationCommunicationMng.DisplayDrImage({
                    dcm_url: dcm_url,
                    thumbnail: file.url,
                    img_id: file.img_id,
                });
            }
        },
        lastPage() {
            let thumb_slide = this.$refs.thumb_slide;
            let left = thumb_slide.getPosition().scrollLeft;
            let thumb_scroll_wrap = this.$refs.thumb_scroll_wrap;
            let scroll_width = thumb_scroll_wrap.clientWidth;
            left -= scroll_width;
            thumb_slide && thumb_slide.scrollTo({ x: left }, 150);
        },
        nextPage() {
            let thumb_slide = this.$refs.thumb_slide;
            let left = thumb_slide.getPosition().scrollLeft;
            let thumb_scroll_wrap = this.$refs.thumb_scroll_wrap;
            let scroll_width = thumb_scroll_wrap.clientWidth;
            left += scroll_width;
            thumb_slide && thumb_slide.scrollTo({ x: left }, 150);
        },
        expandRightWidth(width) {
            console.log("MdGallery right gallery emit ");
            this.$refs.rightGallery.style.width = width;
            if (width == "60%") {
                this.isExpand = true;
            } else {
                this.isExpand = false;
            }
            let domRect = getRealtimeConsultationSize(this.$refs.topSwiper);
            initVideoPage({ file: this.currentFile, domRect });
            initDcm(this.currentFile);
        },
        regainVideo() {
            if (this.isPlayVideo) {
                window.CWorkstationCommunicationMng.showRealTimeVideo({ type: 4 });
            }
        },
        playVideoError(){
            this.showVideoErrorTips()
        },
    },
};
</script>
<style lang="scss">
.multicenter_gallery_dialog {
    background: rgb(47, 47, 47);
    height: 90% !important;
    display: flex;
    flex-direction: column;
    margin-top: 5vh !important;
    box-shadow: 10px 8px 30px #666;
    border: 1px solid #fff;
    .el-dialog__header {
        border-bottom: none !important;
        .el-dialog__close {
            font-size: 22px;
            color: #fff !important;
        }
        .el-dialog__close:hover {
            color: #909399;
        }
        button {
            top: 5px;
            right: 10px;
        }
    }
    .el-dialog__body {
        position: relative;
        flex: 1;
        display: flex;
        padding: 0;
        height: calc(100% - 30px);
        overflow: hidden;
    }
    .mr_gallery {
        color: #000;
        background-color: #a9bfbe;
    }
    .el-tabs__header {
        margin-bottom: 0;
    }
    .el-tabs__nav {
        width: 100%;
        display: flex;
        background-color: #fff;
        color: #000;
        .el-tabs__item {
            flex: 1;
            text-align: center;
            padding: 0;
        }
        .el-tabs__item.is-active,
        .el-tabs__item:hover {
            color: #fff;
            background: #779a98;
        }
        .el-tabs__active-bar {
            background-color: #779a98;
            width: 50%;
        }
    }
    .case_view_wrap {
        height: 100%;
        flex: 1;
        overflow: hidden;
    }
    .annotation_view_wrap {
        height: calc(100% - 44px);
    }
    .reject_dialog {
        .el-dialog__close {
            color: #000 !important;
        }
        .el-textarea {
            width: 100%;
            margin-top: 10px;
        }
        .reject_btn {
            width: 80px;
            float: right;
            margin-top: 15px;
        }
    }
}
.left-gallery {
    width: 70%;
    display: flex;
    flex-direction: column;
    padding: 0 10px;
    #gallery-box {
        flex: 1;
        position: relative;
        width: 100%;
        height: 100%;
        z-index: 1;
        overflow: hidden;
        .main_swiper {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            overflow: hidden;
            .mui-slider-group {
                height: 100%;
                font-size: 0;
                position: relative;
                transition: all 0s linear;
                white-space: nowrap;
                .mui-slider-item {
                    font-size: 14px;
                    position: relative;
                    display: inline-block;
                    z-index: 1;
                    width: 100%;
                    height: 100%;
                    vertical-align: top;
                    white-space: normal;
                    -webkit-user-select: none;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    user-select: none;
                    .preview,.main_video{
                        max-width: 100%;
                        max-height: 100%;
                        position: absolute;
                        top: 0;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        margin: auto;
                        cursor: pointer;
                    }
                }
                .loading_span {
                    position: absolute !important;
                    z-index: 9;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    width: 42px;
                    height: 42px;
                    .el-loading-mask {
                        background: none;
                    }
                }
            }
        }
    }
    .thumb_wrap {
        height: 120px;
        padding: 0px 20px 4px;
        margin-top: 15px;
        position: relative;
        .thumb_loading {
            position: absolute;
            top: 0;
            width: calc(100% - 40px);
            height: 100%;
            background-color: #212121;
            z-index: 99;
        }
        .thumb_scroll_wrap {
            width: 100%;
            height: 100%;
            user-select: none;
            .thumb_slide {
                position: relative;
                width: 100%;
                height: 100%;
                z-index: 1;
                .thumb_item {
                    width: 156px;
                    height: 116px;
                    background: #000;
                    position: relative;
                    margin-right: 1px;
                    cursor: pointer;
                    &.current_thumb {
                        border: 3px solid #599592;
                    }
                    .image_tag {
                        color: #fff;
                        position: absolute;
                        bottom: 6px;
                        z-index: 9;
                        right: 4px;
                        background: #0030c6;
                        line-height: 1.5;
                        padding: 0 4px;
                        border-radius: 4px;
                        &.tag_2 {
                            background: #f90012;
                        }
                        &.tag_3 {
                            background: #00c626;
                        }
                    }
                    .preview {
                        max-width: 100%;
                        max-height: 100%;
                        position: absolute;
                        top: 0;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        margin: auto;
                    }
                    p {
                        font-size: 12px;
                        text-align: center;
                        color: #fff;
                        position: absolute;
                        white-space: nowrap;
                        top: 46%;
                        left: 50%;
                        z-index: 2;
                        transform: translate(-50%, -50%) scale(0.9);
                    }
                    .iconvideo_fill_light,
                    .iconpicture {
                        position: absolute;
                        bottom: 0;
                        color: #fff;
                        font-size: 24px;
                        line-height: 1;
                        z-index: 2;
                    }
                    .comment_number {
                        position: absolute;
                        left: 2px;
                        top: 2px;
                        color: #fff;
                        background: #56C7FD;
                        border: 1px solid white;
                        width: 20px;
                        border-radius: 50%;
                        height: 20px;
                        font-size: 12px;
                        line-height: 20px;
                        text-align: center;
                        z-index: 2;
                    }
                    .unread_tip {
                        position: absolute;
                        right: 0.2rem;
                        top: 0.2rem;
                        border-radius: 50%;
                        background-color: #f00;
                        width: 8px;
                        height: 8px;
                        z-index: 2;
                    }
                    .empty_thump {
                        width: 100%;
                        height: 100px;
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        background-color: #2c2d2f;
                        transform: translate(-50%, -50%);
                    }
                    .view_name {
                        color: #fff;
                        position: absolute;
                        top: 6px;
                        z-index: 9;
                        left: 2px;
                        transform: none;
                        font-size: 14px;
                        white-space: normal;
                        text-align: left;
                    }
                }
                .__bar-is-horizontal,
                .__bar-is-vertical {
                    display: none;
                }
            }
        }
        .last_page {
            transform: rotate(90deg) scaleX(1.5);
            position: absolute;
            left: 0px;
            top: 50px;
            color: #fff;
            font-size: 18px;
            line-height: 16px;
            cursor: pointer;
        }
        .next_page {
            transform: rotate(270deg) scaleX(1.5);
            position: absolute;
            right: 0px;
            top: 50px;
            color: #fff;
            font-size: 18px;
            line-height: 16px;
            cursor: pointer;
        }
    }
}
.right-gallery {
    width: 30%;
    height: calc(100% - 30px);
    height: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 10;
    overflow: hidden;
}
</style>
