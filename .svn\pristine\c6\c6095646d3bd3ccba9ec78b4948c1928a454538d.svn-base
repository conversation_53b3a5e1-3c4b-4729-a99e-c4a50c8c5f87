<template>
    <div class="onlineTestOverview_container">
        <div class="custom_header">
            <div class="back_btn" @click="back">
                <i class="el-icon-arrow-left"></i>
                <span>{{ lang.back_button }}</span>
            </div>
        </div>
        <div class="custom_body" ref="customBody" v-loading="loading">
            <template v-if="!loading">
                <div class="top_info_section">
                    <div class="icon_area">
                        <div class="placeholder_icon">
                            <i class="el-icon-collection"></i>
                        </div>
                    </div>
                    <div class="details_area">
                        <div class="detail_top">
                            <div class="detail_title">
                                {{ title }}
                            </div>
                        </div>
                        <div class="detail_bottom">
                            <div class="detail_info">
                                <div class="info_row">
                                    <span class="info_label">{{ lang.exam_questions_count || "试卷题数" }}:</span>
                                    <span class="info_value">{{ questionsCount }}题</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{ lang.exam_deadline || "截止时间" }}:</span>
                                    <span class="info_value">{{ deadline }}</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{ "考试次数" }}:</span>
                                    <span class="info_value">{{ examDuration }}</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{ lang.exam_pass_score || "考试模式" }}:</span>
                                    <span class="info_value">{{ examMode }}</span>
                                </div>
                                <!-- <div class="info_row">
                                <span class="info_label">{{ "考试类型" }}:</span>
                                <span class="info_value">{{ examTypeText }}</span>
                            </div> -->
                            </div>
                            <div class="detail_btns">
                                <el-button type="primary" @click="startTest" :loading="isStartingTest">
                                    {{ lang.start_test_button || "开始考试" }}
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content_section exam_history_section">
                    <h3>{{ lang.exam_history_title || "考试历史" }}</h3>
                    <el-table :data="examHistoryData" style="width: 100%">
                        <el-table-column prop="examTime" :label="lang.exam_time || '考试时间'"></el-table-column>
                        <el-table-column
                            prop="passCount"
                            :label="lang.answered_count || '已答题数'"
                        ></el-table-column>
                        <el-table-column prop="examResult" :label="lang.exam_result || '考试结果'">
                            <template slot-scope="scope">
                                <span
                                    :class="[
                                        'result-text',
                                        {
                                            'result-pass': scope.row.examResult === '通过',
                                            'result-fail': scope.row.examResult === '不通过',
                                        },
                                    ]"
                                    >{{ scope.row.examResult }}</span
                                >
                            </template>
                        </el-table-column>
                        <el-table-column :label="'操作'" width="200">
                            <template slot-scope="scope">
                                <div class="operation-buttons-container">
                                    <el-button
                                        @click="viewAnalysis(scope.row)"
                                        type="text"
                                        size="small"
                                        class="option-btn"
                                    >
                                        {{ "查看分析" }}
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </template>
        </div>
        <router-view></router-view>
    </div>
</template>

<script>
import base from "../../lib/base";
import {
    CLOUD_TEST_TYPE,
    SMART_TECH_TRAINING_ROLE,
    SMART_TECH_TRAINING_TEST_TYPE,
    SMART_TECH_TRAINING_TEST_RETRY_TYPE,
} from "@/module/ultrasync_pc/lib/constants";
import Tool from "@/common/tool";
import service from "@/module/ultrasync_pc/service/service";
import moment from "moment";
export default {
    mixins: [base],
    name: "OnlineTestOverview",
    components: {},
    data() {
        return {
            loading: true,
            isStartingTest: false,
            title: "",
            deadline: "",
            questionsCount: 0,
            examDuration: "",
            examMode: "",
            passScore: "",
            examHistoryData: [],
            userRole: "",
            testInfo: {},
            testId: "",
            trainingId: "",
            pagerInfo: [],
        };
    },
    computed: {
        examTypeText() {
            if (!this.testInfo || !this.testInfo.type) {
                return "";
            }
            switch (this.testInfo.type) {
            case SMART_TECH_TRAINING_TEST_TYPE.ATTACHMENT_UPLOAD:
                return "附件上传";
            case SMART_TECH_TRAINING_TEST_TYPE.ONLINE_QUIZ:
                return "在线答题";
            default:
                return "未知类型";
            }
        },
        maxRetryText() {
            if (!this.testInfo) {
                return "";
            }
            return this.testInfo.maxRetry === 0 ? "不限次数" : this.testInfo.maxRetry + "次";
        },
    },
    created() {
        this.userRole = this.$route.params.role;
        this.testId = this.$route.params.testId;
        this.trainingId = this.$route.params.trainingId;

        // 统一处理API请求
        this.loading = true;
        Promise.all([this.getTrainingTestInfoByTestId(), this.getTrainingTestAnswerHistory()]).finally(() => {
            this.loading = false;
        });
    },
    beforeDestroy() {},
    methods: {
        back() {
            if (this.$router) {
                this.$router.go(-1);
            } else {
                console.warn("Vue Router not found for back() method.");
            }
        },
        startTest() {
            this.isStartingTest = true;
            this.getTrainingTestInfoByTestId()
                .then((testData) => {
                    if (!testData || !testData.deadline) {
                        this.$message.error("获取考试截止时间失败，无法开始考试。");
                        return;
                    }
                    const deadlineTimestamp = testData.deadline;
                    const currentTimeUnix = moment().unix();

                    if (currentTimeUnix > deadlineTimestamp) {
                        this.$message.warning("考试已截止，无法开始考试。");
                    } else {
                        Tool.loadModuleRouter({
                            name: "SmartTechTrainingExamProject_Exam",
                            params: {
                                ...this.$route.params,
                                testData: testData,
                                pager_type: CLOUD_TEST_TYPE.ANSWER,
                            },
                        });
                    }
                })
                .catch((error) => {
                    console.error("startTest - getTrainingTestInfoByTestId error:", error);
                    this.$message.error("获取考试信息失败，请稍后重试。");
                })
                .finally(() => {
                    this.isStartingTest = false;
                });
        },
        viewAnalysis(row) {
            console.log("View analysis for row:", row);
            // TODO: Implement view analysis logic
        },
        updateExamDuration() {
            const attemptCount = this.examHistoryData.length;
            const maxRetry = this.maxRetryText;
            this.examDuration = `${attemptCount}/${maxRetry}`;
        },
        getTrainingTestInfoByTestId() {
            return new Promise((resolve, reject) => {
                service
                    .getTrainingTestInfoByTestId({
                        testID: this.testId,
                        trainingID: this.trainingId,
                    })
                    .then((res) => {
                        console.log(res, "res");
                        if (res.data.error_code === 0) {
                            this.testInfo = res.data.data;
                            // 更新页面显示数据
                            if (this.testInfo) {
                                this.title = this.testInfo.title || "未命名考试";
                                this.questionsCount = this.testInfo.questionCount || 0;

                                // 格式化截止时间
                                if (this.testInfo.deadline) {
                                    this.deadline = moment.unix(this.testInfo.deadline).format("YYYY-MM-DD");
                                }
                                this.pagerInfo = this.testInfo.pagerInfo || [];
                                // 处理考试模式
                                this.examMode =
                                    this.testInfo.retryType === SMART_TECH_TRAINING_TEST_RETRY_TYPE.MULTIPLE
                                        ? "多次作答"
                                        : "单次作答";

                                this.updateExamDuration();
                            }
                            resolve(res.data.data);
                        } else {
                            this.$message.error(res.data.message || "获取考试详情失败");
                            reject(new Error(res.data.message || "获取考试详情失败"));
                        }
                    })
                    .catch((error) => {
                        console.error("getTrainingTestInfoByTestId error:", error);
                        this.$message.error("网络错误，获取考试详情失败");
                        reject(error);
                    });
            });
        },
        getTrainingTestAnswerHistory() {
            return service
                .getTrainingTestAnswerHistory({
                    testID: this.testId,
                })
                .then((res) => {
                    console.log(res, "res");
                    if (res.data.error_code === 0 && res.data.data && Array.isArray(res.data.data)) {
                        // 处理考试历史数据
                        this.examHistoryData = res.data.data.map((item) => {
                            return {
                                examTime: item.createdAt
                                    ? moment(item.createdAt).format("YYYY-MM-DD HH:mm:ss")
                                    : "未知时间",
                                passCount: item.passCount || 0,
                                examResult: item.isPass ? "通过" : "不通过",
                            };
                        });

                        // 更新考试次数
                        this.updateExamDuration();
                    }
                    return res;
                })
                .catch((err) => {
                    console.log(err, "err");
                    return err;
                });
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";

.onlineTestOverview_container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f7f9fc;

    .custom_header {
        height: 60px;
        background-color: #fff;
        display: flex;
        align-items: center;
        padding: 0 20px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        z-index: 10;

        .back_btn {
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #606266;
            font-size: 14px;
            transition: color 0.2s;

            i {
                margin-right: 5px;
                font-size: 16px;
            }

            &:hover {
                color: #409eff;
            }
        }
    }

    .custom_body {
        height: 100%;
        padding: 25px;
        overflow-y: auto;
        background-color: #f7f9fc;
    }
}

.top_info_section {
    display: flex;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    justify-content: center;
    padding-bottom: 60px;
    border-bottom: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .icon_area {
        margin-right: 25px;

        .placeholder_icon {
            width: 160px;
            height: 160px;
            border-radius: 50%;
            background-color: #ffd700;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 30px;
            color: #fff;

            i {
                font-size: 80px;
            }
        }
    }

    .details_area {
        display: flex;
        flex-direction: column;

        .detail_top {
            max-width: 700px;

            .detail_title {
                font-size: 22px;
                color: #303133;
                margin-bottom: 15px;
                font-weight: bold;
                margin-top: 15px;
            }
        }

        .detail_bottom {
            max-width: 700px;

            .detail_info {
                display: flex;
                flex-wrap: wrap;
                margin: 10px 0;

                // 每条信息
                .info_row {
                    width: 50%; // 两列
                    display: flex;
                    align-items: center; // 垂直居中
                    margin-bottom: 8px;
                    justify-content: flex-start; // 所有行都左对齐

                    .info_label {
                        min-width: 80px;
                        font-size: 14px;
                        color: #909399;
                        text-align: left;
                        margin-right: 5px;
                    }

                    .info_value {
                        font-size: 14px;
                        color: #303133;
                        font-weight: 500;
                    }
                }
            }

            .detail_btns {
                display: flex;
                justify-content: center;
                margin-top: 22px;

                .el-button {
                    width: 120px;
                }
            }
        }
    }
}

.content_section {
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border-bottom: none;

    h3 {
        margin-bottom: 18px;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
    }
}
</style>
