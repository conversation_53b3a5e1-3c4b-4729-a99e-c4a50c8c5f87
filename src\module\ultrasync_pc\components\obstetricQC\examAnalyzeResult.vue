<!-- 统计画廊下检查结果汇总展示 -->
<template>
    <div v-show="isShowExamSummary">
        <el-dialog
            :title="title"
            class="obstetric_qc_exam_result_dialog"
            :visible.sync="ishow"
            :close-on-click-modal="false"
            width="90%"
            height="100%"
            :before-close="closeTable"
            :append-to-body="true"
            :modal="false"
        >
            <div class="obstetric_qc_exam_result_dialog">
                <div class="exam_result_dialog flex flex_column align_ccenter align_icenter flex_nowrap">
                    <div class="title">
                        {{ lang.obstetric_qc.result }}
                    </div>
                    <div class="row">
                        {{ lang.exam_socre_text }} {{ ai_result.score }} ({{ lang.full_mark }}{{ 100 }},
                        {{ this.lang.view_score_display_rule }})
                    </div>
                    <div class="row flex flex_row flex_nowrap">
                        <div>{{ lang.overall_evaluation }}:</div>
                        <div>{{ ai_result.summary }}</div>
                    </div>

                    <div class="title">
                        {{ lang.view_detail }}
                    </div>
                    <div class="view_list">
                        <div class="row flex align_ccenter align_icenter flex_row">
                            <div class="content flex flex_row flex_column">
                                <template v-if="ai_iamge.fdata.length > 0">
                                    <table class="half_width">
                                        <tbody>
                                            <tr style="height: 50px">
                                                <td>{{ lang.index_num }}</td>
                                                <td>{{ lang.view_type }}</td>
                                                <td>{{ lang.view_name }}</td>
                                                <td>{{ lang.ai_score }}</td>
                                                <td>{{ lang.view_quality }}</td>
                                            </tr>
                                            <tr v-for="(item, index) of ai_iamge.fdata" :key="index">
                                                <td>{{ Number(index) + 1 }}</td>
                                                <td :rowspan="item.total" v-if="item.dispaly_view_class">
                                                    {{ lang.obstetric_qc[viewClass[item.view_class].name] }}
                                                </td>
                                                <td
                                                    :class="{ name: item.mc_resource_map }"
                                                    @click="requestOpenGallery($event, item.mc_resource_map, 'ONE')"
                                                >
                                                    {{ getItemName(item) }}
                                                </td>
                                                <td>
                                                    {{
                                                        item.mc_resource_map
                                                            ? toFixedNumber(
                                                                  item.mc_resource_map.ai_report.report.view_score
                                                              )
                                                            : 0
                                                    }}
                                                </td>
                                                <td
                                                    :class="{
                                                        error: !item.mc_resource_map,
                                                        nonstand:
                                                            item.mc_resource_map && item.mc_resource_map.quality == 2,
                                                    }"
                                                >
                                                    {{
                                                        item.mc_resource_map
                                                            ? item.mc_resource_map.ai_report.report.quality
                                                            : item.view_class ==
                                                              obstetricEarlyPregnancy.viewClass.base_view
                                                            ? lang.deletion
                                                            : ""
                                                    }}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <div class="empty_tab"></div>
                                    <table class="half_width">
                                        <tbody>
                                            <tr style="height: 50px">
                                                <td>{{ lang.index_num }}</td>
                                                <td>{{ lang.view_type }}</td>
                                                <td>{{ lang.view_name }}</td>
                                                <td>{{ lang.ai_score }}</td>
                                                <td>{{ lang.view_quality }}</td>
                                            </tr>
                                            <tr v-for="(item, index) of ai_iamge.fdata" :key="index">
                                                <template v-if="ai_iamge.hdata[index]">
                                                    <td>{{ Number(index) + 1 + ai_iamge.medium }}</td>
                                                    <td
                                                        :rowspan="ai_iamge.hdata[index].total"
                                                        v-if="ai_iamge.hdata[index].dispaly_view_class"
                                                    >
                                                        {{
                                                            lang.obstetric_qc[
                                                                viewClass[ai_iamge.hdata[index].view_class].name
                                                            ]
                                                        }}
                                                    </td>
                                                    <td
                                                        :class="{ name: ai_iamge.hdata[index].mc_resource_map }"
                                                        @click="
                                                            requestOpenGallery(
                                                                $event,
                                                                ai_iamge.hdata[index].mc_resource_map,
                                                                'ONE'
                                                            )
                                                        "
                                                    >
                                                        {{ getItemName(ai_iamge.hdata[index]) }}
                                                    </td>
                                                    <td>
                                                        {{
                                                            ai_iamge.hdata[index].mc_resource_map
                                                                ? toFixedNumber(
                                                                      ai_iamge.hdata[index].mc_resource_map.ai_report
                                                                          .report.view_score
                                                                  )
                                                                : 0
                                                        }}
                                                    </td>
                                                    <td
                                                        :class="{
                                                            error: !ai_iamge.hdata[index].mc_resource_map,
                                                            nonstand:
                                                                ai_iamge.hdata[index].mc_resource_map &&
                                                                ai_iamge.hdata[index].mc_resource_map.quality == 2,
                                                        }"
                                                    >
                                                        {{
                                                            ai_iamge.hdata[index].mc_resource_map
                                                                ? ai_iamge.hdata[index].mc_resource_map.ai_report.report
                                                                      .quality
                                                                : ai_iamge.hdata[index].view_class ==
                                                                  obstetricEarlyPregnancy.viewClass.base_view
                                                                ? lang.deletion
                                                                : ""
                                                        }}
                                                    </td>
                                                </template>
                                                <template v-else>
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                </template>
                                            </tr>
                                        </tbody>
                                    </table>
                                </template>
                                <template v-else>
                                    <div class="no_data_text">{{ lang.no_data_txt }}</div>
                                </template>
                            </div>
                        </div>
                        <div class="flex align_ccenter align_icenter flex_row justify_center">
                            <el-button
                                native-type="submit"
                                type="primary"
                                class="general_confirm_button_"
                                @click="requestOpenGallery($event, null, 'ALL')"
                            >
                                {{ lang.check_view_info }}
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import base from "../../lib/base";
import service from "../../service/multiCenterService.js";
import examServer from "../../service/multiCenterService.js";
import { getRealUrl, deDuplicatingImg, formatString, toFixedNumber } from "../../lib/common_base";
import { cloneDeep, sortBy } from "lodash";
// import subSectionGallery from './subSectionGallery'
export default {
    mixins: [base],
    name: "MulticenterPage",
    components: {},
    props: {
        currentExam: {
            type: Object,
            default: () => {
                return {};
            },
        },
        isShowExamSummary: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            toFixedNumber,
            obstetricEarlyPregnancy: this.$store.state.multicenter.obstetricEarlyPregnancy,
            title: "",
            ai_result: {
                score: "",
                desc: {},
                summary: "",
            },
            ai_iamge: {
                total: 0, //总数
                medium: 0, //总监数
                fdata: [], //前半部分
                hdata: [], //后半部分
            },
            default_ai_iamge: {
                total: 0, //总数
                medium: 0, //总监数
                fdata: [], //前半部分
                hdata: [], //后半部分
            },
            overall_evaluation_desc: {},
        };
    },
    computed: {
        mc_options() {
            return this.$store.state.multicenter.optionList[
                this.$store.state.multicenter.obstetricEarlyPregnancy.mcOpId
            ];
        },
        ishow() {
            // console.error(this.isShowExamSummary)
            return this.isShowExamSummary;
        },
        viewClass() {
            let viewClass = this.$store.state.multicenter.obstetricEarlyPregnancy.viewClass;
            let list = [];
            for (let k in viewClass) {
                list[viewClass[k]] = { name: k };
            }
            return list;
        },
    },
    created() {
        this.title = this.lang.obstetric_qc.title;
    },
    mounted() {},
    watch: {
        currentExam: {
            handler(value, oldValue) {
                if (value && value.preset_mc_view_option) {
                    this.setResult(cloneDeep(value));
                    let mc_view_option = cloneDeep(value.preset_mc_view_option);
                    let mc_group_option = cloneDeep(value.preset_mc_group_option);
                    this.ai_iamge = cloneDeep(this.default_ai_iamge);
                    let values = [];
                    let view_class_count = { base_view: 0, optional_view: 1, non_view: 2 };
                    for (let key in value.preset_mc_view_option) {
                        let view = value.preset_mc_view_option[key];
                        if (view.enabled) {
                            values.push(cloneDeep(view));
                        }
                    }

                    let group_views = {};
                    let new_values = [];
                    if (values && values.length) {
                        values.forEach((item) => {
                            if (
                                item.mc_resource_map &&
                                item.mc_resource_map.ai_report &&
                                item.mc_resource_map.ai_report.finshed
                            ) {
                                let report = item.mc_resource_map.ai_report.report;

                                if (report.view_desc && report.view_desc.length > 0) {
                                    let list = cloneDeep(report.view_desc);
                                    list[0] = item.name + ": " + list[0];
                                    this.ai_result.desc = [this.ai_result.desc, list];
                                }
                                item.mc_resource_map.quality = 1;
                                item.mc_resource_map.ai_report.report.quality = this.lang.basic_standard;
                                report.view_score = report.view_score * 100;
                                if (report.view_score > item.ai_height.lowest) {
                                    item.mc_resource_map.ai_report.report.quality = this.lang.standard;
                                    item.mc_resource_map.quality = 0;
                                    item.mc_resource_map.quality = 0;
                                }
                                if (report.view_score <= item.ai_lower.highest) {
                                    item.mc_resource_map.ai_report.report.quality = this.lang.non_standard;
                                    item.mc_resource_map.quality = 2;
                                }
                            }

                            //切面组数据
                            if (item.group_view_id) {
                                group_views[item.group_view_id] = mc_group_option[item.group_view_id];
                                group_views[item.group_view_id].views = group_views[item.group_view_id].views || [];
                                group_views[item.group_view_id].views.push(item);
                                group_views[item.group_view_id].enabled = true;
                                group_views[item.group_view_id].group_view_id = item.group_view_id;
                                group_views[item.group_view_id].view_class = item.view_class;
                            } else {
                                new_values.push(item);
                            }
                        });
                    }
                    //切面组数据-得分计算
                    for (let key in group_views) {
                        let group_view = group_views[key];
                        let items = {};
                        for (let i in group_view.item) {
                            let item = group_view.item[i];
                            items[item.id] = item;
                            items[item.id].score = 0;
                        }
                        let isSuccess = false;
                        for (let j in group_view.views) {
                            let view = group_view.views[j];
                            // if(view.mc_resource_map&&view.mc_resource_map.ai_report&&view.mc_resource_map.ai_report.report&&view.mc_resource_map.ai_report.report.group_item&&view.mc_resource_map.ai_report.report.group_item&&view.mc_resource_map.ai_report.report.group_item.length>0){
                            if (
                                view.mc_resource_map &&
                                view.mc_resource_map.ai_report &&
                                view.mc_resource_map.ai_report.report &&
                                view.mc_resource_map.ai_report.report.group_item &&
                                view.mc_resource_map.ai_report.report.group_item
                            ) {
                                isSuccess = true;
                                let group_item =
                                    view.mc_resource_map.ai_report.report.group_item.length > 0
                                        ? view.mc_resource_map.ai_report.report.group_item[0].score
                                        : [];
                                if (!Array.isArray(group_item)) {
                                    group_item = [group_item];
                                }
                                for (let k in group_item) {
                                    let score = group_item[k];
                                    if (k in items) {
                                        if (score >= items[k].score) {
                                            items[k].score = score;
                                        }
                                    }
                                }
                            }
                        }
                        group_views[key].score = 0;
                        for (let l in items) {
                            group_views[key].score = group_views[key].score + items[l].score * items[l].rate;
                        }
                        if (isSuccess) {
                            group_views[key].mc_resource_map = {
                                ai_report: {
                                    finshed: true,
                                    report: {
                                        item: [],
                                        structure: [],
                                        isSuccess: true,
                                        view_score: group_views[key].score * 100,
                                        group_view_type: group_views[key].id,
                                        view_type: group_views[key].id,
                                        quality: this.lang.basic_standard,
                                    },
                                },
                                score: group_views[key].score,
                                quality: 1,
                            };
                            if (group_views[key].score * 100 > group_views[key].ai_height.lowest) {
                                group_views[key].mc_resource_map.ai_report.report.quality = this.lang.standard;
                                group_views[key].mc_resource_map.quality = 0;
                            }
                            if (group_views[key].score * 100 <= group_views[key].ai_lower.highest) {
                                group_views[key].mc_resource_map.ai_report.report.quality = this.lang.non_standard;
                                group_views[key].mc_resource_map.quality = 2;
                            }
                        }
                    }
                    values = [...new_values, ...Object.values(group_views)];
                    values = sortBy(values, (v) => {
                        return -1 * (v.mc_resource_map ? v.mc_resource_map.score : 0);
                    });
                    values = sortBy(values, (v) => {
                        return Number(v.view_class);
                    });

                    // values.forEach(item=>{

                    // })
                    this.ai_iamge.total = values.length;
                    if (this.ai_iamge.total > 0) {
                        let i = this.ai_iamge.total / 2;
                        let j = this.ai_iamge.total % 2;
                        if (i > 0) {
                            if (j == 0) {
                                this.ai_iamge.medium = i;
                                this.ai_iamge.fdata = values.slice(0, i);
                                this.ai_iamge.hdata = values.slice(i, this.ai_iamge.total);
                            } else {
                                this.ai_iamge.medium = Math.floor(i) + 1;
                                this.ai_iamge.fdata = values.slice(0, i + 1);
                                this.ai_iamge.hdata = values.slice(i + 1, this.ai_iamge.total);
                                this.ai_iamge.hdata.push(undefined);
                            }
                        } else {
                            this.ai_iamge.medium = 1;
                            this.ai_iamge.hdata.push(undefined);
                        }
                    } else {
                    }
                } else {
                    this.ai_iamge = cloneDeep(this.default_ai_iamge);
                }

                this.ai_iamge.fdata = this.calcTdRowSpan(this.ai_iamge.fdata);
                this.ai_iamge.hdata = this.calcTdRowSpan(this.ai_iamge.hdata);
                // console.error(this.ai_iamge)
                this.overall_evaluation_desc[1] = value.image_num ? value.image_num : 0;
            },
            deep: true,
        },
    },
    methods: {
        getItemName(item) {
            if (item.key && item.key in this.lang) {
                return this.lang[item.key];
            }
            return item.name;
        },
        calcTdRowSpan(list) {
            let view_class_number = {
                0: { index: 0, total: 0, enabled: false },
                1: { index: 0, total: 0, enabled: false },
                2: { index: 0, total: 0, enabled: false },
            };
            let current_class = -1;
            list = list.reduce((h, v, i) => {
                if (v) {
                    if (current_class != v.view_class) {
                        current_class = v.view_class;
                        v.dispaly_view_class = true;
                        view_class_number[current_class].index = i;
                        view_class_number[current_class].enabled = true;
                        ++view_class_number[current_class].total;
                    } else {
                        v.dispaly_view_class = false;
                        ++view_class_number[current_class].total;
                    }
                    h.push(v);
                }
                return h;
            }, []);
            for (let k in view_class_number) {
                if (view_class_number[k] && view_class_number[k].enabled) {
                    list[view_class_number[k].index].total = view_class_number[k].total;
                }
            }
            return list || [];
        },

        closeTable() {
            this.isShow = false;
            this.$emit("change");
        },
        requestOpenGallery(event, mc_resource_map, type) {
            // console.error('image.mc_resource_map.type:',mc_resource_map)
            if (type == "ALL") {
                let hasSuccessView = false;
                for (let i in this.currentExam.preset_mc_view_option) {
                    let image = this.currentExam.preset_mc_view_option[i];
                    if (image && image.mc_resource_map) {
                        hasSuccessView = true;
                        this.$emit("requestOpenGallery", this.currentExam, null);
                        break;
                    }
                }
                if (!hasSuccessView) {
                    this.$emit("requestOpenGallery", this.currentExam, null);
                }
            } else {
                if (mc_resource_map) {
                    let view_ids = [];
                    if (
                        mc_resource_map &&
                        mc_resource_map.ai_report &&
                        mc_resource_map.ai_report.report &&
                        mc_resource_map.ai_report.report.group_view_type
                    ) {
                        view_ids =
                            this.currentExam.preset_mc_group_option[mc_resource_map.ai_report.report.group_view_type]
                                .types || [];
                    }
                    this.$emit("requestOpenGallery", this.currentExam, mc_resource_map.type, view_ids);
                }
            }
        },
        setResult(value) {
            //time_out_msg
            this.ai_result = { score: "", desc: {} };
            this.ai_result.score = value.score;
            this.ai_result.total = value.image_num;
            this.ai_result.finshed = value.non_standard + value.basic_standard + value.standard;
            this.ai_result.standard = value.standard;
            this.ai_result.non_standard = value.non_standard;
            this.ai_result.basic_standard = value.basic_standard;
            this.ai_result.deletion = 0;
            this.ai_result.integrity_rate = value.integrity_rate;
            this.ai_result.standardization_rate = value.standardization_rate;
            if (this.mc_options && this.mc_options.more_details && value.recognition_base_num > 0) {
                this.ai_result.deletion = this.mc_options.more_details.activedNumber[0] - value.recognition_base_num;
            }
            this.ai_result.compliance_rate = value.compliance_rate;
            this.ai_result.summary = Object.keys(this.ai_result).reduce((h, v) => {
                h = h.replace(new RegExp("{" + v + "}", "g"), this.ai_result[v]);
                return h;
            }, this.lang.overall_evaluation_desc);
            // console.error(this.ai_result)
        },
    },
};
</script>
<style lang="scss">
.obstetric_qc_exam_result_dialog {
    height: 100%;
    .el-dialog__body {
        background: rgba(235, 240, 242, 1);
    }
    .el-dialog__header {
        background: rgba(235, 240, 242, 1);
    }
    .el-dialog {
        margin-top: 5vh !important;
        height: 90% !important;
        weight: 90% !important;
    }
    .exam_result_dialog {
        height: 100%;
        width: 100%;
        padding-left: 50px;
        .title {
            width: 100%;
            font-size: 16px;
            font-weight: bold;
            color: #62615f;
            margin-bottom: 15px;
        }
        .row {
            margin-bottom: 15px;
            width: 100%;
            color: #62615f;
            font-size: 14px;
        }
        .view_list {
            width: 100%;
            flex: 1;
            padding-bottom: 20px;
            .row {
                padding-top: 10px;
                padding-bottom: 20px;
                width: 100%;
            }
            .header {
                font-size: 16px;
                font-weight: bold;
                color: black;
            }
            .content {
                width: 95%;
                .half_width {
                    width: calc(50% - 8px);
                }
                .empty_tab {
                    width: 15px;
                }
                background: rgba(235, 240, 242, 1);
                tr {
                }
                th,
                td {
                    border: 1px solid #bbb;
                    font-size: 14px;
                    padding: 10px 6px 10px 6px;
                    text-align: center;
                    vertical-align: middle;
                    .summary {
                        min-height: 100px;
                        text-align: left;
                        padding-left: 15px;
                        display: flex;
                        align-items: left;
                        .summary_row {
                            padding-bottom: 10px;
                        }
                    }
                }
                table {
                    color: #333;
                    border: 1px solid #bbb;
                    border-collapse: collapse;
                    width: 100%;
                    .name {
                        color: #0000ff;
                    }
                    background: rgba(235, 240, 242, 1);
                }
                .error {
                    color: red;
                }
                .nonstand {
                    color: #ff9933;
                    background: rgba(235, 240, 242, 1);
                }
                .no_data_text {
                    text-align: center;
                    margin: auto;
                    margin-top: 20px;
                    margin-bottom: 20px;
                }
            }
            .detail {
                width: 150px;
                text-align: center;
                color: #fff;
                background: #779a98;
            }
        }
    }
    .flex {
        display: -webkit-flex;
        -webkit-flex-wrap: wrap;
    }
    .flex_column {
        flex-direction: column;
    }
    .align_ccenter {
        align-content: center;
    }
    .align_icenter {
        align-items: center;
    }
    .flex_row {
        flex-direction: row;
    }
    .justify_center {
        justify-content: center;
    }
    .flex_nowrap {
        flex-wrap: nowrap;
    }
}
</style>
