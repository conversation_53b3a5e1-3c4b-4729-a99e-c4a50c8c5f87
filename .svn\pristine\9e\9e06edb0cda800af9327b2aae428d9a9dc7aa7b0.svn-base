<template>
    <div class="supervisor-management-container">
        <!-- 筛选/操作区域 -->
        <div class="filter-section">
            <el-form :inline="true" :model="filters" class="filter-form">
                <el-form-item label="用户昵称">
                    <el-input v-model="filters.nickname" placeholder="请输入用户昵称" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="handleSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-plus" @click="handleAddNewSupervisor">新增</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 内容区域 -->
        <div class="content-section">
            <el-table :data="supervisorList" stripe style="width: 100%" v-loading="loading">
                <el-table-column type="index" label="编号" width="80"></el-table-column>
                <el-table-column prop="nickname" label="昵称">
                    <template slot-scope="scope">
                        <el-input v-if="scope.row.isEditing" v-model="scope.row.nickname" placeholder="请输入昵称"></el-input>
                        <span v-else>{{ scope.row.nickname }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="role" label="角色权限">
                    <template slot-scope="scope">
                        <el-select v-if="scope.row.isEditing" v-model="scope.row.role" placeholder="请选择角色">
                            <el-option
                                v-for="item in roleOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                        <span v-else>{{ getRoleLabel(scope.row.role) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <div v-if="scope.row.isEditing">
                            <el-button type="primary" size="small" @click="handleSave(scope.row, scope.$index)">确定</el-button>
                            <el-button size="small" @click="handleCancelEdit(scope.row, scope.$index)">取消</el-button>
                        </div>
                        <div v-else>
                            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
                            <el-button type="danger" size="small" @click="handleDelete(scope.row, scope.$index)">删除</el-button>
                            <!-- 根据原型图，暂时不添加编辑按钮 -->
                            <!-- <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button> -->
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
// import base from "../../lib/base"; // 假设暂时不需要 base mixin，如果需要再引入

export default {
    name: "SupervisorManagement",
    // mixins: [base],
    data() {
        return {
            filters: {
                nickname: ''
            },
            loading: false,
            supervisorList: [],
            roleOptions: [
                { label: 'Supervisor', value: 'Supervisor' },
                { label: 'Admin', value: 'Admin' },
                { label: 'User', value: 'User' } // 示例添加一个User角色
            ],
            nextId: 1, // 用于前端生成临时 ID
            originalDataBeforeEdit: null, // 用于存储编辑前的数据副本
        };
    },
    created() {
        this.fetchSupervisors();
    },
    methods: {
        fetchSupervisors() {
            this.loading = true;
            // 模拟API调用获取数据
            setTimeout(() => {
                const mockData = [
                    {
                        id: this.nextId++,
                        nickname: '迈瑞-老纪',
                        role: 'Supervisor',
                        isEditing: false,
                        isNew: false
                    },
                    {
                        id: this.nextId++,
                        nickname: '乳腺多中心',
                        role: 'Admin', // 假设角色不同
                        isEditing: false,
                        isNew: false
                    },
                    {
                        id: this.nextId++,
                        nickname: '张三',
                        role: 'User',
                        isEditing: false,
                        isNew: false
                    }
                ];
                this.supervisorList = mockData;
                this.loading = false;
            }, 500);
        },
        handleSearch() {
            this.loading = true;
            // 模拟查询逻辑：根据 filters.nickname 过滤 supervisorList
            // 实际项目中应调用API
            setTimeout(() => {
                if (this.filters.nickname) {
                    // 注意：这里仅为前端演示，实际应重新拉取或用更高效前端过滤
                    const filteredList = this.supervisorList.filter(supervisor =>
                        supervisor.nickname.toLowerCase().includes(this.filters.nickname.toLowerCase())
                    );
                    // 为了演示，我们暂时直接修改列表，真实场景可能是更新一个用于显示的列表副本
                    // 或者提示用户这是模拟的，真实数据会从后端获取
                    this.$message.info(`模拟按"${this.filters.nickname}"筛选，实际应调API。显示 ${filteredList.length} 条`);
                    // this.supervisorList = filteredList; // 不直接修改原始列表，除非fetchSupervisors重新加载
                } else {
                    this.fetchSupervisors(); // 如果搜索条件为空，重新加载全部数据
                    this.$message.success("已加载全部数据（模拟）");
                }
                this.loading = false;
            }, 500);
        },
        handleAddNewSupervisor() {
            // 检查是否已经有正在编辑的新行
            const existingNewRow = this.supervisorList.find(item => item.isNew && item.isEditing);
            if (existingNewRow) {
                this.$message.warning('请先保存或取消当前新增的行');
                return;
            }

            const newSupervisor = {
                id: null, // 临时 ID，保存时生成
                nickname: '',
                role: '', // 默认空角色，让用户选择
                isEditing: true,
                isNew: true // 标记为新行
            };
            this.supervisorList.unshift(newSupervisor); // 添加到列表顶部
        },
        handleSave(row, index) {
            if (!row.nickname || !row.role) {
                this.$message.error('昵称和角色权限不能为空');
                return;
            }

            if (row.isNew) { // 新增保存
                row.id = this.nextId++; // 前端生成ID
                row.isEditing = false;
                row.isNew = false;
                this.$message.success(`Supervisor "${row.nickname}" 新增成功`);
                // 实际应用中：调用API保存，成功后可能需要刷新列表或更新该行数据(如获取后端ID)
            } else { // 编辑保存
                row.isEditing = false;
                this.$message.success(`Supervisor "${row.nickname}" 修改成功`);
                // 实际应用中：调用API更新
            }
            this.originalDataBeforeEdit = null; // 清除编辑前数据副本
        },
        handleCancelEdit(row, index) {
            if (row.isNew) { // 取消新增
                this.supervisorList.splice(index, 1);
                this.$message.info('新增操作已取消');
            } else { // 取消编辑
                if (this.originalDataBeforeEdit) {
                    // 恢复数据
                    Object.assign(row, this.originalDataBeforeEdit);
                }
                row.isEditing = false;
                this.$message.info('编辑操作已取消');
            }
            this.originalDataBeforeEdit = null; // 清除编辑前数据副本
        },
        // 如果需要从显示状态切换到编辑状态，需要此方法
        handleEdit(row) {
            // 检查是否有其他行（包括新行）正在编辑中
            const currentlyEditingRow = this.supervisorList.find(item => item.isEditing && item !== row);
            if (currentlyEditingRow) {
                this.$message.warning(`请先完成对 "${currentlyEditingRow.nickname || '(新条目)'}" 的编辑操作。`);
                return;
            }
            this.originalDataBeforeEdit = { ...row }; // 保存副本以备取消
            row.isEditing = true;
        },
        handleDelete(row, index) {
            this.$confirm(`确定要删除 Supervisor "${row.nickname}" 吗?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.supervisorList.splice(index, 1);
                this.$message.success(`Supervisor "${row.nickname}" 删除成功`);
                // 实际应用中：调用API删除
            }).catch(() => {
                this.$message.info('删除操作已取消');
            });
        },
        getRoleLabel(roleValue) {
            const role = this.roleOptions.find(option => option.value === roleValue);
            return role ? role.label : roleValue; // 如果找不到匹配的label，直接返回value
        }
    }
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/aiChat.scss"; // 假设这个文件存在且包含通用样式

.supervisor-management-container {
    padding: 20px;
    background-color: #f5f7fa; // 参考 studentManagement
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .filter-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1); // 参考 studentManagement
        flex-shrink: 0;

        .filter-form {
            .el-form-item {
                margin-bottom: 0;
                margin-right: 15px;
                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }

    .content-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1); // 参考 studentManagement
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .el-table {
            flex: 1;
            // 表格内输入框和选择器样式调整
            ::v-deep .el-input input,
            ::v-deep .el-select .el-input__inner {
                height: 32px; // 调整行内编辑时组件的高度，使其更紧凑
                line-height: 32px;
            }
            ::v-deep .el-select .el-input .el-select__caret {
                line-height: 32px; // 调整下拉箭头位置
            }
        }
    }
}
</style>
