let IT = {
    "hospital_name_repeated": "Nome dell'ospedale ripetuto",
    "please_enter_threshold": "Inserisci la soglia",
    "auto_recognition_threshold_tips": "Impostare la soglia corrispondente separatamente in base al tipo di controllo e la funzione di riconoscimento automatico dopo l'impostazione mostrerà solo i risultati superiori alla soglia in base alle impostazioni del tipo di controllo corrispondente.",
    "auto_recognition_threshold": "Soglia di riconoscimento automatico",
    "topic_summary_no_score": " {a} argomenti in questa categoria",
    "ob_right_ventricular_outflow_tract_view": "Taglio di uscita camera destra",
    "ob_right_ventricular_outflow_tract_view_group": "Taglio di uscita camera destra",
    "renal_long_axis_section_group": "Gruppo di taglio a asse lungo del rene",
    "right_kidney": "Rene destro",
    "left_kidney": "Rene sinistro",
    "spinal_sagittal_section_group": "Gruppo di taglio a freccia della spina dorsale",
    "ob_sacral_caudal_segment": "Sezione finale",
    "ob_cervicothoracic_junction": "petto collo",
    "angle_between_sound_beam_long_axis_femur": "Angolo tra il fascio e l'asse femminile da 60°-90°",
    "short_axis_section_of_biventricular_myocardium": "Taglio a albero corto a due cavità",
    "ob_weekclear_IVS_display": "L'intervallo tra stanze non è chiaro",
    "ob_unclear_IVS_display": "Spazio camera non chiaro",
    "ob_unclear_LV_display": "Il ventricolo sinistro non è chiaro.",
    "ob_weekclear_LV_display": "Il ventricolo sinistro mostra scarsa chiarezza",
    "ob_weekclear_pulmonary_artery_and_ductus_arteriosus_display": "Arterie polmonari e catetri arteriosi mostrano scarsa chiarezza",
    "ob_unkclear_pulmonary_artery_and_ductus_arteriosus_display": "Arterie polmonari e catetri non chiari",
    "ob_weekclear_TV_display": "La triplice mostra scarsa chiarezza",
    "ob_unkclear_TV_display": "Le tre valvole non mostrano chiarezza",
    "ob_weekclear_main_pulmonary_artery_and_ductus_arteriosus_display": "Arterie polmonari principali e catetri arteriosi mostrano scarsa chiarezza",
    "ob_unkclear_main_pulmonary_artery_and_ductus_arteriosus_display": "Arterie polmonari principali e catetri arteriosi non chiari",
    "ob_unkclear_RV_display": "Camera destra non chiara",
    "ob_weekclear_RV_display": "La camera destra mostra scarsa chiarezza",
    "ob_weekclear_IVC_display": "La vena inferiore non mostra chiarezza",
    "ob_unclear_IVC_display": "La vena inferiore non è chiara",
    "ob_weekclear_LA_display": "L'atrio sinistro mostra scarsa chiarezza",
    "ob_unclear_LA_display": "L'atrio sinistro non è chiaro",
    "ob_weekclear_RA_display": "L'atrio destro mostra scarsa chiarezza",
    "ob_unclear_RA_display": "L'atrio destro non è chiaro",
    "ob_weekclear_head_and_neck_arterial_branch_display": "I rami delle arterie della testa e del collo mostrano scarsa chiarezza",
    "ob_unclear_head_and_neck_arterial_branch_display": "Ramo dell'arteria della testa e del collo non chiaro",
    "ob_weekclear_ARCH_display": "Arco dell'arteria principale mostra scarsa chiarezza",
    "ob_unclear_ARCH_display": "Arco dell'arteria principale non chiaro",
    "ob_unclearx_image_display": "L'immagine non è chiara",
    "ob_weekclear_superior_vena_cava_display": "La vena superiore non mostra chiarezza",
    "ob_unclear_superior_vena_cava_display": "La vena superiore non è chiara",
    "ob_weekclear_DA_display": "Arteria bassa mostra scarsa chiarezza",
    "ob_unclear_DA_display": "Arteria bassa non chiara",
    "ob_weekclear_ASC_display": "L'arteria principale non mostra chiarezza",
    "ob_unclear_ASC_display": "L'arteria principale non è chiara",
    "ob_weekclear_brach_of_right_pulmonary_artery_display": "Ramo dell'arteria polmonare destra mostra scarsa chiarezza",
    "右肺动脉分支显示不清晰": "Ramo dell'arteria polmonare destra non chiaro",
    "ob_weekclear_rama_arteria_pulmonar_izquierda_display": "Ramo dell'arteria polmonare sinistra mostra scarsa chiarezza",
    "ob_unclear_rama_arteria_pulmonar_izquierda_display": "Ramo dell'arteria polmonare sinistra non chiaro",
    "ob_weekclear_image_display": "L'immagine non è chiara",
    "ob_weekclear_gallbladder_display": "La vescica biliare mostra scarsa chiarezza",
    "ob_unclear_spine_display": "La colonna vertebrale non è chiara",
    "ob_unclear_liver_display": "Il fegato non è chiaro",
    "ob_unclear_image_display": "Immagine non visibile",
    "ob_unclear_diaphragm_display": "Muscoli vaghi",
    "ob_unclear_magenblase_display": "Bubble di stomaco non chiaro",
    "ob_unclear_heart_display": "Il cuore non mostra chiaramente",
    "ob_unclear_lung_display": "polmoni non chiari",
    "head_and_neck_arterial_branch": "Ramo dell'arteria della testa e del collo",
    "long_axis_section_aortic_arch_view": "Taglio dell'arco dell'arteria principale",
    "longitudinal_spine_conus_medullaris": "Sezione della colonna vertebrale - cono del midollo spinale",
    "brach_of_right_pulmonary_artery": "Ramo dell'arteria polmonare destra",
    "pulmonary_artery_bifurcation_view": "Forcolatura dell'arteria polmonare",
    "placental_umbilical_cord_entrance_umbilical_cord_entrance": "Taglio dell'ingresso del cordone ombelicale della placenta - ingresso del cordone ombelicale",
    "blood_flow_map_attachment_site_umbilical_blood_vessels": "Grafico del flusso sanguigno del vaso ombelicale",
    "umbilical_cord_placenta_attachment_site": "Taglio della placenta del cordone ombelicale",
    "cervical_internal_opening_cervix_placenta_attachment_site": "Facciatura del collo dell'utero - vescica in gravidanza",
    "cervical_internal_opening_cervix_fetal_head": "Taglio della bocca interna del collo uterino - testa pneumatica",
    "cervical_internal_opening_cervix_cervical_internal_mouth": "Taglio della bocca interna del collo dell'utero - bocca interna del collo dell'utero",
    "cervical_internal_opening_cervix": "Taglio del collo dell'utero - cervicale",
    "cervical_internal_opening": "bocca interna cervicale",
    "neck_of_uterus": "cervicale",
    "cervical_internal_opening_view": "Taglio interno cervicale",
    "ob_plantar": "Fondo dei piedi",
    "plantar_bilateral_view": "Taglio del fondo del piede (due lati)",
    "character_7foot": "7 parole piedi",
    "longitudinally_cut_foot": "Taglio piedi",
    "transverse_calf_foot_7foot_bilateral_view": "Taglio della gamba e del piede (7 piedi) Taglio della faccia (due lati)",
    "visible_anthor_tibia_and_fibula": "Visibile dall'altra estremità del tibio.",
    "visible_one_tibia_and_fibula": "Ossea tibia visibile",
    "long_axisvsection_tibia_fibula_bilateral_view": "Taglio dell'asse lungo dell'osso tibiano (due lati)",
    "ob_femur": "Ossa femminile",
    "duce_points_angle_between_sound_beam_long": "Angolo inferiore a 45° tra il fascio e l'asse femminile",
    "visible_metaphysis_anthor_end_femur": "L'altra estremità dell'osso femminile è visibile.",
    "visible_metaphysis_one_end_femur": "Visibile l'osso femminile",
    "femoral_long_axis_bilateral_view": "Taglio a lungo asse femminile (a due lati)",
    "vertical_cutting_hand": "Taglia la mano",
    "forearm_hand_longitudinal_bilateral_view": "Antebraccio e sezione longitudinale (due lati)",
    "crown_hand": "Mano della corona",
    "ob_palm": "Palmo della mano",
    "coronary_hand_ulna_and_radius_bilateral_view": "Taglio a mano corona (due lati)",
    "ulna_and_radius": "Scala delle ossa",
    "visible_anthor_radius_and_ulna": "Visibile dall'altra estremità",
    "visible_one_radius_and_ulna": "Visibile da un lato della scala",
    "long_axis_radius_ulna_bilateral": "Taglio dell'asse lungo (a due lati)",
    "long_bone_endpoints": "Punto estremo osseo lungo",
    "ob_humerus": "Ossea",
    "duce_points_angle_between_sound_beam_long_axis_humerus": "Angolo inferiore a 45° tra il fascio acustico e l'asse lunghissimo",
    "angle_sound_beam_and_long_axis_humerus": "Angolo 60°-90° tra il fascio sonoro e l'asse",
    "tibia_and_fibula": "osso tibia",
    "ob_ductus_arteriosus_arch_view": "Taglio arco del catetere arterioso",
    "pulmonary_artery_and_ductus_arteriosus": "Arterie polmonari e catetri arteriosi",
    "ob_upper_inferior_vena_cava_section": "Sezione venosa superiore e inferiore",
    "short_axis_great_arteries_heart": "Taglio corto dell'arteria cardiaca",
    "visible_metaphysis_other_end_humerus": "L'altra estremità dell'osso è visibile.",
    "visible_metaphysis_one_end_humerus": "Visibile un'estremità dell'osso",
    "long_axis_section_humerus_bilateral": "Taglio dell'asse lungo (due lati)",
    "longitudinal_pine_full_view": "Sezione della colonna vertebrale - tutto il percorso",
    "longitudinal_pine_mid_view": "Sezione della colonna vertebrale - mezzo",
    "conus_medullaris": "Cono del midollo spinale",
    "sacral_vertebra_point": "Punto vertebrale",
    "caudal_vertebrae": "Vertebra della coda",
    "sacral_tail": "Fine",
    "longitudinal_spine_coccygeal_segment": "Sezione della colonna vertebrale - coda",
    "ob_skin": "pelle",
    "cervical_vertebra": "Vertebra cervicale",
    "skin_contour": "Profilo della pelle",
    "vertebral_body": "Vertebra",
    "vertebral_arch": "Arco vertebrale",
    "longitudinal_spine_cervical_thoracic_segment": "Sezione della colonna vertebrale - torace del collo",
    "ob_coronary_kidney_view": "Sezione coronaria del rene",
    "ob_sagittal_section_right_kidney": "Sezione freccia del rene destro",
    "ob_sagittal_section_left_kidney": "Sezione freccia del rene sinistro",
    "bilateral_renal_pelvis": "Cucina renale bilaterale",
    "bilateral_kidneys": "Reni bilaterali",
    "horizontal_cross_section_both_kidneys": "Sezione orizzontale a due reni",
    "bladder_double_umbilical_artery_section_umbilical_artery": "Sezione della vescica doppia ombelicale - ombelicale",
    "dual_umbilical_artery_section_bladder_bladder": "Sezione della vescica doppia ombelicale - vescica",
    "intersection_of_umbilical_wheel": "Incontro della ruota ombelicale",
    "ob_ilateral_umbilical_artery_blood_flow": "Flusso sanguigno dell'arteria ombelicale bilaterale",
    "ob_bladder": "vescica",
    "horizontal_bladder_and_bilateral_umbilical_artery_blood_flow_map": "Grafico del flusso sanguigno della vescica e dell'ombelicale bilaterale",
    "ob_pinal_cone": "Cono della spina dorsale",
    "ob_sectional_view_of_umbilical_cord_abdominal_wall_entrance": "Taglio dell'ingresso della parete addominale del cordone ombelicale",
    "ob_gallbladder_view": "Taglio della vescica biliare",
    "extra_deduction_for_kidney_damage": "Deduczione renale extra",
    "ob_one_rib_on_each_side": "Una costola a sinistra e a destra.",
    "ob_complete_abdominal_wall": "parete addominale completa",
    "descending_aorta_and_inferior_vena_cava": "Arteria bassa, vena inferiore",
    "coronary_section_diaphragm_view": "Taglio coronario muscolare",
    "sagittal_section_right_diaphragm_view": "Sezione a freccia del muscolo craniale destro",
    "ob_diaphragm": "Muscoli",
    "sagittal_section_left_diaphragm": "Sezione a freccia del muscolo craniale sinistro",
    "ob_trachea": "Tubi di aria",
    "confluence_of_aortic_arch_ductus_arteriosus": "L'arco dell'arteria principale converge con il catetere dell'arteria (struttura a forma di V)",
    "ob_three_vessel_tracheal_view": "Trivascolare taglio",
    "rama_de_la_arteria_pulmonar_izquierda": "Ramo dell'arteria polmonare sinistra",
    "main_pulmonary_artery_and_ductus_arteriosus": "Arterie polmonari principali e catetri arteriosi",
    "ob_three_vessel_view": "Tri vasi sanguigni",
    "main_pulmonary_artery": "Arteria polmonare principale",
    "obcleft_ventricular_outflow_tract_section": "Sezione di uscita camera sinistra",
    "ob_ight_ventricular_outflow_tract_view_2": "Sezione di uscita camera destra2",
    "ob_ight_ventricular_outflow_tract_view_1": "Taglio di uscita camera destra1",
    "superior_vena_cava": "Vene superiore",
    "ductus_arteriosus": "Catetri arteriali",
    "main_pulmonary_artery_continuous_with_right_ventricle": "Arteria polmonare principale e ventricular destra",
    "inferior_vena_cava_aorta": "Arteria principale inferiore",
    "multiple_ribs": "Molte costole.",
    "single_rib": "Una singola costola",
    "renal_pelvis": "Cucina renale",
    "ob_kidney": "Reni",
    "umbilical_cord_abdominal_wall_entrance": "Ingresso della parete addominale del cordone ombelicale",
    "umbilical_vein": "Vene ombelicale",
    "ob_magenblase": "Bubble di stomaco",
    "ob_bdominal_circumference_view": "Taglio periferico",
    "ob_continuous_ascending_aorta_and_interventricular_septum": "Intervalo continuo tra arteria e camera",
    "ob_left_ventricular_outflow_tract": "Escesa camera sinistra",
    "ob_spine": "Spina dorsale",
    "ob_lungs": "polmoni",
    "ob_four_chamber_view": "Il cuore a quattro cavità",
    "ob_extra_deduction_points_left_ventricular_outflow_tract": "Sconto aggiuntivo per l'uscita della camera sinistra",
    "ob_bilateral_lungs": "Doppio polmone",
    "ob_pinal_vertebrae": "Vertebra vertebrale",
    "ob_at_least_one_pulmonary_vein_enters_the_left_atrium": "Almeno una vena polmonare nella stanza sinistra.",
    "ob_cross_shaped_endocardial_cushion": "Endocardiale mat crociato",
    "ob_foramen_ovale_valve": "Ovulo rotolo",
    "ob_four_chambers_of_the_heart": "Le quattro camere del cuore",
    "ob_mandibular_bone_sagittal_plane": "Mascella inferiore (freccia)",
    "ob_maxillary_bone_sagittal_plane": "Mascella superiore (freccia)",
    "ob_lower_jaw_sagittal_plane": "Mascella inferiore (freccia)",
    "ob_lower_lip_sagittal_plane": "Labbra inferiore (freccia)",
    "ob_upper_lip_sagittal_plane": "Labbra superiore (freccia)",
    "ob_frontal_bone_sagittal_plane": "Ossa frontale (freccia)",
    "ob_nasal_bone_sagittal_plane": "Naso (freccia)",
    "ob_nose_tip_sagittal_plane": "Punta del naso (freccia)",
    "median_sagittal_plane_of_face_view": "Taglio a freccia centrale",
    "ob_mandible": "Mascella inferiore",
    "ob_chin": "Che?",
    "ob_maxilla": "Mascella superiore",
    "tip_of_the_nose": "Punta del naso",
    "frontal_bone": "Ossa frontale",
    "ob_lens": "Corpo cristallino",
    "ob_nasal_bone_and_maxillary_frontal_process": "Naso e mascella superiore",
    "eye_frame": "Cornice per gli occhi",
    "ob_cross_section_of_binocular_sphere_view": "Sezione a due occhi",
    "nasal_bone": "Naso",
    "bilateral_crystalline_lenses": "Bilaterale cristallo",
    "ob_bilateral_eye_sockets": "Orbita bilaterale",
    "ob_jaw": "Mascella inferiore",
    "lower_lip": "labbra inferiore",
    "ob_coronal_section_nose_and_lip_view": "Naso e labbra corona taglio",
    "upper_lip": "Labbra superiore",
    "ob_bilateral_nostrils": "Naso bilaterale",
    "ob_posterior_cranial_fossa_pool": "Piscina posteriore craniale",
    "cerebellum": "Cervello piccolo",
    "ob_transverse_section_of_cerebellum_view": "Sezione trasversale del cervello",
    "ob_cisterna_magna": "Piscina posteriore del cranio",
    "ob_verebellar_hemisphere": "Emisfero del cervello",
    "ob_vermis_of_cerebellum": "Cervello piccolo",
    "ob_ransverse_section_through_lateral_ventricle_view": "Sezione transversale ventricolare laterale",
    "ob_educed_points_with_for_brain_foot": "Piedi extra nel cervello",
    "ob_anterior_horn_of_lateral_ventricle": "Angolo anteriore ventricolare laterale",
    "ob_posterior_horn_of_lateral_ventricle": "Corno posteriore ventricolare laterale",
    "ob_choroid_plexus": "Rete",
    "ob_transverse_section_through_thalamus_view": "Sezione transversale del talamo",
    "ob_educed_points_with_cerebellum": "Deduczione extra del cervello",
    "ob_image_quality_and_detail_desc": "Qualità dell'immagine (chiarezza, profondità adeguata, strutturazione centrale)",
    "ob_brain_middle": "Linea centrale del cervello",
    "ob_skull_ring": "Anello del cranio",
    "ob_cerebral_peduncle": "I piedi del cervello",
    "ob_thalamus": "Talamo",
    "ob_sylvian_fissure": "Frattura esterna del cervello",
    "ob_transparent_compartment": "Divisione trasparente",
    "statistic": {
        "user_online_avg": "Durata media online degli utenti",
        "answer_sheet_report": "Compiti a casa e valutazione",
        "answer_sheet_avg": "Valore medio dei compiti e della valutazione",
        "device_report": "Rapporto sulle statistiche delle apparecchiature",
        "content_report": "Relazione sulle statistiche dei contenuti",
        "device_overview": "Panoramica delle informazioni di base",
        "large_billboard": "",
        "statistic_report": "",
        "statistic_data_title": "",
        "maintenance_range": {
            "0": "",
            "1": "",
            "2": "",
            "3": "",
            "6": "",
            "15": ""
        },
        "deviceDetection": "",
        "QCStatistics": "",
        "dr_distribution": "",
        "adult": "",
        "child": "",
        "dr_exam_types": {
            "P07": "",
            "P06": "",
            "P05": "",
            "P04": "",
            "P03": "",
            "P02": "",
            "P01": ""
        },
        "technician": "",
        "quality_images": "",
        "BI_DR_title": "",
        "exam_create_image": "",
        "exam_create_video": "",
        "device_search": "",
        "active_level": "",
        "deviceFailureTip": "",
        "examCountOfDevice": "",
        "byDevice": "",
        "video_count": "",
        "statistic_device_new_exam": "",
        "byUploader": "",
        "byExam": "",
        "unbind": "",
        "doppler_user_report": "",
        "table_attendee_distribution": "",
        "statistic_paper": "",
        "library_accumulated_views": "",
        "region_txt": "",
        "branch_txt": "",
        "correction_teacher": "",
        "assignment_name": "",
        "answer_sheet_report_title": "",
        "answerSheetChartTitle": "",
        "probeUsageNumber": "",
        "owner_departments": "",
        "data_detail_export": "",
        "time_range_tip_month": "",
        "probe": {
            "others": "",
            "S": "",
            "P": "",
            "L": "",
            "C": ""
        },
        "probeUsageRate": "",
        "min_tip": "",
        "max_tip": "",
        "not_support_map": "",
        "total": "",
        "summary_user_count": "",
        "doppler_offline_number": "",
        "doppler_standby_number": "",
        "doppler_working_number": "",
        "device_install_time_chart": "",
        "all_exam_types": {
            "0": "",
            "1": "",
            "2": "",
            "3": "",
            "4": "",
            "5": "",
            "6": "",
            "7": "",
            "8": "",
            "9": "",
            "10": "",
            "11": "",
            "12": ""
        },
        "summary_exam_count_ingroup": "",
        "table_exam_type_count": "",
        "table_total_working_time": "",
        "table_total_startup_time": "",
        "group_joined_count": "",
        "equipment_ownership": "",
        "using_departments": "",
        "exam_type_count": "",
        "year_range": {
            "0": "",
            "1": "",
            "2": "",
            "3": "",
            "4": ""
        },
        "exam_create": "",
        "device_exam_report": "",
        "BI_device_subtitle": "",
        "chart_legend_other": "",
        "chart_legend_group": "",
        "summary_org_count": "",
        "summary_image_count": "",
        "summary_exam_count": "",
        "statistic_device": "",
        "statistic_group": "",
        "examCountOfOrganization": "",
        "deviceStatus": "",
        "deviceUtilizationRate": "",
        "deviceInstallTime": "",
        "BI_title_dr_demo": "",
        "image_count": "",
        "minute_text": "",
        "sex_map": {
            "0": "",
            "1": "",
            "2": ""
        },
        "table_library_like": "",
        "table_library_view": "",
        "table_library_title": "",
        "table_iworks_finish_count": "",
        "table_iworks_standard_count": "",
        "table_patient_sex": "",
        "table_patient_name": "",
        "table_iworks_name": "",
        "table_send_ts": "",
        "table_exam_id": "",
        "group_types_map": {
            "0": "",
            "1": ""
        },
        "increased_group_total": "",
        "increased_group_avg": "",
        "table_group_create_ts": "",
        "table_group_admin": "",
        "table_group_creator": "",
        "table_group_type": "",
        "user_online_duration_report": "",
        "user_online_duration": "",
        "doppler_user_total": "",
        "doppler_user_avg": "",
        "table_first_login_ulink": "",
        "device_active_number": "",
        "device_utilization_rate_report": "",
        "device_utilization_rate": "",
        "device_status_report": "",
        "device_status": "",
        "device_status_map": {
            "0": "",
            "1": "",
            "2": "",
            "3": ""
        },
        "table_comment_count": "",
        "table_tag_count": "",
        "table_exam_type": "",
        "table_exam_date": "",
        "table_birthday": "",
        "table_offline_time": "",
        "table_standby_duration": "",
        "table_last_standby_time": "",
        "table_online_duration": "",
        "table_last_startup_time": "",
        "table_current_status": "",
        "device_install_time_report": "",
        "doppler_number": "",
        "iworks_statistic_report": "",
        "doppler_status": "",
        "doppler_utilization_rate": "",
        "doppler_install_time": "",
        "doppler_user": "",
        "library_statistic_report": "",
        "iworksCompliance": "",
        "search_user_name": "",
        "group_active_avg": "",
        "table_video_count": "",
        "other_exam_type": "",
        "device_install_time": "",
        "table_install_ts": "",
        "table_product_type": "",
        "distance_training_number": "",
        "remote_consultation_number": "",
        "device_increased_report": "",
        "total_device": "",
        "device_increased_avg": "",
        "device_increased": "",
        "devices_report": "",
        "device_types": {
            "doppler": "",
            "ulinker": "",
            "isync": ""
        },
        "device_type_title": "",
        "breastAI_usage_report": "",
        "breastAI_usage": "",
        "user_types": {
            "0": "",
            "1": "",
            "2": "",
            "3": "",
            "4": ""
        },
        "time_txt": "",
        "total_user": "",
        "user_active_avg": "",
        "user_increased_avg": "",
        "time_range_tip": "",
        "table_product_name": "",
        "table_series_number": "",
        "table_device_id": "",
        "table_last_login_account": "",
        "table_hospital": "",
        "table_device_name": "",
        "table_mac_addr": "",
        "table_exam_count": "",
        "table_last_login_time": "",
        "table_login_times": "",
        "table_online_time": "",
        "table_last_send_time": "",
        "table_fail_count": "",
        "table_success_count": "",
        "table_image_count": "",
        "table_upload_account": "",
        "table_city": "",
        "table_province": "",
        "table_organization": "",
        "table_email": "",
        "table_user_type": "",
        "table_register_time": "",
        "table_nickname": "",
        "table_account": "",
        "table_review_duration": "",
        "table_review_times": "",
        "table_live_duration": "",
        "table_attendee_count": "",
        "table_live_end": "",
        "table_live_start": "",
        "table_live_type": "",
        "table_host": "",
        "table_group_subject": "",
        "table_group_id": "",
        "table_subject": "",
        "table_index": "",
        "data_export": "",
        "data_list": "",
        "distance_training": "",
        "remote_consultation": "",
        "live_tip2": "",
        "live_tip1": "",
        "live_attendee_count": "",
        "live_duration": "",
        "live_times": "",
        "search_btn": "",
        "time_map": {
            "1M": "",
            "TY": "",
            "1Y": "",
            "6M": "",
            "3M": ""
        },
        "weeks": {
            "0": "",
            "1": "",
            "2": "",
            "3": "",
            "4": "",
            "5": "",
            "6": ""
        },
        "ulinkerIncreasedUsers": "",
        "end_date": "",
        "start_date": "",
        "theme": "",
        "online_statistic": "",
        "library_statistic": "",
        "iworks_statistic": "",
        "exam_increased_report": "",
        "device_manage": "",
        "increased_group_report": "",
        "group_active_report": "",
        "user_active_report": "",
        "user_increased_report": "",
        "live_report": "",
        "exam_count": "",
        "exam_distribution": "",
        "back_btn": "",
        "device_distribution": "",
        "cancel_btn": "",
        "confirm_btn": "",
        "device_related": "",
        "iworks_protocol": "",
        "group_related": "",
        "content_related": "",
        "user_related": "",
        "selected": "",
        "months": {
            "1": "",
            "2": "",
            "3": "",
            "4": "",
            "5": "",
            "6": "",
            "7": "",
            "8": "",
            "9": "",
            "10": "",
            "11": "",
            "12": ""
        },
        "show_type_map": {
            "0": "",
            "1": ""
        },
        "show_type": "",
        "search_group_name": "",
        "statistic_time": "",
        "BI_setting_title": "",
        "iworksUseTimes": "",
        "iworksUserCount": "",
        "library": "",
        "breastAI": "",
        "videoIncreased": "",
        "imageIncreased": "",
        "examIncreased": "",
        "dopplerTotal": "",
        "dopplerIncreased": "",
        "ulinkerTotal": "",
        "ulinkerIncreased": "",
        "iSyncTotal": "",
        "iSyncIncreased": "",
        "liveDuration": "",
        "liveUserTimes": "",
        "liveCount": "",
        "groupTotal": "",
        "groupIncreased": "",
        "groupActive": "",
        "userActive": "",
        "userTotal": "",
        "userIncreased": "",
        "BI_subtitle": "",
        "BI_title_demo": "",
        "BI_title": ""
    },
    "data_too_old_to_locate": "I dati sono troppo vecchi per individuare il messaggio originale",
    "unable_locate_original_message": "Impossibile individuare il messaggio originale",
    "quoted_content_has_been_withdrawn": "Il contenuto citato è stato ritirato",
    "quoted_content_has_been_deleted": "Il contenuto citato è stato cancellato",
    "group_has_joined": "Siete già entrati a far parte di questo gruppo",
    "scan_qr_code": "Identificare il codice QR nell'immagine",
    "locate_original_message": "Individua la posizione originale",
    "share_to_conversation": "Condividi alla conversazione",
    "download_to_local": "Scarica in locale",
    "share_qrcode_to": "Condividi il codice QR del gruppo a",
    "quote_title": "citazione",
    "temporarily_store": "",
    "total_score": "",
    "improvement_suggestions": "",
    "analysis_result": "",
    "comprehensive_evaluation": "",
    "ulinker_settings": "",
    "AI_analysis_results": "",
    "answer_not_submitted_next_tips": "",
    "re_answer_cleared_tips": "",
    "answer_not_submitted_leave_tips": "",
    "evaluating": "",
    "please_wait_ai_analysis": "",
    "ai_analysis_encountered_retry_tips": "",
    "answer_automatically_review_tips": "",
    "please_answer_here": "",
    "used_time": "",
    "other_answers": "",
    "best_answer": "",
    "my_answer": "",
    "statistical_analysis": "",
    "answering_requirements": "",
    "video_materials": "",
    "completed_total": "",
    "answer_record": "",
    "professional_imaging_knowledge_tips": "",
    "you_could_say_tips": "",
    "delete_apply_multicenter_tips": "",
    "overdue_assignment_transmit_tip": "",
    "password_rule_incorrect": "",
    "PacsServerError": "",
    "uploading": "",
    "upload_success": "",
    "exam_overdue_creator_tip": "",
    "student_org": "",
    "single_chat": "",
    "not_group_chat": "",
    "table_drag_to_reorder": "",
    "edit_collection_group_options_tips": "",
    "unsaved_item": "",
    "image_label": "",
    "homework": {
        "upload": {
            "error": {
                "noOptions": "",
                "invalidScore": "",
                "fieldRequired": "",
                "maxColumns": "",
                "maxRows": "",
                "general": "",
                "fileFormat": "",
                "fileSize": ""
            },
            "download_excel_template": "",
            "upload_excel": ""
        },
        "detail_requirements": "",
        "exam_not_exist": "",
        "revoke": "",
        "revoke_exam_confirm": "",
        "operation_min_subtopic": "",
        "progress": {
            "legend": {
                "graded": "",
                "ungraded": "",
                "current": "",
                "alreadyDone": "",
                "notDone": ""
            },
            "completionStatus": "",
            "correctionHeader": "",
            "answeringHeader": ""
        },
        "confirm_delete_topic": "",
        "delete_paper": "",
        "delete_failed ": "",
        "delete_success": "",
        "confirm_delete_paper": "",
        "save_failed": "",
        "save_success": "",
        "topic_answer_required": "",
        "topic_score_required": "",
        "exam_title_required": "",
        "reference_answer": "",
        "exam_title_tip": "",
        "upload_requirements": {
            "item5": "",
            "item4": "",
            "item3": "",
            "item2": "",
            "item1": ""
        },
        "preview_exam_content": ""
    },
    "welcome_to_realm_imaging_intelligence": "",
    "leave_ai": "",
    "expand_menu": "",
    "empty_title": "",
    "empty_nodes": "",
    "empty_group_options": "",
    "collection_requirements": "",
    "integrity_verified_submit_cases": "",
    "basic_configuration": "",
    "delete_apply_create_multicenter": "",
    "error": {
        "paperAssignmentNoAuth": "",
        "assignmentNotBelongToUser": "",
        "paperAssignmentHasBeenRevoked": "",
        "paperAnswerSheetNotExist": "",
        "paperAnswerSheetLocked": "",
        "paperAssignmentDuplication": "",
        "paperSubmitDueTimeError": "",
        "codeAfsError": "",
        "patientExternalSame": "",
        "timestampError": "",
        "userIsLiving": "",
        "liveStatusError": "",
        "livingCreating": "",
        "userNeedSafeAuthToken": "",
        "userCanNotBindAccount": "",
        "codeNotSupportSelfNet": "",
        "paramsError": "",
        "userEmailInvalid": "",
        "codeMustRequired": "",
        "userLoginNameInvalidate": "",
        "userPasswordLockError": "",
        "userReferralCodeError": "",
        "userEmailError": "",
        "userMobileError": "",
        "userPwdEnhanceError": "",
        "userLoginNameRegisted": "",
        "userMobileRegisted": "",
        "userEmailHasRegisted": "",
        "codeError": "",
        "codeSendError": "",
        "codeCountLimitError": "",
        "codeTimerLimitError": "",
        "imageCodeError": "",
        "LimitMobileLogin": "",
        "userWaitVerification": "",
        "userStatusFail": "",
        "userPasswordError": "",
        "userLoginNameError": "",
        "operateFrequenceError": "",
        "userLoginBusy": "",
        "deviceInfoNotFound": "",
        "deviceNotFound": "",
        "multiCenterExamStatusError": "",
        "multiCenterHadCollector": "",
        "multiCenterLackReviewer": ""
    },
    "standardization_rate": "",
    "integrity_rate": "",
    "jump_external_links_tips": "",
    "switch_app_client_to_workstation_tip": "",
    "share_paper_to": "",
    "average_duration": "",
    "cardiovascular": "",
    "human_body_diagram": "",
    "welcome_clinical_thinking_exercise_tips": "",
    "random_exercise": "",
    "total_completion_count": "",
    "re_answer": "",
    "recent_submission_time": "",
    "completion_times": "",
    "topic_types": "",
    "exercises_my_completed_title": "",
    "clinical_thinking_practice_title": "",
    "ai_welcome_tips": "",
    "answering": "",
    "thinking": "",
    "historical_records": "",
    "screen_shot": "",
    "sorry_an_error_occurred_please_try_again": "",
    "server_request_exception_tips": "",
    "user_has_terminated_answering": "",
    "input_talk_about_tips": "",
    "input_talk_about_tips_Shift_Enter": "",
    "new_chat": "",
    "deep_thinking": "",
    "ai_chat": "",
    "select_video_window_to_be_recorded": "",
    "detailed_mode": "",
    "simplified_mode": "",
    "mode_select": "",
    "edit_collection_group": "",
    "collection_group": "",
    "collection_category": "",
    "please_select_group_title": "",
    "add_collection_group": "",
    "formatDateTime": {
        "yyyy-MM-dd HH:mm:ss": "",
        "yyyy-MM-dd HH:mm": "",
        "yyyy-MM-dd HH": "",
        "yyyy-MM-dd": "",
        "yyyy-MM": "",
        "yyyy": ""
    },
    "please_fill_field_name_tips": "",
    "number_options_greater_2": "",
    "empty_element_title_tips": "",
    "new_options_default_selection": "",
    "not_required_filed": "",
    "required_filed": "",
    "option_name": "",
    "edit_options": "",
    "date_time": "",
    "check_box": "",
    "radio": "",
    "supply_exam_image": {
        "err_tip": {
            "select_up_to_number_files": "",
            "unsupported_image_format": "",
            "many_tasks_have_failed_tips": "",
            "select_up_to_500_files": "",
            "not_exam_sender": "",
            "import_exam_image_succ": "",
            "import_exam_image_failed": "",
            "upload_file_failed": "",
            "invalid_exam_id": "",
            "invalid_exam_path": "",
            "unsupported_file_format": "",
            "more_than_one_file": "",
            "no_files": ""
        }
    },
    "only_training_tips": "",
    "dopplerControl": {
        "Disconnect": "",
        "UPDATE": "",
        "SAVEIMG": "",
        "CALIPER": "",
        "MEASURE": "",
        "PWMode": "",
        "MMode": "",
        "BMode": "",
        "CMode": "",
        "ImageQuality": "",
        "Freeze": "",
        "DuplexOrTriplex": "",
        "Tint Map": "",
        "Quick Angle": "",
        "SV": "",
        "Angle": "",
        "Unfreeze": "",
        "SwitchMode": "",
        "FlowOnly": "",
        "StereoFlowSwitch": "",
        "WF": "",
        "GlazingFlow": "",
        "Packer Size": "",
        "FineSteer": "",
        "Steer": "",
        "Scale": "",
        "Baseline": "",
        "iClear+": "",
        "iClear": "",
        "HDscope": "",
        "iBeam": "",
        "iOneTouch": "",
        "iTouch": "",
        "Depth": "",
        "Zoom": "",
        "Dyn Ra": "",
        "Gray Map": "",
        "UMA": "",
        "Persistence": "",
        "Gain": "",
        "Smooth": ""
    },
    "doppler_controlling_device_tips": "",
    "doppler_controlling_device_name": "",
    "effect_after_restart": "",
    "white_board": "",
    "whiteboard": {
        "more": "",
        "large": "",
        "normal": "",
        "small": "",
        "mini": "",
        "thin": "",
        "thick": "",
        "close": "",
        "clear": "",
        "laserPointer": "",
        "shape": "",
        "eraser": "",
        "text": "",
        "pencil": "",
        "selector": "",
        "clicker": ""
    },
    "userBindPacsAccountError": "",
    "video_type_proportion": "",
    "image_type_proportion": "",
    "relieve": "",
    "unbind_description": "",
    "relation": "",
    "bind_description": "",
    "unbind_tip": "",
    "account_association": "",
    "remove_association": "",
    "information_system": "",
    "associated": "",
    "visit": "",
    "cloud_platform": "",
    "my_application": "",
    "scan_to_download": "",
    "email_login": "",
    "mobile_login": "",
    "ecology_welcome": "",
    "scan_login_title": "",
    "qrcode_login": "",
    "password_login": "",
    "verification_login": "",
    "choose_language": "",
    "ecology_title": "",
    "cardiac_parasternal_short_axis_psax": "",
    "Cardiac_Smart": "",
    "unsave_tip": "",
    "web_live_name_support_tips": "",
    "LOCAL_TRACK_ERROR": {
        "NOT_SUPPORTED": "",
        "MEDIA_OPTION_INVALID": "",
        "DEVICE_NOT_FOUND": "",
        "PERMISSION_DENIED": "",
        "MICROPHONE_PERMISSION_DENIED": "",
        "CAMERA_PERMISSION_DENIED": "",
        "CONSTRAINT_NOT_SATISFIED": "",
        "SHARE_AUDIO_NOT_ALLOWED": "",
        "OTHERS": "",
        "NOT_READABLE": ""
    },
    "cloud_record": "",
    "account_sync_completed_tips": "",
    "notify_linker_sync_account_tips": "",
    "notify_linker_start_live_tips": "",
    "cardiac_left_cardiac_ventricle": "",
    "cardiac_right_cardiac_ventricle": "",
    "cardiac_right_cardiac_atrium": "",
    "U-Linker_agree_binding": "",
    "cardiac_left_cardiac_atrium": "",
    "microphone_detection_tips": "",
    "camera_preview_tips": "",
    "cardiac_parasternal_left_ventricular_long_axis": "",
    "cardiac_parasternal_short_axis_psax_apex": "",
    "cardiac_parasternal_short_axis_psax_pm": "",
    "cardiac_parasternal_short_axis_psax_mv": "",
    "cardiac_parasternal_short_axis": "",
    "cardiac_aortic_arch_view": "",
    "cardiac_long_axis_inferior_vena_cava_below_xiphoid_process": "",
    "cardiac_cross_four_chambers_below_xiphoid_process": "",
    "cardiac_five_chambers_apex_heart": "",
    "cardiac_four_chambers_apex_heart": "",
    "cardiac_three_chambers_apex_heart": "",
    "cardiac_two_chambers_apex_heart": "",
    "cardiac_short_axis_major_artery": "",
    "parasternal_left_ventricular_long_axis": "",
    "cardiac_papillary_muscle": "",
    "cardiac_aortic_arch": "",
    "cardiac_ascending_aorta": "",
    "cardiac_hepatic_vein": "",
    "cardiac_inferior_vena_cava": "",
    "cardiac_atrial_septum": "",
    "cardiac_interventricular_septum": "",
    "cardiac_pulmonary_aortic_valve": "",
    "cardiac_pulmonary_aorta": "",
    "cardiac_tricuspid_valve": "",
    "cardiac_right_atrium": "",
    "cardiac_descending_aorta": "",
    "cardiac_mitral_valve": "",
    "cardiac_aorta": "",
    "cardiac_left_atrium": "",
    "cardiac_left_ventricle": "",
    "right_ventricular_outflow_tract": "",
    "unbind": "",
    "fault_description": "",
    "fault_code": "",
    "fault_time": "",
    "historical_faults": "",
    "received_binding_from_pc_tips": "",
    "bind_ultrasound_device_title": "",
    "binding": "",
    "instruction_sent": "",
    "processed": "",
    "unprocessed": "",
    "exam_duration": "",
    "video_number": "",
    "image_mode": "",
    "network_camera_setting": "",
    "exam_type_proportion": "",
    "latest_reporting_time": "",
    "device_model": "",
    "groupQrcodeExpire": "",
    "latest_version": "",
    "desktop_text": "",
    "upgrade_input_method": "",
    "reapply_btn": "",
    "withdrawn": "",
    "existing_items": "",
    "new_options": "",
    "preview": "",
    "delete_item_tips": "",
    "delete_category_contents_tips": "",
    "adding_child_nodes_tips": "",
    "empty_categories_submitted_tips": "",
    "add_standard_library_fields": "",
    "add_custom_collection_fields": "",
    "add_subcategories": "",
    "add_same_level_category": "",
    "downgrade_action": "",
    "upgrade_action": "",
    "move_action": "",
    "homework_search_key": "",
    "step_tip": "",
    "score_integer_tip": "",
    "deadline_exceeded_tip": "",
    "homework_overdue_tip": "",
    "homework_correcting_tip": "",
    "homework_cancheck_tip": "",
    "publish_at": "",
    "homework_type3": "",
    "homework_type1": "",
    "homework_type2": "",
    "homework_type4": "",
    "homework_type5": "",
    "device_binding_success_and_more_info": "",
    "whether_pull_xxx_equipment_into_group": "",
    "copy_link": "",
    "submit_paper_tip": "",
    "correct_paper_tip": "",
    "question_score": "",
    "correction_progress": "",
    "zero_point": "",
    "next_question": "",
    "prev_question": "",
    "score_range": "",
    "score_less": "",
    "avg_point": "",
    "min_point": "",
    "max_point": "",
    "point_tip": "",
    "grades_distribution": "",
    "unfinished_number": "",
    "complete_number": "",
    "to_be_complete_number": "",
    "correction_teacher_empty_tip": "",
    "deadline_empty_tip": "",
    "allow_view_correction": "",
    "set_homework": "",
    "select_homework": "",
    "assign_homework_to": "",
    "error_tip": "",
    "correct_tip": "",
    "submitted_answers": "",
    "homework_operation_step": "",
    "stop_collecting": "",
    "start_collecting": "",
    "correction_time": "",
    "correction": "",
    "student_name": "",
    "not_submitted": "",
    "corrected": "",
    "pending_correction": "",
    "feedback_time": "",
    "correcting": "",
    "assign_homework": "",
    "share_paper": "",
    "submission_time": "",
    "arrange_time": "",
    "assign_people": "",
    "enter_correction": "",
    "creation_time": "",
    "author": "",
    "paper_results": "",
    "paper_duration": "",
    "paper_statistics": "",
    "complete_again": "",
    "release_time": "",
    "deadline": "",
    "paper_question_count": "",
    "paper_total_score": "",
    "view_homework": "",
    "topic_type": {
        "operation": "",
        "shortAnswer": "",
        "multiSelect": "",
        "singleSelect": ""
    },
    "exam_incomplete": "",
    "sorry_no_post_dat_with_tag": "",
    "my_cloud_exam": "",
    "famous_teacher_post": "",
    "vetus_club_post": "",
    "functional_technology_post": "",
    "medical_workers_home_post": "",
    "explosive_product_post": "",
    "operation_guidance": "",
    "clinical_beauty_map_post": "",
    "new_hot_post": "",
    "certificate_agree_tips": "",
    "live_expired_tips": "",
    "re_analyze_tips": "",
    "re_analyze_title": "",
    "reason_for_deficiency_text": "",
    "exam_bank": "",
    "arranged_exam": "",
    "topic_count": "",
    "topic_summary": "",
    "correcting_exam": "",
    "exam_completed": "",
    "cloud_exam": "",
    "input_your_name": "",
    "image_tag_unique_tip": "",
    "verify_expired": "",
    "multicenter_release_tip": "",
    "retry": "",
    "ai_doppler_layout_list_error": "",
    "not_filled": "",
    "apply_time": "",
    "apply_name": "",
    "apply_account": "",
    "enter_age_wrong": "",
    "standard_library": "",
    "add_category": "",
    "unit": "",
    "filed_name": "",
    "add_field": "",
    "case_submitting_tip": "",
    "not_filled_tip": "",
    "multicenter_collection_progress": "",
    "multicenter_collected": "",
    "multicenter_reject_tip": "",
    "enter_correct_number": "",
    "please_select_end_time": "",
    "domain_verify": "",
    "setting_management": "",
    "uploader_nickname": "",
    "uploader_login_name": "",
    "please_input_login_name": "",
    "data_detail_export": "",
    "Abdomen_smart_abd": "",
    "multicenter_approve": "",
    "start_ai_live_analyze": "",
    "apply_btn": "",
    "required_tip": "",
    "multicenter_remarks": "",
    "multicenter_materials": "",
    "multicenter_unit_count": "",
    "multicenter_case_count": "",
    "multicenter_project_cycle": "",
    "PI_unit_name": "",
    "multicenter_name": "",
    "withdrawal_of_application": "",
    "multicenter_release": "",
    "multicenter_config_tip": "",
    "multicenter_approval": "",
    "applying_tip": "",
    "pending_approval_tip": "",
    "to_be_configured": "",
    "pending_approval": "",
    "myocardial_strain_multicenter": "",
    "create_multicenter": "",
    "view_groupset_statistics_report": "",
    "view_groupset_bi_data_display": "",
    "groupset_admin_abilities_tips": "",
    "my_manage_groupset": "",
    "my_created_groupset": "",
    "auth_mng": "",
    "library": {
        "tag": "",
        "category": ""
    },
    "check_filter_result_of_post": "",
    "total_count_of_post": "",
    "is_optional": "",
    "ExamBelongMultiCenterError": "",
    "batch_cancel_download": "",
    "batch_continue_downloading": "",
    "pause_download": "",
    "paused": "",
    "delete_message_by_other": "",
    "delete_message_by_self": "",
    "device_pixel_Ratio_change_tips": "",
    "delete_case_fail": "",
    "delete_case_success": "",
    "reselect_view_type_anaylze_tips": "",
    "go_download": "",
    "reselect_view_type_tips": "",
    "reselect_view_type_anaylze_success": "",
    "reselect_view_type_anaylze_fail": "",
    "send_by_ctrl_enter": "Premere Ctrl+Invio per inviare",
    "delete_exam_tips": "",
    "multi_center_patient_id_empty": "",
    "feedback_success_tips": "",
    "save_txt": "",
    "AiAnalyzeFailError": "",
    "AiAnalyzeError": "",
    "exam_mode": "",
    "View_database_tables": "",
    "jump_external_browser_tips": "",
    "cancel_tasks_ask": "",
    "open_directory_failed_tips": "",
    "continue_downloading_ask": "",
    "cancel_download": "",
    "feedback_date_range": "",
    "feedbacker": "",
    "continue_downloading": "",
    "save_directory": "",
    "download_task_manager": "",
    "reselect_view_type": "",
    "please_describe_your_issue": "",
    "question_feedback": "",
    "failed": "",
    "success": "",
    "expiration_date": "",
    "delete_case_confirm": "",
    "delete_case": "",
    "patientExternalEditNotAllowed": "",
    "big_files_tips": "",
    "display_exam_picture_with_struct": "",
    "AiServerConnectError": "",
    "multi_center_patient_id_too_long": "",
    "multi_center_patient_id": "",
    "system_administrator": "",
    "group_view_summary": "",
    "not_friend_tips": "",
    "ABD_Struct_Convergence_Lower_Cavity": "",
    "ABD_Struct_Right_Hepatic_Vein": "",
    "ABD_Struct_Left_Hepatic_Vein": "",
    "ABD_Struct_Middle_Hepatic_Vein": "",
    "ABD_Struct_Pancreas": "",
    "ABD_Struct_left_External_Superior_Branch": "",
    "ABD_Struct_Left_Inferior_External_Branch": "",
    "ABD_Struct_Left_Lateral_Branch": "",
    "ABD_Struct_Left_Medial_Branch": "",
    "ABD_Struct_Sagittal_Portal_Vein": "",
    "ABD_Struct_Corner": "",
    "ABD_Struct_Long_Aaxis_Inferior_Vena_Cava": "",
    "ABD_Struct_Long_Axis_Abdominal_Aorta": "",
    "ABD_Struct_Left_Lobe": "",
    "ABD_Struct_Caudal_Lobe_Liver": "",
    "ABD_Struct_Gallbladder": "",
    "ABD_Struct_Right_Lobe": "",
    "ABD_Struct_Kidney": "",
    "ABD_Struct_Portal_Vein": "",
    "ABD_UNDEFINED": "",
    "ABD_Liver_2nd_PH": "",
    "ABD_Panc_LongAxis": "",
    "ABD_Liver_Lt_Portal_Vein": "",
    "ABD_GB_LongAxis": "",
    "ABD_Liver_LHV_IVC": "",
    "ABD_Liver_Lt_Lobe_AAo": "",
    "ABD_Liver_Lt_Lobe_Caudate": "",
    "ABD_Liver_1st_PH": "",
    "ABD_Liver_Rt_Kidney": "",
    "ABD_Portal_Vein": "",
    "alternative_images": "",
    "selected_image": "",
    "edit_report_unenable_tips": "",
    "move_down": "",
    "move_up": "",
    "dr_ai_analyze_statistics_error": "",
    "select_transfer_image_max": "",
    "select_image": "",
    "inspection_overview": "",
    "not_started": "",
    "refresh_status": "",
    "initiated_live": "",
    "join_live_streaming": "",
    "ai_search_suggest": "",
    "level_d_count_and_rate": "",
    "level_c_count_and_rate": "",
    "level_b_count_and_rate": "",
    "level_a_count_and_rate": "",
    "operation_doctor": "",
    "body_position": "",
    "sub_part": "",
    "part": "",
    "view_score_display_rule": "",
    "everyone": "",
    "test_time_tips": "",
    "is_visible_text": "",
    "after_nickname_modified_in_group": "",
    "my_nickname_in_group": "",
    "group_modify_nickname": "",
    "group_nick_name": "",
    "level_d": "",
    "level_c": "",
    "level_b": "",
    "level_a": "",
    "not_group_member_live_tips": "",
    "dr_ai_analyze_statistics": "",
    "dr_ai_analyze": "",
    "display_exam_original_picture": "",
    "box_color": "",
    "deletion_text": "",
    "view_deletion": "",
    "structure_evaluation_text": "",
    "exam_type_text": "",
    "pravicy_policy_content": {
        "j": {
            "lead": {
                "d": "",
                "c": "",
                "b": "",
                "a": ""
            },
            "zipcode": "",
            "address": "",
            "email": "",
            "website": "",
            "title": ""
        },
        "i": {
            "lead": "",
            "title": ""
        },
        "h": {
            "lead": "",
            "title": ""
        },
        "g": {
            "lead": "",
            "title": ""
        },
        "f": {
            "lead": "",
            "title": ""
        },
        "e": {
            "content": {
                "f": "",
                "e": "",
                "d": "",
                "c": "",
                "b": "",
                "a": ""
            },
            "title": ""
        },
        "d": {
            "content": {
                "g": "",
                "f": "",
                "e": "",
                "d": "",
                "c": "",
                "b": "",
                "a": ""
            },
            "lead": "",
            "title": ""
        },
        "c": {
            "content": {
                "g": "",
                "f": "",
                "e": "",
                "d": "",
                "c": "",
                "b": "",
                "a": ""
            },
            "title": ""
        },
        "b": {
            "content": {
                "b": {
                    "content": "",
                    "lead": ""
                },
                "a": {
                    "content": "",
                    "lead": ""
                }
            },
            "title": ""
        },
        "a": {
            "content": {
                "f": "",
                "e": "",
                "d": "",
                "c": "",
                "b": "",
                "a": ""
            },
            "lead": "",
            "title": ""
        }
    },
    "pravicy_policy_catalogue": {
        "content": {
            "j": "",
            "i": "",
            "h": "",
            "g": "",
            "f": "",
            "e": "",
            "d": "",
            "c": "",
            "b": "",
            "a": ""
        },
        "title": "",
        "lead": ""
    },
    "pravicy_policy_foreword": {
        "content": {
            "g": "",
            "f": "",
            "e": "",
            "d": "",
            "c": "",
            "b": "",
            "a": "",
            "brief": ""
        },
        "title": ""
    },
    "pravicy_policy_date": {
        "public": "",
        "effective": "",
        "update": ""
    },
    "pravicy_policy_title": "",
    "pravicy_policy_company_name": "",
    "filename_illegal_characters": "",
    "quick_start_guide": "",
    "iworks_clip_summary_text": "",
    "data_loading_failed_tips": "",
    "avatar_preview_tips": "",
    "files_export_failed": "",
    "exam_original_picture": "",
    "exam_picture": "",
    "downloaded_task_failed": "",
    "downloaded_task_downloaded": "",
    "download_task_total": "",
    "download_task_completed": "",
    "avatar_preview": {
        "submit": "",
        "wait": "",
        "general": ""
    },
    "avatar_preview_title": "",
    "data_preparing": "",
    "no_ai_result": "",
    "missing_members": "",
    "missing_keys": "",
    "invalid_access": "",
    "live_disabled": "",
    "qr_install_app": {
        "unknown_server_type": "",
        "add_friend": "",
        "join_group": "",
        "allow_clipboard": "",
        "first_start_with_var": "",
        "start_directly": "",
        "has_downloaded": "",
        "download": ""
    },
    "personal_profile_tips": "",
    "personal_profile": "",
    "exam_socre_text": "",
    "discover": "",
    "chat_text": "",
    "view_count": "",
    "completion_rate": "",
    "quality_score": "",
    "jiajia_summary": "",
    "test_time": "",
    "test_time_text": "",
    "iWorks_test": "",
    "video_path_full_tips": "",
    "auto_cancel_video_tips": "",
    "cookies_privacy_agreement_acknowledge": "",
    "cookies_privacy_agreement_content": "",
    "cookies_privacy_agreement": "",
    "instruction_manual_version": "",
    "wechat_unsupported_tip": "",
    "userNoAuth": "",
    "other_side": "",
    "one_side": "",
    "exam_mode_fetal_heart": "",
    "exam_mode_obe": "",
    "export_live_data": "",
    "upload_image_max_text": "",
    "upload_ai_image": "",
    "upload_normal_image": "",
    "supplementary_image_types": "",
    "new_exam_switch_image_type_tip": "",
    "submit_ai_image_upload_tip": "",
    "leave_ai_image_upload_tip": "",
    "power_image": "",
    "color_image": "",
    "no_iclear_image": "",
    "b_image": "",
    "cover_image": "",
    "power_mode": "",
    "color_mode": "",
    "no_color_mode": "",
    "color_type": "",
    "group_apply_expire": "",
    "group_apply_pass": "",
    "group_apply_success": "",
    "send_files": "",
    "unit_file": "",
    "file_to_be_sent": "",
    "like_action": "",
    "group_join_input_tip": "",
    "group_join_apply_tip": "",
    "group_join_verify_btn": "",
    "group_join_verify_tip": "",
    "group_join_verify": "",
    "send_success": "",
    "sender": "",
    "total_duration": "",
    "latest_half_year_label": "",
    "latest_months_label": "",
    "latest_month_label": "",
    "latest_week_label": "",
    "message_type": "",
    "search_by_specified_memcontent": "",
    "search_by_date": "",
    "search_by_group_member": "",
    "upload_log_file_success": "",
    "upload_log_file": "",
    "choose_a_chat": "",
    "chunk_load_error": "",
    "remote_control_response_timeout": "",
    "push_stream_not_allow_remote_control": "",
    "search_uploader_name": "",
    "search_patient_name": "",
    "confirm_patient_information": "",
    "exam_created_success_tips": "",
    "never_notify": "",
    "delete_group_manager": "",
    "add_group_manager": "",
    "group_manager_permissions6": "",
    "group_manager_permissions5": "",
    "group_manager_permissions4": "",
    "group_manager_permissions3": "",
    "group_manager_permissions2": "",
    "group_manager_permissions1": "",
    "group_manager_permissions": "",
    "manager": "",
    "group_managers": "",
    "group_manage": "",
    "not_allowed_deleted_invited": "",
    "exam_number": "",
    "exam_info_text": "",
    "basic_info_text": "",
    "input_select_tips": "",
    "input_enter_tips": "",
    "leave_new_exam_tip": "",
    "create_new_exam": "",
    "end_time_greater_tips": "",
    "transfer_group_error": "",
    "transfer_group_confirm": "",
    "transfer_group": "",
    "clear_text": "",
    "contacts_select": "",
    "reselect_chat_send": "",
    "send_original_chat": "",
    "export_task_submitted": "",
    "exported_video_use_cover": "",
    "area_cannot_cropped": "",
    "delete_video_clip_not_allowed": "",
    "delete_video_track_not_allowed": "",
    "cloud_resources_processed": "",
    "server_resource_retried": "",
    "instruction_manual": "",
    "cannot_edit_multiple_modules": "",
    "update_ready_tip": "",
    "no_permission_operate": "",
    "case_database_fliter": {
        "papillary_carcinoma": "",
        "mucinous_carcinoma": "",
        "cephaloma": "",
        "infiltrating_lobular_carcinoma": "",
        "infiltrating_ductal_carcinoma": "",
        "invasive_breast_cancer": "",
        "lobular_carcinoma_in_situ": "",
        "ductal_carcinoma_in_situ": "",
        "noninvasive_breast_cancer": "",
        "pathological_classification_breast_cancer": "",
        "bi_rads_6": "",
        "bi_rads_5": "",
        "bi_rads_4c": "",
        "bi_rads_4b": "",
        "bi_rads_4a": "",
        "bi_rads_3": "",
        "bi_rads_2": "",
        "bi_rads_1": "",
        "bi_rads_type_text": "",
        "bi_rads_type": "",
        "coarse_calcification": "",
        "microcalcification": "",
        "intraductal_calcification": "",
        "calcification_out_mass": "",
        "calcification_in_mass": "",
        "no_calcification": "",
        "calcification": "",
        "mixed_change": "",
        "acoustic_shadow": "",
        "echo_enhancement": "",
        "no_change": "",
        "posterior_echo": "",
        "uneven_echo": "",
        "mixed_echo": "",
        "isoechoic": "",
        "hyperechoic": "",
        "hypoechoic": "",
        "anechoic": "",
        "echo_type": "",
        "hairpin_like": "",
        "microphylation": "",
        "angled": "",
        "finishing": "",
        "vague": "",
        "edge": "",
        "unparallel": "",
        "parallel": "",
        "direction": "",
        "irregular": "",
        "circular": "",
        "oval": "",
        "shape": "",
        "bi_rads_feature": "",
        "malignant": "",
        "benign": "",
        "benign_or_malignant": ""
    },
    "obstetric_qc": {
        "obstetric_mid_pregnancy_view_description": "",
        "obstetric_mid_pregnancy_view_name": "",
        "title": "",
        "nalysis_rconsider_uncompleted": "",
        "nalysis_uncompleted": "",
        "quality_user": "",
        "quality_user_leader": "",
        "quality_control_leader": "",
        "preset_data_set": "",
        "nalysis_completed": "",
        "result": "",
        "non_view": "",
        "base_view": "",
        "optional_view": ""
    },
    "verall_evaluation_qc": "",
    "obstetric_qc_ai": "",
    "no_relative_data_find": "",
    "ai_non_standard_view_text": "",
    "ai_recognition_view": "",
    "last_update_time": "",
    "uploader": "",
    "organization_name_too_long": "",
    "orgn_name_too_long": "",
    "patient_name_too_long": "",
    "patient_id_too_long": "",
    "exam_time_interval_too_long": "",
    "data_list": "",
    "ai_nalysis_no_result_tips": "",
    "ai_nalysis_result_tips": "",
    "reconsidered": "",
    "structure_name": "",
    "structure_evaluation": "",
    "cancel_rconsider_success": "",
    "cancel_rconsider_fail": "",
    "cancel_rconsider_tips": "",
    "exam_date": "",
    "view_type_select_tips": "",
    "view_type": "",
    "view_type_ai": "",
    "cancel_reconsider": "",
    "apply_reconsider": "",
    "institution_id": "",
    "institution_name": "",
    "compliance_rate": "",
    "view_compliance_rate": "",
    "exam_compliance_rate": "",
    "reason_for_deficiency": "",
    "go_location_of_file": "",
    "check_view_info": "",
    "view_quality_text": "",
    "view_quality": "",
    "view_detail": "",
    "group_view_score": "",
    "view_score_text": "",
    "view_score": "",
    "view_name_uploader": "",
    "view_name_text": "",
    "view_name": "",
    "latest_exam_time": "",
    "image_number": "",
    "non_standard": "",
    "basic_standard": "",
    "standard": "",
    "reset_data_tips": "",
    "last_uploader": "",
    "search_criteria": "",
    "ai_score_no_standard": "",
    "ai_score_basic_standard": "",
    "ai_score_standard": "",
    "ai_score": "",
    "score_value": "",
    "total_score_value": "",
    "reconsider_result_new_tips": "",
    "overall_evaluation_desc": "",
    "overall_evaluation": "",
    "view_group_name": "",
    "view_group": "",
    "view": "",
    "score_items": "",
    "full_mark": "",
    "proportion_weight_value": "",
    "proportion_weight": "",
    "exam_view_page": "",
    "view_view_page": "",
    "detect_image": "",
    "image_effects": "",
    "deletion": "",
    "display": "",
    "status_text": "",
    "status": "",
    "obstetric_qc_multicenter": "",
    "ai_error": {
        "invalid": "",
        "anaylse_timeout": "",
        "report_anaylse_fail": "",
        "anaylse_fail": "",
        "connect_fail": "",
        "disconnect": "",
        "prohibit_call": ""
    },
    "load_more": "",
    "video_clip_msg": "",
    "clip_time": "",
    "generated_video_clips": "",
    "failed_to_enter_editing_mode": "",
    "export_video": "",
    "exit_clip_tips": "",
    "pthological_conclusion": "",
    "ultrasonic_diagnosis": "",
    "update_confirm_tip": "",
    "video_clips": "",
    "local_library": "",
    "take_picture": "",
    "searc_in_case_database_one_picture": "",
    "searc_in_case_database_many_picture": "",
    "ai_searc_in_case_database": "",
    "searc_in_case_database_result": "",
    "is_no_case_text": "",
    "picture_is_no_roi": "",
    "get_picture_info_fail": "",
    "picture_is_only_jpg_jpeg_bmp_png": "",
    "picture_is_too_blurred": "",
    "custom_server": "",
    "default_server": "",
    "current_server": "",
    "favorite_confirm_private": "",
    "favorite_confirm_public": "",
    "favorite_confirm_tip": "",
    "favorites_private_tip": "",
    "favorites_public_tip": "",
    "live_has_no_playback": "",
    "rotate_right": "",
    "rotate_left": "",
    "zoom_out": "",
    "zoom_in": "",
    "local_camera": "",
    "camera_error": "",
    "mic_error": "",
    "thyroid_multicenter": "",
    "hfr_multicenter": "",
    "repeat_supply": "",
    "switch_to_aux_screen": "",
    "switch_to_main_screen": "",
    "server_ended_live": "",
    "image_type_not_compliant": "",
    "set_to_ordinary_ultrasound": "",
    "set_to_RCEH": "",
    "set_to_HFRR": "",
    "RCEH": "",
    "HFRR": "",
    "ordinary_ultrasound": "",
    "picture_type": "",
    "end_cloud_record": "",
    "start_cloud_record": "",
    "only_host_end_cloud_recording": "",
    "only_host_initiate_cloud_recording": "",
    "review_detail_title": "",
    "live_detail_title": "",
    "more_settinngs": "",
    "share_QR_code": "",
    "recording_ended": "",
    "recording_turned_on": "",
    "whose_recording_file": "",
    "cancel_btn": "",
    "envTitleMap": {
        "CE": "",
        "CN": ""
    },
    "recommend_download_app": "",
    "retract_more_details": "",
    "expand_more_details": "",
    "is_in_conference": "",
    "file_in_progress": "",
    "live_setting": "",
    "someone": "",
    "mainstream_is_sharing": "",
    "data_traffic_waring": "",
    "waiting_for_reconnection": "",
    "public_group_tip": "",
    "private_group_tip": "",
    "not_allow_modify_others_data": "",
    "cancel_favorite": "",
    "live_broadcast_viewers": "",
    "live_broadcast_duration": "",
    "live_broadcast_information": "",
    "live_broadcast_initiation_time": "",
    "live_broadcast_initiator": "",
    "view_details": "",
    "exit_edit": "",
    "organization_name": "",
    "not_set_tip": "",
    "avatar_title": "",
    "initiated_live_broadcast": "",
    "teaching_live": "",
    "consultation_live": "",
    "universal_live": "",
    "got_it": "",
    "select_organization": "",
    "enter_organization": "",
    "improve_infomation": "",
    "modify_nickname": "",
    "body_parts": "",
    "patient_sex": "",
    "patient_age": "",
    "patient_name": "",
    "apply_description": "",
    "apply_friend_remark": "",
    "apply_friend_info": "",
    "edit_review_info": "",
    "live_token_has_expired": "",
    "live_connection_continue_waiting_tips": "",
    "keep_waiting": "",
    "send_apply_add_friend": "",
    "apply_add_friend": "",
    "live_conference_reconnecting": "",
    "reload_page_tip": "",
    "resource_being_generated": "",
    "start_live_broadcast": "",
    "whether_last_stream_pushing_action": "",
    "whether_enable_automatic_recording": "",
    "linking": "",
    "input_device_remark": "",
    "edit_device_remark": "",
    "no_device_remark": "",
    "current_device": "",
    "hospital_name_length_limit_32": "",
    "hospital_name_length_limit_0": "",
    "hospital_name_exist": "",
    "input_hospital_name": "",
    "add_hospital_name": "",
    "not_upload_text": "",
    "in_silent_streaming": "",
    "live_session_in_progress": "",
    "share_link_success": "",
    "searc_in_case_database": "",
    "share_email_title": "",
    "whether_remove_equipment_from_group": "",
    "share_sms": "",
    "share_wechat": "",
    "exam_images": "",
    "not_connected": "",
    "piece_tip": "",
    "exam_images_title": "",
    "whether_pull_equipment_into_group": "",
    "traceless_failed": "",
    "traceless_error": "",
    "traceless_success": "",
    "traceless_slide": "",
    "undefined_name": "",
    "input_device_name": "",
    "edit_device_name": "",
    "device_detail": "",
    "default_push_way": "",
    "back_to_login": "",
    "tv_wall_text": "",
    "remote_camera": "",
    "main_stream_screen": "",
    "quick_lauch_live_title": "",
    "start_record": "",
    "weblive_live_not_yet": "",
    "switch_video_to_main": "",
    "switch_video_to_aux": "",
    "weblive_download_client_tips": "",
    "weblive_download_app_tips": "",
    "no_support_browser_live": "",
    "live_des": "",
    "conneting": "",
    "error_live_address_tip": "",
    "remote_current_mode": "",
    "storeState": "",
    "please_enter_code": "",
    "open_system_browser": "",
    "device_binding_success": "",
    "more_device_title": "",
    "device_list": "",
    "device_binding_tip": "",
    "authorized_devices": "",
    "device_binding": "",
    "end_live_tips": "",
    "quit_live_tips": "",
    "end_live": "",
    "quit_live": "",
    "whether_dissolve_live": "",
    "same_label_can_selected": "",
    "conference_seeding": "",
    "live_not_allow_operation": "",
    "applying_join_room_error": "",
    "open_mic_to_many_tips": "",
    "processing_wait": "",
    "lost_connect_with_server": "",
    "live_ended": "",
    "reset_email_success": "",
    "reset_email_title": "",
    "reset_email_tip": "",
    "auth_by_mobile": "",
    "auth_by_password": "",
    "download_tip": "",
    "userLoginQrCodeError": "",
    "qrcode_time_out": "",
    "qrcode_time": "",
    "time_out_msg": "",
    "cancel_login": "",
    "auto_login_device": "",
    "login_to_pc": "",
    "is_force_conference_by_group_owner": "",
    "push_stream_setting": "",
    "merge_choose_other": "",
    "merge_choose_current": "",
    "merge_account_tip": "",
    "verify_bind_description": "",
    "tap_to_join_conference": "",
    "service_setting": "",
    "network_setting": "",
    "thyroid": {
        "export_excel_confirm": "",
        "thyroid_multicenter_form": "",
        "export_excel": "",
        "shell_two_hardness_SD": "",
        "shell_two_hardness_min": "",
        "shell_two_hardness_max": "",
        "shell_two_hardness_mean": "",
        "shell_one_hardness_SD": "",
        "shell_one_hardness_min": "",
        "shell_one_hardness_max": "",
        "shell_one_hardness_mean": "",
        "shell_zero_point_five_hardness_SD": "",
        "shell_zero_point_five_hardness_min": "",
        "shell_zero_point_five_hardness_max": "",
        "shell_zero_point_five_hardness_mean": "",
        "lesion_hardness_SD": "",
        "lesion_hardness_min": "",
        "lesion_hardness_max": "",
        "lesion_hardness_mean": "",
        "high_resolution_shear_wave_elastic_ultrasound": "",
        "shear_wave_elastic_ultrasound": "",
        "quantitative_longitudinal": "",
        "qualitative_Rago_criteria": "",
        "strain_type_elastic_ultrasound": "",
        "focus_enhancement_direction": "",
        "focus_enhancement_mode": "",
        "enhancement_degree_of_focus_refer_to_thyroid": "",
        "enhancement_time_of_focus_refer_to_thyroid": "",
        "high_frame_rate_contrast_ultrasound": "",
        "conventional_contrast_enhanced_ultrasound": "",
        "focal_blood_flow_CPP": "",
        "ultrafine_blood_flow_imaging_UMA": "",
        "focal_blood_flow_AdlerGrade": "",
        "color_Doppler_CDFI": "",
        "TI_RADS_category": "",
        "microcalcification_in_lesion": "",
        "lesion_margin": "",
        "aspect_ratio_of_focus": "",
        "focal_echo": "",
        "focus_size_diameter_line_2": "",
        "focus_size_diameter_line_1": "",
        "focus_location": "",
        "focus_No": "",
        "focus_number": "",
        "focus": "",
        "cervical_lymph_nodes": "",
        "thyroid_parenchyma_echo": "",
        "overview_of_neck_ultrasound": "",
        "followUpReuslt": "",
        "pathologyResult": "",
        "FNA_result": "",
        "pathology_or_follow_up": "",
        "thyrotropin_receptor_antibody": "",
        "anti_thyroid_peroxidase_antibody": "",
        "thyroglobulin_antibody": "",
        "thyrotropin": "",
        "free_triiodothyronine": "",
        "free_thyroxine": "",
        "tetraiodothyronine": "",
        "triiodothyronine": "",
        "supply_thyroid_function_text": "",
        "supply_choice": {
            "points_5": "",
            "points_4": "",
            "points_3": "",
            "points_2": "",
            "mixability": "",
            "centrifugality": "",
            "centripetal": "",
            "annular_enhancement": "",
            "uneven_enhancement": "",
            "uniform_enhancement": "",
            "punctate_enhancement": "",
            "high_enhancement": "",
            "Isoenhancement": "",
            "low_enhancement": "",
            "no_enhancement": "",
            "fast_rewind": "",
            "sync_enhancements": "",
            "fast_forward": "",
            "level_III": "",
            "level_II": "",
            "level_I": "",
            "level_0": "",
            "category_5": "",
            "category_4C": "",
            "category_4B": "",
            "category_4A": "",
            "category_3": "",
            "unsmooth": "",
            "smooth": "",
            "no_erect": "",
            "erect": "",
            "high_echo": "",
            "isoechoic": "",
            "low_echo": "",
            "very_low_echo": "",
            "right": "",
            "left": "",
            "isthmus": "",
            "highEchoGroup": "",
            "microcalcification": "",
            "cysticDegeneration": "",
            "no_suspicious_MT_lymph_nodes": "",
            "suspicious_MT_lymph_nodes": "",
            "lesion_is_significantly_enlarged": "",
            "no_change_of_benign_lesions": "",
            "no_lymph_node_metastasis": "",
            "lymph_node_metastasis": "",
            "malignant": "",
            "benign": "",
            "ambiguity": "",
            "not_have": "",
            "have": ""
        },
        "confirm_passing_the_case": "",
        "caseid_not_null": ""
    },
    "hfr": {
        "HFRR_diagnosis": "",
        "no_same_no_annotate": "",
        "case_info_no_complete": "",
        "pass_review": "",
        "search_other_options": {
            "0": "",
            "1": "",
            "2": "",
            "-1": ""
        },
        "same_annotation": "",
        "case_info_complete": "",
        "no_assignment": "",
        "assignment_B": "",
        "assignment_A": "",
        "approval_btn": "",
        "topic_text": "",
        "belonging_topic_text": "",
        "end_comment_text": "",
        "new_create_comment": "",
        "confirm_to_approve_B": "",
        "confirm_to_approve_A": "",
        "project_name_title": "",
        "only_view_mode_tip": "",
        "review_info_submit_success": "",
        "review_info_save_success": "",
        "judge_btn": "",
        "confirm_notice_text": "",
        "annotation_view_label": "",
        "annotateCase_option": {
            "HCC": "",
            "equal": "",
            "vague": "",
            "spoke_like": "",
            "disordered_irregular_shape": "",
            "coarse_twisted_shape": "",
            "branch_like": "",
            "centrifugal": "",
            "centripetal": "",
            "whole_part": "",
            "inset_part": "",
            "peripheral_part": "",
            "random_distribution": "",
            "inset_high_strength": "",
            "inset_no_strength": "",
            "no_high": "",
            "no_early": "",
            "early": ""
        },
        "distance_to_body": "",
        "is_CA_subside": "",
        "diagnosis": "",
        "delay_phase": "",
        "venous_phase": "",
        "arterial_phase": "",
        "HFRR_delay_phase": "",
        "HFRR_venous_phase": "",
        "HFRR_arterial_phase": "",
        "delay_lesions_enchance_degree": "",
        "RCEH_delay_phase": "",
        "portal_lesions_enchance_degree": "",
        "RCEH_portal_phase": "",
        "vascular_morphology": "",
        "CA_direction": "",
        "first_strength_area": "",
        "strength_distribution_features": "",
        "strength_uniform_distribution": "",
        "lesions_enchance_degree": "",
        "lesions_enchance_time": "",
        "RCEH_arterial_phase": "",
        "case_date_text": "",
        "assign_succuss_text": "",
        "confirm_to_assign_exam": "",
        "data_examine_btn": ""
    },
    "numberOfImages": "",
    "HFRR_diagnosis": "",
    "RCEH_diagnosis": "",
    "character_type": {
        "0": "",
        "1": "",
        "2": "",
        "3": "",
        "4": "",
        "5": "",
        "6": ""
    },
    "exam_status": {
        "1": "",
        "2": "",
        "3": "",
        "4": "",
        "5": "",
        "6": "",
        "-1": ""
    },
    "sex": {
        "0": "",
        "1": "",
        "2": ""
    },
    "no_uniformity": "",
    "uniformity": "",
    "export_empty_tip": "",
    "export_fail_tip": "",
    "export_case": "",
    "number_text": "",
    "please_input_nickname": "",
    "group_by": "",
    "reject_success_tip": "",
    "reject_reason_no_white": "",
    "recent_two_month": "",
    "recent_one_month": "",
    "recent_two_week": "",
    "reject_reason_title": "",
    "case_view_label": "",
    "view_btn": "",
    "reject_btn": "",
    "query_btn": "",
    "is_modify_authorition": "",
    "authorition": "",
    "nickname": "",
    "assign_btn": "",
    "exam_type": "",
    "case_num": "",
    "index_num": "",
    "case_status": "",
    "end_date": "",
    "date_to": "",
    "start_date": "",
    "upload_datetime": "",
    "permission_assignment": "",
    "exam_view_title": "",
    "multicenter_title": "",
    "select_from_friend_list": "",
    "select_from_group_list": "",
    "length_limit_of": "",
    "server_disconnect": "",
    "AGORA_MEDIA_DEVICE_STATE_TYPE": {
        "0": "",
        "1": "",
        "2": "",
        "4": "",
        "8": "",
        "16": ""
    },
    "AGORA_MEDIA_DEVICE_TYPE": {
        "0": "",
        "1": "",
        "2": "",
        "3": "",
        "4": "",
        "-1": ""
    },
    "groupSetNameExists": "",
    "ota_update": "",
    "reverse_control_fail_another": "",
    "disconnect_control": "",
    "reverse_control_gain": "",
    "reverse_control_depth": "",
    "reverse_control_name": "",
    "input_collection_name": "",
    "group_favorite_total_text": "",
    "personal_favorite_text": "",
    "update_tag_success": "",
    "select_at_least_item": "",
    "change_a_tag": "",
    "new_a_tag": "",
    "exit_same_tag_name": "",
    "reverse_control_disconnect": "",
    "reverse_control_loading": "",
    "reverse_control_close": "",
    "reverse_control_reject": "",
    "reverse_control_confirm_tip": "",
    "reverse_control_title": "",
    "delete_group_favorite_tag_confirm": "",
    "group_favorite_text": "",
    "live_replay_second": "",
    "live_replay_minute": "",
    "live_replay_hour": "",
    "private_comment": "",
    "current_live_status": "",
    "referral_introduce_A4": "",
    "referral_introduce_Q4": "",
    "referral_introduce_A3": "",
    "referral_introduce_Q3": "",
    "referral_introduce_A2": "",
    "referral_introduce_Q2": "",
    "referral_introduce_A1": "",
    "referral_introduce_Q1": "",
    "how_to_get_referral": "",
    "no_referral_code": "",
    "client_only_tip": "",
    "export_start_tip": "",
    "login_or_register_email": "",
    "login_or_register_mobile": "",
    "live_detail": "",
    "iworks_fail_label": "",
    "iworks_score_label": "",
    "only_jpg_png": "",
    "not_support_del_invite_members": "",
    "no_permission_to_open_room": "",
    "cover_upload": "",
    "live_broadcast_cancel": "",
    "live_broadcast_end": "",
    "live_broadcasting": "",
    "begin_in_minute": "",
    "waiting": "",
    "live_room": "",
    "remark_text": "",
    "set_remark": "",
    "quick_entry": "",
    "login_need_password": "",
    "login_with_account": "",
    "login_with_mobile": "",
    "picture_unit": "",
    "search_result_tip": "",
    "hot_search_tip": "",
    "delete_all_history_tip": "",
    "delete_history_tip": "",
    "live_invite_status3": "",
    "live_invite_status2": "",
    "live_invite_status0": "",
    "live_invite_status4": "",
    "live_invite_status1": "",
    "live_theme": "",
    "send_invitation": "",
    "search_invite_member": "",
    "describe": "",
    "available_live": "",
    "my_booking_live": "",
    "edit_live": "",
    "booking_live": "",
    "my_live": "",
    "upload_live_cover": "",
    "enter_live_des": "",
    "select_live_time": "",
    "enter_live_name": "",
    "group": "",
    "friend": "",
    "dissolve_live": "",
    "live": "",
    "moderator": "",
    "mine": "",
    "reset_mobile_success": "",
    "welcome_tip": "",
    "referral_success_tip": "",
    "fill_in_referral_code": "",
    "login_directly": "",
    "register_by_mobile": "",
    "reset_password_way": "",
    "not_surpport_forget": "",
    "read_and_agree": "",
    "other_register_way": "",
    "other_login_way": "",
    "desensitization_reception": "",
    "ask_want_to_cancel_apply_speak_permission": "",
    "ask_want_to_apply_speak_permission": "",
    "allows_members_self_mute": "",
    "destroy_replace_text": "",
    "account_destroy_tip": "",
    "unmute_all": "",
    "members_manage_title": "",
    "is_agree_all_user_speak": "",
    "is_agree_user_speak": "",
    "all_members_silenced": "",
    "more_than_six_mute": "",
    "no_mute": "",
    "all_mute": "",
    "mute_setting_voice_ctrl_mode": "",
    "log_off_waring": "",
    "admin_cannot_autologin": "",
    "cancel_account": "",
    "unrecognized_device_type": "",
    "device_not_logged_server": "",
    "incorrect_network": "",
    "box_not_found": "",
    "switch_account": "",
    "account_logged_in": "",
    "phone_no_login": "",
    "share_risk_content": "",
    "codeMustRequired": "",
    "userChangePwdNeedLogin": "",
    "email_verification_code_empty": "",
    "userTokenError": "",
    "userOutOfTrail": "",
    "image_code_tip": "",
    "image_code_empty": "",
    "verify_send_email": "",
    "verify_send_sms": "",
    "verify_description": "",
    "verify_title": "",
    "please_agree_privacy_policy": "",
    "please_agree": "",
    "email_is_invalid_input_again": "",
    "verify_with_email": "",
    "verify_with_mobile": "",
    "forget_password_get_email_code": "",
    "email_verification_code": "",
    "register_email": "",
    "equipment_testing": "",
    "slide_tip": "",
    "case_database_error_code": "",
    "case_database_tip": "",
    "private_group_not_share": "",
    "case_database_id": "",
    "image_count": "",
    "live_playback": "",
    "product_name_value_ce": "",
    "product_name_value_cn": "",
    "software_description_ce": "",
    "software_description_cn": "",
    "device_notice_camera_insert": "",
    "device_notice_camera_pull_out": "",
    "case_require_no_filled": "",
    "case_database_title": "",
    "confirm_update_report": "",
    "refute_reason_text": "",
    "live_management": "",
    "privacy_welcome_p3": "",
    "privacy_welcome_p2": "",
    "privacy_welcome_p1": "",
    "privacy_welcome_title": "",
    "ultrasync_privacy_protocol": "",
    "exit_group_tip": "",
    "join_group_tip": "",
    "add_btn": "",
    "replace_btn": "",
    "autograph": "",
    "examining_doctor": "",
    "report_time": "",
    "ultrasonic_prompt": "",
    "ultrasonic_discovery": "",
    "exam_position": "",
    "sending_physician": "",
    "bed_number": "",
    "inpatient_number": "",
    "outpatient_number": "",
    "department_title": "",
    "ultrasound_number": "",
    "report_subtitle": "",
    "report_title": "",
    "registration_certificate_no": "",
    "product_model": "",
    "product_name": "",
    "conversation_not_init": "",
    "no_realtime_tip": "",
    "will_open_conversation": "",
    "enter_live": "",
    "enter_gallery": "",
    "browser_no_support_video": "",
    "browser_no_support_audio": "",
    "file_has_downloaded": "",
    "save_path": "",
    "select_groupset": "",
    "groupset_not_activity": "",
    "select_statistics": "",
    "details": "",
    "ultrasync_live": "",
    "conference_invitation": "",
    "unsurpport_pdf": "",
    "file_storage_manage": "",
    "privacy_statement_title": "",
    "domain_name_tip": "",
    "other_data": "",
    "file_upload_exception": "",
    "ftp_path_tip": "",
    "reselect_upload_file": "",
    "copyright": "",
    "no_videocamera_be_detected": "",
    "videocamera_muti_devices": "",
    "videocamera_title": "",
    "select_micro_device_text": "",
    "watch_supply_case_title": "",
    "case_exam_status": {
        "judgeSubmit": "",
        "reviewed": "",
        "assigned": "",
        "reject": "",
        "submited": "",
        "saved": "",
        "unsubmit": ""
    },
    "patient_case_status": "",
    "supply_case_close_btn": "",
    "privacy_policy": "",
    "file_downloading": "",
    "open_file": "",
    "file_download_progress": "",
    "less_than_1kb": "",
    "file_size": "",
    "file_name": "",
    "wait_download_file": "",
    "tip_file_open_fail": "",
    "tip_file_download_success": "",
    "tip_file_download_fail": "",
    "confirm_delete_file": "",
    "image_corruption_text": "",
    "ban_to_negative_number": "",
    "mobile_number_is_invalid_input_again_cn": "",
    "invite_you_join_group": "",
    "import_licence": "",
    "no_more_text": "",
    "playing_video_tip": "",
    "login_fail_with_lock_tip": "",
    "enhance_password_tip": "",
    "please_choose_topic_text": "",
    "now_topic_text": "",
    "liver_topic_text": "",
    "gall_bladder_text": "",
    "gall_bladder_no_file_preview": "",
    "no_upload_report_text": "",
    "upload_file_error_text": "",
    "delete_groupset_tip": "",
    "back_button": "",
    "ref_res_expired": "",
    "init_supply_case_fail": "",
    "supply_case_fail": "",
    "supply_case_success": "",
    "choose_date_time": "",
    "ultrasound_diagonse_time": "",
    "cdfi_blood_flow": "",
    "echo_text": "",
    "sizeof_lesions_two": "",
    "sizeof_lesions_one": "",
    "target_lesions_site": "",
    "number_of_gallbladder_lesions": "",
    "number_of_intrahepatic_lesions": "",
    "grey_scale_ultrasound_text": "",
    "pathologic_diagonsis_text": "",
    "more_mri_diagonse_text": "",
    "first_time_mri_diagonse_text": "",
    "mri_diagonse_text": "",
    "cirrhosis_text": "",
    "hepatitis_text": "",
    "patient_history_text": "",
    "tumor_markers_text": "",
    "indirect_bilirubin_text": "",
    "direct_bilirubin_text": "",
    "total_bilirubin_text": "",
    "alanine_aminotransferase_text": "",
    "aspartate_aminotransferase_text": "",
    "biochemical_examination_text": "",
    "anti_hiv_text": "",
    "treponema_antibody_text": "",
    "anti_hcv_text": "",
    "four_before_operation_text": "",
    "select_placeholder_text": "",
    "no_type": "",
    "no_need_follow_diagonse": "",
    "mixed_echo": "",
    "high_level": "",
    "middle_level": "",
    "low_level": "",
    "mutiple": "",
    "sigle": "",
    "adenomatous_polyp": "",
    "cholesterol_polyp": "",
    "matastatic_carcinoma": "",
    "poorly_differentiated_hcc": "",
    "moderately_poorly_differentiated_hcc": "",
    "moderately_differentiated_hcc": "",
    "moderately_well_differentiated_hcc": "",
    "well_differentiated_hcc": "",
    "no_FNH": "",
    "negative": "",
    "positive": "",
    "ICC": "",
    "FNH": "",
    "no": "",
    "yes": "",
    "hepatitis_five_items_text": "",
    "blood_cell_text": "",
    "middle_size_cell_rate": "",
    "white_cell_text": "",
    "red_cell_text": "",
    "supply_patient_age": "",
    "supply_patient_name": "",
    "supply_blood_routine_text": "",
    "supply_patient_info_text": "",
    "supply_case_title": "",
    "cannot_share_qrcode_text": "",
    "is_recording_text": "",
    "skip_to_unread_text": "",
    "consulation_review_text": "",
    "exam_patient_text": "",
    "withdraw_chat_message_fail": "",
    "withdraw_chat_message_fail_sended_by_others": "",
    "send_recoding_text": "",
    "other_cancel_realtime_voice_text": "",
    "self_cancel_realtime_voice_text": "",
    "exam_conversation_creator_tag": "",
    "groupset_manager": "",
    "groupset_member_title": "",
    "next_step_text": "",
    "select_audio_device_text": "",
    "exceeded_max_withdrawal": "",
    "re_edit": "",
    "revocation_message_by_other": "",
    "revocation_message_by_self": "",
    "revocation_message": "",
    "get_country_list_fail": "",
    "search_groupsets_text": "",
    "search_recent_chat_text": "",
    "choose_country": "",
    "in_total_text": "",
    "group_has_deleted_text": "",
    "select_hospital": "",
    "check_groupset_exist_text": "",
    "search_type_error": "",
    "display_all_data_error_txt": "",
    "search_none_text": "",
    "real_time_warning_message": "",
    "warning_title": "",
    "tv_wall_warning_message": "",
    "device_notice_unusual_exit": "",
    "device_notice_unusual_continue": "",
    "device_notice_unusual_message": "",
    "device_notice_unusual_title": "",
    "device_notice_now_device_pull_out": "",
    "analyze_min_save_time": "",
    "analyze_max_save_time": "",
    "analyze_avarage_save_time": "",
    "analyze_min_download_time": "",
    "analyze_max_download_time": "",
    "analyze_avarage_download_time": "",
    "analyze_min_text": "",
    "analyze_max_text": "",
    "analyze_avarage_text": "",
    "analyze_total_download_text": "",
    "charts_sunday_text": "",
    "charts_saturday_text": "",
    "charts_friday_text": "",
    "charts_thursday_text": "",
    "charts_wednesday_text": "",
    "charts_tuesday_text": "",
    "charts_monday_text": "",
    "charts_online_device_text": "",
    "charts_device_unit_text": "",
    "charts_device_total_text": "",
    "parsetime_seconds_text": "",
    "parsetime_hours_text": "",
    "not_online_text": "",
    "online_text": "",
    "online_rate": "",
    "unsupported_location_notice": "",
    "repeat_client_text": "",
    "link_connected_fail_text": "",
    "link_expired_text": "",
    "groupset_no_conversation_notice": "",
    "device_number_text": "",
    "ultrasonic_today_text": "",
    "device_online_rate_text": "",
    "back_text": "",
    "remote_QC_image_quality_text": "",
    "live_broadcast_volume_text": "",
    "accumulation_text": "",
    "the_day_text": "",
    "live_control_statistics_text": "",
    "remote_ultrasonic_center_text": "",
    "no_support_webrtc_text": "",
    "voice_device_error_text": "",
    "rt_voice_connect_error_prefix": "",
    "webrtc_connect_fail_text": "",
    "room_need_close_affix": "",
    "join_room_err_prefix": "",
    "no_speak_permission": "",
    "groupset_text": "",
    "no_found_group_text": "",
    "no_found_user_text": "",
    "more_text": "",
    "recent_chat_text": "",
    "group_chat_text": "",
    "contact_text": "",
    "wechat_invite_affix": "",
    "wechat_invite_prefix": "",
    "my_groupset": "",
    "group_set_profile": "",
    "delete_groupset_text": "",
    "already_friend_msg": "",
    "paste": "",
    "copy": "",
    "copy_text_success": "",
    "join_group_by_qrcode_tips": "",
    "scaned_tip": "",
    "qrcode_expired": "",
    "add_group_successful": "",
    "auto_add_sharer_text": "",
    "group_card_apply_btn": "",
    "group_visiting_card_title": "",
    "no_description_tip": "",
    "groupset_delete_attendee": "",
    "groupset_add_attendee": "",
    "groupset_description": "",
    "choose_only_one_file_to_handle": "",
    "group_qrcode_card_notice_text": "",
    "group_qrcode_card_text": "",
    "unrecognized_qrcode": "",
    "no_group_id": "",
    "no_user_id": "",
    "qrcode_card_notice_text": "",
    "qrcode_card_text": "",
    "skip_set_password": "",
    "set_password_first": "",
    "filter_hospital": "",
    "input_info_check_tip": "",
    "length_limit_info": "",
    "input_info_check_err": "",
    "name_null": "",
    "live_name": "",
    "input_new_name": "",
    "rename": "",
    "exam_end_time": "",
    "exam_start_time": "",
    "exam_mode_label": "",
    "exam_patient_sex": "",
    "exam_patient_age": "",
    "exam_patient_name": "",
    "all_iworks": "",
    "more_iworks_text": "",
    "exception_to_login_again": "",
    "index_nav_files": "",
    "library_video": "",
    "library_image": "",
    "protocol_title": "",
    "view_not_uploaded": "",
    "click_to_refresh": "",
    "cancel_download_confirm": "",
    "view_txt": "",
    "checkout_protocol": "",
    "export_running": "",
    "media_transfer": {
        "error": {
            "add_task_error_waiting_queue_full": "",
            "add_task_error": ""
        },
        "clip_tip_startup": "",
        "creating_task": ""
    },
    "search_input_key": "",
    "version": "",
    "protocol_version": "",
    "doppler_version": "",
    "ecr_version": "",
    "product_manufacturer": "",
    "product_type": "",
    "hospital_associate": "",
    "hospital_director": "",
    "hospital_location": "",
    "hospital_address": "",
    "hospital_name": "",
    "mac_addr": "",
    "loginLoading": "",
    "task_manager": {
        "media_transfer": {
            "error": {
                "delete_task_error": "",
                "query_task_error": ""
            },
            "status_list": {
                "1": "",
                "2": "",
                "3": "",
                "4": "",
                "5": ""
            },
            "operation_import": "",
            "progress": "",
            "status": "",
            "image_name": "",
            "image_source": "",
            "task_id": ""
        },
        "title": ""
    },
    "add_association": "",
    "association_hospitals": "",
    "bi_data_display": "",
    "location": "",
    "device_has_no_network": "",
    "groupset_msg_empty": "",
    "select_all_group": "",
    "select_all_friend": "",
    "get_groupset_exam_fail": "",
    "get_groupset_detail_fail": "",
    "groupset_detail_navbar": "",
    "groupset_exam_count": "",
    "groupset_video_count": "",
    "groupset_image_count": "",
    "groupset_navbar": "",
    "delete_group_set_success": "",
    "delete_group_set_fail": "",
    "delete_groupset_confirm": "",
    "update_group_set_success": "",
    "update_group_set_fail": "",
    "query_groupset_list_fail": "",
    "create_group_set_success": "",
    "create_group_set_fail": "",
    "group_set_name_empty": "",
    "edit_group_set": "",
    "group_list_empty": "",
    "group_set_name": "",
    "create_group_set": "",
    "group_statistics": "",
    "group_set_statistics": "",
    "mute_message_number": "",
    "mute_notifications": "",
    "female": "",
    "male": "",
    "exam_manager": {
        "error": {
            "sendto_target_invalid": "",
            "sendto_error": "",
            "mutiple_selected_unmatch": "",
            "single_selected_unmatch": "",
            "conversation_not_open": "",
            "patient_id_repeated": "",
            "patient_id_empty": "",
            "import_exam_image_error": "",
            "new_exam_error": "",
            "search_exam_error": ""
        },
        "sendto_success_tip": "",
        "import_exam_image_success_tip": "",
        "import_exam_image_tip": "",
        "ai_image_info": "",
        "image_info": "",
        "exam_info": "",
        "import_exam_image_confirm": "",
        "new_exam": "",
        "title": ""
    },
    "operation": "",
    "exam_time": "",
    "patient_id": "",
    "online_time": "",
    "user_id": "",
    "cellphone": "",
    "time": "",
    "resource_id": "",
    "exam_id": "",
    "series_number": "",
    "group_name": "",
    "user_name": "",
    "active": "",
    "optional": "",
    "users_without_hospital": "",
    "all_users_of_this_hospital": "",
    "hospital_user": "",
    "unread_message_tip": "",
    "someone_mention": "",
    "mention_dialog_title": "",
    "reset_mobile_tip_reset_mobile": "",
    "verify_password": "",
    "reset_login_name_tip_reset_login_name": "",
    "reset_login_name_success": "",
    "reset_login_name": "",
    "group_by_month": "",
    "group_by_week": "",
    "group_by_day": "",
    "sort_by_upload_ts": "",
    "sort_by_exam_ts": "",
    "exam_group_separation_char": "",
    "in_consultation_text": "",
    "msg_review_tip": "",
    "review_all": "",
    "share_link_to_wx": "",
    "device_bar_type": {
        "6": "",
        "7": ""
    },
    "query_faq_fail": "",
    "faq_title": "",
    "delete_conference_tip": "",
    "conference_begin_tip": "",
    "reserved_conference_tip": "",
    "reserved_success": "",
    "delete_reserved_tip": "",
    "reserved_duration_illegal": "",
    "reserved_starttime_illegal": "",
    "reserved_overtime": "",
    "add_reserved_conference": "",
    "conference_subject_empty": "",
    "end_time": "",
    "start_time": "",
    "reserved_conference_time": "",
    "reserved_conference_date_tip": "",
    "reserved_conference_date": "",
    "reserved_conference_subject": "",
    "reserved_conference": "",
    "notification_body": {
        "new_message": "",
        "friend_apply": "",
        "join_group": ""
    },
    "notification_reqeust_tip": "",
    "scan_room_list_none": "",
    "tv_wall_setting_edit_error": "",
    "scan_room_sort_by_name": "",
    "scan_room_sort_by_creation_ts": "",
    "scan_room_sort": "",
    "scan_room_list": "",
    "tv_wall_setting": "",
    "sharing_starting": "",
    "loading_module_text": "",
    "loading_fail": "",
    "login_sms_verification_code_empty": "",
    "login_mobile_phone_empty": "",
    "edit_txt": "",
    "reset_mobile": "",
    "reset_password_fail": "",
    "set_admin_error": "",
    "set_admin_confirm": "",
    "set_admin": "",
    "download_app_tip": "",
    "file_transfer_assistant": "",
    "service_accounts": "",
    "referral_code_generate_err": {
        "time_out": "",
        "database_err": "",
        "none_referral_code": "",
        "unknown_err": ""
    },
    "referral_code_tip_expire": "",
    "referral_code_tip": "",
    "referral_code_generate": "",
    "invite_registration": "",
    "search_friend": "",
    "referral_code_is_invalid_input_again": "",
    "referral_code": "",
    "mindray_library": "",
    "reupload_frame_tip": "",
    "not_login_and_fail_to_start_rt_video": "",
    "history_tip": "",
    "switch_language_to_shoutdown": "",
    "push_image_mode_set_fail": "",
    "push_image_mode_set_succ": "",
    "push_image_mode_setting": "",
    "push_image_mode_confirm_to_set": "",
    "push_image_mode_fluency": "",
    "push_image_mode_clarity": "",
    "push_image_mode": "",
    "realtime_video_review_text": "",
    "realtime_video_text": "",
    "report_text": "",
    "change_voice_device_need_to_close_realtime": "",
    "can_not_change_voice_device_in_browser": "",
    "history_version_introduce_content": "",
    "history_version_introduce_time": "",
    "history_version_introduce_version": "",
    "history_version_title": "",
    "query_history_version_fail": "",
    "history_version_introduce": "",
    "history_version": "",
    "only_creator_can_edit": "",
    "no_service_tip": "",
    "no_announcement_tip": "",
    "is_save_change": "",
    "semi_public_group_tip": "",
    "security_restrictions": "",
    "send_realtime_to_conversation": "",
    "send_uf_to_conversation": "",
    "no_BI_RADS_features_results_tip": "",
    "no_BI_RADS_features": "",
    "results_reference_only": "",
    "BI_RADS_features_results_tip": "",
    "BI_RADS_features": {
        "0": {
            "0": "",
            "1": ""
        },
        "1": {
            "0": "",
            "1": "",
            "2": ""
        },
        "2": {
            "0": "",
            "1": "",
            "2": "",
            "3": "",
            "4": ""
        },
        "3": {
            "0": "",
            "1": "",
            "2": "",
            "3": "",
            "4": ""
        },
        "4": {
            "0": "",
            "1": "",
            "2": "",
            "3": ""
        },
        "5": {
            "0": "",
            "1": ""
        },
        "6": {
            "0": "",
            "1": "",
            "2": ""
        }
    },
    "not_exam_picture": "",
    "sort_by_time": "",
    "sort_by_exam": "",
    "sort_by_group": "",
    "nls_probationary_expiry_tip": "",
    "multi_select": "",
    "no_new_version": "",
    "confirm_update_to_new_version": "",
    "version_update": "",
    "cur_version": "",
    "sned_to_analyze_tip": "",
    "action_analyze_text": "",
    "mindray_analyze": "",
    "ai_analyze": "",
    "click_here": "",
    "share_live_addr_tip4": "",
    "share_live_addr_tip3": "",
    "share_live_addr_tip2": "",
    "share_live_addr_tip1": "",
    "get_live_addr_error": "",
    "set_record_name": "",
    "live_address": "",
    "auto_analyze": "",
    "from_ai_analyze": "",
    "share_analyze_result": "",
    "can_not_analyze_video": "",
    "analyze_error": "",
    "analyzing": "",
    "see_trace_tip": "",
    "malignant_probability": "",
    "bengin_probability": "",
    "analyze_fail_tip": "",
    "analyze_result_tip": "",
    "close_consultation": "",
    "export_comment_fail": "",
    "export_comment": "",
    "multi_audio_device": "",
    "record_no_network": "",
    "admin_hospital_delete": "",
    "admin_hospital_name_empty": "",
    "admin_user_id": "",
    "admin_active_user_tip": "",
    "admin_forbidden_user_tip": "",
    "admin_relief_director_tip": "",
    "admin_setup_director_tip": "",
    "admin_revoking_permissions_tip": "",
    "admin_granting_permissions_tip": "",
    "admin_pass_empty": "",
    "admin_add": "",
    "admin_hospital_name": "",
    "admin_revoking_permissions": "",
    "admin_granting_permissions": "",
    "admin_disabled": "",
    "admin_activation": "",
    "admin_activation_and_disabled": "",
    "admin_setting_password": "",
    "admin_is_director": "",
    "admin_is_manager": "",
    "admin_batch_pass": "",
    "admin_pass": "",
    "admin_approve": "",
    "admin_referee_nickname": "",
    "admin_approver_nickname": "",
    "admin_register_date": "",
    "admin_login_name": "",
    "admin_tab_hospital_manage": "",
    "admin_tab_user_manage": "",
    "admin_tab_approve": "",
    "admin_account_not_admin": "",
    "admin_login_title": "",
    "register_workstation_fail": "",
    "register_workstation_success": "",
    "stop_test_microphone": "",
    "stop_test_speaker": "",
    "microphone_input_strength": "",
    "testing_microphone": "",
    "testing_speaker": "",
    "no_microphone_be_detected": "",
    "no_speaker_be_detected": "",
    "microphone_muti_devices": "",
    "speaker_muti_devices": "",
    "microphone_title": "",
    "speaker_title": "",
    "ultrasync_box_mac_empty_tip": "",
    "workstation_MAC": "",
    "workstation_consulting_room": "",
    "register_workstation_title": "",
    "arrangement_text": "",
    "enter_conversation": "",
    "export_no_select": "",
    "general_mode": "",
    "tv_wall_mode": "",
    "download_image_success": "",
    "download_directory_empty": "",
    "download_image_space": "",
    "download_image_name": "",
    "download_exam_num_space": "",
    "download_exam_num_empty": "",
    "download_choose_btn": "",
    "download_directory": "",
    "download_name": "",
    "download_exam_num": "",
    "pusher_mac_format_error": "",
    "max_ultrasync_box_length": "",
    "max_scan_room_name_length": "",
    "delete_ultrasync_box_text": "",
    "delete_scan_room_text": "",
    "other_infomation": "",
    "device_id": "",
    "ultrasync_box_mac": "",
    "scan_room_lastest_user": "",
    "scan_room_hospital": "",
    "scan_room_name": "",
    "ultrasound_device_mng": "",
    "pusher_manage": "",
    "scan_room_mng": "",
    "system_info_text": "",
    "cloud_statistics_iworks_get_token_err": "",
    "cloud_statistics_iworks2": "",
    "cloud_statistics_iworks": "",
    "cloud_statistics_range_empty": "",
    "cloud_statistics_day_text": "",
    "cloud_statistics_hour_text": "",
    "minute_text": "",
    "cloud_statistics_second_text": "",
    "cloud_statistics_client_type": "",
    "cloud_statistics_tags_count": "",
    "cloud_statistics_comment_count": "",
    "cloud_statistics_group_message_count": "",
    "cloud_statistics_start_offline_consultation_count": "",
    "cloud_statistics_active_users_count_tip": "",
    "cloud_statistics_active_users_count": "",
    "cloud_statistics_by_the_hour": "",
    "cloud_statistics_by_the_month": "",
    "cloud_statistics_by_the_week": "",
    "cloud_statistics_by_the_day": "",
    "cloud_statistics_last_year": "",
    "cloud_statistics_last_thirty_days": "",
    "cloud_statistics_last_seven_days": "",
    "cloud_statistics_bar_chart": "",
    "cloud_statistics_line_chart": "",
    "cloud_statistics_chart_type": "",
    "cloud_statistics_fineness_with_colon": "",
    "cloud_statistics_client_type_short": "",
    "cloud_statistics_new_group_number": "",
    "cloud_statistics_global_group_number": "",
    "cloud_statistics_active_live_user_count": "",
    "cloud_statistics_live_user_total_count": "",
    "cloud_statistics_ulink_user_tip": "",
    "cloud_statistics_ulink_user": "",
    "cloud_statistics_ulink_active_user_count_tip": "",
    "cloud_statistics_ulink_active_user_count": "",
    "cloud_statistics_ulink_user_count": "",
    "cloud_statistics_ulink_user_total_count": "",
    "cloud_statistics_total_ultra_device_ulink": "",
    "cloud_statistics_ulink_install_device_total_count": "",
    "cloud_statistics_ulink_install_device_count": "",
    "cloud_statistics_live_duration_enter": "",
    "cloud_statistics_live_duration": "",
    "cloud_statistics_group_total_count": "",
    "cloud_statistics_user_total_count": "",
    "cloud_statistics_active_group_count_tip": "",
    "cloud_statistics_active_group_count": "",
    "cloud_statistics_new_group_message_count": "",
    "cloud_statistics_new_realtime_count": "",
    "cloud_statistics_total_exam_video": "",
    "cloud_statistics_total_exam_picture": "",
    "cloud_statistics_total_case_count": "",
    "cloud_statistics_new_exam_video": "",
    "cloud_statistics_new_exam_picture": "",
    "cloud_statistics_new_case_count_for_iworks": "",
    "cloud_statistics_new_case_count": "",
    "cloud_statistics_new_user_approve_number": "",
    "cloud_statistics_new_user_number": "",
    "cloud_statistics_receive_real_time_consultation": "",
    "cloud_statistics_launch_real_time_consultation": "",
    "cloud_statistics_active_group_peak": "",
    "cloud_statistics_online_user_peak": "",
    "cloud_statistics_exam_number": "",
    "cloud_statistics_chat_messages_other": "",
    "cloud_statistics_chat_messages_sound": "",
    "cloud_statistics_chat_messages_realtime_video": "",
    "cloud_statistics_chat_messages_image": "",
    "cloud_statistics_chat_messages_realtime_review": "",
    "cloud_statistics_chat_messages_cine": "",
    "cloud_statistics_chat_messages_frame": "",
    "cloud_statistics_chat_messages_text": "",
    "cloud_statistics_chat_messages_number": "",
    "cloud_statistics_access_realtime_person_time": "",
    "cloud_statistics_access_realtime_total_time": "",
    "cloud_statistics_times_of_realtinme_voice": "",
    "cloud_statistics_total_time_of_realtinme_voice": "",
    "cloud_statistics_viewing_person_time": "",
    "cloud_statistics_viewing_total_time": "",
    "cloud_statistics_real_time_consultation_times": "",
    "cloud_statistics_real_time_consultation_total_time": "",
    "cloud_statistics_increased_conversation": "",
    "cloud_statistics_total_conversation": "",
    "cloud_statistics_user_total_online_time": "",
    "cloud_statistics_online_users": "",
    "cloud_statistics_increased_users": "",
    "cloud_statistics_total_users": "",
    "cloud_statistics_increased_approved_users": "",
    "cloud_statistics_total_approved_users": "",
    "cloud_statistics_this_month": "",
    "cloud_statistics_this_week": "",
    "cloud_statistics_today": "",
    "cloud_statistics_inquiry": "",
    "cloud_statistics_statistical_item_result": "",
    "cloud_statistics_statistical_item": "",
    "cloud_statistics_diagram": "",
    "cloud_statistics_interval_table": "",
    "cloud_statistics_global_table": "",
    "device_mng": "",
    "cloud_statistics_title": "",
    "background_manage_title": "",
    "enter_purpose_tip": "",
    "fill_in_purpose": "",
    "temp_attendee_text": "",
    "video_cannot_played": "",
    "exam_conversation_title": "",
    "add_exam_opinion_success": "",
    "add_exam_conclusion_success": "",
    "exam_conclusion_text": "",
    "exam_opinion_text": "",
    "cancel_conversation_btn": "",
    "close_conversation_btn": "",
    "add_exam_conclusion": "",
    "app_no_speak_permission": "",
    "applying_join_group": "",
    "apply_join_semi_group": "",
    "toggle_to": "",
    "search_empty_tip": "",
    "choose_file": "",
    "submit_btn": "",
    "download_title": "",
    "share_video_limited": "",
    "history_tag": "",
    "stop_multi_frame_first": "",
    "device_save_video_unit": "",
    "device_save_video_num": "",
    "device_save_picture_num": "",
    "device_create_exam": "",
    "get_repository_list_fail": "",
    "no_search_data": "",
    "load_pdf_error": "",
    "scan_to_login": "",
    "upload_date_text": "",
    "delete_chat_message_warm_sended_by_others": "",
    "delete_chat_message_fail_sended_by_others": "",
    "ultrasync_box_detele_fail_online": "",
    "ultrasync_box_detele_fail": "",
    "scan_room_detele_fail_online": "",
    "scan_room_detele_fail": "",
    "delete_chat_message_fail": "",
    "auto_join_group": "",
    "control_panel": "",
    "auth_fail": "",
    "auth_ok": "",
    "scan_mindray_ultrasync": "",
    "join_group_without_approve": "",
    "join_group_with_approve": "",
    "semi_public_group_text": "",
    "private_group_text": "",
    "public_group_text": "",
    "group_realtime_review": "",
    "group_conversion_file": "",
    "group_all_file": "",
    "attendee_member_text": "",
    "to_scan_device": "",
    "send_by_enter": "",
    "chat_history": "",
    "clear_history": "",
    "more_features": "",
    "camera_direct_seeding": "",
    "desktop_direct_seeding": "",
    "ultrasound_seeding": "",
    "voice_recording": "",
    "export_image": "",
    "upload_file": "",
    "emoticon": "",
    "search_history_text": "",
    "search_file_text": "",
    "search_group_text": "",
    "search_friend_text": "",
    "version_info": "",
    "uncalibrated_text": "",
    "calibrater_text": "",
    "inputPatientOrFtpInfo": "",
    "shutdown_fail": "",
    "shutdown_succ": "",
    "save_multi_frame_fail": "",
    "save_multi_frame_succ": "",
    "save_multi_frame_start": "",
    "save_single_frame_fail": "",
    "save_single_frame_succ": "",
    "update_device_cur_session_fail": "",
    "device_offline": "",
    "realtime_video_error": {
        "ultrasync_box_not_binded_switch": "",
        "ultrasync_box_offline_switch": "",
        "push_error_switch": "",
        "push_camera_desktop_error_switch": "",
        "push_local_desktop_error_switch": "",
        "push_error": "",
        "push_camera_desktop_error": "",
        "push_local_desktop_error": "",
        "rt_video_started": "",
        "timeout": "",
        "rt_video_started_by_others": "",
        "camera_error": "",
        "video_transmit_server_not_run": "",
        "ultraSync_box_image_reset": "",
        "ultraSync_box_push_error": "",
        "ultrasync_box_no_signal": "",
        "unbind_scan_room_user": "",
        "ultrasync_box_disabled": "",
        "ultrasync_box_reset": "",
        "ultrasync_box_offline": "",
        "unknown_error": ""
    },
    "set_catch_option_error": {
        "ultrasync_box_pushing": "",
        "ultrasync_box_setting": "",
        "ultrasync_box_offline": "",
        "ultrasync_box_not_binded": "",
        "timeout": ""
    },
    "pusher_enable_error": {
        "ultrasync_box_no_signal": "",
        "ultrasync_box_disabled": "",
        "ultrasync_box_offline": "",
        "ultrasync_box_not_binded": "",
        "unknown_error": ""
    },
    "ultrasync_box_disabled": "",
    "starting_rt_video": "",
    "stop_device_ultrasound_desktop": "",
    "start_device_ultrasound_desktop": "",
    "shutdown_confirm": "",
    "save_multi_frame_stop": "",
    "save_multi_frame": "",
    "save_single_frame": "",
    "please_enter_ftp_path": "",
    "please_enter_ftp_password": "",
    "please_enter_ftp_account": "",
    "ftp_port": "",
    "ftp_path": "",
    "ftp_password": "",
    "ftp_account": "",
    "ftp_anonymous": "",
    "ftp_info": "",
    "forbid_input_quotation": "",
    "please_enter_patien_id": "",
    "requestTimeout": "",
    "modify_btn_text": "",
    "current_conversation": "",
    "toggle_conversation_to": "",
    "ultrasound_device_title": "",
    "recent_consultation": "",
    "recent_images": "",
    "camera_ex_enter_by_other_client": "",
    "request_done_by_other_clients": "",
    "user_passive_exit_group": "",
    "user_exit_group_fail": "",
    "user_exit_group_succ": "",
    "user_exit_group_tip": "",
    "creator_user_exit_group_tip": "",
    "exit_group": "",
    "delete_group": "",
    "i_o_error": "",
    "send_message_error": "",
    "audio_not_support": "",
    "invalid_qrcode": "",
    "init_not_ready": "",
    "file_does_not_exist": "",
    "webrtc_transfer_close": "",
    "speak_panel": "",
    "start_conversation_error": "",
    "banned_this_moment": "",
    "copy_text_not_support": "",
    "copy_text_fail": "",
    "text_has_copied": "",
    "tag_emoji_err": "",
    "system_tag_top": "",
    "custom_tag_top": "",
    "tag_repeat_add": "",
    "update_failed_text": "",
    "update_success_text": "",
    "exam_types": {
        "0": "",
        "1": "",
        "2": "",
        "3": "",
        "4": "",
        "5": "",
        "6": "",
        "7": "",
        "8": "",
        "9": "",
        "10": "",
        "-1": ""
    },
    "no_more_data": "",
    "exam_view_mode": "",
    "normal_view_mode": "",
    "freedom_voice_ctrl_mode": "",
    "group_setting_voice_ctrl_mode": "",
    "group_setting_view_mode": "",
    "search_key": "",
    "searching": "",
    "search": "",
    "exam_flesh_btn": "",
    "confirm_min_app": "",
    "delete_tag_msg_text": "",
    "add_tag_msg_text": "",
    "add_comment_msg_text": "",
    "no_net_no_open_img": "",
    "no_net_no_open_video": "",
    "exam_date_more_tip": "",
    "exam_date_less_tip": "",
    "search_text": "",
    "save_not_support_tip": "",
    "save_support_fail_tip": "",
    "save_null_fail_tip": "",
    "has_download_tip": "",
    "gallery_dowloading_tip": "",
    "confirm_create_group": "",
    "patient_birthday_text": "",
    "sender_nickname": "",
    "exam_browse_text": "",
    "transfer_helper_text": "",
    "use_app_tip": "",
    "share_to_wechat_failed": "",
    "share_to_wechat_succeed": "",
    "network_error_tip": "",
    "machine_transmit_tip": "",
    "machine_local_confirm": "",
    "machine_destroy_confirm": "",
    "machine_local_btn": "",
    "machine_destroy_btn": "",
    "cancel_select_all": "",
    "select_all": "",
    "machine_info_serial": "",
    "machine_info_type": "",
    "machine_info_name": "",
    "disconnet_machine_btn": "",
    "machine_info": "",
    "ultrasound_machine_title": "",
    "password_error_text": "",
    "modify_photo_success": "",
    "card_request_fail": "",
    "card_request_sended": "",
    "card_applying_tip": "",
    "card_chat_btn": "",
    "visiting_card_title": "",
    "tag_text_has_newline": "",
    "tag_text_null": "",
    "comment_text_null": "",
    "close_realtime_video_tip": "",
    "request_realtime_video_tip": "",
    "close_voice_system_tip": "",
    "request_voice_system_tip": "",
    "search_join_tip": "",
    "remove_group_tip2": "",
    "active_exit_group_tip": "",
    "remove_group_tip1": "",
    "invited_join_group_tip2": "",
    "invited_join_group_tip1": "",
    "cannot_delete_tag": "",
    "age_unit_month": "",
    "age_unit_year": "",
    "unknow_text": "",
    "action_clip_text": "",
    "action_set_exam_info_text": "",
    "action_favorite_text": "",
    "action_delete_text": "",
    "transmit_fail_tip": "",
    "transmiting_tip": "",
    "transmit_less_tip": "",
    "transmit_comfirm_msg": "",
    "transmit_title": "",
    "add_favorites_fail": "",
    "add_favorites_success": "",
    "operating_text": "",
    "share_to_wechat_moment": "",
    "share_to_wechat_friends": "",
    "share_to_wechat": "",
    "attendee_join_group_already": "",
    "invite_wechat_user_to_group": "",
    "invite_wechat_user": "",
    "wechat_no_wechat_installed": "",
    "wechat_is_not_supported": "",
    "send_failed": "",
    "wechat_user_tap_cancel_and_back": "",
    "wechat_regular_wrong_type": "",
    "wechat_get_user_info_failed": "",
    "wechat_get_access_token_failed": "",
    "wechat_create_group": "",
    "remote_ultrasonic_consultation_system": "",
    "authentication_authorization_failed": "",
    "invalid_sharing_service": "",
    "mindray_final_mission": "",
    "account_format_tip": "",
    "enhanced_password_format_tip": "",
    "password_format_tip": "",
    "nickname_cannot_contain_special_character": "",
    "single_picture_sharing": "",
    "multi_picture_sharing": "",
    "choose_transmit_tip": "",
    "choose_favorites_tip": "",
    "realtime_review_msg": "",
    "unknown_error": "",
    "one_mobile_register_multiple_user_forbid": "",
    "SMS_validation_is_not_supported": "",
    "userNotExist": "",
    "data_base_error_contact_admin": "",
    "retrieve_sms_verification_code": "",
    "sms_verification_code_failed": "",
    "sms_verification_code": "",
    "submit_sms_verification_code": "",
    "user_reset_password_button": "",
    "mobile_number_is_invalid_input_again": "",
    "nickname_should_not_be_empty": "",
    "modify_password_fail_database_err": "",
    "modify_password_fail_password_same": "",
    "modify_password_fail_password_incorrect": "",
    "modify_password_fail_account_incorrect": "",
    "personal_information_fail_mobile_phone_repeated": "",
    "personal_information_fail_nickname_repeated": "",
    "personal_information_fail_email_repeated": "",
    "personal_information_fail_name_pure_numbers": "",
    "personal_information_fail_name_repeated": "",
    "modify_basic_info_success": "",
    "modify_password_success": "",
    "confirm_password_and_new_password_not_match": "",
    "confirm_password_and_password_not_same": "",
    "confirm_password_length_not_correct": "",
    "password_length_not_correct": "",
    "login_name_length_not_correct": "",
    "voice_manager_text": "",
    "close_realtime_video": "",
    "close_voice_chat": "",
    "request_realtime_video": "",
    "request_voice_chat": "",
    "group_modify_subject_title": "",
    "confirm_txt": "",
    "group_add_attendee_title": "",
    "group_all_attendee_title": "",
    "group_all_file_title": "",
    "toggle_unlive_record_tip": "",
    "toggle_live_record_tip": "",
    "group_setting_whether_live_record": "",
    "group_setting_is_live_record": "",
    "group_setting_is_need_to_approve": "",
    "group_setting_is_public": "",
    "group_setting_announce": "",
    "more_attendee_text": "",
    "more_file_text": "",
    "creator_tag_text": "",
    "group_setting_title": "",
    "yesterday_text": "",
    "cancel_button_text": "",
    "confirm_button_text": "",
    "disable_speak_comfirm": "",
    "apply_speak_disagree_comfirm": "",
    "apply_speak_agree_comfirm": "",
    "all_forbidden_speak": "",
    "authorize_speak_text": "",
    "attendee_text": "",
    "modify_confirm_password_input": "",
    "modify_new_password_input": "",
    "modify_old_password_input": "",
    "modify_new_password_label": "",
    "modify_old_password_label": "",
    "browser_no_support_recording": "",
    "no_audio_input_warn": "",
    "loaded_all_message_no_net": "",
    "loaded_all_message": "",
    "image_all_loaded": "",
    "has_chosen_text": "",
    "not_choose_text": "",
    "upload_net_error": "",
    "upload_forbidden_file_type_text": "",
    "upload_min_text": "",
    "upload_max_text": "",
    "record_short_text": "",
    "cancel_recording_text": "",
    "recording_sound_text": "",
    "chat_upload_text": "",
    "login_another": "",
    "tip_title": "",
    "create_group_text": "",
    "unsupport_video_text": "",
    "gallery_navbar_patient": "",
    "label_txt": "",
    "gallery_navbar_comment": "",
    "gallery_add_tags_btn": "",
    "gallery_add_comment_btn": "",
    "unsupport_msg_type": "",
    "choose_shared_image": "",
    "max_transfer_image": "",
    "max_share_image": "",
    "msg_type_sound_label": "",
    "msg_type_image_string_label": "",
    "msg_type_consultation_file_label": "",
    "msg_type_iworks_protocol_label": "",
    "msg_type_consultation_file": "",
    "msg_type_iworks_protocol": "",
    "msg_type_file": "",
    "msg_type_video": "",
    "msg_type_sound": "",
    "msg_type_image": "",
    "loading_complete": "",
    "bottom_loading_text": "",
    "click_more_text": "",
    "bottom_drop_text": "",
    "bottom_pull_text": "",
    "top_pull_text": "",
    "scanner_text": "",
    "hold_to_talk": "",
    "send_message_txt": "",
    "init_conversation_err": "",
    "no_data_txt": "",
    "applying_txt": "",
    "tmp_joined_txt": "",
    "joined_txt": "",
    "subject_empty_tip": "",
    "create_group_empty_list": "",
    "group_chat_params": "",
    "group_create_steps": {
        "choose_friends": "",
        "edit_params": "",
        "finish": "",
        "group_create_progress_100": "",
        "group_create_progress_50": ""
    },
    "group_chat_name": "",
    "search_group_title": "",
    "create_group_title": "",
    "response_disaccept_friend": "",
    "response_accept_friend": "",
    "added_txt": "",
    "no_user_result": "",
    "search_friend_tip": "",
    "search_err": "",
    "search_friend_txt": "",
    "disagree_txt": "",
    "agree_txt": "",
    "apply_group_txt": "",
    "apply_friend_txt": "",
    "operate_success": "",
    "operate_err": "",
    "logout": "",
    "my_groups": "",
    "new_apply": "",
    "friends_title": "",
    "page_loading_tip": "",
    "international_title": "",
    "modify_basic_info_text": "",
    "system_setting_text": "",
    "change_language_success": "",
    "into_tv_wall_after_login": "",
    "auto_push_stream_for": "",
    "auto_push_stream": "",
    "auto_download_attachment": "",
    "auto_forwarding_for": "",
    "auto_forwarding": "",
    "other_setting_text": "",
    "modify_photo_text": "",
    "modify_password_text": "",
    "personal_setting_title": "",
    "software_version_number": "",
    "app_and_server_build_version": "",
    "about_ultrasync": "",
    "cloud_favorites": "",
    "local_patients": "",
    "setting_title": "",
    "add_friend_text": "",
    "create_group_btn": "",
    "files": "",
    "contacts": "",
    "index_nav_mine": "",
    "index_nav_chat": "",
    "forget_password_getcode": "",
    "forget_password_title": "",
    "register_reset_btn": "",
    "register_sex": "",
    "register_mobile": "",
    "register_confirm_password": "",
    "register_password": "",
    "register_account": "",
    "register_account_or_mobile": "",
    "register_title": "",
    "to_forget_password": "",
    "to_register": "",
    "download_mobile_app": "",
    "download_window": "",
    "auto_login": "",
    "register_success": "",
    "login_password_empty": "",
    "login_account_empty": "",
    "login_password": "",
    "login_account": "",
    "login_title": "",
    "network_unavailable": "",
    "new_version": "",
    "app_name": "",
    "RemoteTeaching": "",
    "RemoteTeaching not available": "",
    "AutoStart": "",
    "CARD": "",
    "AppWorkstation": "",
    "AppClient": "",
    "unknown": "",
    "StopTest": "",
    "TestMicrophone": ""
}

export default IT