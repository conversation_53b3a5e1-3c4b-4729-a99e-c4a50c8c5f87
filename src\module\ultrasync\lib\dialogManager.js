/**
 * DialogManager - 弹出窗管理器
 * 单例模式实现，全局共用，管理所有弹窗的打开和关闭
 */

import Tool from '@/common/tool'

class DialogManager {
    constructor() {
        // 存储所有注册的弹窗
        this.dialogs = new Map();
        // 存储事件监听器
        this.listeners = new Map();
        // 兼容现有系统
        this.legacyMode = true;
    }

    /**
     * 获取DialogManager的单例实例
     * @returns {DialogManager} DialogManager的单例实例
     * @static
     */
    static getInstance() {
        if (!DialogManager.instance) {
            DialogManager.instance = new DialogManager();
        }
        return DialogManager.instance;
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     * @private
     */
    generateId() {
        return Tool.genID(3);
    }

    /**
     * 注册弹窗
     * @param {Object} dialog - 弹窗实例
     * @param {Function} options.open - 打开弹窗的方法
     * @param {Function} options.close - 关闭弹窗的方法
     * @param {boolean} options.canCloseOnPopstate - 是否可以在popstate时关闭
     * @param {boolean} options.canClose - 是否可以关闭
     * @param {string} options.id - 可选，指定弹窗ID
     * @returns {string} 弹窗ID
     */
    register(dialog, { open, close, canCloseOnPopstate = true, canClose = true, id = null }) {
        const dialogId = id || this.generateId();

        // 存储弹窗及其操作方法
        this.dialogs.set(dialogId, {
            dialog,
            open,
            close,
            canCloseOnPopstate,
            canClose,
            isOpen: false
        });

        // 触发注册事件
        this.emit('register', dialogId, dialog);

        return dialogId;
    }

    /**
     * 注销弹窗
     * @param {string} id - 弹窗ID
     * @returns {boolean} 是否成功注销
     */
    unregister(id) {
        if (!this.dialogs.has(id)) {
            return false;
        }

        const dialogInfo = this.dialogs.get(id);
        // 触发注销事件
        this.emit('unregister', id, dialogInfo.dialog);

        return this.dialogs.delete(id);
    }

    /**
     * 打开弹窗
     * @param {string} id - 弹窗ID
     * @param {...any} args - 传递给打开方法的参数
     * @returns {boolean} 是否成功打开
     */
    openDialog(id, ...args) {
        if (!this.dialogs.has(id)) {
            return false;
        }

        const dialogInfo = this.dialogs.get(id);
        if (dialogInfo.open) {
            dialogInfo.open(...args);
            dialogInfo.isOpen = true;

            // 触发打开事件
            this.emit('open', id, dialogInfo.dialog);

            // 兼容现有系统
            if (this.legacyMode && window.vm && window.vm.$root) {
                if (!window.vm.$root.currentDialogList) {
                    window.vm.$root.currentDialogList = [];
                }

                // 检查是否已经在列表中
                const exists = window.vm.$root.currentDialogList.some(item => item.id === id);
                if (!exists) {
                    window.vm.$root.currentDialogList.push({
                        id,
                        el: dialogInfo.dialog
                    });
                }
            }

            return true;
        }

        return false;
    }

    /**
     * 关闭弹窗
     * @param {string} id - 弹窗ID
     * @param {...any} args - 传递给关闭方法的参数
     * @returns {boolean} 是否成功关闭
     */
    closeDialog(id, ...args) {
        if (!this.dialogs.has(id)) {
            return false;
        }

        const dialogInfo = this.dialogs.get(id);
        if (dialogInfo.close && dialogInfo.canClose) {
            dialogInfo.close(...args);
            dialogInfo.isOpen = false;

            // 触发关闭事件
            this.emit('close', id, dialogInfo.dialog);

            // 兼容现有系统
            if (this.legacyMode && window.vm && window.vm.$root && window.vm.$root.currentDialogList) {
                window.vm.$root.currentDialogList = window.vm.$root.currentDialogList.filter(item => item.id !== id);
            }

            return true;
        }

        return false;
    }

    /**
     * 关闭所有弹窗
     * @returns {number} 成功关闭的弹窗数量
     */
    closeAllDialogs() {
        let closedCount = 0;

        for (const [id, dialogInfo] of this.dialogs.entries()) {
            if (dialogInfo.isOpen && dialogInfo.close && dialogInfo.canClose) {
                dialogInfo.close();
                dialogInfo.isOpen = false;

                // 触发关闭事件
                this.emit('close', id, dialogInfo.dialog);

                closedCount++;
            }
        }

        // 兼容现有系统
        if (this.legacyMode && window.vm && window.vm.$root) {
            window.vm.$root.currentDialogList = [];
        }

        return closedCount;
    }

    /**
     * 处理路由变化
     * @param {Object} to - 目标路由
     * @param {Object} from - 来源路由
     * @param {Function} next - 路由控制函数
     */
    handleRouteChange(to, from, next) {
        // 检查是否有不能在popstate时关闭的弹窗
        const hasBlockingDialog = this.getOpenDialogs().some(([id, dialogInfo]) => {
            return !dialogInfo.canCloseOnPopstate;
        });

        if (hasBlockingDialog) {
            // 有阻塞弹窗，阻止路由变化
            next(false);
            return;
        }

        // 关闭所有可以在popstate时关闭的弹窗
        for (const [id, dialogInfo] of this.getOpenDialogs()) {
            if (dialogInfo.canCloseOnPopstate) {
                this.closeDialog(id);
            }
        }

        // 允许路由变化
        next();
    }

    /**
     * 获取当前打开的所有弹窗
     * @returns {Array} 打开的弹窗数组，每项为 [id, dialogInfo]
     */
    getOpenDialogs() {
        return Array.from(this.dialogs.entries()).filter(([_, dialogInfo]) => dialogInfo.isOpen);
    }

    /**
     * 检查弹窗是否打开
     * @param {string} id - 弹窗ID
     * @returns {boolean} 是否打开
     */
    isDialogOpen(id) {
        if (!this.dialogs.has(id)) {
            return false;
        }
        return this.dialogs.get(id).isOpen;
    }

    /**
     * 获取弹窗信息
     * @param {string} id - 弹窗ID
     * @returns {Object|null} 弹窗信息
     */
    getDialogById(id) {
        if (!this.dialogs.has(id)) {
            return null;
        }
        return this.dialogs.get(id);
    }

    /**
     * 检查是否有打开的弹窗
     * @returns {boolean} 是否有打开的弹窗
     */
    hasOpenDialogs() {
        return this.getOpenDialogs().length > 0;
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }

        this.listeners.get(event).push(callback);
    }

    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(event, callback) {
        if (!this.listeners.has(event)) {
            return;
        }

        const callbacks = this.listeners.get(event);
        const index = callbacks.indexOf(callback);

        if (index !== -1) {
            callbacks.splice(index, 1);
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {...any} args - 事件参数
     */
    emit(event, ...args) {
        if (!this.listeners.has(event)) {
            return;
        }

        for (const callback of this.listeners.get(event)) {
            callback(...args);
        }
    }

    /**
     * 从现有系统迁移弹窗
     * @param {Object} dialog - 弹窗实例
     * @param {string} id - 弹窗ID
     * @returns {string} 新的弹窗ID
     */
    migrateFromLegacy(dialog, id) {
        // 从现有弹窗实例中提取方法
        const open = dialog.showDialog ? dialog.showDialog.bind(dialog) : null;
        const close = dialog.closeDialog ? dialog.closeDialog.bind(dialog) : null;
        const canCloseOnPopstate = dialog.checkCanCloseOnPopstate ? dialog.checkCanCloseOnPopstate() : true;
        const canClose = dialog.checkCanClose ? dialog.checkCanClose() : true;

        // 注册到新系统
        return this.register(dialog, { open, close, canCloseOnPopstate, canClose, id });
    }
}

// 导出单例实例
export default DialogManager.getInstance();
