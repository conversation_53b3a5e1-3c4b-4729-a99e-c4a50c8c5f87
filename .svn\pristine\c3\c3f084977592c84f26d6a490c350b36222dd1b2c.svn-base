<template>
    <div :class="{ data_view_page_parent: true, over_all: false }">
        <div class="header_name new_exam_form">
            <div class="data_select">
                <queryForm @handleSearch="handleSearch" :activeView="'question_feedback'"> </queryForm>
            </div>
        </div>
        <div class="data_view_page_content">
            <div class="content_header_button">
                <div>
                    <p class="title rate">{{ lang.data_list }}</p>
                    <el-button native-type="submit" type="primary" class="button" @click="handleExport">
                        {{ lang.export_comment }}
                    </el-button>
                </div>
            </div>
            <div class="content_list" ref="content_list">
                <div class="pagelist obstetric_qc_data_view_page_table" v-loading="loading">
                    <el-table
                        v-if="!isNoData"
                        :data="imageList"
                        row-key="id"
                        ref="imageList"
                        :header-cell-style="{ color: 'black' }"
                        class="tableAuto"
                        :max-height="tabHeight"
                        border
                    >
                        <el-table-column
                            v-for="item in imageTableColumn"
                            :prop="item.field"
                            :label="lang[item.key] + item.unit"
                            stripe
                            :width="item.width"
                            :show-overflow-tooltip="true"
                            :key="item.key"
                        >
                            <template slot-scope="scope" slot="header">
                                <el-tooltip class="item" effect="dark" :content="scope.column.label" placement="top">
                                    <span>{{ scope.column.label }}</span>
                                </el-tooltip>
                            </template>
                            <template slot-scope="scope">
                                <div v-if="item.field == 'index'">
                                    {{ (tab_conf.pageNo - 1) * tab_conf.pageSize + scope.$index + 1 }}
                                </div>
                                <div v-else-if="item.field == 'feedbacker'">
                                    {{ scope.row.sender.nickname }}
                                </div>
                                <div v-else-if="item.field == 'created_at'">
                                    {{ formatDate(scope.row.created_at) }}
                                </div>
                                <div v-else-if="item.field == 'question'">
                                    <pre class="question-content">{{ scope.row.question }}</pre>
                                </div>
                                <div v-else>
                                    {{ scope.row[item.field] }}
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="no_data" v-if="isNoData">{{ noDataTips }}</div>
                    <el-image v-if="isNoData" class="md-data-empty" src="static/resource_pc/images/nodata.png" />
                </div>
            </div>
            <div class="content_page_num">
                <el-pagination
                    v-if="!isNoData"
                    background
                    :current-page="tab_conf.pageNo"
                    :layout="tab_conf.layout"
                    :page-size="tab_conf.pageSize"
                    :total="tab_conf.tatal"
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                />
            </div>
        </div>
        <a ref="link" style="display: none"></a>
    </div>
</template>
<script>
import base from "../../../lib/base";
import Tool from "@/common/tool.js";
import service from "../../../service/multiCenterService.js";
import queryForm from "../../../components/obstetricQC/queryForm";
import moment from "moment";
import { cloneDeep, sortBy } from "lodash";
import obstetricTool from "../../../lib/obstetricTool";
import { transferPatientInfo, getRealUrl, deDuplicatingImg, toFixedNumber } from "../../../lib/common_base";

export default {
    mixins: [base, obstetricTool],
    components: { queryForm },
    data() {
        return {
            config: this.$store.state.multicenter.config,
            obstetricEarlyPregnancy: this.$store.state.multicenter.obstetricEarlyPregnancy,
            types: this.$store.state.multicenter.type,
            //分页数据
            tab_conf: {
                pageNo: 1,
                pageSize: 10,
                layout: "total, sizes, prev, pager, next, jumper",
                tatal: 0,
            },
            //是否加载中
            loading: false,
            hasData: false,
            //搜索条件
            queryForm: {
                multi_center_patient_id: "",
                patient_name: "",
                organization_name: "",
                dateRange: [
                    moment().subtract(1, "years").format("YYYY-MM-DD z"),
                    moment(new Date()).format("YYYY-MM-DD z"),
                ],
                group_id: "", //群id 是否申请复议
                view_id: "", //切面id 是否申请复议
                uploader_id: "", //上传者
                feedbacker_ids: "",
                feedback_date_range: [
                    moment().subtract(1, "years").format("YYYY-MM-DD z"),
                    moment(new Date()).format("YYYY-MM-DD z"),
                ],
            },
            //图片或者检查列表
            imageList: [],
            //日期选项控制
            pickerOptions: {
                shortcuts: [
                    {
                        text: window.vm.$store.state.language.recent_two_week,
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 13);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: window.vm.$store.state.language.recent_one_month,
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setMonth(start.getMonth() - 1);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: window.vm.$store.state.language.recent_two_month,
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setMonth(start.getMonth() - 2);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                ],
                disabledDate: (time) => {
                    return time.getTime() > new Date().getTime();
                },
            },
            imageTableColumn: [
                { key: "index_num", field: "index", width: "70px", unit: "" },
                { key: "multi_center_patient_id", field: "external_id", width: "150px", unit: "" },
                { key: "group", field: "group_name", width: "120px", unit: "" },
                { key: "feedbacker", field: "feedbacker", width: "120px", unit: "" },
                { key: "feedback_date_range", field: "created_at", width: "150px", unit: "" },
                { key: "describe", field: "question", unit: "" },
            ],
            defaultTabHeight: 450,
            tabHeight: "450px",
        };
    },
    computed: {
        noDataTips() {
            if (this.loading) {
                return this.lang.searching;
            } else {
                if (this.hasData) {
                    return this.lang.searching;
                } else {
                    return this.lang.no_relative_data_find;
                }
            }
            // return this.loading? this.lang.searching : this.lang.no_relative_data_find
        },
        enterByGroup() {
            return this.$store.state.multicenter.enterByGroup;
        },
        isNoData() {
            return (this.imageList || []).length == 0;
        },
        currentMulticenter() {
            return this.$store.state.multicenter.currentMulticenter || {};
        },
    },
    created() {
        this.debounceSearch = Tool.debounce(this.search, 1000);
        this.uploadDateRange = [
            moment().subtract(1, "years").format("YYYY-MM-DD z"),
            moment(new Date()).format("YYYY-MM-DD z"),
        ];
        this.enterByGroup.cid ? (this.queryForm.group_id = this.enterByGroup.cid) : "";
        this.init();
    },
    mounted() {},
    updated() {},
    methods: {
        async init() {
            await this.fetchData();
        },
        async fetchData() {
            let that = this;
            that.imageList = [];
            that.loading = true;
            that.hasData = false;
            let condition = {
                start_time: moment(that.queryForm.feedback_date_range[0]).format("YYYY-MM-DD z") + " 00:00:00", //时间范围 默认展示最近3个月，最长不能超过12个月；
                end_time: moment(that.queryForm.feedback_date_range[1]).format("YYYY-MM-DD z") + " 23:59:59",
                multi_center_patient_id: that.queryForm.multi_center_patient_id || "", //病人id, 模糊查询
            };
            if (that.queryForm.group_id && that.queryForm.group_id !== "") {
                condition.group_id = that.queryForm.group_id;
            }

            if (that.queryForm.feedbacker_ids) {
                if (Array.isArray(that.queryForm.feedbacker_ids)) {
                    if (that.queryForm.feedbacker_ids.length > 0) {
                        condition.user_ids = that.queryForm.feedbacker_ids;
                    }
                } else {
                    condition.user_ids = [that.queryForm.feedbacker_ids];
                }
            }
            condition.pageNo = that.tab_conf.pageNo;
            condition.pageSize = that.tab_conf.pageSize;
            let data = await that.fetchFeedBackImages(condition);
            that.tab_conf.tatal = data.total;
            that.imageList = (data.list || []).reduce((h, v) => {
                let group_name = v.group_name;
                if (group_name && (group_name === "single chat" || group_name === "single_chat")) {
                    group_name = this.lang.single_chat;
                }
                console.error(group_name);
                v.group_name = group_name;
                h.push(v);
                return h;
            }, []);
            that.imageList = data.list;
            this.$nextTick(() => {
                this.$refs.imageList && this.$refs.imageList.doLayout();
            });
            return;
        },
        async fetchFeedBackImages(condition) {
            let that = this;
            console.log("fetchFeedBackImages:", condition);
            let result = { total: 0, list: [] };
            return new Promise(async (resolve, reject) => {
                service
                    .getFeedBackImages({
                        mcID: that.currentMulticenter.id, //多中心id
                        page: condition.pageNo,
                        pageSize: condition.pageSize,
                        condition,
                    })
                    .then(async (res) => {
                        that.loading = false;
                        console.log(res);
                        if (res.data.error_code == 0) {
                            resolve(res.data.data);
                            return;
                        } else {
                            reject(result);
                            return;
                        }
                    });
            });
        },

        handleSizeChange(val) {
            if (this.$refs.content_list && this.$refs.content_list.offsetHeight > this.defaultTabHeight) {
                this.tabHeight = this.$refs.content_list.offsetHeight + "px";
            } else {
                this.tabHeight = this.defaultTabHeight + "px";
            }
            this.tab_conf.pageNo = 1;
            this.tab_conf.pageSize = val;
            this.fetchData();
        },
        handleCurrentChange(val) {
            this.tab_conf.pageNo = val;
            this.fetchData();
        },
        formatDate(date, style = "YYYY-MM-DD HH:mm:ss") {
            if (date) {
                return moment(date).format(style);
            } else {
                return "";
            }
        },
        handleSearch(queryForm) {
            if (this.loading) {
                return;
            }
            this.tab_conf.pageNo = 1;
            this.queryForm = queryForm;
            this.fetchData();
        },
        escapeCsvCharacter(v) {
            let result = v;
            if (v) {
                result = (v + "").replace(/\"/g, '""');
            }
            return result;
        },
        async handleExport() {
            let that = this;
            console.log("导出");
            that.loading = true;
            let condition = {
                start_time: moment(that.queryForm.feedback_date_range[0]).format("YYYY-MM-DD z") + " 00:00:00", //时间范围 默认展示最近3个月，最长不能超过12个月；
                end_time: moment(that.queryForm.feedback_date_range[1]).format("YYYY-MM-DD z") + " 23:59:59",
                multi_center_patient_id: that.queryForm.multi_center_patient_id || "", //病人id, 模糊查询
            };
            if (that.queryForm.group_id && that.queryForm.group_id !== "") {
                condition.group_id = that.queryForm.group_id;
            }

            if (that.queryForm.feedbacker_ids) {
                if (Array.isArray(that.queryForm.feedbacker_ids)) {
                    if (that.queryForm.feedbacker_ids.length > 0) {
                        condition.user_ids = that.queryForm.feedbacker_ids;
                    }
                } else {
                    condition.user_ids = [that.queryForm.feedbacker_ids];
                }
            }
            condition.pageNo = 1;
            condition.pageSize = 10000000000;
            let data = await that.fetchFeedBackImages(condition);

            let list = (data.list || []).reduce(
                (h, v, index) => {
                    let group_name = v.group_name;
                    if (group_name && (group_name === "single chat" || group_name === "single_chat")) {
                        group_name = this.lang.single_chat;
                    }
                    h.push(
                        [
                            index + 1,
                            v.external_id,
                            group_name,
                            v.sender.nickname,
                            this.formatDate(v.created_at),
                            ('"' + v.question.replace(/"/g, '""') + '"').replace(/,/g, ",").replace(/\n/g, "\r\n") +
                                "\r\n",
                        ].join(",")
                    );
                    return h;
                },
                [
                    [
                        this.lang.index_num,
                        this.lang.multi_center_patient_id,
                        this.lang.group,
                        this.lang.feedbacker,
                        this.lang.feedback_date_range,
                        this.lang.describe + "\r\n",
                    ].join(","),
                ]
            );
            // let blob = new Blob(list,{type: "application/csv" });
            let blob = new Blob(["\uFEFF" + list.join("")], { type: "text/csv;charset=utf-8;" });
            const url = window.URL.createObjectURL(blob); // 设置路径
            const link = this.$refs.link;
            link.href = url;
            link.download = `${this.lang.obstetric_qc_multicenter}.csv`; // 设置文件名
            link.click();
            URL.revokeObjectURL(url); // 释放内存
        },
    },
};
</script>

<style lang="scss" scoped>
.data_view_page_parent {
    width: 100%;
    background-color: rgb(242, 246, 249);
    height: calc(100% - 40px);
    flex-direction: column;
    justify-content: space-around;
}
.over_all {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 2;
    overflow-y: auto;
}
.no_data {
    text-align: center;
    font-size: 16px;
}
.data_view_page_content {
    padding: 0px 20px 15px 20px;
    background-color: rgb(242, 246, 249);
    padding-bottom: 20px;
    display: flex;
    flex: 1 1 auto;
    flex-direction: column;
    justify-content: space-around;
    .content_list {
        flex: 1 1 auto;
    }
    .content_page_num {
        height: 34px;
        padding: 6px 0 0 0;
    }
    .content_header_button {
        height: 40px;
        margin-bottom: 10px;
        display: -webkit-flex;
        -webkit-flex-wrap: wrap;
        justify-content: space-between;
        flex-direction: row;
        .title {
            display: inline;
            line-height: 40px !important;
            margin-right: 10px;
        }
        .view_select {
            height: 30px !important;
            margin-bottom: 15px;
            margin-left: 15px;
        }
        .button {
            color: white;
            background: #779a98;
            border: 0px;
            font-size: 15px;
            margin-top: 3px;
            padding: 5px 11px 8px 11px;
            margin-left: 15px;
        }
        .rate {
            font-size: 18px;
            color: black;
            font-weight: bold;
            line-height: 40px;
        }
        div {
            height: 40px;
        }
    }
    .pagelist {
        // height:calc(100% - 100px);
        display: flex;
        justify-content: center;
        flex-direction: column;
        word-break: break-all;
        word-wrap: break-word;
        th {
            word-break: keep-all !important;
            word-wrap: break-word !important;
        }
        .name {
            color: #0000cc;
        }
        .index {
            color: #0000cc;
        }
        .md-data-empty {
            margin: auto;
            height: 400px;
            text-align: center;
        }
        .disable {
            color: #cdd1d1;
        }
        .opeation_icon {
            display: flex;
            justify-content: space-between;
            color: #779a98;
        }
    }
    .table_header {
        .cell {
            color: black;
        }
        color: black;
    }
    .question-content {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}
.header_name {
}
.new_exam_form {
    display: -webkit-flex;
    -webkit-flex-wrap: wrap;
    justify-content: space-between;
    flex-direction: column;
    .title {
        font-size: 18px;
        font-weight: bold;
        color: black;
    }
    .el-form {
        padding: 0px;
        margin: 0px;
    }
    .field_form {
        height: 100px;
    }
    .field_arear {
        width: 100%;
        & > div {
            width: 100%;
            display: -webkit-flex;
            -webkit-flex-wrap: wrap;
            justify-content: space-between;
            flex-direction: row;
        }
        .field {
            width: 30%;
            text-align: left;
        }
        .last {
            text-align: right;
        }
    }
    .tips_arear {
        width: 100%;
        height: 100px;
        display: -webkit-flex;
        -webkit-flex-wrap: nowrap;
        justify-content: space-between;
        flex-direction: row;
        .left {
            width: 50%;
        }
        .right {
            width: 30%;
            color: red;
        }
    }
    .button_arear {
        width: 100%;
        height: 100px;
        display: -webkit-flex;
        -webkit-flex-wrap: wrap;
        justify-content: center;
        flex-direction: row;
        .button_ele {
            width: 200xp;
            margin: 30px 50px;
        }
        .general_cancel_button {
            height: 30px;
            padding-top: 7px;
            background: white;
            border: 1px solid black;
            color: black;
        }
        .general_confirm_button {
            height: 30px;
            padding-top: 7px;
        }
    }
    .pictures_arear {
        width: 100%;
        display: -webkit-flex;
        -webkit-flex-wrap: wrap;
        flex-direction: column;
        justify-content: center;
        & > div {
            padding-top: 10px;
            padding-bottom: 10px;
            text-align: center;
        }
    }
}

.obstetric_qc_data_view_page_table {
    overflow: auto;
    display: flex;
    align-items: center;
    .el-loading-mask {
        background-color: rgba(255, 255, 255, 0);
    }
    .el-table__header-wrapper {
        .el-table__header {
            margin: 0;
        }
        th {
            .cell {
                // word-break: keep-all;
                // // word-wrap: break-word;
                // white-space: pre-wrap;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
    .el-table__body-wrapper {
        td {
            padding: 6px 0px;
            div {
                div {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                width: calc(100% - 0px) !important;
            }
        }
    }
    .el-table--scrollable-x .el-table__body-wrapper {
        z-index: 2;
    }
}
</style>
<style lang="scss">
.el-tooltip__popper {
    max-width: 50%;
}
</style>
