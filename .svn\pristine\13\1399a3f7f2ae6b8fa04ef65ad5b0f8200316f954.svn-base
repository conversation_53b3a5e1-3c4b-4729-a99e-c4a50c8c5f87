<template>
    <div v-show="isShowGallery" class="gallery_page second_level_page">
        <canvas style="display: none" id="ai_canvas"></canvas>
        <div class="gallery">
            <span class="close_gallery" @click="closeGallery">
                <i class="iconfont svg_icon_back icon-back"></i>
            </span>
            <p class="index_tip">{{ index + 1 }}/{{ galleryList.length }}</p>
            <div
                class="ai_search_suggest"
                v-show="showAISearchSuggest && false"
                @click="searchInCaseData()"
                :title="lang.searc_in_case_database"
            >
                {{ lang.ai_search_suggest }}
            </div>
            <div class="mui-slider" id="my_slider" :key="sliderKey">
                <div class="mui-slider-group" :class="{ img_comment_mode: isShowComment }" ref="sliderGroup">
                    <div
                        v-for="(file, index) of galleryList"
                        class="mui-slider-item"
                        :class="{ 'mui-zoom-wrapper': checkResourceType(file) === 'image' }"
                        @tap="toggleCommentMode"
                        :key="file.resource_id + '_' + file.send_ts"
                        :data-attr="file.resource_id"
                    >
                        <template v-if="checkResourceType(file) === 'image'">
                            <div class="mui-zoom-scroller" @doubletap="preventTap">
                                <real-image :fileItem="file" :ref="`realImage_${index}`"></real-image>
                                <template v-if="file.img_encode_type">
                                    <img
                                        v-if="file.img_encode_type.toUpperCase() == systemConfig.file_type.DCM"
                                        src="static/resource/images/poster_video.png"
                                        @tap.stop="initDCMPlayer($event, index)"
                                        class="play_video_btn"
                                    />
                                    <img
                                        v-if="file.img_encode_type.toUpperCase() == systemConfig.file_type.PDF"
                                        src="static/resource/images/poster_video.png"
                                        @tap.stop="showPdf(file)"
                                        class="play_video_btn"
                                    />
                                </template>
                            </div>
                        </template>
                        <template v-else-if="checkResourceType(file) === 'video'">
                            <real-image :fileItem="file" :ref="`realImage_${index}`"></real-image>
                            <img
                                src="static/resource/images/poster_video.png"
                                @tap.stop="initVideoPage($event, index)"
                                class="play_video_btn"
                            />
                        </template>
                        <template v-else-if="file.msg_type == systemConfig.msg_type.File">
                            <img @click="showPdf(file)" :src="file.url_local" class="file" :ref="'image_' + index" />
                        </template>
                        <template
                            v-else-if="
                                file.msg_type == systemConfig.msg_type.RealTimeVideoReview ||
                                file.img_type_ex == systemConfig.msg_type.RealTimeVideoReview
                            "
                        >
                            <div class="file" @tap.stop="initVideoPage($event, index)" :ref="'image_' + index">
                                <p class="review_text">{{ lang.live_playback }}</p>
                                <p>{{ formatTime(file.start_ts) }}<br />{{ formatTime(file.stop_ts) }}</p>
                                <i class="icon iconfont icon-videofill"></i>
                            </div>
                        </template>
                        <template
                            v-else-if="
                                file.msg_type == systemConfig.msg_type.VIDEO_CLIP ||
                                file.img_type_ex == systemConfig.msg_type.VIDEO_CLIP
                            "
                        >
                            <div class="file" @tap.stop="initVideoPage($event, index)" :ref="'image_' + index">
                                <p class="review_text">{{ lang.video_clips }}</p>
                                <p>{{ formatTime(file.start_ts) }}</p>
                                <i class="icon iconfont icon-videofill"></i>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            <div v-show="isShowModal" class="btns" :class="{ is_horizontal: isHorizontalMode }">
                <template v-if="currentFile && !currentFile.is_default_image">
                    <div
                        v-show="
                            !isShowMoreBtn &&
                            (isShowDownload ||
                                isShowShareWechat ||
                                appPlus ||
                                (functionsStatus.breastAI &&
                                    isShowAnalyze &&
                                    systemConfig.serverInfo.enable_ai_analyze &&
                                    !isCE) ||
                                (functionsStatus.breastCases &&
                                    systemConfig.serverInfo.ai_searcher_server &&
                                    systemConfig.serverInfo.ai_searcher_server.enable &&
                                    !isCE))
                        "
                        class="download_btn"
                        @click.stop="openMoreBtn"
                    >
                        <i class="iconfont icon-shenglve"></i>
                    </div>
                    <div v-if="isShowMoreBtn" class="download_btn" @click.stop="closeMoreBtn">
                        <i class="iconfont icon-fanhui"></i>
                    </div>
                    <div v-show="isShowMoreBtn" class="more_btns">
                        <div v-if="isShowDownload" class="download_btn" @click.stop="download">
                            <i class="iconfont icon-icondownload"></i>
                        </div>
                        <div v-if="isShowShareWechat" class="download_btn" @click.stop="shareToWechat">
                            <i class="iconfont icon-wechat"></i>
                        </div>

                        <div
                            v-if="
                                functionsStatus.breastCases &&
                                systemConfig.serverInfo.ai_searcher_server &&
                                systemConfig.serverInfo.ai_searcher_server.enable &&
                                !isCE
                            "
                            class="download_btn"
                            @click.stop="searchInCaseData"
                        >
                            <i class="iconfont icon-search-image f1"></i>
                        </div>
                        <div
                            v-if="
                                functionsStatus.breastAI &&
                                isShowAnalyze &&
                                systemConfig.serverInfo.enable_ai_analyze &&
                                !isCE
                            "
                            class="transfer_btn"
                            @click.stop="analyze"
                        >
                            <i class="iconfont icon-zhinenghua"></i>
                        </div>
                        <div v-if="appPlus" class="download_btn" @click.stop="openLightnessAdjusting">
                            <i class="iconfont icon-icon-2"></i>
                        </div>
                        <div v-if="appPlus" class="download_btn">
                            <i
                                v-show="!isHorizontalMode"
                                @click.stop="toggleHorizontal(true)"
                                class="iconfont icon-fullscreen"
                            ></i>
                            <i
                                v-show="isHorizontalMode"
                                @click.stop="toggleHorizontal(false)"
                                class="iconfont icon-fullscreen-exit"
                            ></i>
                        </div>
                        <!-- <div v-if="isShowCopyMediaBtn" class="download_btn" @click.stop="copyImage">
                            <i class="iconfont icon-copy" style="font-size: 21px;"></i>
                        </div> -->
                    </div>
                    <div v-if="isShowTransferBtn" class="transfer_btn" @click.stop="transfer">
                        <i class="iconfont icon-share"></i>
                    </div>
                    <div v-if="isShowFavoriteBtn" class="download_btn" @click.stop="handleSaveFavoriteAction">
                        <i class="iconfont icon-heart" :class="{ active: checkFavoriteStatus() }"></i>
                    </div>
                    <div v-show="!hideLike" class="download_btn" @click.stop="handleLikeAction">
                        <i :class="['iconfont icon-zan', checkLikeAction() ? 'like' : '']"></i>
                    </div>
                    <div v-show="isShowQRCodeBtn" class="download_btn" @click.stop="handleQRCode">
                        <i class="iconfont icon-scan"></i>
                    </div>
                    <div
                        v-if="isShowToGroup && galleryList[index] && galleryList[index].group_id"
                        class="download_btn"
                        @click="toFileGroup"
                    >
                        <i class="iconfont icon-groupchat-icon"></i>
                    </div>
                </template>
            </div>
        </div>
        <div class="comment" v-show="isShowComment">
            <template v-if="currentFile && !currentFile.is_default_image">
                <div class="comment_title">
                    <!-- <i v-show="!isShowComment" class="icon iconfont icon-up toggle_comment_btn"></i>
                  <i v-show="isShowComment" @tap="hideComment($event)" class="icon iconfont icon-down toggle_comment_btn"></i> -->
                    <div class="comment_navbar">
                        <div
                            class="navbar_item"
                            @tap="toggleItem('comment')"
                            :class="{ active: itemActive == 'comment' }"
                        >
                            {{ lang.gallery_navbar_comment }}({{ commentNumber }})
                        </div>
                        <div class="navbar_item" @tap="toggleItem('tags')" :class="{ active: itemActive == 'tags' }">
                            {{ lang.label_txt }}({{ tagList.length }})
                        </div>
                        <div
                            v-if="ai_clips.ai_type > 0 && functionsStatus.breastAI"
                            class="navbar_item"
                            @tap="toggleItem('analyze')"
                            :class="{ active: itemActive == 'analyze' }"
                        >
                            {{ lang.ai_analyze }}
                        </div>
                        <template v-else>
                            <div
                                v-if="currentFile.patient_id"
                                class="navbar_item"
                                @tap="toggleItem('patient')"
                                :class="{ active: itemActive == 'patient' }"
                            >
                                {{ lang.gallery_navbar_patient }}
                            </div>
                        </template>
                    </div>
                </div>
                <div class="comment_container">
                    <div class="comment_container_item" v-show="itemActive == 'comment'">
                        <div class="comment_list">
                            <template v-for="(comment, c_index) of commentList">
                                <template
                                    v-if="!comment.is_private || (comment.is_private && comment.author_id === user.uid)"
                                >
                                    <div
                                        v-if="comment.status !== 0"
                                        class="comment_list_item clearfix"
                                        :ref="'comment_' + c_index"
                                        :key="c_index"
                                    >
                                        <pre>{{ comment.content }}</pre>
                                        <span class="comment_author">{{ comment.nickname }}</span>
                                        <span class="comment_time">{{ formatTime(comment.post_ts) }}</span>
                                        <span class="comment_private_text" v-if="comment.is_private">{{
                                            lang.private_comment
                                        }}</span>
                                    </div>
                                </template>
                            </template>
                        </div>
                        <p class="edit_comment_btn" @click="openEditModal">{{ lang.gallery_add_comment_btn }}</p>
                    </div>
                    <div class="comment_container_item tags_container" v-show="itemActive == 'tags'">
                        <div v-show="addingTags" class="full_loading_spinner">
                            <van-loading color="#00c59d" />
                        </div>
                        <div class="tag_list clearfix" ref="tag_list_container">
                            <span v-for="(item, index) of tagList" @tap="deleteTag(index)" class="fl" :key="index">
                                {{ item.tags }}
                                <span class="checknum">{{ item.checknum }}</span>
                            </span>
                            <span class="fl add_tag_btn" @click="openAddCustomTag">{{
                                lang.gallery_add_tags_btn
                            }}</span>
                        </div>
                        <div class="tag_names_list">
                            <div class="tag_names_list_title">
                                <div class="tag_names_navbar_item active">{{ lang.custom_tag_top }}</div>
                            </div>
                            <div class="tag_names_list_container">
                                <div class="tag_names_list_container_item">
                                    <div class="tag_names clearfix">
                                        <span
                                            v-for="(item, index) of allTags"
                                            @tap="addUserTag(index)"
                                            :class="{ checked: item.checked == true }"
                                            class="fl"
                                            :key="index"
                                        >
                                            {{ item.caption }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <template
                        v-if="
                            currentFile.patient_id &&
                            (!functionsStatus.breastAI || (functionsStatus.breastAI && ai_clips.ai_type <= 0))
                        "
                    >
                        <div class="comment_container_item patient_container" v-show="itemActive == 'patient'">
                            <p>{{ lang.patient_name }}: {{ patientInfo.patient_name }}</p>
                            <p>{{ lang.patient_sex }}: {{ patientInfo.patient_sex }}</p>
                            <p>{{ lang.patient_age }}: {{ patientInfo.patient_age }}</p>
                        </div>
                    </template>
                    <template v-else>
                        <div v-if="itemActive == 'analyze'" class="comment_container_item analyze_container">
                            <template v-if="ai_clips.ai_type == aiPresetData.typeIndex.breast">
                                <template v-if="ai_clips.mark_list.length > 0">
                                    <!-- <p v-if="ai_clips.mark_list[0] && ai_clips.mark_list[0].BI_RADS_features">
                                        {{ lang.BI_RADS_features[1][ai_clips.mark_list[0].BI_RADS_features[1]] }}，
                                        {{ lang.BI_RADS_features[0][ai_clips.mark_list[0].BI_RADS_features[0]] }}，
                                        <template v-if="Array.isArray(ai_clips.mark_list[0].BI_RADS_features[2])">
                                            {{
                                                ai_clips.mark_list[0].BI_RADS_features[2][0] == 1
                                                    ? lang.BI_RADS_features[2][0]
                                                    : ""
                                            }}
                                            {{
                                                ai_clips.mark_list[0].BI_RADS_features[2][1] == 1
                                                    ? lang.BI_RADS_features[2][1]
                                                    : ""
                                            }}
                                            {{
                                                ai_clips.mark_list[0].BI_RADS_features[2][2] == 1
                                                    ? lang.BI_RADS_features[2][2]
                                                    : ""
                                            }}
                                            {{
                                                ai_clips.mark_list[0].BI_RADS_features[2][3] == 1
                                                    ? lang.BI_RADS_features[2][3]
                                                    : ""
                                            }}
                                            {{
                                                ai_clips.mark_list[0].BI_RADS_features[2][4] == 1
                                                    ? lang.BI_RADS_features[2][4]
                                                    : ""
                                            }}，
                                        </template>
                                        <template v-else>
                                            {{ lang.BI_RADS_features[2][mark_list[0].BI_RADS_features[2]] }}，
                                        </template>
                                        {{ lang.BI_RADS_features[3][ai_clips.mark_list[0].BI_RADS_features[3]] }}，
                                        {{ lang.BI_RADS_features[4][ai_clips.mark_list[0].BI_RADS_features[4]] }}，
                                        {{ lang.BI_RADS_features[5][ai_clips.mark_list[0].BI_RADS_features[5]] }}，
                                        {{ lang.BI_RADS_features[6][ai_clips.mark_list[0].BI_RADS_features[6]] }}
                                    </p>
                                    <p v-else>{{ lang.no_BI_RADS_features }}</p>
                                    -->
                                    <p v-if="summary" class="conclution">
                                        {{ lang.case_database_fliter.bi_rads_type_text }}
                                        {{ summary.BI_RADS_results }}<br />
                                        {{ lang.BI_RADS_features_results_tip }}
                                        <template
                                            v-if="
                                                (summary.BI_RADS_features_results[0] &&
                                                    summary.BI_RADS_features_results[0] > -1) ||
                                                (summary.BI_RADS_features_results[3] &&
                                                    summary.BI_RADS_features_results[3] > -1) ||
                                                (summary.BI_RADS_features_results[2] &&
                                                    Array.isArray(summary.BI_RADS_features_results[2]) &&
                                                    summary.BI_RADS_features_results[2][0] > 0)
                                            "
                                        >
                                            {{
                                                lang.BI_RADS_features[1][summary.BI_RADS_features_results[1]]
                                                    ? lang.BI_RADS_features[1][summary.BI_RADS_features_results[1]] +
                                                      "，"
                                                    : ""
                                            }}
                                            {{
                                                lang.BI_RADS_features[0][summary.BI_RADS_features_results[0]]
                                                    ? lang.BI_RADS_features[0][summary.BI_RADS_features_results[0]] +
                                                      "，"
                                                    : ""
                                            }}
                                            <template v-if="Array.isArray(ai_clips.mark_list[0].BI_RADS_features[2])">
                                                {{
                                                    summary.BI_RADS_features_results[2][0] == 1
                                                        ? lang.BI_RADS_features[2][0]
                                                        : ""
                                                }}
                                                {{
                                                    summary.BI_RADS_features_results[2][1] == 1
                                                        ? lang.BI_RADS_features[2][1]
                                                        : ""
                                                }}
                                                {{
                                                    summary.BI_RADS_features_results[2][2] == 1
                                                        ? lang.BI_RADS_features[2][2]
                                                        : ""
                                                }}
                                                {{
                                                    summary.BI_RADS_features_results[2][3] == 1
                                                        ? lang.BI_RADS_features[2][3]
                                                        : ""
                                                }}
                                                {{
                                                    summary.BI_RADS_features_results[2][4] == 1
                                                        ? lang.BI_RADS_features[2][4]
                                                        : ""
                                                }}，
                                            </template>
                                            <template v-else>
                                                {{
                                                    lang.BI_RADS_features[2][summary.BI_RADS_features_results[2]]
                                                        ? lang.BI_RADS_features[2][
                                                              summary.BI_RADS_features_results[2]
                                                          ] + "，"
                                                        : ""
                                                }}
                                            </template>
                                            {{
                                                lang.BI_RADS_features[3][summary.BI_RADS_features_results[3]]
                                                    ? lang.BI_RADS_features[3][summary.BI_RADS_features_results[3]] +
                                                      "，"
                                                    : ""
                                            }}
                                            {{
                                                lang.BI_RADS_features[4][summary.BI_RADS_features_results[4]]
                                                    ? lang.BI_RADS_features[4][summary.BI_RADS_features_results[4]] +
                                                      "，"
                                                    : ""
                                            }}
                                            {{
                                                lang.BI_RADS_features[5][summary.BI_RADS_features_results[5]]
                                                    ? lang.BI_RADS_features[5][summary.BI_RADS_features_results[5]] +
                                                      "，"
                                                    : ""
                                            }}
                                            {{
                                                lang.BI_RADS_features[6][summary.BI_RADS_features_results[6]]
                                                    ? lang.BI_RADS_features[6][summary.BI_RADS_features_results[6]] +
                                                      ","
                                                    : ""
                                            }}
                                        </template>
                                        <template v-else>
                                            {{ lang.no_BI_RADS_features_results_tip }}
                                        </template>
                                    </p>
                                    <p v-else>{{ lang.no_BI_RADS_features_results_tip }}</p>
                                    <p class="tip">{{ lang.results_reference_only }}</p>
                                </template>
                                <div v-else class="no_data">{{ lang.no_ai_result }}</div>
                            </template>

                            <template
                                v-if="
                                    ai_clips.ai_type == aiPresetData.typeIndex.abdomen ||
                                    ai_clips.ai_type == aiPresetData.typeIndex.cardiac
                                "
                            >
                                <template v-if="ai_clips.ai_report.length > 0">
                                    <template v-if="nameDeal(ai_clips.ai_report[0]) != '--'">
                                        <p>
                                            {{ lang.exam_type_text }}
                                            {{
                                                lang.exam_types[currentFile.exam_type]
                                                    ? lang.exam_types[currentFile.exam_type]
                                                    : "--"
                                            }}
                                        </p>
                                        <p>
                                            {{ lang.view_name_text }}
                                            {{ nameDeal(ai_clips.ai_report[0]) }}
                                        </p>
                                        <template v-if="ai_clips.ai_type != aiPresetData.typeIndex.cardiac">
                                            <p>
                                                {{ lang.compliance_rate }}:
                                                {{
                                                    ai_clips.ai_report[0].score != undefined
                                                        ? toFixedNumber(ai_clips.ai_report[0].score) * 10 + "%"
                                                        : "--"
                                                }}
                                            </p>
                                            <p>
                                                {{ lang.view_quality_text }}
                                                {{ qualityDeal(ai_clips.ai_report[0]) }}
                                            </p>
                                        </template>
                                        <div class="struc_arear">
                                            <div class="label">{{ lang.structure_evaluation_text }}</div>
                                            <div class="value">
                                                <template
                                                    v-if="
                                                        ai_clips.ai_report[0].dispalyStructs &&
                                                        ai_clips.ai_report[0].dispalyStructs.length > 0
                                                    "
                                                >
                                                    <table>
                                                        <thead>
                                                            <tr>
                                                                <td>{{ lang.structure_name }}</td>
                                                                <td>{{ lang.box_color }}</td>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr
                                                                v-for="(item, index) of ai_clips.ai_report[0]
                                                                    .dispalyStructs"
                                                                :key="index"
                                                            >
                                                                <td>{{ lang[item.id] }}</td>
                                                                <td>
                                                                    <span
                                                                        class="arear"
                                                                        :style="{ background: item.color }"
                                                                        v-if="item.isExist"
                                                                    >
                                                                    </span>
                                                                    <span class="error" v-else>
                                                                        {{ lang.deletion }}
                                                                    </span>
                                                                </td>
                                                                <!-- <td>{{item[0].rate}}</td> -->
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </template>
                                                <template v-else> -- </template>
                                            </div>
                                        </div>
                                        <p class="tip">{{ lang.results_reference_only }}</p>
                                    </template>
                                    <div v-else class="no_data">{{ lang.no_ai_result }}</div>
                                </template>
                                <div v-else class="no_data">{{ lang.no_ai_result }}</div>
                            </template>
                            <template v-if="ai_clips.ai_type == aiPresetData.typeIndex.obstetrical">
                                <template
                                    v-if="
                                        ai_clips.obstetrical_clips.length > 0 &&
                                        ai_clips.obstetrical_clips[0] &&
                                        ai_clips.obstetrical_clips[0].viewName
                                    "
                                >
                                    <h4>{{ lang.verall_evaluation_qc }}</h4>
                                    <p>
                                        {{ lang.exam_type_text }}
                                        {{
                                            lang.exam_types[currentFile.exam_type]
                                                ? lang.exam_types[currentFile.exam_type]
                                                : "--"
                                        }}
                                    </p>
                                    <p>
                                        {{ lang.view_name_text }}
                                        {{ getItemName(ai_clips.obstetrical_clips[0]) }}
                                    </p>
                                    <p>
                                        {{ lang.view_score_text }}
                                        {{
                                            ai_clips.obstetrical_clips[0].viewName
                                                ? toFixedNumber(ai_clips.obstetrical_clips[0].viewScore) +
                                                  "(" +
                                                  lang.full_mark +
                                                  "100)"
                                                : "--"
                                        }}
                                    </p>
                                    <p>
                                        {{ lang.view_quality_text }}
                                        {{
                                            ai_clips.obstetrical_clips[0].viewName
                                                ? ai_clips.obstetrical_clips[0].viewQuality
                                                : "--"
                                        }}
                                    </p>
                                    <p>
                                        {{ lang.reason_for_deficiency_text }}
                                        {{
                                            ai_clips.obstetrical_clips[0].viewName
                                                ? ai_clips.obstetrical_clips[0].viewReason
                                                : "--"
                                        }}
                                    </p>
                                    <template>
                                        <h4>{{ lang.structure_evaluation_text }}</h4>
                                        <table
                                            v-if="
                                                ai_clips.obstetrical_clips[0] &&
                                                ai_clips.obstetrical_clips[0].viewItemList &&
                                                ai_clips.obstetrical_clips[0].viewItemList.length > 0
                                            "
                                        >
                                            <thead></thead>
                                            <tbody>
                                                <tr>
                                                    <td>{{ lang.score_items }}</td>
                                                    <td>{{ lang.score_value }}</td>
                                                    <td>{{ lang.proportion_weight }}</td>
                                                    <td>{{ lang.box_color }}</td>
                                                </tr>
                                            </tbody>
                                            <tbody>
                                                <tr
                                                    v-for="(item, id, index) of ai_clips.obstetrical_clips[0]
                                                        .viewItemList"
                                                    :key="index"
                                                >
                                                    <td>{{ getItemName(item[0]) }}</td>
                                                    <td :class="{ error: !item[0].isHave }">
                                                        {{
                                                            item[0].isHave
                                                                ? toFixedNumber(item[0].score)
                                                                : lang.deletion
                                                        }}
                                                    </td>
                                                    <td>{{ item[0].rate }}</td>
                                                    <td>
                                                        <span
                                                            class="arear"
                                                            :style="{ background: item[0].color }"
                                                            v-if="item[0].existStruct"
                                                        >
                                                        </span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <div class="tips" v-else>{{ lang.obstetric_qc.nalysis_uncompleted }}</div>
                                    </template>
                                    <template v-if="false && ai_clips.obstetrical_clips[0].groupViewId">
                                        <h4 class="title">{{ lang.group_view_summary }}</h4>
                                        <p>
                                            {{ lang.view_group_name }}:
                                            {{
                                                ai_clips.obstetrical_clips[1].group_view_name
                                                    ? getItemName(ai_clips.obstetrical_clips[1].group_view)
                                                    : "--"
                                            }}
                                        </p>
                                        <p>
                                            {{ lang.group_view_score }}:
                                            {{
                                                ai_clips.obstetrical_clips[1].group_view_name
                                                    ? toFixedNumber(
                                                          ai_clips.obstetrical_clips[1].group_view_score * 100
                                                      ) +
                                                      "(" +
                                                      lang.full_mark +
                                                      "100)"
                                                    : "--"
                                            }}
                                        </p>
                                        <h4>{{ lang.view_group }}-{{ lang.structure_evaluation }}</h4>

                                        <table v-if="ai_clips.obstetrical_clips[1].group_item">
                                            <thead></thead>
                                            <tbody>
                                                <tr>
                                                    <td>{{ lang.score_items }}</td>
                                                    <td>{{ lang.score_value }}</td>
                                                    <td>{{ lang.proportion_weight }}</td>
                                                </tr>
                                            </tbody>
                                            <tbody>
                                                <tr
                                                    v-for="(item, index) of ai_clips.obstetrical_clips[1].group_item"
                                                    :key="index"
                                                >
                                                    <td>{{ getItemName(item) }}</td>
                                                    <td :class="{ error: item.score <= 0 }">
                                                        {{
                                                            item.score <= 0
                                                                ? lang.deletion
                                                                : toFixedNumber(item.score * 100)
                                                        }}
                                                    </td>
                                                    <td>{{ item.rate }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </template>
                                    <p class="tip">{{ lang.results_reference_only }}</p>
                                </template>
                                <div v-else class="no_data">{{ lang.no_ai_result }}</div>
                            </template>
                        </div>
                    </template>
                </div>
            </template>
        </div>
        <div class="video_page" v-if="isShowViode">
            <i class="icon iconfont icon-back close_video_page" @click.stop="closeVideo"></i>
            <video
                class="main_video"
                :src="mainVideoSrc"
                controls
                @error="videoError"
                @timeupdate="timeupdate"
                ref="mainVideo"
            >
                {{ lang.unsupport_video_text }}
            </video>
            <video
                class="gesture_video"
                v-show="gestrueVideoSrc != ''"
                controls
                :src="gestrueVideoSrc"
                ref="gestrueVideo"
            >
                {{ lang.unsupport_video_text }}
            </video>
            <audio :src="mainAudioSrc" ref="mainAudio"></audio>
        </div>
        <dcm-player v-if="isShowDCMPlayer" ref="dcm_player" :isShow.sync="isShowDCMPlayer"></dcm-player>
        <div class="edit_comment_modal needsclick" :class="{ showCommentModal: showCommentModal }">
            <div class="edit_comment_panel needsclick">
                <!-- <p class="title">{{lang.add_comment_title}}</p> -->
                <div class="edit_comment_container clearfix needsclick">
                    <div
                        class="comment_editer needsclick"
                        :contenteditable="showCommentModal"
                        ref="comment_text"
                        @input="changeText"
                        @click="enterEditMode($event)"
                        tabindex="1"
                    ></div>
                    <span class="send_comment primary_bg fr" @tap="sendComment">{{ lang.send_message_txt }}</span>
                    <span class="colse_comment primary_bg fl" @tap="closeComment">{{ lang.cancel_btn }}</span>
                </div>
                <div class="rainbow_row clearfix">
                    <div class="block1 fl"></div>
                    <div class="block2 fl"></div>
                    <div class="block3 fl"></div>
                    <div class="block4 fl"></div>
                    <div class="block5 fl"></div>
                </div>
            </div>
        </div>
        <div v-if="isShowPdfPage" class="pdf_page fourth_level_page">
            <mrHeader @click-left="closePdf">
                <template #title>
                    {{ galleryList[index].file_name }}
                </template>
            </mrHeader>
            <div class="pdf_container" @click="toggleShowOperator">
                <template v-if="unsupportedPdf">
                    <p class="tip">{{ lang.unsurpport_pdf }}</p>
                </template>
                <template v-else>
                    <canvas
                        :style="{ width: scale + '%' }"
                        v-for="(pageNum, index) in pageSum"
                        :id="'the_canvas_' + pageNum"
                        :key="index"
                    ></canvas>
                    <p v-if="loadingPdf" class="tip">loading...</p>
                </template>
            </div>
            <div class="operator" v-show="isShowOperator">
                <i @click.stop="narrow" :class="{ disable: scale == 100 }" class="iconfont icon-minus"></i>
                <i @click.stop="enlarge" :class="{ disable: scale == 200 }" class="iconfont icon-plus1"></i>
            </div>
        </div>
        <div v-show="isShowLightness" class="lightness_modal" @click="closeLightness">
            <div class="lightness_process" @click.stop>
                <p>{{ lightnessPercent }}%</p>
                <van-slider v-model="lightnessPercent" @change="adjustingLightness" />
            </div>
        </div>
    </div>
</template>

<script>
import base from "../lib/base";
import share_to_wechat from "../lib/share_to_wechat";
import Tool from "@/common/tool.js";
import { Toast, Loading, Slider } from "vant";
import {
    updateResoureceReview,
    getRealUrl,
    enterEditMode,
    transferPatientInfo,
    sendToAnalyze,
    cancelFavoriteCommit,
    patientDesensitization,
    parseImageListToLocal,
    generateGalleryFileId,
    getResourceTempStatus,
    checkResourceType,
    getResourceTempState,
    toFixedNumber,
    getMessageAiReportFromLocal,
} from "../lib/common_base";
import { dealObstetricReport, dealExamListWithGroupView } from "../lib/obstetricTool";
import RealImage from "../components/realImage.vue";
import dcmPlayer from "./DCMPlayer.vue";
import service from "../service/service";
import { trim } from "lodash";
export default {
    mixins: [base, share_to_wechat],
    name: "GalleryComponent",
    components: {
        RealImage,
        dcmPlayer,
        VanLoading: Loading,
        VanSlider: Slider,
    },
    data() {
        return {
            getMessageAiReportFromLocal,
            getResourceTempState,
            toFixedNumber,
            getResourceTempStatus,
            checkResourceType,
            getRealUrl,
            isShowGallery: false,
            index: 0,
            operate: "",
            isShowComment: false,
            itemActive: "",
            tagItemActive: "userTag",
            isShowViode: false,
            mainVideoSrc: "",
            gestrueVideoSrc: "",
            addingTags: false,
            commentText: "",
            retentionTimer: null, //停留时间
            showAISearchSuggest: false, //显示推送ai
            noneComment: this.$route.meta.noneComment || false, //不显示评论
            isShowTransferBtn: this.$route.meta.isShowTransferBtn || false, //显示转发按钮
            isShowFavoriteBtn: this.$route.meta.isShowFavoriteBtn || false, //显示收藏按钮
            isShowAnalyze: this.$route.meta.isShowAnalyze || false, //显示AI分析按钮
            isShowToGroup: this.$route.meta.isShowToGroup || false,
            is_private: this.$route.meta.is_private || 0, //显示私有评论还是公开评论
            isShowDownload: !this.$route.meta.hideShowDownload, //显示下载按钮
            hideLike: this.$route.meta.hideLike || false,
            preventTapTimeout: null,
            showCommentModal: false,
            isShowModal: true,
            mainAudioSrc: "",
            isScreenHorizontal: false, //屏幕水平放置
            galleryLength: 0,
            sliderKey: 0,
            isShowPdfPage: false,
            pageSum: 0,
            isShowOperator: true,
            unsupportedPdf: false,
            loadingPdf: false,
            scale: 100,
            sliderTimer: 0,
            isInitGallery: false,
            isShowMoreBtn: false,
            lightnessPercent: 0,
            isShowLightness: false,
            videoCommitFile: null,
            videoCommitStamp: 0,
            videoCommitInterval: null,
            isShowDCMPlayer: false,
            loadedAll: false,
            galleryList: [],
            colors: this.$store.state.aiPresetData.colors,
            commentList: [],
            getUserFavoriteStatus: false,
            getLikeCount: 0,
            getUserLikeStatus: false,
            hasQRCode: false,
            qrCodeData: null,
            qrCodeWorker: null,
        };
    },
    computed: {
        globalParams() {
            return this.$store.state.globalParams;
        },
        isShowShareWechat() {
            return this.isShowWechat && this.$route.meta.isShowShareWechat;
        },
        isShowQRCodeBtn() {
            return this.currentFile && this.checkResourceType(this.currentFile) === "image" && this.hasQRCode;
        },
        // isShowCopyMediaBtn() {
        //     return this.currentFile && this.checkResourceType(this.currentFile) === 'image';
        // },
        isHorizontalMode() {
            //横屏
            return this.isScreenHorizontal;
        },
        aiPresetData() {
            return this.$store.state.aiPresetData;
        },
        resource_id() {
            return this.galleryList[this.index] && this.galleryList[this.index].resource_id;
        },
        commentItem() {
            let commentObj = this.$store.state.gallery.commentObj;
            return commentObj[this.resource_id] || {};
        },
        summary() {
            return this.commentItem.ai_analyze_report && this.commentItem.ai_analyze_report.summary;
        },
        currentFile() {
            let currentFile = this.galleryList[this.index];
            return currentFile;
        },
        ai_clips() {
            let currentFile = this.galleryList[this.index];
            let ai_analyze_report = this.getMessageAiReportFromLocal(currentFile);
            let result = {
                ai_type: 0,
                ai_report: [],
                obstetrical_clips: [],
                mark_list: [],
            };
            if (ai_analyze_report && ai_analyze_report.clips && ai_analyze_report.clips[this.resource_id]) {
                let clips = ai_analyze_report.clips[this.resource_id];
                // 腹部
                if (
                    ai_analyze_report.type == this.aiPresetData.typeIndex.abdomen ||
                    ai_analyze_report.type == this.aiPresetData.typeIndex.cardiac
                ) {
                    result.ai_type = ai_analyze_report.type;
                    let ai_report = clips[0];
                    let views = null;
                    if (result.ai_type && result.ai_type == this.aiPresetData.typeIndex.abdomen) {
                        views = this.$store.state.aiPresetData?.iworksAbdomenTest.views || {};
                    }
                    if (result.ai_type && result.ai_type == this.aiPresetData.typeIndex.cardiac) {
                        views = this.$store.state.aiPresetData?.cardiacViews.views || {};
                    }
                    const child_id =
                        ai_report.child_id && ai_report.child_id >= 0 && views[ai_report.child_id]
                            ? ai_report.child_id
                            : -1;
                    if (ai_report.id >= 0 && views[ai_report.id]) {
                        let viewObj = views[ai_report.id];
                        if (child_id > -1) {
                            viewObj = views[child_id];
                        }
                        let dispalyStructs = viewObj.details.map((v) => {
                            return v;
                        });
                        for (let i in dispalyStructs) {
                            let struct = viewObj.details[i];
                            for (let j in ai_report.struct || []) {
                                let item = ai_report.struct[j];
                                if (item.type == struct.id) {
                                    item.isExist = item.position && item.position.length > 0;
                                    // hasStruct = true
                                    dispalyStructs[i] = { ...dispalyStructs[i], ...item };
                                }
                            }
                        }
                        ai_report["dispalyStructs"] = this.getArearcolor(dispalyStructs);
                        ai_report["dispalyStructs"] = ai_report.dispalyStructs.sort((a, b) => {
                            return b.isExist - a.isExist;
                        });
                        result.ai_report = [ai_report];
                    }
                }
                // 产科
                if (ai_analyze_report.type == this.aiPresetData.typeIndex.obstetrical) {
                    result.ai_type = this.aiPresetData.typeIndex.obstetrical;
                    let obstetrical_report = clips[0];
                    let obstetric_view = dealObstetricReport(obstetrical_report);
                    let obstetric_group_view = dealExamListWithGroupView(obstetrical_report);
                    // dealExamListWithGroupView
                    result.obstetrical_clips = [obstetric_view, obstetric_group_view];
                }
            }
            if (ai_analyze_report && ai_analyze_report.mark_list && ai_analyze_report.mark_list[this.resource_id]) {
                let mark_list = ai_analyze_report.mark_list[this.resource_id];
                // 乳腺
                if (ai_analyze_report.type == this.aiPresetData.typeIndex.breast) {
                    result.ai_type = this.aiPresetData.typeIndex.breast;
                    result.mark_list = mark_list;
                }
            }
            return result;
        },
        isInternalNetworkEnv() {
            return this.systemConfig.serverInfo.network_environment;
        },
        commentNumber() {
            let num = 0;
            this.commentList.forEach((element) => {
                if (element.status !== 0) {
                    if (!element.is_private || (element.is_private && this.user.uid === element.author_id)) {
                        num++;
                    }
                }
            });
            return num;
        },
        tagList() {
            //已添加标签去重
            let list = this.commentItem.tags_list || [];
            let tempObj = {};
            let tempArr = [];
            let uid = this.user.uid;
            for (let tag of list) {
                if (!tempObj[tag.tags]) {
                    tempObj[tag.tags] = tag;
                    tag.checknum = 1;
                } else {
                    tempObj[tag.tags].checknum += 1;
                }
                if (tag.sender_id == uid) {
                    tempObj[tag.tags].selfCheck = true;
                }
            }
            for (let tag in tempObj) {
                tempArr.push(tempObj[tag]);
            }
            return tempArr;
        },
        allTags() {
            //所有标签
            let allList = this.$store.state.gallery.tagTopInfo.allTagTop || [];
            let result = [];
            let obj = {};
            for (let tag of allList) {
                tag.checked = false;
                result.push(tag);
            }
            for (let item of this.tagList) {
                if (item.selfCheck) {
                    for (let tag of result) {
                        if (item.tags == tag.caption) {
                            tag.checked = true;
                            break;
                        }
                    }
                }
            }
            return result;
        },
        patientInfo() {
            let obj = {};
            let image = this.galleryList[this.index] || {};
            obj = transferPatientInfo(image);
            let sexArr = [this.lang.male, this.lang.female, this.lang.unknown];
            if (image && image.live_record_data) {
                if (image.live_record_data.name) {
                    obj.patient_name = image.live_record_data.name;
                }
                if (image.live_record_data.age) {
                    obj.patient_age = image.live_record_data.age;
                }
                if (image.live_record_data.hasOwnProperty("gender")) {
                    obj.patient_sex = sexArr[Number(image.live_record_data.gender)];
                }
            }
            return obj;
        },
        appPlus() {
            return this.isApp;
        },
        loadMore() {
            return this.gallery.loadMore || false;
        },
    },
    watch: {
        // galleryList(cur,old){
        //     var that=this;
        //     if (!this.isInitGallery) {
        //         this.galleryLength=cur.length;
        //         return;
        //     }
        //     let recieve=cur.length-this.galleryLength;
        //     console.log('galleryList',this.index)
        //     if(this.index === -1){
        //         const fileId = this.$store.state.gallery.fileId
        //         this.positionIndexByFileId(fileId)

        //     }else{
        //         if (recieve==1) {
        //             const fileId = generateGalleryFileId(cur[this.index+1])
        //             this.positionIndexByFileId(fileId)
        //             // this.$nextTick(()=>{
        //             //     //正在画廊中往画廊添加一张图片时，自动定位到index+1
        //             //     var gallery=window.mui('.mui-slider')
        //             //     var temp=gallery.slider()
        //             //     if (temp) {
        //             //         console.log('changeImage gotoItem')
        //             //         temp.refresh()
        //             //         temp.gotoItem(that.index+1,0)
        //             //     }
        //             // })
        //         }
        //     }

        //     this.galleryLength=cur.length
        // }
        "$store.state.gallery.commentObj": {
            handler(val) {
                if (!this.isShowGallery) {
                    return;
                }
                if (val && this.currentFile && this.currentFile.resource_id && val[this.currentFile.resource_id]) {
                    if (
                        val[this.currentFile.resource_id].ai_analyze_report &&
                        val[this.currentFile.resource_id].ai_analyze_report.status
                    ) {
                        this.$nextTick(() => {
                            if (this.index >= 0) {
                                this.updateMarkListIfNeed();
                            }
                        });
                    }
                }
            },
        },
    },
    mounted() {
        this.mediaQuery = window.matchMedia("(orientation: portrait)"); // 横屏方向
        this.mediaQuery.addListener(this.handleOrientationChange);
        this.$nextTick(() => {
            this.$root.eventBus.$off("openCommentModal").$on("openCommentModal", this.openCommentModal);
            this.$root.eventBus.$off("initGallery").$on("initGallery", this.initGallery);
            this.$root.eventBus.$off("destroyGallery").$on("destroyGallery", this.destroyGallery);
            this.$root.eventBus.$off("endVideoCommit").$on("endVideoCommit", this.endVideoCommit);
            this.$root.eventBus.$off("shareToWechatHandler").$on("shareToWechatHandler", this.shareToWechatHandler);
            this.$root.eventBus.$off("updateLoadedDcm").$on("updateLoadedDcm", this.updateLoadedDcm);
            this.$root.eventBus.$off("leaveGalleryRouter").$on("leaveGalleryRouter", this.leaveGalleryRouter);
            this.$root.eventBus.$off("enterGalleryRouter").$on("enterGalleryRouter", this.enterGalleryRouter);

            // 初始化二维码识别Worker
            this.initQRCodeWorker();
        });
    },
    beforeDestroy() {
        this.mediaQuery.removeListener(this.handleOrientationChange);
        this.hasQRCode = false;
        if (this.qrCodeWorker) {
            this.qrCodeWorker.terminate();
            this.qrCodeWorker = null;
        }
    },
    methods: {
        getItemName(item) {
            if (item.key && item.key in this.lang) {
                return this.lang[item.key];
            }
            return item.name;
        },
        initGallery() {
            const cacheData = window.vm.$store.state.gallery.cacheData[window.vm.$route.path];
            let galleryList = [];
            if (this.$store.state.gallery.list.length > 0) {
                galleryList = this.$store.state.gallery.list;
                this.index = this.$store.state.gallery.index;
            } else if (cacheData && cacheData.list.length > 0) {
                window.vm.$store.commit("gallery/setGallery", cacheData);
                galleryList = cacheData.list;
                this.index = cacheData.index;
            }

            this.galleryList = this.formatGalleryList(galleryList);

            this.operate = "";
            this.isShowModal = true;
            // this.itemActive=''
            this.isShowViode = false;
            this.mainVideoSrc = "";
            this.gestrueVideoSrc = "";
            this.addingTags = false;
            this.commentText = "";
            this.noneComment = window.vm.$route.meta.noneComment || false;
            this.toggleComment(true);
            this.isShowTransferBtn = window.vm.$route.meta.isShowTransferBtn || false;
            this.isShowFavoriteBtn = window.vm.$route.meta.isShowFavoriteBtn || false;
            // this.isShowShareWechat=window.vm.$route.meta.isShowShareWechat||false
            this.isShowAnalyze = window.vm.$route.meta.isShowAnalyze || false;
            this.isShowToGroup = window.vm.$route.meta.isShowToGroup || false;
            this.hideLike = window.vm.$route.meta.hideLike || false;

            this.is_private = window.vm.$route.meta.is_private || 0;
            this.isShowDownload = !window.vm.$route.meta.hideShowDownload;
            this.preventTapTimeout = null;
            this.showCommentModal = false;
            this.isShowModa = false;
            this.mainAudioSrc = "";
            this.isScreenHorizontal = false;
            this.galleryLength = 0;
            this.isShowGallery = true;
            this.isInitGallery = true;
            this.sliderKey = new Date().valueOf();
            this.isShowMoreBtn = false;
            var that = this;
            this.$store.commit("gallery/sortTags");
            this.$nextTick(async () => {
                if (that.index == 0) {
                    //点击第一张图时触发change
                    that.changeHandler(0);
                }

                document.getElementById("my_slider").addEventListener("slide", that.slideHandler);
                var gallery = window.mui(".mui-slider");
                var sliderObj = gallery.slider();

                window.mui(".mui-zoom-wrapper").zoom();
                try {
                    await this.checkIfNeedLoadMore();
                } finally {
                    sliderObj.gotoItem(that.index, 0);
                    this.shouldAutoPlay();
                }
                that.setItemActive(that.index);
                // this.index=this.index > this.galleryLength? 0 : this.index
            });
        },
        formatGalleryList(oList) {
            let list = [];
            oList.forEach((item) => {
                if (this.getResourceTempState(item.resource_id) === 1) {
                    list.push(item);
                }
            });
            return list;
        },
        closeGallery() {
            this.back();
        },
        destroyGallery() {
            this.isShowGallery = false;
            this.isInitGallery = false;
            this.isScreenHorizontal = false;
            this.isShowMoreBtn = false;
            setTimeout(() => {
                this.$store.commit("gallery/clearGalleryData");
            }, 300);
            window.removeEventListener("deviceorientation", this.handleOrientation);
            document.getElementById("my_slider") &&
                document.getElementById("my_slider").removeEventListener("slide", this.slideHandler);
            this.endVideoCommit();
        },
        slideHandler(event) {
            console.log("slideHandler");
            let index = event.detail.slideNumber;
            this.$store.commit("gallery/setGallery", {
                index: index,
            });
            this.changeHandler(index);
        },
        loadMoreConversationGalleryData() {
            return new Promise((resolve, reject) => {
                let cid = this.$store.state.gallery.cid;
                const conversation = this.conversationList[cid];
                if (!cid || !conversation || this.loadedAll) {
                    reject(false);
                    return;
                }
                let controller = conversation.socket;
                const galleryObj = conversation.galleryObj;
                let start = galleryObj.index;
                let that = this;
                if (start == -1) {
                    //全部加载完了
                    this.loadedAll = true;
                    Toast(that.lang.image_all_loaded);
                    reject(false);
                    return;
                } else {
                    this.loadingMore = true;
                    controller.emit(
                        "get_gallery_messages",
                        {
                            start: start,
                            count: this.systemConfig.consultationImageShowNum,
                        },
                        function (is_succ, data) {
                            if (is_succ) {
                                patientDesensitization(data.gallery_list);
                                parseImageListToLocal(data.gallery_list, "url");
                                data.cid = cid;
                                that.$store.commit("conversationList/pushMoreGroupFile", data);
                                if (that.galleryList.list == galleryObj.total_count) {
                                    this.loadedAll = true;
                                    Toast(that.lang.image_all_loaded);
                                    reject(false);
                                    return;
                                }
                                resolve(true);
                            } else {
                                reject(false);
                                return;
                            }
                        }
                    );
                }
            });
        },
        async checkIfNeedLoadMore() {
            if (this.galleryList.length - this.index <= 3 && this.loadMore) {
                // await this.loadMoreConversationGalleryData()
                this.$store.state.gallery.loadMoreCallback();
                return true;
            }
            return false;
        },
        setItemActive(index) {
            if (index < 0) {
                this.itemActive = "";
            }
            //产科质控
            let file = this.galleryList[index];
            if (file.group_id || file.gmsg_id || file.sender_id) {
                //AI分析
                let storeItem = this.$store.state.gallery.commentObj[file.resource_id];
                let enabled = this.functionsStatus.breastAI && this.ai_clips.ai_type > 0;
                if (enabled) {
                    if (this.itemActive == "patient") {
                        this.itemActive = "comment";
                    }
                    this.itemActive = this.itemActive || "analyze";
                } else {
                    if (this.itemActive === "analyze") {
                        this.itemActive = "comment";
                    } else {
                        this.itemActive = this.itemActive || "comment";
                    }
                }
            } else {
                this.itemActive = "comment";
            }
        },
        changeHandler(index) {
            this.index = index;
            this.showAISearchSuggest = false;
            // 无论切换到什么类型的文件，都重置二维码检测状态
            this.hasQRCode = false;
            this.qrCodeData = null;
            this.setItemActive(index);
            this.endVideoCommit(); // 关闭回放计时的上报
            // if(this.mark_list.length === 0 &&this.itemActive === 'analyze'){
            //     this.itemActive = 'comment'
            // }
            this.clearResourceDetail();
            this.getResourceDetail();
            let cid = this.galleryList[this.index].group_id;
            if (cid && !this.conversationList[cid]) {
                //展示当前图片是打开会话
                this.openConversation(cid, 13);
            } else {
                // updateResoureceReview(cid);
            }
            //快速划过不预加载图片
            clearTimeout(this.sliderTimer);
            this.sliderTimer = setTimeout(() => {
                this.preLoad(index);
                if (index - 1 >= 0) {
                    this.preLoad(index - 1);
                }
                if (index + 1 < this.galleryList.length) {
                    this.preLoad(index + 1);
                }
                this.checkIfNeedLoadMore();
            }, 300);
            let currentFile = this.galleryList[this.index];
            if (currentFile.resource_id) {
                if (this.$store.state.gallery.commentObj[currentFile.resource_id]) {
                    let item = this.$store.state.gallery.commentObj[currentFile.resource_id];
                    if ("showAISearchSuggest" in item) {
                        this.showAISearchSuggest = item.showAISearchSuggest;
                    } else {
                        this.showAISearchSuggest = false;
                        // this.retentionTimer=setTimeout(()=>{
                        //     this.serarchInCaseDatabaseWithBackground(currentFile)
                        // },800)
                    }
                } else {
                    this.showAISearchSuggest = false;
                    // this.retentionTimer=setTimeout(()=>{
                    //     this.serarchInCaseDatabaseWithBackground(currentFile)
                    // },800)
                }
            }
        },
        preLoad(index) {
            let target = this.$refs[`realImage_${index}`];
            target && target[0] && target[0].preload();

            // 如果是当前显示的图片且是图片类型，在加载时检测二维码
            let imageObj = this.galleryList[index] || {};
            if (this.index === index && this.checkResourceType(imageObj) === "image") {
                this.detectQRCode(imageObj);
            }
        },
        shouldAutoPlay() {
            let imageObj = this.galleryList[this.index];
            if (!imageObj) {
                return;
            }
            if (imageObj.img_encode_type && imageObj.img_encode_type.toUpperCase() == this.systemConfig.file_type.DCM) {
                this.initDCMPlayer({}, this.index);
            } else {
                if (
                    this.checkResourceType(imageObj) === "video" ||
                    this.checkResourceType(imageObj) === "review_video"
                ) {
                    this.initVideoPage({}, this.index);
                }
            }
        },
        initComment() {
            if (!this.itemActive) {
                this.itemActive = "comment";
            }
            this.toggleComment(true);
            this.isShowModal = true;
        },
        hideComment() {
            // e.cancelBubble=true
            this.isShowComment = false;
            this.isShowModal = false;
        },
        toggleItem(type) {
            this.itemActive = type;
        },
        initVideoPage(e, index) {
            e.cancelBubble = true;
            var that = this;
            var imageObj = Object.assign({}, this.galleryList[index]);
            console.log(imageObj, "initVideoPage");
            if (
                imageObj.msg_type == this.systemConfig.msg_type.Video ||
                imageObj.img_type_ex == this.systemConfig.msg_type.Video
            ) {
                this.mainVideoSrc = imageObj.url.replace(imageObj.thumb, "");
            } else if (
                imageObj.msg_type == this.systemConfig.msg_type.Cine ||
                imageObj.img_type_ex == this.systemConfig.msg_type.Cine
            ) {
                let url = "";
                if (imageObj.isExamFile || imageObj.isTransferFile || imageObj.isLocalPatientFile) {
                    //检查浏览点开会诊视频
                    url = imageObj.url2;
                    if (!imageObj.loaded) {
                        return;
                        // Toast(this.lang.video_not_ready)
                    }
                } else {
                    if (imageObj.mainVideoSrc) {
                        //存在mainVideoSrc直接播放该地址
                        url = imageObj.mainVideoSrc;
                    } else {
                        url = imageObj.url.replace("thumbnail.jpg", "DeviceVideo.");
                        url += imageObj.img_encode_type;
                        imageObj.gesture_url =
                            imageObj.url.replace("thumbnail.jpg", "GestureVideo.") + imageObj.img_encode_type;
                    }
                }
                this.mainVideoSrc = url;
            } else if (this.checkResourceType(imageObj) === "review_video") {
                // this.mainVideoSrc=imageObj.mp4FileUrl||imageObj.ultrasound_url
                this.mainVideoSrc = imageObj.ultrasound_url;
                this.startVideoCommit(imageObj);
                console.log("*** mainVideoSrc: ", imageObj.ultrasound_url);
                console.log("*** gestrueVideoSrc: ", imageObj.gesture_url);
            }
            if (imageObj.img_has_gesture_video) {
                this.gestrueVideoSrc = imageObj.gesture_url;
            } else {
                this.gestrueVideoSrc = "";
            }
            this.mainAudioSrc = imageObj.voice_url || "";
            if (Tool.checkAppClient("App")) {
                var json = {
                    conversation_id: imageObj.cid || 0,
                    consultation_id: 0,
                    img_id: imageObj.img_id,
                    type: imageObj.msg_type,
                    subject: imageObj.des,
                    spriteImageList: imageObj.spriteImageList || [],
                    resourceId: imageObj.resource_id,
                };

                if (this.mainVideoSrc) {
                    json.ultrasound_video = Tool.percentURLEncode(this.mainVideoSrc);
                }
                if (this.gestrueVideoSrc) {
                    json.gesture_video = Tool.percentURLEncode(this.gestrueVideoSrc);
                }
                if (this.mainAudioSrc) {
                    json.voice_url = Tool.percentURLEncode(this.mainAudioSrc);
                }
                if (this.checkResourceType(imageObj) === "review_video") {
                    json.type = this.systemConfig.msg_type.Cine;
                }
                json.video_with_audio = imageObj.video_with_audio ? 1 : 0;

                if (
                    imageObj.msg_type == this.systemConfig.msg_type.Video ||
                    imageObj.img_type_ex == this.systemConfig.msg_type.Video
                ) {
                    if (this.mainVideoSrc) {
                        if ("http" == imageObj.url.substr(0, 4)) {
                            json.ultrasound_video = Tool.percentURLEncode(this.mainVideoSrc);
                        } else {
                            let socketServer =
                                this.systemConfig.server_type.protocol +
                                this.systemConfig.server_type.host +
                                this.systemConfig.server_type.port;
                            json.ultrasound_video = socketServer + "/" + Tool.percentURLEncode(this.mainVideoSrc);
                        }
                        json.video_with_audio = 1;
                    }
                }
                if (
                    imageObj.msg_type == this.systemConfig.msg_type.Cine ||
                    imageObj.img_type_ex == this.systemConfig.msg_type.Cine
                ) {
                    if (this.mainVideoSrc && imageObj.imported_from) {
                        json.video_with_audio = 1;
                        imageObj.from_repository = true;
                    }
                }

                json.from_repository = imageObj.from_repository;
                if (window.mui.os.ios) {
                    window.CWorkstationCommunicationMng.enterGalleryMode();
                }
                if (this.isInternalNetworkEnv) {
                    json.ultrasound_video = Tool.replaceInternalNetworkEnvImageHost(json.ultrasound_video);
                    json.gesture_video = Tool.replaceInternalNetworkEnvImageHost(json.gesture_video);
                    json.voice_url = Tool.replaceInternalNetworkEnvImageHost(json.voice_url);
                }
                window.CWorkstationCommunicationMng.switchVideo(json);
                if (imageObj.resource_id) {
                    this.getSpriteImageListByResourceId(imageObj.resource_id)
                        .then((res) => {
                            window.CWorkstationCommunicationMng.updateSpriteImageListByResourceId({
                                spriteImageList: res.data,
                                resourceId: imageObj.resource_id,
                            });
                        })
                        .catch((error) => {
                            console.error(error);
                        });
                }
            } else {
                this.isShowViode = true;
                this.$nextTick(function () {
                    that.$refs.mainVideo.play();
                    that.$refs.gestrueVideo.play();
                    that.$refs.mainAudio.play();
                });
                console.log("play2");
            }
        },
        closeVideo() {
            this.$refs.mainVideo.pause();
            this.$refs.gestrueVideo.pause();
            this.$refs.mainAudio.pause();
            this.isShowViode = false;
            // this.back();
        },
        timeupdate(e) {
            console.log("timeupdate", e);
        },
        deleteTag(tagIndex) {
            if (!Tool.checkSpeakPermission(this.galleryList[this.index].group_id, this.user.uid)) {
                Toast(this.lang.app_no_speak_permission);
                return;
            }
            //todo
            let tag = this.tagList[tagIndex];
            if (!tag.selfCheck) {
                Toast(this.lang.cannot_delete_tag);
                return;
            }
            for (let item of this.commentItem.tags_list) {
                if (item.tags == tag.tags && item.sender_id == this.user.id) {
                    //选中原标签数组中自己发的那条
                    tag = item;
                    break;
                }
            }
            var that = this;
            this.addingTags = true;
            let resource_id = this.galleryList[this.index].resource_id;
            let img_id = this.galleryList[this.index].img_id;
            let cid = this.galleryList[this.index].group_id;
            var controller = this.conversationList[cid].socket;
            var sendDate = {
                resource_id: resource_id,
                img_id: img_id,
                tag: tag.tags,
            };
            controller.emit("del_tags", sendDate, function (is_succ, data) {
                if (is_succ) {
                    that.$store.commit("gallery/deleteTagList", data);
                    that.$store.commit("gallery/reduceTagCount", data);
                } else {
                    Toast("del_tags error");
                }
                that.addingTags = false;
            });
        },
        addUserTag(tagIndex) {
            if (!Tool.checkSpeakPermission(this.galleryList[this.index].group_id, this.user.uid)) {
                Toast(this.lang.app_no_speak_permission);
                return;
            }
            let item = this.allTags[tagIndex];
            this.addTag(item);
        },
        addTag(item) {
            if (item.checked) {
                return;
            } else {
                //todo
                //tag是tag_list中的数据结构
                var that = this;
                this.addingTags = true;
                let resource_id = this.galleryList[this.index].resource_id;
                let img_id = this.galleryList[this.index].img_id;
                let cid = this.galleryList[this.index].group_id;
                var controller = this.conversationList[cid].socket;
                var sendDate = {
                    resource_id: resource_id,
                    img_id: img_id,
                    tag: item.caption,
                    is_private: this.is_private,
                };
                controller.emit("add_tags", sendDate, function (is_succ, data) {
                    if (is_succ) {
                        that.$store.commit("gallery/addTagList", data);
                        that.$store.commit("gallery/addTagCount", data);
                    } else {
                        Toast("add_tags error");
                    }
                    that.addingTags = false;
                });
            }
        },
        changeText() {
            this.commentText = this.$refs.comment_text.innerHTML;
        },
        sendComment() {
            let str = this.commentText;
            str = str.replace(/<div>/g, "\n");
            str = str.replace(/<br>/g, "\n");
            str = str.replace(/<\/div>/g, "");
            str = str.replace(/&nbsp;/g, " ");
            str = str.trim();
            if (str.length == 0) {
                Toast(this.lang.comment_text_null);
                return;
            } else {
                var that = this;
                let resource_id = this.galleryList[this.index].resource_id;
                let img_id = this.galleryList[this.index].img_id;
                let cid = this.galleryList[this.index].group_id;
                var controller = this.conversationList[cid].socket;
                let sendDate = {
                    resource_id: resource_id,
                    img_id: img_id,
                    parent_id: 0,
                    content: str,
                    is_private: this.is_private ? 1 : 0,
                };
                controller.emit("add_comment", sendDate, function (is_succ, data) {
                    if (is_succ) {
                        // that.$store.commit('gallery/addCommentToList',data);
                        that.commentList.push(data);
                        that.closeComment();
                        setTimeout(() => {
                            //延时等待新加评论渲染到页面
                            let index = that.commentList.length - 1;
                            let dom = that.$refs["comment_" + index][0];
                            dom.scrollIntoViewIfNeeded(false);
                        }, 0);
                    } else {
                        Toast("add_comment error");
                    }
                    // that.addingTags=false
                });
                this.commentText = "";
                this.$refs.comment_text.innerHTML = "";
            }
        },

        closeComment() {
            this.commentText = "";
            this.$refs.comment_text.blur();
            this.$refs.comment_text.innerHTML = "";
            this.showCommentModal = false;
            window.getSelection().removeAllRanges();
        },
        async transfer() {
            if (!Tool.checkSpeakPermission(this.galleryList[this.index].group_id, this.user.uid)) {
                Toast(this.lang.app_no_speak_permission);
                return;
            }
            await this.closeMaxScreen();
            let path = this.$route.path;
            path += "/transmit";
            this.$root.transmitTempList = [];
            this.$root.transmitTempList.push(Object.assign({}, this.galleryList[this.index]));
            this.$router.push(path);
        },
        analyze() {
            if (!Tool.checkSpeakPermission(this.galleryList[this.index].group_id, this.user.uid)) {
                Toast(this.lang.app_no_speak_permission);
                return;
            }
            this.$root.transmitTempList = [];
            this.$root.transmitTempList.push(Object.assign({}, this.galleryList[this.index]));
            sendToAnalyze();
        },
        async searchInCaseData() {
            if (!Tool.checkSpeakPermission(this.galleryList[this.index].group_id, this.user.uid)) {
                Toast(this.lang.app_no_speak_permission);
                return;
            }
            let list = [this.galleryList[this.index]];
            if (list && list.length > 1) {
                Toast(this.lang.searc_in_case_database_many_picture);
            } else if (list && list.length == 1) {
                let msg = list[0];
                let islegal = Tool.isLegalForematForSearchImage(msg);
                if (islegal) {
                    let pattern = new RegExp("^data:image.*", "i");
                    msg.realUrl = msg.realUrl && pattern.test(msg.realUrl) ? "" : msg.realUrl;
                    let searchParams = {
                        type: "url", //text,url,file
                        content: msg,
                    };
                    this.$store.commit("caseDatabase/updateSearchParams", searchParams);
                    await this.closeMaxScreen();
                    this.$nextTick(() => {
                        this.$router.push("/index/case_database");
                    });
                } else {
                    Toast(this.lang.picture_is_only_jpg_jpeg_bmp_png);
                }
            } else {
                Toast(this.lang.searc_in_case_database_one_picture);
            }
        },
        download() {
            if (!Tool.checkSpeakPermission(this.galleryList[this.index].group_id, this.user.uid)) {
                Toast(this.lang.app_no_speak_permission);
                return;
            }

            //下载到相册
            if (!this.isApp) {
                Toast(this.lang.use_app_tip);
                return;
            }
            let that = this;
            let img = this.galleryList[this.index];
            let surl =
                this.systemConfig.server_type.protocol +
                this.systemConfig.server_type.host +
                this.systemConfig.server_type.port +
                "/";
            let savePath = "";
            if (img.isExamFile || img.isTransferFile || img.isLocalPatientFile) {
                //一键转发和检查浏览的本地图片
                if (img.msg_type == this.systemConfig.msg_type.Frame) {
                    if (img.img_encode_type.toUpperCase() === "PDF") {
                        savePath = img.url.replace("thumbnail.jpg", "SingleFrame.pdf");
                    } else {
                        savePath = img.realUrl;
                    }
                } else if (img.msg_type == this.systemConfig.msg_type.Cine) {
                    savePath = img.url2;
                }
                console.log("savePath", savePath);
                if (!savePath) {
                    Toast(that.lang.save_null_fail_tip);
                    return;
                }
                //由于IOS的路径不能被IO访问，但可以执行save操作，故先save后moveTo重命名，再save一次为了刷新相册
                if (true) {
                    try {
                        Tool.createCWorkstationCommunicationMng({
                            name: "gallerySave",
                            emitName: "NotifyGallerySave",
                            params: { url: savePath },
                            timeout: null,
                        }).then((galleryRes) => {
                            if (galleryRes.error_code == "0") {
                                console.log("save success callback", galleryRes);
                                var pathArr = galleryRes.url.split("/");
                                var fileName = pathArr.pop();
                                var timestamp_f = new Date().getTime();
                                var fileName_new = timestamp_f + "_" + fileName;
                                var sd_path_cell = "_documents/Ruitong/" + that.systemConfig.server_type.host + "/";
                                var copyPath = "";
                                try {
                                    Tool.createCWorkstationCommunicationMng({
                                        name: "convertLocalFileSystemURL",
                                        emitName: "NotifyConvertedLocalFileSystemURL",
                                        params: { url: sd_path_cell },
                                        timeout: 1500,
                                    }).then((res) => {
                                        copyPath = res.url;
                                        try {
                                            Tool.createCWorkstationCommunicationMng({
                                                name: "resolveLocalFileSystemURL",
                                                emitName: "NotifyResolvedLocalFileSystemURL",
                                                params: {
                                                    action: "moveTo",
                                                    local_url: galleryRes.file,
                                                    remote_url: copyPath + "/" + fileName_new,
                                                },
                                                timeout: 2500,
                                            }).then((res) => {
                                                if (res.error_code == "0") {
                                                    let saveTempPath = "file://" + copyPath + fileName_new;
                                                    Tool.createCWorkstationCommunicationMng({
                                                        name: "gallerySave",
                                                        emitName: "NotifyGallerySave",
                                                        params: { url: saveTempPath },
                                                        timeout: null,
                                                    }).then((galleryRes) => {
                                                        if (galleryRes.error_code == "0") {
                                                            console.log("save success callback", galleryRes);
                                                            Toast(that.lang.has_download_tip);
                                                            try {
                                                                Tool.createCWorkstationCommunicationMng({
                                                                    name: "resolveLocalFileSystemURL",
                                                                    emitName: "NotifyResolvedLocalFileSystemURL",
                                                                    params: {
                                                                        action: "remove",
                                                                        local_url: copyPath + "/" + fileName_new,
                                                                    },
                                                                    timeout: 1500,
                                                                }).then((res) => {});
                                                            } catch (error) {
                                                                Toast(error);
                                                            }
                                                        } else {
                                                            Toast(galleryRes.error_message);
                                                        }
                                                    });
                                                } else {
                                                    Toast(res.error_message);
                                                }
                                            });
                                        } catch (error) {
                                            Toast(error);
                                        }
                                    });
                                } catch (error) {
                                    Toast(error);
                                }
                            } else {
                                console.log("gallerySave error", galleryRes.error_message);
                                // if (e.code==-3310||e.code==8||e.code==-100) {
                                //     Toast(that.lang.access_denied_tip);
                                // } else{
                                //     Toast(that.lang.save_support_fail_tip);
                                // }
                                Toast(galleryRes.error_message);
                            }
                        });
                    } catch (error) {
                        Toast(error);
                    }
                }
                return;
            }
            if (
                img.msg_type == this.systemConfig.msg_type.Image ||
                img.img_type_ex == this.systemConfig.msg_type.Image
            ) {
                savePath = img.url.replace(img.thumb, "");
            } else if (
                img.msg_type == this.systemConfig.msg_type.Frame ||
                img.img_type_ex == this.systemConfig.msg_type.Frame
            ) {
                if (img.img_encode_type.toUpperCase() === "PDF") {
                    savePath = img.url.replace("thumbnail.jpg", "SingleFrame.pdf");
                } else {
                    savePath = img.url.replace("thumbnail.jpg", "SingleFrame.jpg");
                }
            } else if (
                img.msg_type == this.systemConfig.msg_type.OBAI ||
                img.img_type_ex == this.systemConfig.msg_type.OBAI
            ) {
                savePath = img.url.replace("/thumbnail.jpg", `.ai`);
            } else if (
                img.msg_type == this.systemConfig.msg_type.Video ||
                img.img_type_ex == this.systemConfig.msg_type.Video
            ) {
                savePath = img.url.replace(img.thumb, "");
            } else if (
                img.msg_type == this.systemConfig.msg_type.Cine ||
                img.img_type_ex == this.systemConfig.msg_type.Cine
            ) {
                // let url=''
                console.log(this.lang.save_null_fail_tip, img);
                if (img.mainVideoSrc) {
                    savePath = img.savePath || img.mainVideoSrc;
                } else {
                    savePath = img.url.replace("thumbnail.jpg", "DeviceVideo.");
                    savePath += img.img_encode_type;
                }
            } else if (this.checkResourceType(img) === "review_video") {
                Toast(that.lang.save_not_support_tip);
                return;
            } else if (img.msg_type == this.systemConfig.msg_type.File) {
                Toast(that.lang.save_not_support_tip);
                return;
            }
            if (!savePath) {
                Toast(that.lang.save_null_fail_tip);
                return;
            }
            if (0 > savePath.indexOf("http")) {
                savePath = surl + savePath;
            }
            let downloadTimer = setTimeout(() => {
                Toast(that.lang.gallery_dowloading_tip);
            }, 500);
            console.log("savePath", savePath);
            var pathArr = savePath.split("/");
            var fileName = pathArr.pop();
            var timestamp_f = new Date().getTime();
            var fileName_new = timestamp_f + "_" + fileName;
            //创建下载任务到临时目录
            var sd_path_cell = "_documents/Ruitong/" + this.systemConfig.server_type.host + "/tmp/" + fileName_new;
            var sd_path = "";
            if (true) {
                try {
                    Tool.createCWorkstationCommunicationMng({
                        name: "convertLocalFileSystemURL",
                        emitName: "NotifyConvertedLocalFileSystemURL",
                        params: { url: sd_path_cell },
                        timeout: 1500,
                    }).then((res) => {
                        sd_path = res.url;

                        // 创建下载任务
                        Tool.createCWorkstationCommunicationMng({
                            name: "createDownload",
                            emitName: "NotifyCreateDownload",
                            params: {
                                url: savePath,
                                options: {
                                    filename: sd_path,
                                    timeout: "5",
                                    retry: "1",
                                    retryInterval: "1",
                                },
                            },
                            timeout: null,
                        }).then((downloadRes) => {
                            // 下载完成
                            // plus.nativeUI.closeWaiting();
                            clearTimeout(downloadTimer);
                            if (downloadRes.error_code == "0") {
                                Tool.createCWorkstationCommunicationMng({
                                    name: "gallerySave",
                                    emitName: "NotifyGallerySave",
                                    params: { url: downloadRes.url },
                                    timeout: null,
                                }).then((galleryRes) => {
                                    if (galleryRes.error_code == "0") {
                                        console.log("save success callback", galleryRes);
                                        Toast(that.lang.has_download_tip);
                                        console.log("save filename", downloadRes.url);
                                        try {
                                            Tool.createCWorkstationCommunicationMng({
                                                name: "resolveLocalFileSystemURL",
                                                emitName: "NotifyResolvedLocalFileSystemURL",
                                                params: { action: "remove", local_url: sd_path },
                                                timeout: 1500,
                                            }).then((res) => {});
                                        } catch (error) {
                                            Toast(error);
                                        }
                                    } else {
                                        console.log("save network error", galleryRes);
                                        // if (e.code==-3310||e.code==8||e.code==-100) {
                                        //     Toast(that.lang.access_denied_tip);
                                        // } else{
                                        //     Toast(that.lang.save_support_fail_tip);
                                        // }
                                        Toast(that.lang.save_support_fail_tip);
                                        try {
                                            Tool.createCWorkstationCommunicationMng({
                                                name: "resolveLocalFileSystemURL",
                                                emitName: "NotifyResolvedLocalFileSystemURL",
                                                params: { action: "remove", local_url: sd_path },
                                                timeout: 1500,
                                            }).then((res) => {});
                                        } catch (error) {
                                            Toast(error);
                                        }
                                    }
                                });
                            } else {
                                Toast("Download failed: " + status);
                            }
                        });
                    });
                } catch (error) {
                    Toast(error);
                }
            }
        },
        toggleCommentMode() {
            //改版评论区
            let that = this;
            this.isShowMoreBtn = false;
            this.preventTapTimeout = setTimeout(function () {
                console.log("toggleCommentMode");
                if (!that.isShowModal) {
                    that.initComment();
                } else {
                    that.hideComment();
                }
                // this.isShowModal=!this.isShowModal;
            }, 200);
        },
        preventTap() {
            clearTimeout(this.preventTapTimeout);
        },
        openEditModal() {
            const group_id = this.galleryList[this.index].group_id;
            if (!Tool.checkSpeakPermission(group_id, this.user.uid)) {
                Toast(this.lang.app_no_speak_permission);
                return;
            }
            var that = this;
            this.showCommentModal = true;
            this.$nextTick(() => {
                that.$refs.comment_text.focus();
                that.$refs.comment_text.scrollIntoViewIfNeeded();
            });
        },
        openAddCustomTag() {
            if (!Tool.checkSpeakPermission(this.galleryList[this.index].group_id, this.user.uid)) {
                Toast(this.lang.app_no_speak_permission);
                return;
            }
            this.$router.push(this.$route.path + "/add_custom_tag");
        },
        openCommentModal(data) {
            this.initComment();
            if (data.type == 1) {
                this.itemActive = "comment";
                let index = this.findCommentIndex(data.message.comment_id);
                setTimeout(() => {
                    //延时等待评论页准备完毕
                    if (index > 0) {
                        let dom = this.$refs["comment_" + index][0];
                        dom.scrollIntoViewIfNeeded(false);
                    }
                }, 0);
            }
            if (data.type == 2) {
                this.itemActive = "tags";
            }
        },
        findCommentIndex(id) {
            for (let i = 0; i < this.commentList.length; i++) {
                if (id == this.commentList[i].comment_id) {
                    return i;
                }
            }
        },
        handleSaveFavoriteAction() {
            let userFavoriteStatus = false;
            let currentFile = this.galleryList[this.index];
            if (this.checkFavoriteStatus()) {
                this.handleCancelFavoriteAction(currentFile);
                return;
            }
            window.vm.$root.eventBus.$emit("initFavoriteConfirm", [currentFile]);
        },
        handleCancelFavoriteAction(currentFile) {
            cancelFavoriteCommit([
                {
                    resource_id: currentFile.resource_id,
                    cid: currentFile.group_id,
                },
            ]);
        },
        // saveFavorite(){
        //     let img=this.galleryList[this.index];
        //     if (img.userFavoriteStatus) {
        //         //取消收藏

        //     }else{
        //         //添加收藏
        //         window.vm.$root.eventBus.$emit('initFavoriteConfirm',[img]);
        //     }

        // },
        shareToWechat() {
            if (!Tool.checkSpeakPermission(this.galleryList[this.index].group_id, this.user.uid)) {
                Toast(this.lang.app_no_speak_permission);
                return;
            }

            let img = this.galleryList[this.index];
            this.$root.eventBus.$emit("shareToWechatHandler", [img]);
        },
        handleOrientationChange(event) {
            if (!this.isShowGallery) {
                return;
            }
            console.log("handleOrientation", event.matches);
            var that = this;
            if (!event.matches) {
                //横屏
                console.log("isScreenHorizontal", true);
                that.isScreenHorizontal = true;
                setTimeout(() => {
                    //旋转屏幕需要改变isScreenHorizontal值
                    that.toggleComment(false);
                }, 450);
            } else {
                //竖屏
                console.log("isScreenHorizontal", false);
                that.isScreenHorizontal = false;
                that.toggleComment(true);
            }

            //resize时重置容器宽度，兼容旋转屏幕时定位异常
            var gallery = window.mui(".mui-slider").slider();
            var paddingLeft = parseFloat(window.mui.getStyles(gallery.wrapper, "padding-left")) || 0;
            var paddingRight = parseFloat(window.mui.getStyles(gallery.wrapper, "padding-right")) || 0;
            var clientWidth = gallery.wrapper.clientWidth;
            gallery.scrollerWidth = gallery.scroller.offsetWidth;
            gallery.wrapperWidth = clientWidth - paddingLeft - paddingRight;
            gallery.currentPage.x = gallery.x = -(gallery.wrapperWidth * gallery.slideNumber);
            gallery.gotoItem(this.index, 0);
        },
        toggleHorizontal(val) {
            console.log("toggleHorizontal", val);
            if (val) {
                this.isShowModal && this.toggleComment(false);
            } else {
                this.isShowModal && this.toggleComment(true);
            }

            window.CWorkstationCommunicationMng.changeOrientation();
        },
        toggleComment(isShow) {
            if (!this.noneComment && !this.isHorizontalMode) {
                this.isShowComment = isShow;
            } else {
                this.isShowComment = false;
            }
        },
        async toFileGroup() {
            const item = this.galleryList[this.index];
            var group_id = item.group_id;
            await this.closeMaxScreen();
            this.openConversation(group_id, 10, null, item);
        },
        showPdf(file) {
            let pdfFile = {};
            if (file.msg_type == this.systemConfig.msg_type.File) {
                pdfFile = file;
            } else if (
                file.msg_type == this.systemConfig.msg_type.Frame &&
                file.img_encode_type.toUpperCase() === "PDF"
            ) {
                let arr = file.url.split("/");
                arr.pop();
                pdfFile.file_type = "pdf";
                pdfFile.file_name = arr.pop();
                pdfFile.url = file.url.replace("thumbnail.jpg", "SingleFrame.pdf");
            } else {
                return;
            }
            this.isShowPdfPage = true;
            this.initPDF(pdfFile);
        },
        initPDF(pdfFile) {
            var that = this;
            if (window.mui.os.ios && /^11/.test(window.mui.os.version)) {
                that.unsupportedPdf = true;
                return;
            } else {
                that.loadingPdf = true;
                let file = pdfFile;
                window.pdfjsLib.GlobalWorkerOptions.workerSrc = "static/resource/pdf.worker.js";
                var loadingTask = window.pdfjsLib.getDocument(file.url);
                loadingTask.promise.then(
                    function (pdf) {
                        that.pageSum = pdf.numPages;
                        that.$nextTick(() => {
                            for (let i = 1; i <= that.pageSum; i++) {
                                that.loadingPdf = false;
                                pdf.getPage(i).then(function (page) {
                                    try {
                                        var scale = 1;
                                        var viewport = page.getViewport(scale);
                                        var canvas = document.getElementById("the_canvas_" + i);
                                        var context = canvas.getContext("2d");
                                        canvas.height = viewport.height;
                                        canvas.width = viewport.width;
                                        var renderContext = {
                                            canvasContext: context,
                                            viewport: viewport,
                                        };
                                        var renderTask = page.render(renderContext);
                                        renderTask.then(function () {
                                            console.log("Page rendered");
                                        });
                                    } catch (e) {
                                        console.log("render pdf but page change !");
                                    }
                                });
                            }
                        });
                    },
                    function (reason) {
                        Toast(that.lang.load_pdf_error + reason);
                        that.unsupportedPdf = true;
                        that.loadingPdf = false;
                    }
                );
            }
        },
        closePdf() {
            this.isShowPdfPage = false;
            this.pageSum = 0;
        },
        toggleShowOperator() {
            this.isShowOperator = !this.isShowOperator;
        },
        enlarge() {
            if (this.scale < 200) {
                this.scale += 20;
            }
        },
        narrow() {
            if (this.scale > 100) {
                this.scale -= 20;
            }
        },
        initDCMPlayer(e, index) {
            e.cancelBubble = true;
            // if (!this.isApp) {
            //     Toast(this.lang.use_app_tip);
            //     return ;
            // }
            let file = this.galleryList[index];
            if (!file.dcm_url) {
                //一键转发、检查浏览文件有dcm_url字段，会话内的文件需要拼接地址
                file.dcm_url = file.url.replace("thumbnail.jpg", `SingleFrame.${file.img_encode_type}`);
            }
            //初始化DCM文件播放器
            this.isShowDCMPlayer = true;
            this.$nextTick(() => {
                this.$refs.dcm_player.initDCMPlayer(file);
            });

            // window.CWorkstationCommunicationMng.DisplayDrImage({
            //     dcm_url:file.dcm_url,
            //     thumbnail:file.url_local,
            //     display:true
            // })
        },
        updateLoadedDcm(json) {
            let file = this.galleryList[this.index];
            for (let item of json.image_list) {
                if (item.img_id == file.img_id) {
                    if (file.img_encode_type.toUpperCase() == this.systemConfig.file_type.DCM) {
                        window.CWorkstationCommunicationMng.DisplayDrImage({
                            loaded: true,
                        });
                        return;
                    }
                }
            }
        },

        updateMarkListIfNeed() {
            //更新了描迹检查是否要重新画描迹
            let index = this.index;
            if (this.$refs[`realImage_${index}`] && this.$refs[`realImage_${index}`][0]) {
                this.$refs[`realImage_${index}`][0].updateMarkListIfNeed(true);
            }
        },
        errorDCM(img) {
            //dcm文件在一键转发打开大图时未生成大图，延时再加载一次
            var imageObj = Object.assign({}, img);
            if (
                imageObj.isTransferFile &&
                imageObj.msg_type == this.systemConfig.msg_type.Frame &&
                imageObj.img_encode_type.toUpperCase() == this.systemConfig.file_type.DCM
            ) {
                this.$store.commit("gallery/updateLocalDcm", {
                    imgObj: imageObj,
                    realUrl: imageObj.url_local,
                });
                setTimeout(() => {
                    this.$store.commit("gallery/updateLocalDcm", {
                        imgObj: imageObj,
                        realUrl: imageObj.tempRealUrl,
                    });
                }, 2000);
            }
        },
        openMoreBtn() {
            this.isShowMoreBtn = true;
        },
        closeMoreBtn() {
            this.isShowMoreBtn = false;
        },
        enterEditMode(e) {
            enterEditMode(e);
        },
        openLightnessAdjusting() {
            Tool.createCWorkstationCommunicationMng({
                name: "getBrightness",
                emitName: "NotifyGetBrightness",
                params: {},
                timeout: null,
            }).then((res) => {
                this.lightnessPercent = Math.round(Number(res.brightness) * 100) || 0;
                this.isShowLightness = true;
            });
        },
        adjustingLightness() {
            window.CWorkstationCommunicationMng.setBrightness({
                brightness: this.lightnessPercent + "",
            });
            console.log("adjustingLightness", this.lightnessPercent / 100);
        },
        closeLightness() {
            this.isShowLightness = false;
        },
        getSpriteImageListByResourceId(resource_id) {
            return new Promise((resolve, reject) => {
                service
                    .getSpriteImageListByResourceId({ resourceId: resource_id })
                    .then((res) => {
                        console.log(res, "getSpriteImageListByResourceId");
                        if (res.data.error_code === 0) {
                            resolve(res.data);
                        } else {
                            reject(res.data.error_msg);
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        startVideoCommit(file) {
            this.videoCommitStamp = Date.now();
            this.videoCommitFile = file;
            this.videoCommitInterval = setInterval(() => {
                this.commitVideoParams();
            }, 5000);
        },
        endVideoCommit() {
            this.commitVideoParams();
            this.videoCommitFile = null;
            this.videoCommitStamp = 0;
            clearTimeout(this.videoCommitInterval);
        },
        commitVideoParams() {
            if (!this.videoCommitFile || !window.main_screen) {
                return;
            }
            let cid = this.videoCommitFile.group_id;
            let data = {
                type: 5,
                cid: cid,
                business_data: {
                    resource_id: this.videoCommitFile.resource_id,
                    uuid: this.videoCommitStamp,
                    duration: Date.now() - this.videoCommitStamp,
                },
                showErrorToast: false,
            };

            window.main_screen.reportReviewEvent(data, (res) => {
                console.log("reportReviewEvent", data);
            });
        },
        positionIndexByFileId(fileId) {
            console.log("positionIndexByFileId", fileId);
            if (!fileId) {
                //未设置打开文件时不定位
                return;
            }
            for (let i = 0; i < this.galleryList.length; i++) {
                if (fileId == generateGalleryFileId(this.galleryList[i])) {
                    this.index = i;
                    break;
                }
            }
            if (this.index == -1) {
                //已有数组里找不到被打开文件，加载更多
                let cid = this.$store.state.gallery.cid;
                const conversation = this.conversationList[cid];
                const galleryObj = conversation.galleryObj;
                if (cid && conversation && galleryObj.index > 0) {
                    // this.loadMoreConversationGalleryData()
                }
            } else {
                this.$nextTick(() => {
                    //正在画廊中往画廊添加一张图片时，自动定位到index+1
                    var gallery = window.mui(".mui-slider");
                    var temp = gallery.slider();
                    if (temp) {
                        console.log("changeImage gotoItem");
                        temp.refresh();
                        temp.gotoItem(this.index, 0);
                    }
                });
            }
        },
        async handleLikeAction() {
            let isLiked = 0;
            let currentFile = this.galleryList[this.index];
            if (!currentFile) {
                return;
            }
            if (
                this.$store.state.resourceTempStatus[currentFile.resource_id] &&
                this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("isLiked")
            ) {
                isLiked = this.$store.state.resourceTempStatus[currentFile.resource_id].isLiked || 0;
            } else {
                isLiked = this.getUserLikeStatus || 0;
            }
            if (isLiked) {
                this.handleUnLikeAction(currentFile);
                return;
            }
            let likes = 0;
            if (
                this.$store.state.resourceTempStatus[currentFile.resource_id] &&
                this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("likes")
            ) {
                likes = this.$store.state.resourceTempStatus[currentFile.resource_id].likes || 0;
            } else {
                likes = this.getLikeCount || 0;
            }

            this.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                resource_id: currentFile.resource_id,
                data: {
                    isLiked: 1,
                    likes: likes + 1,
                },
            });
            const { data } = await service.likeResourceAction({ resourceID: currentFile.resource_id });
        },
        async handleUnLikeAction() {
            let currentFile = this.galleryList[this.index];
            if (!currentFile) {
                return;
            }
            let likes = 0;
            if (
                this.$store.state.resourceTempStatus[currentFile.resource_id] &&
                this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("likes")
            ) {
                likes = this.$store.state.resourceTempStatus[currentFile.resource_id].likes || 0;
            } else {
                likes = this.getLikeCount || 0;
            }
            this.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                resource_id: currentFile.resource_id,
                data: {
                    isLiked: 0,
                    likes: likes - 1,
                },
            });
            const { data } = await service.unLikeResourceAction({ resourceID: currentFile.resource_id });
        },
        checkLikeAction() {
            let isLiked = 0;
            let currentFile = this.galleryList[this.index];
            if (!currentFile) {
                return;
            }

            if (
                this.$store.state.resourceTempStatus[currentFile.resource_id] &&
                this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("isLiked")
            ) {
                isLiked = this.$store.state.resourceTempStatus[currentFile.resource_id].isLiked || 0;
            } else {
                isLiked = this.getUserLikeStatus || 0;
            }
            return isLiked;
        },
        checkFavoriteStatus() {
            let userFavoriteStatus = false;
            let currentFile = this.galleryList[this.index];
            if (!currentFile) {
                return;
            }

            if (
                this.$store.state.resourceTempStatus[currentFile.resource_id] &&
                this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("userFavoriteStatus")
            ) {
                userFavoriteStatus =
                    this.$store.state.resourceTempStatus[currentFile.resource_id].userFavoriteStatus || false;
            } else {
                userFavoriteStatus = this.getUserFavoriteStatus || false;
            }
            return userFavoriteStatus;
        },
        parseLikeText() {
            let currentFile = this.galleryList[this.index];
            if (!currentFile) {
                return 0;
            }
            let likes = 0;
            if (this.getLikeCount) {
                likes = this.getLikeCount || 0;
            } else if (
                this.$store.state.resourceTempStatus[currentFile.resource_id] &&
                this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("likes")
            ) {
                likes = this.$store.state.resourceTempStatus[currentFile.resource_id].likes || 0;
            }

            let isLiked = 0;
            if (
                this.$store.state.resourceTempStatus[currentFile.resource_id] &&
                this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("isLiked")
            ) {
                isLiked = this.$store.state.resourceTempStatus[currentFile.resource_id].isLiked || 0;
            } else {
                isLiked = this.getUserLikeStatus || 0;
            }
            if (!isLiked) {
                if (likes > 0) {
                    return likes;
                } else {
                    return `${this.lang.like_action}`;
                }
            } else {
                return likes;
            }
        },
        videoError(err) {
            this.closeVideo();
            Toast(this.lang.video_cannot_played);
        },
        enterGalleryRouter({ to, from, next }) {
            console.log({ to, from, next });
        },
        leaveGalleryRouter({ to, from, next }) {
            if (this.isScreenHorizontal) {
                window.CWorkstationCommunicationMng.changeOrientation();
                next(false);
            } else {
                if (to.path === "/index") {
                    this.$store.commit("gallery/clearGalleryCacheData");
                } else {
                    this.$store.commit("gallery/cacheGalleryData");
                }
                next(true);
            }
        },
        closeMaxScreen() {
            return new Promise((resolve) => {
                if (this.isScreenHorizontal) {
                    window.CWorkstationCommunicationMng.changeOrientation();
                    setTimeout(() => {
                        resolve(true);
                    }, 450);
                } else {
                    resolve(true);
                }
            });
        },
        nameDeal(report) {
            let views = null;
            if (report.type && report.type == this.aiPresetData.typeIndex.abdomen) {
                views = this.$store.state.aiPresetData?.iworksAbdomenTest.views || {};
            }
            if (report.type && report.type == this.aiPresetData.typeIndex.cardiac) {
                views = this.$store.state.aiPresetData?.cardiacViews.views || {};
            }
            const child_id = report.child_id && report.child_id >= 0 && views[report.child_id] ? report.child_id : -1;

            let key = "";
            if (views) {
                if (child_id > -1) {
                    key = views[child_id] ? views[child_id].key : "";
                } else {
                    key = views[report.id] ? views[report.id].key : "";
                }
            }
            if (key) {
                if (key.toLowerCase() == "undefined") {
                    key = "";
                }
            } else {
                if (views && report.clip_id >= 0) {
                    for (let k in views) {
                        let view = views[k];
                        if (view.key == report.clip_id || view.id == report.clip_id) {
                            key = view.key;
                            if (key) {
                                if (key.toLowerCase == "undefined") {
                                    key = "";
                                }
                            }
                        }
                    }
                }
            }
            if (key) {
                return this.lang[key];
            } else {
                return "--";
            }
        },
        qualityDeal(report) {
            let views = null;
            if (report.type && report.type == this.aiPresetData.typeIndex.abdomen) {
                views = this.$store.state.aiPresetData?.iworksAbdomenTest.views || {};
            }
            if (report.type && report.type == this.aiPresetData.typeIndex.cardiac) {
                views = this.$store.state.aiPresetData?.cardiacViews.views || {};
            }
            let quality = report.quality;
            if (quality == undefined || quality == null) {
                if (views && report.clip_id) {
                    for (let k in views) {
                        let view = views[k];
                        if (view.key == report.clip_id) {
                            quality = 1;
                            if (report.score > view.ai_height.lowest) {
                                quality = 0;
                            }
                            if (report.score < view.ai_lower.highest) {
                                quality = 2;
                            }
                        }
                    }
                }
            }
            if (quality == 2) {
                return this.lang.non_standard;
            } else if (quality == 1) {
                return this.lang.basic_standard;
            } else if (quality == 0) {
                return this.lang.standard;
            }

            return "--";
        },
        getArearcolor(structure) {
            let n = 0;
            let colorStruc = [];
            for (let k in structure) {
                let v = structure[k];
                if (v.isExist) {
                    v.color = this.colors[n];
                    colorStruc.push(v);
                } else {
                    colorStruc.push(v);
                }
                n++;
            }
            return colorStruc;
        },
        serarchInCaseDatabaseWithBackground(currentFile) {
            console.log("serarchInCaseDatabaseWithBackground");
            let realUrl = currentFile.realUrl || getRealUrl(currentFile);
            if (typeof realUrl == "object") {
                realUrl = realUrl.serverRealUrl;
            }
            let islegal = Tool.isLegalForematForSearchImage({ ...currentFile, realUrl });
            if (
                islegal &&
                realUrl &&
                this.currentFile.resource_id &&
                this.currentFile.exam_id &&
                this.currentFile.exam_type &&
                this.currentFile.exam_type == 8
            ) {
                let condition = {
                    pageNo: 1,
                    pageSize: 1,
                    type: "BREASTSEARCH",
                    sender_id: this.user.id,
                    group_id: this.currentFile.group_id,
                    resource_id: this.currentFile.resource_id,
                    url: realUrl,
                    is_detect: true,
                };
                let ai_searcher_server = this.$store.state.systemConfig.serverInfo.ai_searcher_server;
                let ajaxServer =
                    ai_searcher_server.protocol +
                    ai_searcher_server.addr +
                    ":" +
                    ai_searcher_server.port +
                    "/" +
                    trim(trim(ai_searcher_server.api, "/"), "\\");
                let url = trim(trim(ajaxServer, "/"), "\\") + "/" + "find_by_image";
                service.requestAiAnalyzeWithUrl({ method: "find_by_image", condition }).then(async (res) => {
                    let data = res.data.data;
                    console.log("res:", res);
                    if (data.resource_id) {
                        let value = {
                            keys: { showAISearchSuggest: false },
                            resource_id: data.resource_id,
                        };
                        if (data.list && data.list.length > 0) {
                            if (data.resource_id == currentFile.resource_id) {
                                this.showAISearchSuggest = true;
                            }
                            value.keys.showAISearchSuggest = true;
                        } else {
                            if (data.resource_id == currentFile.resource_id) {
                                this.showAISearchSuggest = false;
                            }
                            value.keys.showAISearchSuggest = false;
                        }
                        this.$store.commit("gallery/updateCommentObjByKey", value);
                    }
                });
            }
        },
        clearResourceDetail() {
            this.commentList = [];
            this.getUserFavoriteStatus = false;
            this.getLikeCount = 0;
            this.getUserLikeStatus = false;
        },
        async getResourceDetail() {
            if (!this.currentFile.resource_id || !this.currentFile.url) {
                return;
            }
            const res = await service.getResourceDetail({ resourceID: this.currentFile.resource_id });
            if (res.data.error_code === 0) {
                this.$set(this, "commentList", res.data.data.commentList);
                this.$store.commit("gallery/setTagList", {
                    resource_id: this.currentFile.resource_id,
                    tagList: res.data.data.tags,
                });
                this.getUserFavoriteStatus = res.data.data.userFavorite;
                this.getLikeCount = res.data.data.likeCount;
                this.getUserLikeStatus = res.data.data.userLikeStatus;
            }
        },
        // copyImage() {
        //     if (this.currentFile && this.currentFile.url) {
        //         // 检查是否能获取到当前显示的图片元素
        //         try {
        //             // 尝试直接从已显示的图片获取数据
        //             const imageIndex = this.index;
        //             const imageComponent = this.$refs[`realImage_${imageIndex}`];

        //             if (imageComponent && imageComponent[0]) {
        //                 // 获取组件内部的img元素
        //                 const imgElement = imageComponent[0].$el.querySelector('img');

        //                 if (imgElement && imgElement.complete) {
        //                     // 如果图片已加载完成，直接使用
        //                     Tool.copyToClipboard(imgElement)
        //                         .then(success => {
        //                             if (success) {
        //                                 this.$toast(this.lang.copy_text_success);
        //                             } else {
        //                                 this.$toast(this.lang.copy_text_fail);
        //                             }
        //                         })
        //                         .catch(err => {
        //                             console.error('复制出错:', err);
        //                             this.$toast(this.lang.copy_text_fail);
        //                         });
        //                     return;
        //                 }
        //             }

        //             // 如果无法直接获取已加载的图片元素，显示提示信息
        //             this.$toast(this.lang.browser_not_support_clipboard || '当前无法复制图片，请尝试下载后使用');
        //         } catch (err) {
        //             console.error('复制图片失败:', err);
        //             this.$toast(this.lang.copy_text_fail);
        //         }
        //     }
        // },

        handleQRCode() {
            if (this.hasQRCode && this.qrCodeData) {
                // 解析二维码数据
                let data = {};
                if (this.qrCodeData.indexOf("?") > -1) {
                    let str = this.qrCodeData.substr(this.qrCodeData.indexOf("?") + 1);
                    let strs = str.split("&");
                    for (let i = 0; i < strs.length; i++) {
                        if (strs[i].split("=").length > 1) {
                            data[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
                        }
                    }
                }

                // 添加act参数
                data.act = this.qrCodeData.indexOf("act=") > -1 ? this.qrCodeData.split("act=")[1].split("&")[0] : "";

                // 处理webLive类型的二维码
                if (this.qrCodeData.indexOf("webLive") > 0) {
                    let resStr = this.qrCodeData.split("webLive/")[1];
                    if (resStr) {
                        let decodedStr = window.atob(resStr);
                        let arr = decodedStr.split("#####");
                        let live_id = 0;
                        let group_id = 0;
                        arr.forEach((item) => {
                            if (item.includes("live_id")) {
                                live_id = Number(item.split("=")[1]);
                            }
                            if (item.includes("group_id")) {
                                group_id = Number(item.split("=")[1]);
                            }
                        });

                        data.live_id = live_id;
                        data.group_id = group_id;
                    }
                }
                this.$root.eventBus.$emit("executeByAction", data);
            } else {
                this.$toast(this.lang.unrecognized_qrcode);
            }
        },
        // 初始化二维码识别Worker
        initQRCodeWorker() {
            if (window.Worker) {
                try {
                    const server_type = this.$store.state.systemConfig.server_type;
                    let host = server_type.protocol + server_type.host + server_type.port;
                    let workerPath = "./static/resource/qrCodeWorker.js";

                    if (host.indexOf("localhost") > -1 || host.indexOf("192.168") > -1) {
                        host = `https://${Tool.getHostConfig().dev}`;
                    }
                    // 创建Worker实例
                    this.qrCodeWorker = new Worker(workerPath);
                    this.qrCodeWorker.postMessage({
                        type: "init",
                        host: host,
                    });

                    this.qrCodeWorker.onmessage = (event) => {
                        const data = event.data;

                        if (data.type === "ready") {
                            console.warn("QR Code Worker !!!REPORTING FOR DUTY!!!");
                        } else if (data.type === "result") {
                            const hasQRCode = data.hasQRCode;
                            const qrCodeData = data.qrCodeData;

                            // 更新状态
                            this.hasQRCode = hasQRCode;
                            this.qrCodeData = qrCodeData;
                            console.warn("二维码识别结果:", hasQRCode ? "找到二维码" : "未找到二维码", qrCodeData);
                        } else if (data.type === "error") {
                            // 处理错误
                            console.error("QR Code Worker错误:", data.error);
                            this.hasQRCode = false;
                            this.qrCodeData = null;
                        }
                    };

                    // 监听Worker错误
                    this.qrCodeWorker.onerror = (error) => {
                        console.error("QR Code Worker错误:", error);
                        this.hasQRCode = false;
                        this.qrCodeData = null;
                    };
                } catch (error) {
                    console.error("初始化QR Code Worker失败:", error);
                    this.qrCodeWorker = null;
                }
            } else {
                console.log("当前浏览器不支持Web Worker");
            }
        },

        // 检测二维码
        detectQRCode(file) {
            //切换图片时，重置二维码状态（已在changeHandler中重置状态，此处不再需要重置）
            // this.hasQRCode = false;
            // this.qrCodeData = null;
            if (this.checkResourceType(file) !== "image" || !file.url) {
                return;
            }

            const img = new Image();
            img.crossOrigin = "Anonymous";

            const that = this;
            img.onload = () => {
                const canvas = document.createElement("canvas");
                const ctx = canvas.getContext("2d");

                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);

                try {
                    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

                    if (that.qrCodeWorker) {
                        that.qrCodeWorker.postMessage({
                            imageData: imageData,
                            id: Date.now(),
                        });
                    }
                } catch (error) {
                    console.error("检测二维码错误:", error);
                    that.hasQRCode = false;
                    that.qrCodeData = null;
                }
            };

            img.onerror = () => {
                that.hasQRCode = false;
                that.qrCodeData = null;
            };
            let realUrlObj = this.getRealUrl(file);
            img.src = realUrlObj.localRealUrl || realUrlObj.serverRealUrl;
        },
    },
};
</script>
<style lang="scss">
.gallery_page {
    color: #fff;
    background-color: #222;
    z-index: 900;
    bottom: 0;
    display: flex;
    flex-direction: column;
    .gallery {
        background-color: #222;
        position: relative;
        width: 100%;
        z-index: 1;
        flex: 3;
        min-height: 1px;
        .index_tip {
            position: absolute;
            right: 0.2rem;
            z-index: 2;
        }
        .close_gallery {
            position: absolute;
            top: 0rem;
            left: 0rem;
            font-size: 1.3rem;
            z-index: 2;
            width: 2rem;
            text-align: left;
            height: 3rem;
            .svg_icon_back {
                font-size: 1rem;
                width: 50%;
                height: 0.9rem;
                fill: #fff;
                position: absolute;
                top: 1rem;
                left: 0.8rem;
            }
        }
        .video_thumb {
            max-width: 100%;
        }
        .review_default_img {
            width: 100%;
        }
        @media only screen and (min-width: 480px) {
            .review_default_img {
                width: 60%;
            }
        }
        .play_video_btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .ai_search_suggest {
            // width: 40px;
            // height: 40px;
            // border: 3px solid #00ffae;
            top: 1rem;
            position: absolute;
            right: 0.8rem;
            color: #00ffae;
            text-align: center;
            display: inline;
            line-height: 1.5rem;
            border-radius: 120px;
            font-weight: 500;
            font-size: 0.75rem;
            // text-shadow: 1px 1px 11px #fefffe;
            // box-shadow: 1px 5px 20px #888888;
            z-index: 10;
        }
        .mui-slider {
            position: absolute;
            height: 100%;
            .mui-slider-group {
                height: 100%;
                .mui-zoom-scroller {
                    height: 100%;
                }
                .file {
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    margin: auto;
                    max-width: 100%;
                    max-height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    font-size: 1.2rem;
                    .review_text {
                        color: yellow;
                    }
                    .icon-videofill {
                        font-size: 3rem;
                        color: #00c59d;
                    }
                }
                &.img_comment_mode {
                    //                         .file{
                    //                             bottom:14.5rem;
                    //                             top:auto;
                    //                             transform: translateY(calc(-10vh + 10%));
                    //                         }
                }
                .van-loading {
                    position: absolute;
                    top: calc(50% - 1rem);
                    left: calc(50% - 1rem);
                    z-index: 99;
                }
            }
        }

        .btns {
            bottom: 0rem;
            right: 0rem;
            width: 100%;
            position: absolute;
            z-index: 4;
            overflow: auto;
            &.is_horizontal {
                .download_btn,
                .transfer_btn {
                    width: 1.2rem;
                    height: 1.2rem;
                    line-height: 1.2rem;
                    margin-bottom: 0.3rem;
                    margin-right: 0.3rem;
                    i {
                        font-size: 0.8rem;
                    }
                }
            }
            .download_btn,
            .transfer_btn {
                width: 1.6rem;
                height: 1.6rem;
                border-radius: 50%;
                background-color: rgba(0, 0, 0, 0.6);
                text-align: center;
                line-height: 1.6rem;
                margin-bottom: 0.6rem;
                margin-right: 0.4rem;
                float: right;
                i {
                    font-size: 1rem;
                }
                .active {
                    color: #00c49e;
                }
                .like {
                    color: #00c49e;
                }
            }
        }
    }
    .comment {
        background-color: rgba(17, 17, 17, 0.7);
        color: #fff;
        width: 100%;
        flex: 2;
        display: flex;
        flex-direction: column;
        overflow: auto;
        .comment_title {
            color: #fff;
            border-bottom: 1px solid #999;
            .comment_navbar {
                display: flex;
                font-size: 0.8rem;
                align-items: center;
                min-height: 1.6rem;
                .navbar_item {
                    flex: 1;
                    text-align: center;
                    word-break: keep-all;
                    &.active {
                        color: #00c59d;
                    }
                }
            }
        }
        .comment_container {
            flex: 1;
            overflow: auto;
            .comment_container_item {
                height: 100%;
                display: flex;
                flex-direction: column;
                .comment_list {
                    flex: 1;
                    overflow: auto;
                    .comment_list_item {
                        margin: 0.5rem;
                        border-bottom: 1px solid #aaa;
                        padding-bottom: 0.5rem;
                        .comment_author {
                            float: right;
                            font-size: 1rem;
                            line-height: 1;
                            margin-left: 0.6rem;
                        }
                        .comment_time {
                            float: right;
                            font-size: 0.8rem;
                            line-height: 1;
                            padding-top: 0.2rem;
                        }
                        .comment_private_text {
                            float: right;
                            font-size: 0.5rem;
                            margin-right: 5px;
                            padding-top: 4px;
                        }
                        pre {
                            word-wrap: break-word;
                            white-space: break-spaces;
                        }
                    }
                }
                .edit_comment_btn {
                    font-size: 0.7rem;
                    padding: 0.2rem 0.3rem;
                    background-color: #01bd97;
                    line-height: 1rem;
                    margin: 0.4rem 0.6rem;
                    border-radius: 0.2rem;
                    text-align: center;
                }
            }
            .tags_container {
                overflow: auto;
                position: relative;
                .tag_list {
                    padding: 0.2rem;
                    border-bottom: 1px solid #999;
                    min-height: 1rem;
                    max-height: 4rem;
                    overflow: auto;
                    & > span {
                        background-color: #00c59d;
                        color: #fff;
                        padding: 0.2rem 0.4rem;
                        border-radius: 0.6rem;
                        font-size: 0.7rem;
                        margin: 0.2rem;
                        .checknum {
                            background-color: #f11;
                            border-radius: 50%;
                            font-size: 0.6rem;
                            line-height: 0.7rem;
                            min-width: 0.7rem;
                            display: inline-block;
                            height: 0.7rem;
                            text-align: center;
                            background: #56c7fd;
                            border: 1px solid white;
                        }
                    }
                    .add_tag_btn {
                        background: #fff;
                        color: #999;
                    }
                }
                .tag_names_list {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    min-height: 0;
                    .tag_names_list_title {
                        height: 1rem;
                        line-height: 1rem;
                        color: #fff;
                        border-bottom: 1px solid #999;
                        display: flex;
                        font-size: 0.8rem;
                        .tag_names_navbar_item {
                            flex: 1;
                            text-align: center;
                            &.active {
                                color: #00c59d;
                            }
                        }
                    }
                    .tag_names_list_container {
                        flex: 1;
                        min-height: 0;
                        overflow: auto;
                        .tag_names_list_container_item {
                            .tag_names {
                                padding: 0.2rem;
                                overflow: auto;
                                & > span {
                                    background-color: #00c59d;
                                    color: #fff;
                                    padding: 0.2rem 0.4rem;
                                    border-radius: 0.6rem;
                                    font-size: 0.7rem;
                                    margin: 0.2rem;
                                }
                                .checked {
                                    background-color: #aaa;
                                }
                            }
                        }
                    }
                }
            }
            .patient_container {
                box-sizing: border-box;
                padding: 0.4rem;
                font-size: 0.9rem;
                p {
                    margin: 0.1rem 0;
                }
            }
            .analyze_container {
                padding: 0.4rem;
                font-size: 0.9rem;
                .conclution {
                    margin: 0.6rem 0;
                }
                p {
                    margin: 0.1rem 0;
                }
                .tip {
                    color: #aaa;
                }
                table {
                    border: 1px solid #bbb;
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 10px;
                    th,
                    td {
                        border: 1px solid #bbb;
                        margin: auto;
                        padding: 5px;
                        border: 1px solid #bbb;
                        padding: 6px;
                        text-align: center;
                        font-family: "Arial Normal", "Arial";
                        font-weight: 400;
                        font-style: normal;
                        font-size: 0.7rem;
                        text-align: center;
                        line-height: normal;
                        vertical-align: middle;
                    }
                    .error {
                        color: red;
                    }
                    .arear {
                        display: inline-block;
                        height: 13px;
                        width: 25px;
                        border: 0px solid;
                        box-shadow: 2px 4px 10px 5px #99999954;
                    }
                }
                .struc_arear {
                    display: flex;
                    flex-direction: row;
                    .label {
                    }
                    .value {
                        margin-left: 0.5rem;
                        margin-right: 0.5rem;
                        flex: 1;
                    }
                }
            }
        }
    }
    .video_page {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        background-color: #333;
        z-index: 20;
        .main_video {
            width: 100%;
            height: auto;
            background-color: #000;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
        .gesture_video {
            width: 8rem;
            height: auto;
            background-color: #000;
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
        }
        .close_video_page {
            position: absolute;
            top: 0.3rem;
            left: 0.3rem;
            font-size: 1.3rem;
            z-index: 1;
        }
    }
    .edit_comment_modal {
        position: absolute;
        top: 0;
        bottom: 0;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        z-index: -1;
        &.showCommentModal {
            z-index: 9;
        }
        .edit_comment_panel {
            background-color: #eee;
            margin: 0 0.2rem;
            position: absolute;
            width: calc(100% - 0.4rem);
            top: 30%;
            .title {
                color: #333;
                padding: 0.2rem 0.4rem;
            }
            .edit_comment_container {
                padding: 0.4rem;
                background-color: #eee;
                .comment_editer {
                    line-height: 1.6rem;
                    background-color: #fff;
                    color: #333;
                    border-radius: 0.2rem;
                    border: 1px solid #ccc;
                    box-sizing: border-box;
                    max-height: 3.2rem;
                    overflow: auto;
                    user-select: text;
                    -webkit-user-select: text;
                    padding: 0 0.4rem;
                }
                .send_comment,
                .colse_comment {
                    font-size: 0.7rem;
                    line-height: 1.2rem;
                    padding: 0.2rem 0.5rem;
                    border-radius: 0.2rem;
                    margin-top: 1.2rem;
                }
            }
        }
    }
    .pdf_page {
        color: #000;
        .pdf_container {
            height: calc(100% - 2.2rem);
            .tip {
                text-align: center;
                margin-top: 3rem;
            }
            canvas {
                margin-bottom: 0.5rem;
            }
        }
        .operator {
            position: absolute;
            z-index: 2;
            top: 3.2rem;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            border-radius: 1rem;
            color: #fff;
            padding: 0 0.4rem;
            i {
                font-size: 1.4rem;
                margin: 0 0.3rem;
            }
            .reverse {
                transform: rotate(180deg);
                display: inline-block;
            }
            .disable {
                color: #aaa;
            }
        }
    }
    .lightness_modal {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        bottom: 0;
        z-index: 5;
        .lightness_process {
            position: absolute;
            width: 70%;
            left: 15%;
            top: 10%;
            z-index: 2;
            p {
                text-align: center;
                color: #fff;
                margin-bottom: 1rem;
            }
        }

        .van-slider__button {
            width: 1rem;
            height: 1rem;
        }
    }
}
</style>
