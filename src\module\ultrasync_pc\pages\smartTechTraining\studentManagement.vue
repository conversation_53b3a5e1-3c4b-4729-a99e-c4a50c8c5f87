<template>
    <div class="student-management-container">
        <!-- 筛选区域 -->
        <div class="filter-section">
            <el-form :inline="true" :model="studentFilters" class="filter-form">
                <el-form-item label="医院名称">
                    <el-input v-model="studentFilters.hospitalName" placeholder="请输入医院名称" clearable></el-input>
                </el-form-item>
                <el-form-item label="姓名/昵称">
                    <el-input v-model="studentFilters.nameOrNickname" placeholder="请输入姓名或昵称" clearable></el-input>
                </el-form-item>
                <el-form-item label="学员状态">
                    <el-select v-model="studentFilters.status" placeholder="请选择学员状态" clearable>
                        <el-option label="全部" value=""></el-option>
                        <el-option label="已注册" value="registered"></el-option>
                        <el-option label="已禁用" value="disabled"></el-option>
                        <!-- 根据实际状态添加更多 -->
                    </el-select>
                </el-form-item>
                <el-form-item label="Supervisor">
                    <el-select v-model="studentFilters.supervisorId" placeholder="请选择Supervisor" clearable>
                        <el-option label="全部" value=""></el-option>
                        <el-option v-for="supervisor in supervisorList" :key="supervisor.id" :label="supervisor.name" :value="supervisor.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="handleStudentSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 学员列表 -->
        <div class="content-section">
            <el-table :data="studentList" border stripe style="width: 100%" v-loading="loadingStudents">
                <el-table-column prop="accountName" label="账号名"></el-table-column>
                <el-table-column prop="studentId" label="学员ID"></el-table-column>
                <el-table-column prop="nickname" label="昵称"></el-table-column>
                <el-table-column prop="registrationTime" label="注册时间"></el-table-column>
                <el-table-column prop="supervisorName" label="Supervisor"></el-table-column>
                <el-table-column prop="mobile" label="手机号码"></el-table-column>
                <el-table-column prop="email" label="邮箱"></el-table-column>
                <el-table-column prop="name" label="姓名"></el-table-column>
                <el-table-column prop="gender" label="性别"></el-table-column>
                <el-table-column prop="hospitalName" label="医院名称"></el-table-column>
                <el-table-column prop="address" label="地址"></el-table-column>
                <el-table-column prop="remarks" label="备注" show-overflow-tooltip></el-table-column>
                <el-table-column prop="status" label="状态">
                    <template slot-scope="scope">
                        <span :class="getStudentStatusClass(scope.row.status)">
                            {{ scope.row.status }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                    <template slot-scope="scope">
                        <div class="operation-buttons-container">
                            <el-button type="text" class="option-btn" @click="handleEditStudent(scope.row)">编辑</el-button>
                            <el-button
                                type="text"
                                class="option-btn"
                                @click="handleToggleStudentStatus(scope.row)"
                               >
                                {{ scope.row.status === '已注册' ? '禁用' : '启用' }}
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <!-- TODO: 分页 -->
        </div>

        <!-- 编辑学员弹窗 -->
        <common-dialog
            :show.sync="editDialogVisible"
            title="编辑学员信息"
            @submit="handleSubmitEditStudent"
            @cancel="handleCancelEditStudent"
            width="500px"
        >
            <el-form :model="editingStudent" ref="editStudentForm" label-width="80px">
                <el-form-item label="Supervisor" prop="supervisorId">
                    <el-select v-model="editingStudent.supervisorId" placeholder="请选择Supervisor" style="width: 100%;">
                        <el-option
                            v-for="supervisor in supervisorList"
                            :key="supervisor.id"
                            :label="supervisor.name"
                            :value="supervisor.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备注" prop="remarks">
                    <el-input
                        type="textarea"
                        :rows="4"
                        placeholder="请输入备注"
                        v-model="editingStudent.remarks">
                    </el-input>
                </el-form-item>
            </el-form>
        </common-dialog>
    </div>
</template>

<script>
// import { SMART_TECH_TRAINING_ROLE } from "../../lib/constants"; // 如果需要角色判断，但在此组件中可能不再直接需要
import base from "../../lib/base"; // 引入 base mixin
import commonDialog from '@/module/ultrasync_pc/MRComponents/commonDialog.vue';

export default {
    name: "StudentManagementContent", // 可以考虑改个更明确的名字，表明是Tab内容
    mixins: [base], // 使用 mixin
    components: { // 添加 components 属性
        commonDialog
    },
    data() {
        return {
            studentFilters: {
                hospitalName: '',
                nameOrNickname: '',
                status: '',
                supervisorId: ''
            },
            supervisorList: [ // 占位数据，应从API获取
                { id: '1', name: 'Dr. Li' },
                { id: '2', name: 'Dr. Wang' },
                { id: '3', name: 'Dr. Chen' }
            ],
            studentList: [ // 占位数据，应从API获取
                {
                    accountName: 'jisd001',
                    studentId: '4545451',
                    nickname: '迈瑞-老纪',
                    registrationTime: '2025-04-22 18:30',
                    supervisorName: 'Dr.Li',
                    mobile: '***********',
                    email: '<EMAIL>',
                    name: '纪树德',
                    gender: '男',
                    hospitalName: '迈瑞',
                    address: '马来西亚',
                    remarks: '这是一条备注信息，可能会比较长。',
                    status: '已注册'
                },
                {
                    accountName: 'jisd0015',
                    studentId: '7878781',
                    nickname: '医学影像',
                    registrationTime: '2025-04-22 18:30',
                    supervisorName: 'Dr.Li',
                    mobile: '**********',
                    email: '<EMAIL>',
                    name: 'Raymond',
                    gender: '男',
                    hospitalName: '迈瑞',
                    address: '马来西亚',
                    remarks: '另一条备注信息。',
                    status: '已禁用'
                }
            ],
            loadingStudents: false,
            editDialogVisible: false,
            editingStudent: {
                supervisorId: '',
                remarks: ''
            },
            currentEditingOriginalStudent: null,
        };
    },
    // computed: {
    //     // isPI 从父组件或路由获取，此组件本身不再直接依赖
    // },
    created() {
        // 根据需要加载初始数据
        // this.fetchSupervisors();
        // this.fetchStudents();
    },
    methods: {
        getStudentStatusClass(status) {
            if (status === '已注册') {
                return 'status-tag-passed-new';
            }
            if (status === '已禁用') {
                return 'status-tag-failed-new';
            }
            return 'status-tag-default-new'; // 默认样式
        },
        handleStudentSearch() {
            // console.log("Searching students with filters:", this.studentFilters);
            this.loadingStudents = true;
            // 模拟API调用
            setTimeout(() => {
                // 在这里根据 this.studentFilters 筛选 this.studentList 或重新从API获取数据
                this.$message.success("查询完成（模拟）");
                this.loadingStudents = false;
            }, 1000);
        },
        handleEditStudent(student) {
            // console.log("Editing student:", student);
            this.currentEditingOriginalStudent = student; // 保存原始对象引用
            // 从 supervisorList 中找到对应的 supervisorId
            const supervisor = this.supervisorList.find(s => s.name === student.supervisorName);
            this.editingStudent = {
                ...student, // 浅拷贝一份，避免直接修改列表数据
                supervisorId: supervisor ? supervisor.id : '', // 将 supervisorName 转为 supervisorId
            };
            this.editDialogVisible = true;
        },
        handleToggleStudentStatus(student) {
            // console.log("Toggling status for student:", student);
            const newStatus = student.status === '已注册' ? '已禁用' : '已注册';
            this.$confirm(`确定要将学员 "${student.name}" 的状态更改为 "${newStatus}" 吗?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                student.status = newStatus; // 本地模拟更新
                this.$message({
                    type: 'success',
                    message: `学员 "${student.name}" 状态已更新为 "${newStatus}"`
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '操作已取消'
                });
            });
        },
        handleSubmitEditStudent() {
            this.$refs.editStudentForm.validate((valid) => {
                if (valid) {
                    // 模拟保存
                    console.log("Submitting student edit:", this.editingStudent);

                    // 更新 studentList 中的数据
                    if (this.currentEditingOriginalStudent) {
                        const supervisor = this.supervisorList.find(s => s.id === this.editingStudent.supervisorId);
                        this.currentEditingOriginalStudent.supervisorName = supervisor ? supervisor.name : '';
                        this.currentEditingOriginalStudent.remarks = this.editingStudent.remarks;
                        // 如果有其他字段也在此表单编辑，也需要更新
                        // 例如，如果 student 对象本身在 editingStudent 中被完整复制和修改，可以这样更新：
                        // Object.assign(this.currentEditingOriginalStudent, this.editingStudent);
                        // 但要注意只更新表单中有的字段，并正确处理 supervisorId 到 supervisorName 的转换
                    }

                    this.$message.success(`学员 "${this.editingStudent.name}" 信息更新成功 (模拟)`);
                    this.editDialogVisible = false;
                } else {
                    console.log('error submit!!');
                    this.$message.error('请检查表单项是否填写正确');
                    return false;
                }
            });
        },
        handleCancelEditStudent() {
            this.editDialogVisible = false;
            this.$message.info('编辑操作已取消');
        },
    }
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/aiChat.scss";
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";

.student-management-container {
    padding: 20px;
    background-color: #f5f7fa;
    // min-height: calc(100vh - 50px); // 将由flex控制高度
    height: 100%; // 确保容器占满父容器高度
    overflow: hidden; // 防止内部滚动条影响布局
    display: flex;
    flex-direction: column;

    .filter-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
        flex-shrink: 0; // 防止筛选区被压缩
        .filter-form {
            .el-form-item {
                margin-bottom: 0; // 保持紧凑
                margin-right: 15px;
            }
            // 可以根据需要调整el-select和el-input的宽度，如果默认宽度不合适
            // .el-select {
            //     width: 200px;
            // }
            // .el-input {
            //     width: 200px;
            // }
        }
    }

    .content-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
        flex: 1; // 占据剩余空间
        display: flex; // 为内部表格和分页的布局做准备
        flex-direction: column;
        overflow: hidden; // 内部自行处理滚动

        .el-table {
            flex: 1; // 表格占据content-section的主要空间
            // 可以考虑添加以下样式使表格内容滚动而不是整个section滚动
            // display: flex;
            // flex-direction: column;
            // overflow: hidden;
            // ::v-deep .el-table__body-wrapper {
            //     overflow-y: auto;
            // }
        }

        // 表格头部样式，如果需要统一
        // ::v-deep .el-table th {
        //     background-color: #F5F7FA !important;
        //     color: #303133 !important;
        //     font-weight: bold;
        // }

        // 分页容器样式 (为 TODO: 分页 预留)
        .pagination-container {
            margin-top: 20px;
            text-align: right;
            flex-shrink: 0; // 防止分页被压缩
        }
    }
}
</style>
