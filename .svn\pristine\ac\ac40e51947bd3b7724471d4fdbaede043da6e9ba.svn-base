<template>
    <div class="grading-review-container">
        <!-- 筛选区域 -->
        <div class="filter-section">
            <el-form :inline="true" :model="filterForm" class="filter-form">
                <el-form-item label="考核要求名称">
                    <el-select v-model="filterForm.taskName" placeholder="请选择考核要求" clearable>
                        <el-option label="图像考核" value="image"></el-option>
                        <el-option label="实操考核" value="operation"></el-option>
                        <el-option label="基础超声课程凭证" value="certificate"></el-option>
                        <!-- 更多考核要求 -->
                    </el-select>
                </el-form-item>
                <el-form-item label="姓名/昵称">
                    <el-input v-model="filterForm.studentNickname" placeholder="请输入姓名或昵称" clearable></el-input>
                </el-form-item>
                <el-form-item label="医院/诊所">
                    <el-input v-model="filterForm.hospitalName" placeholder="请输入医院或诊所" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch" icon="el-icon-search">查询</el-button>
                    <el-button @click="handleReset" icon="el-icon-refresh">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- Tab切换区域 -->
        <div class="tabs-section">
            <el-tabs v-model="activeTab" class="smart-tech-tabs smart-tech-tabs--small" @tab-click="handleTabClick" style="height: 100%; display: flex; flex-direction: column;">
                <el-tab-pane :label="`待批改/审核 (${tabCounts.pending})`" name="pending">
                    <div class="table-wrapper" v-if="activeTab === 'pending'">
                        <el-table
                            v-loading="pendingLoading"
                            :data="pendingTableData"
                            style="width: 100%"
                            border stripe
                        >
                            <el-table-column prop="taskName" label="考核要求名称" min-width="180" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="studentNickname" label="姓名/昵称" min-width="120"></el-table-column>
                            <el-table-column prop="hospitalName" label="医院/诊所" min-width="180" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="submissionTime" label="提交时间" min-width="160"></el-table-column>
                            <el-table-column prop="duration" label="考核用时" min-width="100"></el-table-column>
                            <el-table-column label="操作" min-width="100" fixed="right" align="center">
                                <template slot-scope="scope">
                                    <div class="operation-buttons-container">
                                        <el-button type="text" @click="goToGrading(scope.row)" class="option-btn">进入批改</el-button>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="pagination-container">
                            <el-pagination
                                @size-change="val => handleSizeChange('pending', val)"
                                @current-change="val => handleCurrentChange('pending', val)"
                                :current-page="pendingCurrentPage"
                                :page-sizes="[10, 20, 50, 100]"
                                :page-size="pendingPageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="pendingTotalItems">
                            </el-pagination>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane :label="`已批改/审核 (${tabCounts.graded})`" name="graded">
                    <div class="table-wrapper" v-if="activeTab === 'graded'">
                        <el-table
                            v-loading="gradedLoading"
                            :data="gradedTableData"
                            style="width: 100%"
                            border stripe
                        >
                            <el-table-column prop="taskName" label="考核要求名称" min-width="180" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="studentNickname" label="姓名/昵称" min-width="120"></el-table-column>
                            <el-table-column prop="hospitalName" label="医院/诊所" min-width="180" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="gradingTime" label="批改时间" min-width="160"></el-table-column>
                            <el-table-column prop="result" label="结果" min-width="100">
                                <template slot-scope="scope">
                                    <span :class="getResultClass(scope.row.result)">{{ scope.row.result }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" min-width="100" fixed="right" align="center">
                                <template slot-scope="scope">
                                    <div class="operation-buttons-container">
                                        <el-button type="text" @click="viewGradedDetail(scope.row)" class="option-btn">查看</el-button>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="pagination-container">
                            <el-pagination
                                @size-change="val => handleSizeChange('graded', val)"
                                @current-change="val => handleCurrentChange('graded', val)"
                                :current-page="gradedCurrentPage"
                                :page-sizes="[10, 20, 50, 100]"
                                :page-size="gradedPageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="gradedTotalItems">
                            </el-pagination>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane :label="`未提交 (${tabCounts.unsubmitted})`" name="unsubmitted">
                    <div class="table-wrapper" v-if="activeTab === 'unsubmitted'">
                        <el-table
                            v-loading="unsubmittedLoading"
                            :data="unsubmittedTableData"
                            style="width: 100%"
                            border stripe
                        >
                            <el-table-column prop="taskName" label="考核要求名称" min-width="180" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="studentNickname" label="姓名/昵称" min-width="120"></el-table-column>
                            <el-table-column prop="hospitalName" label="医院/诊所" min-width="180" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="deadlineTime" label="截止时间" min-width="160"></el-table-column>
                            <!-- 无操作列 -->
                        </el-table>
                        <div class="pagination-container">
                            <el-pagination
                                @size-change="val => handleSizeChange('unsubmitted', val)"
                                @current-change="val => handleCurrentChange('unsubmitted', val)"
                                :current-page="unsubmittedCurrentPage"
                                :page-sizes="[10, 20, 50, 100]"
                                :page-size="unsubmittedPageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="unsubmittedTotalItems">
                            </el-pagination>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
export default {
    name: "GradingReview",
    components: {},
    data() {
        return {
            filterForm: {
                taskName: '',
                studentNickname: '',
                hospitalName: '',
            },
            activeTab: 'pending',
            tabCounts: { pending: 12, graded: 56, unsubmitted: 225 }, // 从原型图获取初始数

            pendingTableData: [],
            pendingLoading: false,
            pendingCurrentPage: 1,
            pendingPageSize: 10,
            pendingTotalItems: 0,

            gradedTableData: [],
            gradedLoading: false,
            gradedCurrentPage: 1,
            gradedPageSize: 10,
            gradedTotalItems: 0,

            unsubmittedTableData: [],
            unsubmittedLoading: false,
            unsubmittedCurrentPage: 1,
            unsubmittedPageSize: 10,
            unsubmittedTotalItems: 0,
        };
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {
        this.fetchData();
    },
    methods: {
        handleSearch() {
            // 重置当前tab的页码
            this[`${this.activeTab}CurrentPage`] = 1;
            this.fetchData();
        },
        handleReset() {
            this.filterForm = {
                taskName: '',
                studentNickname: '',
                hospitalName: ''
            };
            // activeTab 不重置，或按需求选择是否重置到pending
            // this.activeTab = 'pending';
            this.handleSearch();
        },
        handleTabClick(tab) {
            // activeTab 已通过v-model更新
            // 每次切换tab时，都重置对应tab的页码到第一页并重新拉取数据
            this[`${this.activeTab}CurrentPage`] = 1;
            this.fetchData();
        },
        fetchData() {
            const currentTab = this.activeTab;
            this[`${currentTab}Loading`] = true;
            this[`${currentTab}TableData`] = []
            console.log(
                `Fetching data for Tab: ${currentTab}, Filters:`, this.filterForm,
                `Page: ${this[`${currentTab}CurrentPage`]}, Size: ${this[`${currentTab}PageSize`]}`
            );

            // 模拟API调用和数据
            setTimeout(() => {
                let allMockData = [];
                if (currentTab === 'pending') {
                    allMockData = [
                        { id: 'p1', taskName: '图像考核', studentNickname: '优先取答题时填写的姓名', hospitalName: '优先取答题时填写的医院', submissionTime: '2016-09-05 15:00', duration: '30分15秒' },
                        { id: 'p2', taskName: '图像考核', studentNickname: '医学影像', hospitalName: '深圳市人民医院', submissionTime: '2016-09-05 15:00', duration: '30分15秒' },
                        { id: 'p3', taskName: '实操考核', studentNickname: 'Jishude_China', hospitalName: '深圳市人民医院', submissionTime: '2016-09-05 15:00', duration: '30分15秒' },
                        { id: 'p4', taskName: '基础超声课程凭证', studentNickname: 'Tina', hospitalName: '深圳市人民医院', submissionTime: '2016-09-05 15:00', duration: '30分15秒' },
                        // 更多待批改数据...
                    ];
                } else if (currentTab === 'graded') {
                    allMockData = [
                        { id: 'g1', taskName: '图像考核', studentNickname: '优先取答题时填写的姓名', hospitalName: '优先取答题时填写的医院', gradingTime: '2016-09-05 15:00', result: '已通过' },
                        { id: 'g2', taskName: '图像考核', studentNickname: '医学影像', hospitalName: '深圳市人民医院', gradingTime: '2016-09-05 15:00', result: '未通过' },
                        { id: 'g3', taskName: '实操考核', studentNickname: 'Jishude_China', hospitalName: '深圳市人民医院', gradingTime: '2016-09-05 15:00', result: '已通过' },
                        { id: 'g4', taskName: '基础超声课程凭证', studentNickname: 'Tina', hospitalName: '深圳市人民医院', gradingTime: '2016-09-05 15:00', result: '已通过' },
                        // 更多已批改数据...
                    ];
                } else if (currentTab === 'unsubmitted') {
                    allMockData = [
                        { id: 'u1', taskName: '图像考核', studentNickname: '优先取答题时填写的姓名', hospitalName: '优先取答题时填写的医院', deadlineTime: '2016-09-05 15:00' },
                        { id: 'u2', taskName: '图像考核', studentNickname: '医学影像', hospitalName: '深圳市人民医院', deadlineTime: '2016-09-05 15:00' },
                        { id: 'u3', taskName: '实操考核', studentNickname: 'Jishude_China', hospitalName: '深圳市人民医院', deadlineTime: '2016-09-05 15:00' },
                        { id: 'u4', taskName: '基础超声课程凭证', studentNickname: 'Tina', hospitalName: '深圳市人民医院', deadlineTime: '2016-09-05 15:00' },
                        // 更多未提交数据...
                    ];
                }

                // 模拟筛选
                let filteredData = allMockData;
                if (this.filterForm.taskName) {
                    filteredData = filteredData.filter(item => item.taskName && item.taskName.toLowerCase().includes(this.filterForm.taskName.toLowerCase()));
                }
                if (this.filterForm.studentNickname) {
                    filteredData = filteredData.filter(item => item.studentNickname && item.studentNickname.toLowerCase().includes(this.filterForm.studentNickname.toLowerCase()));
                }
                if (this.filterForm.hospitalName) {
                    filteredData = filteredData.filter(item => item.hospitalName && item.hospitalName.toLowerCase().includes(this.filterForm.hospitalName.toLowerCase()));
                }

                // 模拟分页
                const total = filteredData.length;
                const start = (this[`${currentTab}CurrentPage`] - 1) * this[`${currentTab}PageSize`];
                const end = this[`${currentTab}CurrentPage`] * this[`${currentTab}PageSize`];
                this[`${currentTab}TableData`] = filteredData.slice(start, end);
                this[`${currentTab}TotalItems`] = total;

                // 更新tabCounts - 实际应从API获取或在数据返回后计算
                // 这里简单用筛选后的长度，实际场景可能需要分别请求count
                // this.tabCounts[currentTab] = total;

                this[`${currentTab}Loading`] = false;
            }, 2500);
        },
        handleSizeChange(tabKey, newSize) {
            this[`${tabKey}PageSize`] = newSize;
            this[`${tabKey}CurrentPage`] = 1;
            this.fetchData();
        },
        handleCurrentChange(tabKey, newPage) {
            this[`${tabKey}CurrentPage`] = newPage;
            this.fetchData();
        },
        goToGrading(row) {
            console.log('进入批改:', row);
            // this.$router.push({ name: 'SpecificGradingPage', params: { id: row.id } });
        },
        viewGradedDetail(row) {
            console.log('查看详情:', row);
            // this.$router.push({ name: 'GradedDetailPage', params: { id: row.id } });
        },
        getResultClass(result) {
            if (result === '已通过') {
                return 'status-tag-passed-new';
            }
            if (result === '未通过') {
                return 'status-tag-failed-new';
            }
            return 'status-tag-default-new'; // Default class using the new pattern
        },
    }
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";

.grading-review-container {
    padding: 20px;
    background-color: #f5f7fa;
    height:100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .filter-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
        flex-shrink: 0;
        .filter-form {
            .el-form-item {
                margin-bottom: 0;
                margin-right: 15px;
            }
            .el-select {
                width: 200px;
            }
            .el-input {
                width: 200px;
            }
        }
    }

    .tabs-section {
        background-color: #fff;
        padding: 0 20px; // 与表格区域的左右padding对齐
        border-radius: 8px; // 现在tabs-section是主要的内容容器，给它完整的圆角
        box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1); // 调整阴影使其作为主要容器
        margin-bottom: 0; // 保持
        position: relative; // 保持
        // top: 1px; // 不再需要微调，因为它是主要容器
        flex: 1; // 让tabs-section占据剩余空间
        display: flex; // 用于内部el-tabs的高度管理
        flex-direction: column; // 用于内部el-tabs的高度管理
        overflow: hidden; // 防止内容溢出
        .el-tabs__content{
            margin-top: 20px;
        }
    }

    // .content-section 已被移除，其样式也一并移除或合并

}
</style>
