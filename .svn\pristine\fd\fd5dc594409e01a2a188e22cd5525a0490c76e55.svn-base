<template>
    <el-dialog
        class="com_dialog"
        :visible.sync="visible"
        v-bind="$attrs"
        :close-on-click-modal="closeOnClickModal"
        :close-on-press-escape="closeOnPressEscape"
        @open="open"
        @opened="opened"
        @close="Cancel"
        @closed="closed"
        :append-to-body="appendToBody"
        :modal="modal"
    >
        <div v-if="!$slots.header" class="dialog-header" v-show="headerShow">
            <p class="title">{{title}}</p>
            <i class="iconfont iconcuohao" @click="handleClickDialogClose"></i>
        </div>
        <div v-else class="com_header" v-show="headerShow">
            <slot name="header" />
        </div>
        <div class="dialog-content">
            <slot></slot>
        </div>


        <div v-if="!$slots.footer"  class="dialog-footer" v-show="footShow" :class="[submitBtnSize==='small'?'smallBtnContainer':'largeBtnContainer']">
            <el-button @click="Reject" v-if="rejectShow" :class="[submitBtnSize==='small'?'smallBtn':'largeBtn']">{{ cancelText }}</el-button>
            <el-button type="primary" @click="Submit" v-if="submitShow" :loading="isSubmitting" :disabled="disabledSubmit" :class="[submitBtnSize==='small'?'smallBtn':'largeBtn']">{{ submitText }}</el-button>
        </div>

        <div v-else class="com_footer" v-show="footShow">
            <slot name="footer" />
        </div>
    </el-dialog>

</template>
<script>
export default {
    inheritAttrs: true,
    props: {
        title:{
            type:String,
            default:''
        },
        headerShow:{
            type: Boolean,
            default: true
        },
        footShow: {
            type: Boolean,
            default: true
        },
        rejectShow: {
            type: Boolean,
            default: false
        },
        cancelText: {
            type: String,
            default: '取消'
        },
        submitShow: {
            type: Boolean,
            default: true
        },
        submitText: {
            type: String,
            default: '确定'
        },
        closeOnClickModal: {
            type: Boolean,
            default: false
        },
        closeOnPressEscape: {
            type: Boolean,
            default: false
        },
        show: {
            type: Boolean,
            default: false
        },
        isSubmitting:{
            type: Boolean,
            default: false
        },
        disabledSubmit:{
            type: Boolean,
            default: false
        },
        submitBtnSize:{
            type: String,
            default: 'small'
        },
        modal: {
            type: Boolean,
            default: false
        },
        appendToBody: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        visible: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit("update:show", val);
            }
        }
    },
    mounted() {
        if (!this.footShow) {
            const dialogFooter = this.$el.querySelector('.el-dialog__footer');
            if (dialogFooter) {
                dialogFooter.style.display = 'none';
            }
        }
    },
    methods: {
        Cancel() {
            this.$emit("cancel");
        },
        Submit() {
            this.$emit("submit");
        },
        closed() {
            this.$emit("closed");
        },
        open() {
            this.$emit("open");
        },
        opened() {
            this.$emit("opened");
        },
        Reject(){
            if(this.$listeners.reject){
                this.$emit('reject');
            }else{
                this.visible = false;
            }
        },
        handleClickDialogClose(){
            if(this.$listeners.handleClickDialogClose){
                this.$emit('handleClickDialogClose')
            }else{
                this.visible = false
            }
        }
    }
};


</script>
<style lang="scss">
.com_dialog{

    .el-dialog{
        border-radius: 6px;
        display: flex;
        flex-direction: column;
        height: auto!important;
    }
    .el-dialog__body{
        height: auto!important;
        min-height: 200px;
        max-height:80vh;
        overflow: hidden;
        flex: 1;
        padding: 0!important;
        display: flex;
        flex-direction: column;
    }
    .el-dialog__header{
        height: 60px!important;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
        padding: 20px!important;
        display: none!important;
    }
    .el-dialog__headerbtn{
        position: static!important;
    }
    .dialog-footer{
        flex-shrink: 0;
        border-top: 1px solid #ccc;
        padding: 10px!important;
        border-radius:0 0 6px 6px;
        &.largeBtnContainer{
            display: flex;
            flex-direction: column;
        }
        .largeBtn{
            width: 100%;
            margin-bottom: 10px;
            &:last-child{
                margin-bottom: 0;
            }
        }
        &.smallBtnContainer{
            display: flex;
            justify-content:flex-end
        }
    }
    .com_footer{
        flex-shrink: 0;
        border-top: 1px solid #ccc;
        padding: 10px!important;
        border-radius:0 0 6px 6px;
        display: flex;
        justify-content:flex-end;
    }
    .dialog-header{
        display: flex;
        align-items: center;
        border-bottom: 2px solid #eee;
        padding: 20px 10px 14px;
        .title{
            font-size: 18px;
            margin: 0 10px;
            font-weight: bold;
        }
        .iconcuohao{
            position: absolute;
            right: 20px;
            top: 16px;
            font-size: 24px;
            color: #888;
            cursor: pointer;
        }
    }

    .dialog-content{
        padding: 20px;
        flex: 1;
        overflow: auto;
    }
    .el-input__inner{
        border: 1px solid #dcdfe6 !important;
    }
    .el-textarea__inner{
        border: 1px solid #dcdfe6 !important;
    }
    .el-form-item__label{
        padding-bottom: 0;
    }
}
</style>
