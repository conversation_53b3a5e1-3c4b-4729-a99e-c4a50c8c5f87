<template>
    <div>
        <!-- <el-dialog
            class="cloud_video_edit_dialog"
            :visible="visible"
            :close-on-click-modal="true"
            :modal="false"
            :before-close="beforeCloseDialog"
            append-to-body
            v-loading="loading"
            element-loading-background="rgba(0, 0, 0, 1)"
            :element-loading-text="loadingText"
        >


            <button @click="visible=false">关闭</button>
            <iframe src="http://localhost:3000/" frameborder="0" width="100%" height="100%"></iframe>
        </el-dialog> -->
        <div id="cloud_video_edit" :style="{ visibility: loaded ? 'unset' : 'hidden' }"></div>

            <!-- <iframe src="http://localhost:3000/" frameborder="0" width="100%" height="100%"></iframe> -->
        <CommonDialog
            :title="lang.export_comment"
            :show.sync="exportVisible"
            v-if="exportVisible"
            width="500px"
            height="auto"
            append-to-body
            :footShow="false"
            class="cloud_video_edit_export"
        >
            <div class="action-button-group">
                <el-button type="primary" round @click="exportResourceVideoClip" v-if="cid">{{lang.send_original_chat}}</el-button>
                <el-button type="primary" round @click="changeGroupExport">{{lang.reselect_chat_send}}</el-button>
                <el-button round @click="exportVisible = false">{{lang.cancel_btn}}</el-button>
            </div>
        </CommonDialog>
        <div class="extraDom" v-show="false">
            <i
                :class="[
                    'Icons_iconfont__3bVf5 ToolBar_mr36__2J1Ln',
                    'cloud_video_edit_selectCover',
                    recallData.length === 0 ? 'disabled' : '',
                ]"
                @click="recallApplyButton"
                ></i
            >
            <i :class="[cutButtonClass, 'cloud_video_edit_selectCover']" @click="cutButtonFn"></i>
            <i :class="[delButtonClass, 'cloud_video_edit_selectCover']" @click="deleteTimeLine"></i>
            <i class="icon iconfont iconimages cloud_video_edit_selectCover" @click="openSelectCoverDialog"></i>
        </div>
    </div>
</template>
<script>
import base from "../../lib/base";
import service from "../../service/service";
import "../../../../common/tool";
import Tool from "../../../../common/tool";
import CommonDialog from "../../MRComponents/commonDialog.vue";
import languages from "@/common/language";

export default {
    mixins: [base],
    name: "CloudVideoEditChild",
    components: {
        CommonDialog,
    },
    data() {
        return {
            visible: true,
            loaded: false,
            exportVisible: false,
            currentExportInfo: {},
            currentCoverImage: "",
            projectId: "",
            mediaId: "",
            delButtonClass: "",
            recallButtonClass: "",
            cutButtonClass: "",
            delButton: null,
            recallButton: null,
            recoverButton: null,
            cutButton: null,
            retryTime: 10,
            retryInterval: 3000,
            loadingText: "",
            retrying: false,
            lastVideoTracks: [],
            loopCheckTimeLineInterval: null,
            recallData: [], //撤回列表
            selectCoverIcon:[],
            resourceId:'',
            cid:0,
            userId:0,
            currentDynamicSrc:''
        };
    },
    computed: {},
    created() {
        this.loadingText = this.lang.loading_module_text;

        console.log(this.resourceId,this.cid,this.userId)
    },
    beforeDestroy() {
        window.removeEventListener('message',this.handleMessage)
    },
    mounted() {
        document.addEventListener("keydown", this.handleRejectKeyDown, true);
        window.addEventListener('message',this.handleMessage)

        // this.$nextTick(()=>{
        //
        // })
    },
    methods: {
        handleMessage(event){
            console.log(event.data)
            const {data} = event
            if(data.type === 'cloudVideoEditParent'){
                if(data.hasOwnProperty('loaded')&&data.loaded){
                    this.resourceId = data.resourceId
                    this.cid=data.cid
                    this.userId = data.userId
                    
                    // 使用父组件传递的语言配置
                    if(data.languageConfig) {
                        this.$store.commit('language/setLanguage', data.languageConfig.langData);
                        this.$store.commit('language/setLanguage', { currentLanguage: data.languageConfig.currentLanguage });
                    } 
                    this.getProjectId();
                }
                if(data.hasOwnProperty('retry')&&data.retry){
                    window.location.reload()
                }
                if(data.hasOwnProperty('destroy')&&data.destroy){
                    window.AliyunVideoEditor.destroy();
                }
                if(data.hasOwnProperty('exportResourceVideoClip')&&data.exportResourceVideoClip){
                    this.exportResourceVideoClip({cid:data.cid})
                }
            }

        },
        setMessageToParent(data={}){
            window.parent.postMessage({type:'cloudVideoEditChild',...data}, '*');
        },
        leaveCloudVideoPage(){
            // clearInterval(this.loopCheckTimeLineInterval);
            // this.loopCheckTimeLineInterval = null;
            // Tool.removeLinkCss("https://g.alicdn.com/thor-server/video-editing-websdk/3.14.0/index.css");
            // this.removeAntMessage();
            // document.removeEventListener("keydown", this.handleRejectKeyDown);
            // this.destroyAliCloudEditSdk();
            // setTimeout(()=>{

            // },0)
            // this.visible = false;
            this.setMessageToParent({back:true})
        },
        handleRejectKeyDown(e) {
            e.stopPropagation();
        },
        async getProjectId() {
            try {
                Tool.loadCSS("https://g.alicdn.com/thor-server/video-editing-websdk/3.14.0/index.css");
                const res = await service.getVideoClipProjectId({
                    resourceId: this.resourceId,
                    userId:this.userId
                });
                console.log("getVideoClipProjectId", res.data);
                this.projectId = res.data.data.projectId;
                this.mediaId = res.data.data.mediaId;
                // this.projectId = '4f128e54b3e74f4aa9270dfd43dcc3d4'
                this.loadAliCloudEditSdk();
            } catch (error) {
                this.$message.error(this.lang.failed_to_enter_editing_mode);
                this.leaveCloudVideoPage()
            }
        },
        destroyAliCloudEditSdk() {
            Tool.removeScript("https://g.alicdn.com/thor-server/video-editing-websdk/3.14.0/index.js");
            this.stopVideoPlay()
            if (window.AliyunVideoEditor) {
                try {
                    window.AliyunVideoEditor.destroy();
                } catch (error) {
                    console.log(error);
                }
            }

        },
        loadAliCloudEditSdk() {
            this.$nextTick(async () => {
                const myLocale = this.lang.currentLanguage==='CN'?"zh-CN":"en-US";
                await Promise.all([
                    Tool.loadJS("https://g.alicdn.com/thor-server/video-editing-websdk/3.14.0/index.js"),
                ]);
                window.AliyunVideoEditor.init({
                    container: document.getElementById("cloud_video_edit"),
                    locale: myLocale,
                    useDynamicSrc: true, // 媒资库默认情况下播放地址会过期，所以需要动态获取
                    customTexts: {
                        // importButton: '自定义导入',
                        // updateButton: this.lang.save_txt,
                        // produceButton: this.lang.export_video,
                        // backButton: this.lang.back_text,
                    },
                    onBackButtonClick: () => {
                        this.$confirm(this.lang.exit_clip_tips, this.lang.tip_title, {
                            confirmButtonText: this.lang.confirm_button_text,
                            cancelButtonText: this.lang.cancel_button_text,
                            type: "warning",
                        })
                            .then(() => {
                                this.leaveCloudVideoPage()
                            })
                            .catch(() => {

                            });
                    },
                    getDynamicSrc: (mediaId, mediaType) => {
                        return service
                            .resourceVideoClip({
                                // https://help.aliyun.com/document_detail/197842.html
                                MediaId: mediaId,
                                Action: "GetMediaInfo",
                            })
                            .then((res) => {
                                console.log("getDynamicSrc", res);
                                // 注意，这里仅作为示例，实际中建议做好错误处理，避免如 FileInfoList 为空数组时报错等异常情况
                                if (res.data.data.MediaInfo.FileInfoList.length === 0) {
                                    console.error("reload", "getDynamicSrc");
                                    this.retryLoadModule();
                                } else {

                                    const dynamicSrc = res.data.data.MediaInfo.FileInfoList[0].FileBasicInfo.FileUrl

                                    if(dynamicSrc!==this.currentDynamicSrc){
                                        this.currentDynamicSrc = this.clearUrlParams(dynamicSrc)
                                        return this.currentDynamicSrc;
                                    }


                                }
                            });
                    },
                    getEditingProjectMaterials: () => {
                        return service
                            .resourceVideoClip({
                                // https://help.aliyun.com/document_detail/209068.html
                                ProjectId: this.projectId,
                                Action: "GetEditingProjectMaterials",
                            })
                            .then((res) => {
                                console.log("getEditingProjectMaterials", res);

                                const data = res.data.data.MediaInfos;
                                this.hideSomeFeatures();
                                if (!data || data[0].FileInfoList.length === 0) {
                                    // console.error('reload','getEditingProjectMaterials')
                                    // this.retryLoadModule()
                                    return [];
                                } else {
                                    return this.transMediaList(data); // 需要做一些数据变换
                                }
                            });
                    },
                    searchMedia: (mediaType) => {
                        // mediaType 为用户当前所在的素材 tab，可能为 video | audio | image，您可以根据这个参数对应地展示同类型的可添加素材

                        return new Promise(async (resolve, reject) => {
                            console.log("searchMedia", mediaType);
                            try {
                                const info = await this.getListMediaBasicInfos();
                                if (!info) {
                                    console.error("reload", "searchMedia");
                                    this.retryLoadModule();
                                } else {
                                    resolve(info);
                                    setTimeout(() => {
                                        this.addTimeline();
                                    }, 800);
                                }
                            } catch (error) {
                                reject(error);
                            }

                            // 调用方需要自己实现展示媒资、选择媒资添加的界面
                            // 关于展示媒资，请参考：https://help.aliyun.com/document_detail/197964.html
                            // searchMediaRef.current = {
                            //     resolve,
                            //     reject
                            // }
                            // setShowSearchMediaModal(true)
                        });
                    },
                    deleteEditingProjectMaterials: async (mediaId, mediaType) => {
                        // 不支持删除资源
                        return new Promise((resolve, reject) => {
                            console.log("deleteEditingProjectMaterials", mediaId);
                            reject();
                        });
                        // return service.resourceVideoClip({
                        //     // https://help.aliyun.com/document_detail/209067.html
                        //     ProjectId: projectId,
                        //     MaterialType: mediaType,
                        //     MaterialIds: mediaId,
                        //     Action:"DeleteEditingProjectMaterials"
                        // }).then((res) => {
                        //     console.log("deleteEditingProjectMaterials", res);

                        // }).catch((error)=>{
                        //     console.error("deleteEditingProjectMaterials", error);
                        // });
                    },
                    getStickerCategories: async () => {
                        const res = await service.resourceVideoClip({
                            // https://help.aliyun.com/document_detail/207796.html
                            BusinessType: "sticker",
                            WebSdkVersion: window.AliyunVideoEditor.version,
                            Action: "ListAllPublicMediaTags",
                        });

                        const stickerCategories = res.data.MediaTagList.map((item) => ({
                            id: item.MediaTagId,
                            name: myLocale === "zh-CN" ? item.MediaTagNameChinese : item.MediaTagNameEnglish, // myLocale 是您期望的语言
                        }));
                        return stickerCategories;
                    },
                    getStickers: async ({ categoryId, page, size }) => {
                        const params = {
                            PageNo: page,
                            PageSize: size,
                            IncludeFileBasicInfo: true,
                            MediaTagId: categoryId,
                            Action: "ListPublicMediaBasicInfos",
                        };

                        const res = await service.resourceVideoClip(params); // https://help.aliyun.com/document_detail/207797.html

                        const fileList = res.data.MediaInfos.map((item) => ({
                            mediaId: item.MediaId,
                            src: item.FileInfoList[0].FileBasicInfo.FileUrl,
                        }));

                        return {
                            total: res.data.TotalCount,
                            stickers: fileList,
                        };
                    },
                    getEditingProject: async () => {
                        const res = await service.resourceVideoClip({
                            // https://help.aliyun.com/document_detail/197837.html
                            ProjectId: this.projectId,
                            Action: "GetEditingProject",
                        });

                        const timelineString = res.data.data.Project.Timeline;
                        const data = {
                            projectId: this.projectId,
                            timeline: timelineString ? JSON.parse(timelineString) : undefined,
                            modifiedTime: res.data.data.Project.ModifiedTime,
                        };
                        console.log('GetEditingProject"', data);

                        setTimeout(() => {
                            this.clickSearchMediaButton();
                        }, 700);
                        return data;
                    },
                    updateEditingProject: ({ coverUrl, duration, timeline, isAuto }) => {
                        return service
                            .resourceVideoClip({
                                // https://help.aliyun.com/document_detail/197835.html
                                ProjectId: this.projectId,
                                CoverURL: coverUrl,
                                Duration: duration,
                                Timeline: JSON.stringify(timeline),
                                Action: "UpdateEditingProject",
                            })
                            .then(() => {
                                // WebSDK 本身会进行自动保存，isAuto 则是告诉调用方这次保存是否自动保存，调用方可以控制只在手动保存时才展示保存成功的提示
                                !isAuto && this.$message.success("save success");
                            });
                    },
                    produceEditingProjectVideo: ({ coverUrl, duration = 0, aspectRatio, timeline, recommend }) => {
                        console.log("produceEditingProjectVideo", {
                            coverUrl,
                            duration,
                            aspectRatio,
                            timeline,
                            recommend,
                        });
                        this.exportVisible = true;
                        return new Promise(async (resolve, reject) => {
                            this.currentExportInfo = {
                                aspectRatio,
                                recommend,
                                timeline,
                                resolve,
                                reject,
                            };

                            // produceVideoRef.current = {
                            //     aspectRatio,
                            //     recommend,
                            //     timeline,
                            //     resolve,
                            //     reject
                            // }
                            // setShowProduceVideoModal(true)
                        });
                    },
                    getEvents(e) {
                        console.log(e, "getEvents");
                    },
                });
                setTimeout(() => {
                    document.addEventListener("keydown", function (event) {
                        event.preventDefault();
                    });
                }, 1000);
            });
        },
        beforeCloseDialog() {},
        /**
         * 将服务端的素材信息转换成 WebSDK 需要的格式
         */
        transMediaList(data) {
            if (!data) {
                return [];
            }
            if (Array.isArray(data)) {
                return data.map((item) => {
                    const basicInfo = item.MediaBasicInfo;
                    let fileBasicInfo = item.FileInfoList[0].FileBasicInfo;
                    fileBasicInfo.FileUrl = this.clearUrlParams(fileBasicInfo.FileUrl)
                    console.error(fileBasicInfo.FileUrl,'fileBasicInfo.FileUrl')
                    const mediaId = basicInfo.MediaId;
                    const result = {
                        mediaId,
                    };
                    const mediaType = basicInfo.MediaType;
                    result.mediaType = mediaType;

                    if (mediaType === "video") {
                        result.video = {
                            title: fileBasicInfo.FileName,
                            duration: Number(fileBasicInfo.Duration),
                            // 源视频的宽高、码率等数据，用于推荐合成数据，不传入或是0时无推荐数据
                            width: Number(fileBasicInfo.Width) || 0,
                            height: Number(fileBasicInfo.Height) || 0,
                            bitrate: Number(fileBasicInfo.Bitrate) || 0,
                            coverUrl: basicInfo.CoverURL,
                        };
                        const spriteImages = basicInfo.SpriteImages;
                        if (spriteImages) {
                            try {
                                const spriteArr = JSON.parse(spriteImages);
                                const sprite = spriteArr[0];
                                const config = JSON.parse(sprite.Config);
                                result.video.spriteConfig = {
                                    num: config.Num,
                                    lines: config.SpriteSnapshotConfig && config.SpriteSnapshotConfig.Lines,
                                    cols: config.SpriteSnapshotConfig && config.SpriteSnapshotConfig.Columns,
                                    cellWidth: config.SpriteSnapshotConfig && config.SpriteSnapshotConfig.CellWidth,
                                    cellHeight: config.SpriteSnapshotConfig && config.SpriteSnapshotConfig.CellHeight,
                                };
                                result.video.sprites = sprite.SnapshotUrlList;
                            } catch (e) {
                                console.log(e);
                            }
                        }
                    } else if (mediaType === "audio") {
                        result.audio = {
                            title: fileBasicInfo.FileName,
                            duration: Number(fileBasicInfo.Duration),
                            coverURL: "", // 您可以给音频文件一个默认的封面图
                        };
                    } else if (mediaType === "image") {
                        result.image = {
                            title: fileBasicInfo.FileName,
                            coverUrl: fileBasicInfo.FileUrl,
                            // 图片的宽高等数据，用于推荐合成数据，不传入或是0时无推荐数据
                            width: Number(fileBasicInfo.Width) || 0,
                            height: Number(fileBasicInfo.Height) || 0,
                        };
                    }

                    return result;
                });
            } else {
                return [data];
            }
        },
        getListMediaBasicInfos() {
            return new Promise(async (resolve, reject) => {
                try {
                    const res = await service.resourceVideoClip({
                        Action: "ListMediaBasicInfos",
                        MediaId: this.mediaId,
                    });
                    if (res.data.data.MediaInfos.length > 0) {
                        let MediaId = res.data.data.MediaInfos[0].MediaId;
                        const addRes = await this.AddEditingProjectMaterials(MediaId);
                        if (addRes.data.data.MediaInfos[0].FileInfoList.length === 0) {
                            this.retryLoadModule();
                            reject("getListFailed");
                        } else {
                            resolve(this.transMediaList(addRes.data.data.MediaInfos));
                        }
                    }
                } catch (error) {
                    reject(error);
                }
            });
        },
        async AddEditingProjectMaterials(MediaId) {
            const res = await service.resourceVideoClip({
                ProjectId: this.projectId,
                MaterialMaps: JSON.stringify({ video: MediaId }),
                Action: "AddEditingProjectMaterials",
            });
            console.log(res, "AddEditingProjectMaterials");
            return res;
        },
        addTimeline() {
            this.clearTimeLine();
            const addDom = document.querySelector('[class^="BasicItem_add"]');
            let timeline = JSON.parse(JSON.stringify(window.AliyunVideoEditor.getProjectTimeline()));
            console.log(timeline, "timeline", addDom);
            var evt = new MouseEvent("click", {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: 20,
                /* whatever properties you want to give it */
            });
            if (addDom && timeline) {
                if (timeline.VideoTracks.length === 0) {
                    addDom.dispatchEvent(evt);
                } else if (timeline.VideoTracks[0].VideoTrackClips.length === 0) {
                    timeline.VideoTracks = [];
                    window.AliyunVideoEditor.setProjectTimeline(timeline);
                    addDom.dispatchEvent(evt);
                } else if (timeline.VideoTracks[0].VideoTrackClips.length > 1) {
                    timeline.VideoTracks = [timeline.VideoTracks[0]];
                    window.AliyunVideoEditor.setProjectTimeline(timeline);
                }
            }

            setTimeout(() => {
                if (!this.checkSpriteImage()) {
                    console.error("reload", "addTimeline");
                    this.retryLoadModule();
                } else {
                    this.loaded = true;
                    this.setMessageToParent({loaded:this.loaded})
                }
                this.loopCheckTimeLineState();
                // this.setDisabledClass('recall')
            }, 300);
        },
        checkSpriteImage() {
            const TrackContainer_sprite_container = document.querySelector(
                '[class^="TrackContainer_sprite_container"]'
            );
            return TrackContainer_sprite_container ? true : false;
        },
        clearTimeLine() {
            let timeline = JSON.parse(JSON.stringify(window.AliyunVideoEditor.getProjectTimeline()));
            timeline.VideoTracks = [];
            window.AliyunVideoEditor.setProjectTimeline(timeline);
        },
        clickSearchMediaButton() {
            const searchMediaDom = document.querySelector('[class^="MaterialContainer_right_actions"] button');
            console.log(searchMediaDom);
            // let timeline = JSON.parse(JSON.stringify(window.AliyunVideoEditor.getProjectTimeline()))
            // console.log(timeline,'timeline')
            this.dispatchClick(searchMediaDom);
        },
        dispatchClick(dom) {
            var evt = new MouseEvent("click", {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: 20,
                /* whatever properties you want to give it */
            });
            if (dom) {
                dom.dispatchEvent(evt);
            }
        },
        dispatchDrag(draggableElement) {
            // 获取需要拖拽的元素 draggableElement
            // const draggableElement = document.getElementById('draggable');

            // 创建一个鼠标按下事件
            const mouseDownEvent = new MouseEvent("mousedown", {
                clientX: 0,
                clientY: 0,
                view: window,
                bubbles: true,
                cancelable: true,
            });

            // 触发鼠标按下事件
            draggableElement.dispatchEvent(mouseDownEvent);

            // 创建一个鼠标移动事件
            const mouseMoveEvent = new MouseEvent("mousemove", {
                clientX: 0,
                clientY: 0,
                view: window,
                bubbles: true,
                cancelable: true,
            });

            // 触发鼠标移动事件
            draggableElement.dispatchEvent(mouseMoveEvent);

            // 创建一个鼠标松开事件
            const mouseUpEvent = new MouseEvent("mouseup", {
                clientX: 0,
                clientY: 0,
                view: window,
                bubbles: true,
                cancelable: true,
            });

            // 触发鼠标松开事件
            draggableElement.dispatchEvent(mouseUpEvent);
        },
        hideSomeFeatures() {
            const views_bodyLeft = document.querySelector('[class^="views_bodyLeft"]');
            const views_bodyRight = document.querySelector('[class^="views_bodyRight"]');
            const views_bodyMiddle = document.querySelector('[class^="views_bodyMiddle"]');
            const HeaderToolBar_shortcut = document.querySelector('[class^="HeaderToolBar_shortcut"]');
            const cutButton = document.querySelectorAll('[class^="ToolBar_toolIcons"] i')[3];
            const ratioButton = document.querySelector('[class^="Player_player_tool_bar__"] [class^="Player_right__"]');
            const HeaderToolBar_save = document.querySelectorAll('[class^="HeaderToolBar_right_part__"] >button')[0];
            this.delButton = document.querySelectorAll('[class^="ToolBar_toolIcons"] i')[4];
            this.recallButton = document.querySelectorAll('[class^="ToolBar_toolIcons"] i')[0];
            this.recoverButton = document.querySelectorAll('[class^="ToolBar_toolIcons"] i')[1];
            this.cutButton = document.querySelectorAll('[class^="ToolBar_toolIcons"] i')[2];

            views_bodyLeft.style.display = "none";
            views_bodyRight.style.display = "none";
            views_bodyMiddle.style.flex = 1;
            HeaderToolBar_shortcut.style.display = "none";
            cutButton.style.display = "none";
            ratioButton.style.display = "none";
            HeaderToolBar_save.style.display = "none";
            this.delButtonClass = this.delButton.getAttribute("class");
            this.recallButtonClass = this.recallButton.getAttribute("class");
            this.cutButtonClass = this.cutButton.getAttribute("class");
            this.delButton.style.display = "none";
            this.recallButton.style.display = "none";
            this.recoverButton.style.display = "none";
            this.cutButton.style.display = "none";

            this.mutationObserveButton(this.delButton, "delButton");
            // this.mutationObserveButton(this.recallButton,'recallButton')
            this.mutationObserveButton(this.cutButton, "cutButton");
            this.appendSelectCoverImageButton();
        },
        appendSelectCoverImageButton() {
            const ToolBar_toolIcons = document.querySelector('[class^="ToolBar_toolIcons"]');
            const selectCoverIcon = document.querySelectorAll(".cloud_video_edit_selectCover");
            console.error(ToolBar_toolIcons, this.selectCoverIcon);
            if(selectCoverIcon.length>0){
                this.selectCoverIcon = selectCoverIcon
            }
            this.selectCoverIcon.forEach((node) => {
                ToolBar_toolIcons.appendChild(node);
            });
        },
        mutationObserveButton(dom, name) {
            // 实例化MutationObserver
            let attrListener = new MutationObserver((mutations) => {
                let dom = mutations[0].target;
                let domClass = dom.getAttribute("class");
                this[`${name}Class`] = domClass;
            });
            attrListener.observe(this[name], { attributes: true, attributeFilter: ["class"] });
        },
        removeAntMessage() {
            //移除ant的提示
            const antMessage = document.querySelector(".ant-message");
            // 拿到父节点:
            let parent = antMessage ? antMessage.parentElement : null;
            // 删除:
            parent && parent.removeChild(antMessage);
        },
        async exportResourceVideoClip({ cid = this.cid } = {}) {
            const timeline = this.currentExportInfo.timeline;
            let duration = 0;
            if (timeline.VideoTracks.length === 0) {
                this.currentExportInfo.reject();
                return;
            } else if (timeline.VideoTracks[0].VideoTrackClips.length === 0) {
                this.currentExportInfo.reject();
                // this.$message.error("当前没有合成的视频信息，不可导出");
                return;
            } else {
                let clipsData = this.currentExportInfo.timeline.VideoTracks[0].VideoTrackClips;

                //     clipsData.forEach(element => {
                //         console.log(element)
                //         duration +=element.duration
                //     });
                // }
                duration = clipsData[clipsData.length - 1].TimelineOut;

                this.setMessageToParent({
                    exportData:{
                        // https://help.aliyun.com/document_detail/197853.html
                        projectId: this.projectId,
                        timeline: JSON.stringify(this.currentExportInfo.timeline),
                        bitrate: this.currentExportInfo.recommend.bitrate,
                        width: this.currentExportInfo.recommend.width,
                        height: this.currentExportInfo.recommend.height,
                        fileName: `${new Date().getTime()}.mp4`,
                        cid,
                        duration,
                        resourceId: this.resourceId,
                    }
                })
                // try {
                //     const res = await service.exportResourceVideoClip();
                //     this.currentExportInfo.resolve();
                //     this.exportVisible = false;
                //     this.$message.success(this.lang.export_task_submitted);
                //     this.leaveCloudVideoPage()
                // } catch (error) {
                //     this.currentExportInfo.reject(error);
                //     console.error(error);
                // }
            }
        },

        changeGroupExport() {
            this.setMessageToParent({
                clickSelectGroupTransmit:true
            })

        },
        canvasToImage() {
            var canvas = document.getElementById("video-canvas");
            var context = canvas.getContext("2d");
            let base64 = canvas.toDataURL("image/png");
            return base64;
        },
        openSelectCoverDialog() {
            const base64 = this.canvasToImage();
            this.setMessageToParent({
                clickCoverButton:true,
                base64Image:base64
            })
        },
        deleteTimeLine() {
            if (this.delButtonClass.indexOf("disabled") > -1) {
                return;
            }
            const { isLastVideoClip, isLastVideoTrack } = this.checkIsLastTimeLineTrack();
            if (isLastVideoTrack) {
                this.$message.error(this.lang.delete_video_track_not_allowed);
                return;
            } else if (isLastVideoClip) {
                this.$message.error(this.lang.delete_video_clip_not_allowed);
                return;
            }
            let oldTimeline = JSON.parse(JSON.stringify(window.AliyunVideoEditor.getProjectTimeline()));
            this.dispatchClick(this.delButton);
            let currentTimeline = JSON.parse(JSON.stringify(window.AliyunVideoEditor.getProjectTimeline()));
            const { hasChange } = this.checkTimeLineChange(oldTimeline, currentTimeline);
            if (hasChange) {
                this.recallData.push(oldTimeline);
            }
        },
        checkIsLastTimeLineTrack() {
            const TrackContainer_singleTrack = document.querySelector('[class^="TrackContainer_singleTrack_"]');
            const TrackContainer_material_block = document.querySelectorAll('[class^="TrackContainer_material_block"]');
            const TrackContainer_material_block_active = document.querySelectorAll(
                '[class^="TrackContainer_material_block"].active'
            );
            let isLastVideoClip = false;
            let isLastVideoTrack = false;
            if (TrackContainer_material_block.length === TrackContainer_material_block_active.length) {
                isLastVideoClip = true;
            }
            let domClass = TrackContainer_singleTrack.getAttribute("class");
            let timeline = JSON.parse(JSON.stringify(window.AliyunVideoEditor.getProjectTimeline()));
            if (domClass.indexOf("active") > -1) {
                isLastVideoTrack = true;
            } else {
                if (timeline.VideoTracks[0].VideoTrackClips.length === 1) {
                    isLastVideoClip = true;
                }
            }
            return {
                isLastVideoClip,
                isLastVideoTrack,
            };
        },
        cutButtonFn() {
            if (this.cutButtonClass.indexOf("disabled") > -1) {
                return;
            }
            let oldTimeline = JSON.parse(JSON.stringify(window.AliyunVideoEditor.getProjectTimeline()));
            this.dispatchClick(this.cutButton);
            let currentTimeline = JSON.parse(JSON.stringify(window.AliyunVideoEditor.getProjectTimeline()));
            const { hasChange } = this.checkTimeLineChange(oldTimeline, currentTimeline);
            if (!hasChange) {
                this.$message.error(this.lang.area_cannot_cropped);
            } else {
                this.recallData.push(oldTimeline);
            }
        },
        recallApplyButton() {
            if (this.recallData.length === 0) {
                return;
            }
            let lastTimeline = this.recallData.pop(-1);
            window.AliyunVideoEditor.setProjectTimeline(lastTimeline);
            // this.dispatchClick(this.recallButton)
        },
        removeDisabledClass(name) {
            // this[`${name}Class`].replace('disabled','')
            this.recallButton.classList.remove("disabled");
        },
        setDisabledClass() {
            this.recallButton.classList.add("disabled");
        },
        retryLoadModule() {
            // if (this.retrying) {
            //     return;
            // }
            // this.destroyAliCloudEditSdk();
            // this.retrying = true;
            // this.retryTime--;

            //
            // //
            // if (this.retryTime >= 1) {
            //     setTimeout(() => {
            //         this.loadAliCloudEditSdk();
            //         this.retrying = false;
            //     }, 3000);
            // } else {
            //     this.$message.error(this.lang.cloud_resources_processed);
            //     this.leaveCloudVideoPage()
            // }
            this.setMessageToParent({loadFailed:true})

        },
        calcUnSpaceTimeLine(currentVideoTracks) {
            let VideoTracks = JSON.parse(JSON.stringify(currentVideoTracks));
            let newVideoTrackClips = [];
            let lastTimeLineOut = 0;
            let lastTimeLineIn = 0;
            let hasChange = false;

            if (this.lastVideoTracks.length > 0) {
                if (JSON.stringify(this.lastVideoTracks) !== JSON.stringify(VideoTracks)) {
                    console.log(JSON.stringify(this.lastVideoTracks));
                    console.log(JSON.stringify(VideoTracks));
                    hasChange = true;
                }
                if (VideoTracks[0].VideoTrackClips.length > this.lastVideoTracks[0].VideoTrackClips.length) {
                    hasChange = false;
                }
            }

            VideoTracks[0].VideoTrackClips.forEach((item, index) => {
                newVideoTrackClips.push(item);
                newVideoTrackClips[index].TimelineIn = lastTimeLineOut;
                newVideoTrackClips[index].TimelineOut = lastTimeLineOut + item.Duration;
                lastTimeLineOut = newVideoTrackClips[index].TimelineOut;
                lastTimeLineIn = newVideoTrackClips[index].TimelineIn;

                // console.log(item.TimelineOut)
                // if(item.TimelineIn!==lastTimeLineOut){
                //   hasChange = true
                // }

                // if(item.TimelineIn!==newVideoTrackClips[index].TimelineIn){
                //   hasChange = true
                // }
                // if(!hasChange&&lastVideoTracks.length>0){
                //   // console.log(item.TimelineOut,lastVideoTracks[0].VideoTrackClips[index].TimelineOut)
                //   if(item.TimelineIn!==lastVideoTracks[0].VideoTrackClips[index].TimelineIn){
                //     hasChange = true
                //   }
                // }
                // if(lastVideoTracks.length>0){
                //   lastVideoTracks[0].VideoTrackClips.forEach((lasItem,lasIndex)=>{
                //     if((item.TimelineIn!==lasItem.TimelineIn) || (item.TimelineOut!==lasItem.TimelineOut)){
                //       hasChange = true
                //     }
                //   })
                // }
            });

            if (hasChange) {
                console.log(hasChange,'hasChange')
            }
            VideoTracks[0].VideoTrackClips = newVideoTrackClips;

            return {
                hasChange,
                VideoTracks,
            };
        },
        checkTimeLineChange(oldTimeline, currentTimeline) {
            console.error(oldTimeline, currentTimeline);
            let oldVideoTracks = oldTimeline.VideoTracks;
            let currentVideoTracks = currentTimeline.VideoTracks;
            let hasChange = false;
            if (JSON.stringify(oldVideoTracks) !== JSON.stringify(currentVideoTracks)) {
                hasChange = true;
            }
            return {
                hasChange,
            };
        },
        setOnlyOneVideoTrack(currentVideoTracks) {
            let videoTracks = JSON.parse(JSON.stringify(currentVideoTracks));
            let newVideoTrack = [];
            newVideoTrack = [videoTracks[0]];
            return newVideoTrack;
        },
        loopCheckTimeLineState() {
            let currentVideoTracks = [];
            if (this.loopCheckTimeLineInterval) {
                clearInterval(this.loopCheckTimeLineInterval);
                this.loopCheckTimeLineInterval = null;
            }
            this.loopCheckTimeLineInterval = setInterval(() => {
                // console.log(window.AliyunVideoEditor.getProjectTimeline())

                let timeline = JSON.parse(JSON.stringify(window.AliyunVideoEditor.getProjectTimeline()));
                const TrackContainer_material_block_active = document.querySelector(
                    '[class^="TrackContainer_material_block"].active'
                );
                if (timeline) {
                    currentVideoTracks = timeline.VideoTracks;
                    const onlyVideoTrack = this.setOnlyOneVideoTrack(currentVideoTracks);
                    const { VideoTracks, hasChange } = this.calcUnSpaceTimeLine(onlyVideoTrack);
                    this.lastVideoTracks = JSON.parse(JSON.stringify(currentVideoTracks));
                    // console.log(VideoTracks,hasChange)
                    if (hasChange) {
                        timeline.VideoTracks = VideoTracks;
                        window.AliyunVideoEditor.setProjectTimeline(timeline);
                        const seekDom = document.querySelector('[class^="SeekCursor_seekerContainer"]');
                        if (seekDom) {
                            if (TrackContainer_material_block_active) {
                                seekDom.style.left =
                                    parseInt(TrackContainer_material_block_active.style.left) + 130 + "px";
                                this.dispatchDrag(seekDom);
                                this.dispatchClick(TrackContainer_material_block_active);
                            } else {
                                seekDom.style.left = 130 + "px";
                                this.dispatchDrag(seekDom);
                            }
                        }

                        console.log(TrackContainer_material_block_active);
                        // const lastTrackContainer = TrackContainer[0]
                        // console.log(TrackContainer)
                        // if(parseInt(seekDom.style.left)>(parseInt(lastTrackContainer.style.left)+ parseInt(lastTrackContainer.style.width))){
                        //   alert(1)
                        // }

                        //todo    需要把指针指向 当前active 的元素
                    }
                }
            }, 300);
        },
        stopVideoPlay(){
            const playButton = document.querySelectorAll('[class^="Player_middle"] >[class^="Player_actionBtn"]')[2];
            if(playButton){
                const toolTips = playButton.querySelector('i').getAttribute('tooltip')
                const isPlay = toolTips === '播放'?false:true
                if(isPlay){
                    this.dispatchClick(playButton)
                }
            }
        },
        clearUrlParams(url) {
            var urlParts = url.split('?');
            if (urlParts.length > 1) {
                return urlParts[0];
            }
            return url;
        }
    },
};
</script>
<style lang="scss">
.ant-message{
    display: none !important;
}
.cloud_video_edit_dialog {

    .el-dialog {
        width: 100%;
        height: 100% !important;
        margin: 0 !important;
        #cloud_video_edit {
            width: 100%;
            height: 100%;
        }
        .el-dialog__header {
            display: none !important;
        }
        .el-dialog__body {
            padding: 0 !important;
            height: 100% !important;
        }
    }
}
#cloud_video_edit{
    width: 100vw;
    height: 100vh;
    background: #000;
}
.cloud_video_edit_export {
    .action-button-group {
        .el-button {
            width: 100%;
            margin-bottom: 14px;
            margin-left: 0;
        }
    }
}
.cloud_video_edit_selectCover {
    font-size: 20px;
    margin-right: 36px;
}
</style>
