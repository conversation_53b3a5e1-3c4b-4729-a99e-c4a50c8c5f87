<template>
<div>
    <el-dialog
      class="cloud_exam_detail cloud_exam_dialog"
      :title="title"
      :visible="true"
      :close-on-click-modal="false"
      width="90%"
      :modal="false"
      :before-close="back">
        <div class="custom_header">
            <p v-if="type !== CLOUD_TEST_TYPE.EDIT" class="title">{{examDetail.title}}</p>
            <p v-else class="title">{{lang.homework.preview_exam_content}}</p>
            <i class="iconfont iconcuohao" @click="back"></i>
        </div>
        <div class="custom_body">
            <div class="correct_exam_detail">
                <div class="exam_detail_left">
                    <div v-if="type === CLOUD_TEST_TYPE.VIEW_RESULT" class="exam_detail_item">{{lang.student_name}}：{{currentAssignment.studentName || currentAssignment.studentInfo?.nickname}}</div>
                    <div v-else-if="type === CLOUD_TEST_TYPE.EDIT" class="exam_detail_item" style="display: flex; align-items: center;">
                        <div style="flex-shrink: 0;">{{lang.author}}：</div>
                        <!-- 编辑模式，可编辑作者 -->
                        <text-field
                            class="exam_detail_author"
                            :value="examDetail.author"
                            :isEdit="true"
                            :placeholder="user.nickname"
                            @change="(val) => handleFieldChange(examDetail, 'author', val)"
                        />
                    </div>
                    <div v-else class="exam_detail_item">{{lang.author}}：{{examDetail.author}}</div>
                    <div class="exam_detail_item">{{lang.paper_total_score}}：{{examDetail.score}}{{lang.point_tip}}</div>
                    <div class="exam_detail_item">{{lang.paper_question_count}}：{{examDetail.questionCount}}</div>
                    <!-- 答题模式，可编辑学生信息 -->
                    <div v-if="type === CLOUD_TEST_TYPE.ANSWER" class="student_info">
                        <div style="display: flex; align-items: center;">
                            <div>{{lang.student_name}}:&nbsp;</div>
                            <el-input
                                v-model="studentName"
                                :placeholder="this.studentName ? this.studentName : lang.input_enter_tips"
                                size="small"
                            ></el-input>
                        </div>
                        <div style="display: flex; align-items: center; margin-left: 10%;">
                            <div>{{lang.admin_hospital_name}}:&nbsp;</div>
                            <el-input
                                v-model="studentOrg"
                                :placeholder="this.studentOrg ? this.studentOrg : lang.input_enter_tips"
                                size="small"
                            ></el-input>
                        </div>
                    </div>
                </div>
                <div class="exam_detail_right">
                    <template v-if="type === CLOUD_TEST_TYPE.VIEW">
                        <el-button class="primary_btn" @click="shareExam" size="large" type="default">{{lang.share_paper}}</el-button>
                        <el-button class="error_btn" @click="deletePaper" size="large" type="default">{{lang.homework.delete_paper}}</el-button>
                        <el-button class="gradient_btn" @click="arrangeExam">{{lang.assign_homework}}</el-button>
                    </template>
                    <!-- <template v-else-if="type === 2 || type === 3">
                        <el-button class="primary_btn" v-if="type === 2" @click="save" size="large" type="default">{{lang.save_txt}}</el-button>
                        <el-button class="error_btn" @click="submit">{{lang.submit_btn}}</el-button>
                    </template> -->
                    <template v-else-if="type === CLOUD_TEST_TYPE.EDIT">
                        <el-button class="error_btn" @click="back" size="large" type="default">{{lang.cancel_btn}}</el-button>
                        <el-button class="primary_btn" @click="saveExam" size="large" type="default">{{lang.save_txt}}</el-button>
                    </template>
                    <div v-else-if="type === CLOUD_TEST_TYPE.VIEW_RESULT" class="exam_detail_item">
                        <span>{{lang.paper_results}}:</span>
                        <span class="assignment_score">{{currentAssignment.score}}</span>
                        <span>{{lang.point_tip}}</span>
                    </div>
                </div>
            </div>
            <div ref="topicContent" class="topic_content" v-if="examDetail">
                <div class="exam-title-wrapper" v-if="type === CLOUD_TEST_TYPE.EDIT">
                    <span class="title-tip-label">{{lang.homework.exam_title_tip}}：</span>
                    <!-- 编辑模式，可编辑试卷标题 -->
                    <text-field
                        :value="examDetail.title"
                        :isEdit="true"
                        :placeholder="lang.input_enter_tips"
                        @change="(val) => handleFieldChange(examDetail, 'title', val)"
                    />
                </div>
                <div class="content-container">
                    <div class="topic_list">
                        <div v-for="(topicType,index) of examDetail.content" :key="index" class="topic_list_item">
                            <div class="topic_summary">{{lang.topic_type[topicType.type]}}（{{getTopicTypeSummary(topicType)}}）</div>
                            <pre v-if="type !== CLOUD_TEST_TYPE.EDIT && topicType.type === 'operation'" class="topic_tip" v-html="lang.homework_operation_step"></pre>
                            <div class="topic_detail" v-for="(topic,j_index) of topicType.list" :key="j_index" @click="setCurrentQuestionByTypeIndex(index, j_index)">
                                <!-- 使用对应的组件渲染不同题型 -->
                                <single-select-topic
                                    v-if="topicType.type === 'singleSelect'"
                                    :topic="topic"
                                    :type="type"
                                    :topicTypeIndex="index"
                                    :topicIndex="j_index"
                                    :disableModify="disableModify"
                                    @field-change="handleFieldChange"
                                    @option-change="handleOptionChange"
                                    @delete-topic="deleteTopic"
                                    @delete-image="deleteImage"
                                    @upload-click="() => uploadStart(index, j_index, '-1')"
                                    @image-order-changed="handleImageOrderChange"
                                />
                                <multi-select-topic
                                    v-else-if="topicType.type === 'multiSelect'"
                                    :topic="topic"
                                    :type="type"
                                    :topicTypeIndex="index"
                                    :topicIndex="j_index"
                                    :disableModify="disableModify"
                                    @field-change="handleFieldChange"
                                    @option-change="handleOptionChange"
                                    @delete-topic="deleteTopic"
                                    @delete-image="deleteImage"
                                    @upload-click="() => uploadStart(index, j_index, '-1')"
                                    @image-order-changed="handleImageOrderChange"
                                />
                                <short-answer-topic
                                    v-else-if="topicType.type === 'shortAnswer'"
                                    :topic="topic"
                                    :type="type"
                                    :topicTypeIndex="index"
                                    :topicIndex="j_index"
                                    :disableModify="disableModify"
                                    @field-change="handleFieldChange"
                                    @delete-topic="deleteTopic"
                                    @delete-image="deleteImage"
                                    @upload-click="() => uploadStart(index, j_index, '-1')"
                                    @image-order-changed="handleImageOrderChange"
                                    @update-value="handleUpdateValue"
                                    @update-correct-score="handleUpdateCorrectScore"
                                />
                                <operation-topic
                                    v-else-if="topicType.type === 'operation'"
                                    :topic="topic"
                                    :type="type"
                                    :topicTypeIndex="index"
                                    :topicIndex="j_index"
                                    :disableModify="disableModify"
                                    @field-change="handleFieldChange"
                                    @subtopic-field-change="handleSubtopicFieldChange"
                                    @delete-topic="deleteTopic"
                                    @delete-subtopic="deleteTopic"
                                    @delete-image="deleteImage"
                                    @upload-click="(subIndex) => uploadStart(index, j_index, subIndex)"
                                    @image-order-changed="handleImageOrderChange"
                                    @update-correct-score="handleUpdateCorrectScore"
                                    @open-collect="openCollect"
                                    @close-collect="closeCollect"
                                />

                                <input v-show="false" ref="uploadComponent" type="file" :accept="acceptFileTypes" multiple @change="handleFileChange($event)"/>
                            </div>
                        </div>
                    </div>
                    <!-- 答题、批改模式，显示进度条 -->
                    <progress-indicator
                        v-if="(type === CLOUD_TEST_TYPE.ANSWER || type === CLOUD_TEST_TYPE.CORRECT) && examDetail.content"
                        :progressList="progressList"
                        @save="save"
                        @submit="submit"
                        :topicType="type"
                        :totalScore="currentTotalScore"
                        @jump="jumpToQuestion"
                    />
                </div>
            </div>
            <!-- <div class="topic_footer">
                <el-pagination
                  background
                  :current-page="topicStep"
                  :layout="layout"
                  :page-size="1"
                  :pager-count="15"
                  :total="examDetail.questionCount"
                  @current-change="handleCurrentChange"
                  style="margin-top:20px;text-align: center;"
                />
            </div> -->
        </div>
        <image-viewer ref="imageViewer"></image-viewer>
        <router-view></router-view>
    </el-dialog>
</div>
</template>
<script>
import base from '../../lib/base';
import service from '../../service/service.js'
import {formatDurationTime,findServiceId} from '../../lib/common_base';
import moment from 'moment';
import imageViewer from '../../MRComponents/imageViewer'
import {uploadFile} from '@/common/oss/index'
import Tool from '@/common/tool.js'
import textField from './components/TextField.vue'
import progressIndicator from "./components/progressIndicator.vue"
import SingleSelectTopic from './components/SingleSelectTopic.vue'
import MultiSelectTopic from './components/MultiSelectTopic.vue'
import ShortAnswerTopic from './components/ShortAnswerTopic.vue'
import OperationTopic from './components/OperationTopic.vue'
import { cloneDeep } from 'lodash'
import { CLOUD_TEST_TYPE } from '../../lib/constants';

export default {
    mixins: [base],
    name: 'cloudExamDetail',
    components: {
        imageViewer,
        textField,
        progressIndicator,
        SingleSelectTopic,
        MultiSelectTopic,
        ShortAnswerTopic,
        OperationTopic,
    },
    data(){
        return {
            paperId:0,
            type:1, // 查看类型 1：查看试卷 2：答卷 3：批卷 4：查看结果 5：编辑试卷
            cid:0, // 群id ，0为全局进入
            examDetail:{},
            currentAssignment:{
                assignmentInfo:{},
                // studentInfo:{}
            },
            useTime:0,
            interval:null,
            uploadIndex:[0,0,0],
            collectIndex:[0,0,0],
            fileTransferAssistant:null,
            layout:'prev, pager, next, jumper',
            pageTotal:0,
            topicStep:1,
            topicTypeStep:1,
            confirmSave:false,
            studentName: '',
            studentOrg: '',
            hasClickedStopCollect: false,
            currentQuestionIndex: 0,
            CLOUD_TEST_TYPE: CLOUD_TEST_TYPE,
        }
    },
    filters:{
        showData(ts){
            return moment(ts).format("YYYY-MM-DD HH:mm:ss z")
        },
        useTime(duration){
            return formatDurationTime(duration)
        },
    },
    computed:{
        disableModify(){
            return this.type !== CLOUD_TEST_TYPE.ANSWER && this.type !== CLOUD_TEST_TYPE.EDIT;
        },
        title(){
            return this.cid === 0 ? this.lang.my_cloud_exam : this.lang.cloud_exam;
        },
        uploadedPaper(){
            return this.$store.state.homework.uploadedPaper;
        },
        acceptFileTypes() {
            const imageTypes = this.getSupportImageType().join(',.').toLowerCase();
            const videoTypes = this.getSupportVideoType().join(',.').toLowerCase();
            return `.${imageTypes},.${videoTypes}`;
        },
        progressList(){
            if (!this.examDetail || !this.examDetail.content) {
                return [];
            }
            const res = this.getAnswer();
            const answers = res.answer;
            let progress = [];

            for (let i = 0; i < answers.length; i++) {
                let isCompleted = false;
                let baseStatus = "none";
                if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                    // 答题模式
                    const topic = this.findTopicByIndex(i);
                    if (topic) {
                        if (topic.type === 'operation') {
                            // 判断实操题：检测子题中是否有上传图片且已停止采集
                            isCompleted = topic.subTopic.some(sub => sub.value && sub.value.length > 0);
                        } else {
                            isCompleted = (typeof answers[i].value === "string" && answers[i].value.trim() !== "") ||
                                    (Array.isArray(answers[i].value) && answers[i].value.length > 0);
                        }
                    }
                } else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                    // 批改模式
                    const topic = this.findTopicByIndex(i);
                    if (topic) {
                        isCompleted = typeof topic.correctScore !== 'undefined' && topic.correctScore !== null;
                    }
                }
                baseStatus = isCompleted ? "done" : "none";
                // 如果该题为当前题，则显示 current 样式
                let status = (i === this.currentQuestionIndex) ? "current" : baseStatus;
                progress.push({
                    status: status,
                    completed: isCompleted
                });
            }
            return progress;
        },
        currentTotalScore() {
            if (this.type !== CLOUD_TEST_TYPE.CORRECT || !this.examDetail || !this.examDetail.content) {
                return 0;
            }
            let totalScore = 0;
            this.examDetail.content.forEach(topicType => {
                topicType.list.forEach(item => {
                    // 只累加已经有correctScore的题目分数
                    if (item.correctScore !== null && item.correctScore !== undefined && item.correctScore !== '') {
                        totalScore += Number(item.correctScore);
                    }
                });
            });
            return totalScore;
        }
    },
    created(){
        this.type = parseInt(this.$route.params.type);
        this.cid = parseInt(this.$route.params.cid);
        this.paperId = this.$route.params.id;
    },
    mounted(){
        if (this.type === CLOUD_TEST_TYPE.VIEW) {
            // 查看试卷
            this.getPaperDetail()
        }else if (this.type === CLOUD_TEST_TYPE.ANSWER) {
            // 考生获取答题卡
            this.getanswerSheetDetail();
        }else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
            // 老师批卷获取答题卡
            // this.getanswerSheetDetail();
            this.getCorrectDetail();
        }else if (this.type === CLOUD_TEST_TYPE.VIEW_RESULT) {
            // 考生查看详情
            this.getanswerSheetDetail();
        }else if (this.type === CLOUD_TEST_TYPE.EDIT) { // && this.$route.query.isPreview
            // 编辑试卷
            const uploadedPaper = this.$store.state.homework.uploadedPaper;
            // if (uploadedPaper) {
            this.examDetail = uploadedPaper.paperInfo;
            this.initAnswer([]);
        }
    },
    beforeRouteLeave(to, from, next) {
        // 在离开组件前提示保存
        if (this.type !== CLOUD_TEST_TYPE.ANSWER || this.confirmSave) {
            next();
            return;
        }
        setTimeout(()=>{
            // 不加延时会闪没
            this.$confirm(
                this.lang.unsave_tip,
                this.lang.tip_title,
                {
                    confirmButtonText: this.lang.save_txt,
                    cancelButtonText: this.lang.cancel_button_text,
                    type: "warning",
                }
            ).then(() => {
                let {answer} = this.getAnswer();
                this.submitAnswer(false,answer)
                this.back();
            }).catch(()=>{
                this.back();
            });
        },100)
        this.confirmSave = true;
        next(false);
    },
    destroyed(){
        clearInterval(this.interval);
        this.closeFileTransferListener();
        if (this.type === CLOUD_TEST_TYPE.CORRECT) {
            this.unlockAnswerSheet()
        }
    },
    watch:{
    },
    methods:{
        getSupportImageType(){
            return Tool.getSupportImageType();
        },
        getSupportVideoType(){
            return Tool.getSupportVideoType();
        },
        shareExam(){
            this.$root.eventBus.$emit('openTransmit',{
                cid:0,
                title:this.lang.share_paper,
                comfirm_msg:this.lang.share_paper_to,
                isServiceTypeNone:1,
                disableChat: true,
                disableGroup: true,
                callback:(data)=>{
                    // todo 分享试题回调
                    let uidList = [];
                    let gidList = [];
                    if (data.from === 'group') {
                        gidList.push(data.cid)
                    }
                    if (data.from === 'friend') {
                        uidList.push(data.uid)
                    }
                    service.sharePaper({
                        paperID:this.paperId,
                        uidList,
                        gidList,
                    }).then(res => {
                        if (res.data.error_code === 0) {
                            this.$message.success(this.lang.share_to_wechat_succeed);
                        }
                    })
                },
            })
        },
        arrangeExam(){
            if (this.cid !== 0) {
                // this.$nextTick(()=>{
                //     this.$root.eventBus.$emit('getParams',{
                //         from:'group',
                //     })
                // })
                this.$router.push(this.$route.fullPath+'/exam_setting/'+this.cid+'?from=group');
            }else{
                this.$root.eventBus.$emit('openTransmit',{
                    cid:0,
                    title:this.lang.assign_homework,
                    disableChat:true,
                    disableFriend:false,
                    disableGroup:false,
                    comfirm_msg:this.lang.assign_homework_to + '：',
                    isServiceTypeNone:1,
                    callback:(data)=>{
                        const id = data.from === 'group' ? data.cid : data.id;
                        // this.$nextTick(()=>{
                        //     this.$root.eventBus.$emit('getParams',{
                        //         from:data.from,
                        //     })
                        // })
                        // this.$router.push(this.$route.fullPath+'/exam_setting/'+id);
                        this.$router.push(this.$route.fullPath+'/exam_setting/'+id+'?from='+data.from);
                    },
                })
            }
        },
        getTopicTypeSummary(topicType){
            let summary = this.lang.topic_summary
            summary = summary.replace('{a}',topicType.count)
            summary = summary.replace('{b}',topicType.totalScore)
            return summary
        },
        getTopicCount(topic){
            let topicCount = this.lang.topic_count;
            topicCount = topicCount.replace('{a}',topic.score);
            return topicCount;
        },
        deleteImage(imageList, index){
            imageList.splice(index, 1);
            // 删除图片后更新数据
            const updatedPaper = cloneDeep(this.$store.state.homework.uploadedPaper);
            updatedPaper.paperInfo = this.examDetail;
            this.$store.commit('homework/setUploadPaper', updatedPaper);
        },
        getPaperDetail(){
            service.getPaperDetail({     //Original Code
                paperID:this.paperId
            }).then(res => {
                if (res.data.error_code === 0) {
                    this.examDetail = res.data.data
                    this.initAnswer([]);
                }
            })
        },
        getCorrectDetail(){
            // 获取批改详情先检查锁，已被别的批改老师锁定则退出
            service.lockAnswerSheet({
                answerSheetID:this.paperId,
            }).then(res=>{
                if (res.data.error_code === 0) {
                    this.getanswerSheetDetail();
                }else{
                    this.back();
                }
            })
        },
        getanswerSheetDetail(){
            service.getanswerSheetDetail({
                answerSheetID:this.paperId
            }).then(res=>{
                console.log(res)
                if (res.data.error_code === 0) {
                    this.examDetail = res.data.data.paperInfo
                    this.currentAssignment = res.data.data;
                    this.useTime = res.data.data.useTime;
                    // 设置学生信息
                    if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                        this.studentName = this.currentAssignment.studentName || '';
                        this.studentOrg = this.currentAssignment.studentOrg || '';
                    }
                    this.initAnswer(this.currentAssignment.answer);
                    if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                        this.useTimeInterval();
                    }else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                        this.initScoreDetail(this.currentAssignment.scoreDetail)
                        this.teacherCorrectLock();
                    }else if (this.type === CLOUD_TEST_TYPE.VIEW_RESULT) {
                        this.initScoreDetail(this.currentAssignment.scoreDetail)
                    }
                }
            })
        },
        save(){
            if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                let {answer,completeCount} = this.getAnswer();
                this.submitAnswer(false, answer, {
                    studentName: this.studentName,
                    studentOrg: this.studentOrg
                })
            }else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                this.submitCorrect()
            }
        },
        submit(){
            if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                let {answer,completeCount} = this.getAnswer();
                let message = this.lang.submit_paper_tip
                message = message.replace('{a}',completeCount)
                message = message.replace('{b}',this.examDetail.questionCount)
                this.$confirm(
                    message,
                    this.lang.tip_title,
                    {
                        confirmButtonText: this.lang.submit_btn,
                        cancelButtonText: this.lang.cancel_button_text,
                    }
                ).then(() => {
                    // 添加学生信息到answer对象
                    this.submitAnswer(true, answer, {
                        studentName: this.studentName,
                        studentOrg: this.studentOrg
                    })
                }).catch(()=>{});
            }else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                this.submitCorrect()
            }
        },
        submitAnswer(isFinish, answer, studentInfo = {}) {
            this.confirmSave = true;
            service.submitAnswer({
                answerSheetID:this.paperId,
                useTime:this.useTime,
                isFinish,
                answer,
                ...studentInfo // 添加学生信息到提交参数
            }).then(res => {
                if (res.data.error_code === 0) {
                    this.$message.success(this.lang.operate_success)
                    if (isFinish) {
                        this.$root.eventBus.$emit("refreshCompletePaper")
                        this.$root.eventBus.$emit("refreshIncompletePaper")
                        this.back();
                    }
                }
            })
        },
        submitCorrect(){
            // 提交批改
            try{
                let detail = this.getScoreDetail();
                let message = this.lang.correct_paper_tip
                message = message.replace('{a}',detail.score)
                this.$confirm(
                    message,
                    this.lang.tip_title,
                    {
                        confirmButtonText: this.lang.submit_btn,
                        cancelButtonText: this.lang.cancel_button_text,
                    }
                ).then(() => {
                    service.submitCorrect({
                        answerSheetID:this.paperId,
                        score:detail.score,
                        scoreDetail:detail.scoreDetail,
                    }).then(res=>{
                        if (res.data.error_code === 0) {
                            this.$message.success(this.lang.operate_success)
                            this.$root.eventBus.$emit("refreshCorrectData")
                            this.back();
                        // } else if (res.data.key === 'paperAssignmentHasBeenRevoked') {
                        } else if (res.data.error_code === -1) {
                            this.$message.error(this.lang.error.paperAssignmentHasBeenRevoked)
                            setTimeout(() => {
                                this.$root.eventBus.$emit("refreshCorrectData")
                                this.back();
                            }, 1500);
                        }
                    })
                }).catch(()=>{});
            } catch(e){
                this.$message.error(e.message);
            }
        },
        useTimeInterval(){
            this.interval = setInterval(()=>{
                this.useTime += 1;
            },1000);
        },
        teacherCorrectLock(){
            this.interval = setInterval(()=>{
                service.lockAnswerSheet({
                    answerSheetID:this.paperId,
                })
            },55000);
        },
        unlockAnswerSheet(){
            service.unlockAnswerSheet({
                answerSheetID:this.paperId,
            })
        },
        initAnswer(answer = []){
            let index = 0;
            this.examDetail.content.forEach(topictype=>{
                topictype.list.forEach(item=>{
                    if (topictype.type === 'operation') {
                        let j_index = 0;
                        let currentAnswer = answer[index] || {};

                        item.subTopic.forEach(subTopic=>{
                            const value = currentAnswer.value&&currentAnswer.value[j_index]
                            if (value) {
                                subTopic.value = value;
                            }else{
                                subTopic.value = [];
                            }
                            window.vm.$set(subTopic,'collecting',false)
                            j_index++;
                        })
                    }else{
                        if (answer[index]) {
                            window.vm.$set(item,'value',answer[index].value)
                        }else{
                            if (topictype.type === 'multiSelect') {
                                window.vm.$set(item,'value',[])
                            }else{
                                window.vm.$set(item,'value','')
                            }
                        }
                    }
                    window.vm.$set(item,'index',++index)
                })
            })
        },
        initScoreDetail(scoreDetail = []){
            let index = 0;
            const answer = this.currentAssignment.answer;
            this.examDetail.content.forEach(topictype=>{
                topictype.list.forEach(item=>{
                    const correctScore = scoreDetail[index];
                    if (correctScore) {
                        // 已经批改过的题，直接采用原有分数
                        window.vm.$set(item, 'correctScore', correctScore.score);
                    } else {
                        if (this.type === CLOUD_TEST_TYPE.CORRECT) {  // 教师批改模式
                            // 仅对提供参考答案的单选和多选题自动批改
                            if ((item.type === 'singleSelect' || item.type === 'multiSelect') && item.answer) {
                                const studentAnswer = answer[index].value;
                                if (item.type === 'singleSelect') {
                                    // 单选题：直接比较参考答案与学生答案
                                    if (item.answer === studentAnswer) {
                                        window.vm.$set(item, 'correctScore', item.score);
                                    } else {
                                        window.vm.$set(item, 'correctScore', 0);
                                    }
                                } else if (item.type === 'multiSelect') {
                                    // 多选题：先对参考答案和学生答案排序后比较
                                    let refAnswer = Array.isArray(item.answer) ? item.answer.slice().sort().join(',') : '';
                                    let stuAnswer = Array.isArray(studentAnswer) ? studentAnswer.slice().sort().join(',') : '';
                                    if (refAnswer === stuAnswer) {
                                        window.vm.$set(item, 'correctScore', item.score);
                                    } else {
                                        window.vm.$set(item, 'correctScore', 0);
                                    }
                                }
                            } else {
                                // 对于未提供参考答案的单选/多选，或全部短答案和实操题，不自动批改，置空默认分数
                                window.vm.$set(item, 'correctScore', null);
                            }
                        }else{
                            window.vm.$set(item,'correctScore',item.score)
                        }
                    }
                    index++;
                })
            })
        },
        getAnswer(){
            let answer = [];
            let completeCount = 0;
            this.examDetail.content.forEach(topictype=>{
                topictype.list.forEach(item=>{
                    if (topictype.type === 'operation') {
                        let arr = []
                        let complete = false;

                        item.subTopic.forEach(subTopic=>{
                            arr.push(subTopic.value);
                            if (subTopic.value.length !== 0 ) {
                                complete = true;
                            }
                        })
                        answer.push({
                            id:item.id,
                            value:arr
                        })
                        if (complete) {
                            completeCount++;
                        }
                    }else{
                        if (topictype.type === 'multiSelect') {
                            item.value.sort();
                        }
                        answer.push({
                            id:item.id,
                            value:item.value
                        })
                        if (typeof item.value === 'string' && item.value !== '') {
                            completeCount++;
                        }else if (typeof item.value === 'object' && item.value.length !== 0) {
                            completeCount++;
                        }
                    }
                })
            })
            return {answer,completeCount};
        },
        getScoreDetail(){
            let scoreDetail = [];
            let score = 0;
            this.examDetail.content.forEach(topicType=>{
                topicType.list.forEach(item=>{
                    // 若批改分数为空，则说明该题未进行批改（教师需要手动赋分）
                    if(item.correctScore === null || item.correctScore === undefined || item.correctScore === ''){
                        throw new Error(this.lang.enter_correct_number);
                    }
                    const intScore = Number(item.correctScore);
                    if (isNaN(intScore) || intScore > Number(item.score) || intScore < 0) {
                        throw new Error(this.lang.enter_correct_number);
                    }
                    if (!Number.isInteger(intScore)) {
                        throw new Error(this.lang.score_integer_tip);
                    }
                    scoreDetail.push({
                        id: item.id,
                        score: intScore,
                    });
                    score += intScore;
                });
            });
            return {scoreDetail, score};
        },
        uploadStart(index, j_index, subIndex) {
            if (this.type === CLOUD_TEST_TYPE.EDIT && subIndex === '-1') {
                this.uploadIndex = [index, j_index];
            } else {
                this.uploadIndex = [index, j_index, subIndex];
            }
            // this.uploadIndex = [index, j_index, subIndex];
            this.$refs.uploadComponent[0].value = '';   //处理无法连续上传相同文件
            this.$refs.uploadComponent[0].click();
        },
        handleFileChange(e){
            let files = e.target.files
            let key,uploadList=[]
            for (const file of files) {
                if (file.size > 20 * 1024 * 1024) {
                    this.$message.error(`${this.lang.upload_max_text}20M`)
                    return
                }
                if (file.size == 0) {
                    this.$message.error(`${this.lang.upload_min_text}0M`)
                    return
                }

                // 检查文件格式
                const fileExt = file.name.split('.').pop().toLowerCase()
                // const isImage = file.type.startsWith('image/')
                // const isVideo = file.type.startsWith('video/')

                //Chromeium类浏览器flv识别有问题
                // console.log('fileType',file.type.split('/')[0])
                // console.log('fileExt',fileExt)
                if (!Tool.getSupportVideoType().includes(fileExt) && !Tool.getSupportImageType().includes(fileExt)) {
                    this.$message.error(this.lang.supply_exam_image.err_tip.unsupported_file_format)
                    return
                }
                this.uploadCollectImage(file,this.uploadIndex);
            }
        },
        uploadCollectImage(file,uploadIndex){
            let dir = new Date().getTime() + parseInt(Math.random() * 1000 + 1000, 10)  // 目录
            var date = new Date();
            var time = date.getFullYear()
                + '-'
                + ((date.getMonth() < 9) ? ('0' + (date.getMonth() + 1)) : (date.getMonth() + 1))
                + '-'
                + (date.getDate()  < 10 ? ('0' + date.getDate()) : date.getDate());
            let filePath = `homework/operationImage/${time}/${dir}/${file.name}`
            let uploadConfig = this.systemConfig.serverInfo.file_upload_config;
            //OSS
            uploadFile({
                bucket:uploadConfig.ossInfo.bucket,
                filePath:filePath,
                file,
                callback:(event, data)=>{
                    console.log('uploadFile',event,data)
                    if ("complete" == event) {
                        let url=uploadConfig.ossInfo.playback_https_addr+'/'+filePath
                        const index = uploadIndex[0]
                        const j_index = uploadIndex[1]
                        const subIndex = uploadIndex[2]
                        const msg_type = Tool.getMsgType(filePath);

                        if (this.type === CLOUD_TEST_TYPE.EDIT) {
                            if (uploadIndex.length === 2) {
                                // 题目主体的图片
                                if (!this.examDetail.content[index].list[j_index].imageList) {
                                    this.examDetail.content[index].list[j_index].imageList = [];
                                }
                                this.examDetail.content[index].list[j_index].imageList.push({
                                    msg_type: msg_type,
                                    url: url,
                                });
                            } else {
                                // 子题目的图片
                                if (!this.examDetail.content[index].list[j_index].subTopic[subIndex].imageList) {
                                    this.examDetail.content[index].list[j_index].subTopic[subIndex].imageList = [];
                                }
                                this.examDetail.content[index].list[j_index].subTopic[subIndex].imageList.push({
                                    msg_type: msg_type,
                                    url: url,
                                });
                            }

                            const updatedPaper = cloneDeep(this.$store.state.homework.uploadedPaper);
                            updatedPaper.paperInfo = this.examDetail;
                            this.$store.commit('homework/setUploadPaper', updatedPaper);
                        } else {
                            // 原有上传逻辑
                            this.examDetail.content[index].list[j_index].subTopic[subIndex].value.push({
                                msg_type: msg_type,
                                url: url,
                            });
                        }

                    } else if ("error" == event) {
                        this.$message.error(this.lang.upload_file_error_text)
                    }
                }
            })
        },
        openCollect(topicTypeIndex, topicIndex, subIndex, subtopic){
            try{
                const [index,j_index,subIndex] = this.collectIndex;
                this.examDetail.content[index].list[j_index].subTopic[subIndex].collecting=false;
            }catch(e){} finally{
                this.collectIndex = [topicTypeIndex, topicIndex, subIndex];
                window.vm.$set(subtopic,'collecting',true);
                this.openFileTransfer();
            }
        },
        closeCollect(topicTypeIndex, topicIndex, subIndex, subtopic) {
            this.hasClickedStopCollect = true;
            this.collectIndex = [0, 0, 0];
            window.vm.$set(subtopic, 'collecting', false);
            this.closeFileTransferListener();
        },
        openFileTransfer:async function(){
            const service_type=this.systemConfig.ServiceConfig.type.FileTransferAssistant
            let fileTransferAssistant=await findServiceId(service_type)
            if (fileTransferAssistant.cid) {
                this.openConversation(fileTransferAssistant.cid,10,0,(is_succ,conversation)=>{
                    this.openFileTransferListener(conversation.id)
                });
            }else{
                this.$root.socket.emit("request_start_single_chat_conversation",{
                    list:[fileTransferAssistant.id,this.user.uid],
                    start_type:undefined,
                    mode:this.systemConfig.ConversationConfig.mode.Single,
                    type:this.systemConfig.ConversationConfig.type.Single
                },async(is_succ,data)=>{
                    if (is_succ) {
                        this.$store.commit('conversationList/initConversation',data)
                        this.$store.commit('examList/initExamObj',data)
                        await Tool.handleAfterConversationCreated(data,'openConversation')
                        this.openFileTransferListener(data)
                    }else{
                        this.$message.error(this.lang.start_conversation_error)
                    }
                })
            }
        },
        openFileTransferListener(cid){
            // 监听传输助手消息通知
            const controler = this.conversationList[cid].socket;
            if (!controler) {
                setTimeout(()=>{
                    this.openFileTransferListener(cid);
                },1000)
            }else{
                this.closeFileTransferListener();
                controler.on("other_say",this.collectImage);
                this.fileTransferAssistant = controler;
            }

        },
        closeFileTransferListener(){
            if (this.fileTransferAssistant) {
                this.fileTransferAssistant.off("other_say",this.collectImage)
                this.fileTransferAssistant = null;
            }
        },
        collectImage(messageList){
            console.log('collectImage1',messageList)
            const msg_type = this.systemConfig.msg_type;
            messageList.forEach(async (msg)=>{
                if (msg.msg_type === msg_type.Frame || msg.msg_type === msg_type.Cine || msg.msg_type === msg_type.EXAM_IMAGES || msg.msg_type === msg_type.OBAI) {
                    //只采集单帧，多帧和聚合消息,和产科质控
                    let type =msg.msg_type;
                    if (msg.msg_type === msg_type.EXAM_IMAGES) {
                        type = msg.cover_msg_type;
                    }
                    let downloadUrl = '';
                    if(type === msg_type.Frame) {
                        downloadUrl=msg.url.replace("thumbnail.jpg",`SingleFrame.${msg.img_encode_type}`);
                    }else if (type === msg_type.Cine) {
                        downloadUrl=msg.url.replace("thumbnail.jpg",`DeviceVideo.${msg.img_encode_type}`);
                    }else if (type === msg_type.OBAI) {
                        downloadUrl=msg.url.replace("thumbnail.jpg",`ScreenShot.${msg.img_encode_type}`);
                    }
                    if (downloadUrl) {
                        const response = await fetch(downloadUrl);
                        const blob = await response.blob();
                        const file = new File([blob], `${msg.file_id}.${msg.img_encode_type}`, {
                            type: blob.type, // 你可以从blob中获取MIME类型
                        });
                        this.uploadCollectImage(file,this.collectIndex);
                        console.log('collectImage',file)
                    }
                }
            })
        },
        handleCurrentChange(step){
            this.topicStep=step;
            let topicLength = 0;
            let topicTypeIndex = 0;
            for(let topictype of this.examDetail.content){
                topicLength +=topictype.list.length;
                if (this.topicStep <= topicLength) {
                    this.topicTypeStep = topicTypeIndex+1;
                    break
                }
                topicTypeIndex++;
            }
        },
        checkScore(topic){
            const intScore = Number(topic.correctScore)
            if (isNaN(intScore) || intScore > topic.score || intScore < 0) {
                this.$message.error(this.lang.enter_correct_number)
            }
            if (!Number.isInteger(intScore)) {
                this.$message.error(this.lang.score_integer_tip)
            }
        },

        handleFieldChange(target, field, value) {
            // 如果是author字段且值为空，则使用用户昵称
            if (field === 'author' && !value?.trim()) {
                target[field] = this.user.nickname;
            } else {
                target[field] = value;
            }

            if (field === 'score') {
                this.recalculateScores(target);
            }

            const uploadedPaper = this.$store.state.homework.uploadedPaper;
            let updatedPaper;

            if (!uploadedPaper) {
                updatedPaper = { paperInfo: this.examDetail };
            } else {
                updatedPaper = cloneDeep(uploadedPaper);
                updatedPaper.paperInfo = this.examDetail;
            }

            this.$store.commit('homework/setUploadPaper', updatedPaper);
            console.log('实时数据:', this.$store.state.homework.uploadedPaper);
        },
        handleOptionChange(option, field, value) {
            option[field] = value;
            const uploadedPaper = this.$store.state.homework.uploadedPaper;
            let updatedPaper;

            if (!uploadedPaper) {
                updatedPaper = { paperInfo: this.examDetail };
            } else {
                updatedPaper = cloneDeep(uploadedPaper);
                updatedPaper.paperInfo = this.examDetail;
            }

            this.$store.commit('homework/setUploadPaper', updatedPaper);
        },
        handleSubtopicFieldChange(topic, subtopic, field, value) {
            subtopic[field] = value;
            if (field === 'score') {
                this.recalculateScores(subtopic);
            }
            const uploadedPaper = this.$store.state.homework.uploadedPaper;
            let updatedPaper;

            if (!uploadedPaper) {
                updatedPaper = { paperInfo: this.examDetail };
            } else {
                updatedPaper = cloneDeep(uploadedPaper);
                updatedPaper.paperInfo = this.examDetail;
            }

            this.$store.commit('homework/setUploadPaper', updatedPaper);
        },
        handleUpdateValue(topic, value) {
            topic.value = value;
        },
        handleUpdateCorrectScore(topic, value) {
            topic.correctScore = value;
        },
        deleteTopic(topicTypeIndex, topicIndex, subTopicIndex) {
            const topicType = this.examDetail.content[topicTypeIndex];
            const topic = topicType.list[topicIndex];

            if (typeof subTopicIndex === 'number') {
                // 删除subTopic
                if (topic.subTopic.length <= 1) {
                    this.$message.warning(this.lang.homework.operation_min_subtopic);
                    return;
                }
                topic.subTopic.splice(subTopicIndex, 1);
                topic.score = topic.subTopic.reduce((sum, subTopic) => sum + Number(subTopic.score || 0), 0);
            } else {
                // 删除topic
                topicType.list.splice(topicIndex, 1);
                // 如果该题型下没有题目了，也删除该题型
                if (topicType.list.length === 0) {
                    this.examDetail.content.splice(topicTypeIndex, 1);
                }
                this.examDetail.questionCount = this.examDetail.content.reduce((sum, type) => sum + type.list.length, 0);
            }
            this.recalculateScores();

            const uploadedPaper = this.$store.state.homework.uploadedPaper;
            let updatedPaper;

            if (!uploadedPaper) {
                updatedPaper = { paperInfo: this.examDetail };
            } else {
                updatedPaper = cloneDeep(uploadedPaper);
                updatedPaper.paperInfo = this.examDetail;
            }

            this.$store.commit('homework/setUploadPaper', updatedPaper);
        },
        saveExam() {
            // 基本验证
            if (!this.examDetail.title?.trim()) {
                this.$message.error(this.lang.homework.exam_title_required);
                return;
            }

            // 验证每道题目是否都有分数
            const noScoreTopics = [];

            this.examDetail.content.forEach(topicType => {
                topicType.list.forEach(topic => {
                    if (!topic.score) {
                        noScoreTopics.push(topic.index);
                    }
                });
            });

            if (noScoreTopics.length) {
                this.$message.error(`${this.lang.homework.topic_score_required}：${noScoreTopics.join(', ')}`);
                return;
            }

            const params = {
                author: this.examDetail.author || this.user.nickname,
                title: this.examDetail.title,
                score: this.examDetail.score,
                questionCount: this.examDetail.questionCount,
                content: this.examDetail.content
            };

            service.savePaper(params).then(res => {
                if (res.data.error_code === 0) {
                    this.$message.success(this.lang.homework.save_success);
                    this.$root.eventBus.$emit('refreshPaperList');
                    // 清空vuex中的数据
                    this.$store.commit('homework/setUploadPaper', null);
                    this.back();
                }
            }).catch(() => {
                this.$message.error(this.lang.homework.save_failed);
            });
        },
        recalculateScores(target) {
            // 重新计算各题型总分和题目数
            this.examDetail.content.forEach(topicType => {
                // topicType.totalScore = topicType.list.reduce((sum, topic) => {return sum + Number(topic.score || 0);});
                topicType.totalScore = topicType.list.reduce((sum, topic) => {
                    if (topicType.type === 'operation' && target && target.hasOwnProperty('score') && topic.subTopic.includes(target)) {
                        // 当修改的是实操题的子题目分数时
                        const subTopicTotal = topic.subTopic.reduce((subSum, subTopic) =>
                            subSum + Number(subTopic.score || 0), 0);
                        topic.score = subTopicTotal;
                        return sum + subTopicTotal;
                    } else if (topicType.type === 'operation' && target === topic) {
                        // 当修改的是实操题总分时，清空所有子题目分数
                        topic.subTopic.forEach(subTopic => {
                            subTopic.score = null;
                        });
                        return sum + Number(topic.score || 0);
                    } else {
                        return sum + Number(topic.score || 0);
                    }
                }, 0);
                topicType.count = topicType.list.length;
            });

            // 重新计算总分
            this.examDetail.score = this.examDetail.content.reduce((sum, type) => sum + Number(type.totalScore), 0);
        },
        deletePaper() {
            this.$confirm(
                this.lang.homework.confirm_delete_paper,
                this.lang.tip_title,
                {
                    confirmButtonText: this.lang.confirm_btn,
                    cancelButtonText: this.lang.cancel_btn,
                    type: 'warning'
                }
            ).then(() => {
                service.deletePaper({
                    paperID: this.paperId
                }).then(res => {
                    if (res.data.error_code === 0) {
                        this.$message.success(this.lang.homework.delete_success);
                        this.$root.eventBus.$emit('refreshPaperList');
                        this.back();
                    }
                }).catch(err => {
                    this.$message.error(this.lang.homework.delete_failed);
                });
            }).catch(() => {});
        },
        // 添加辅助方法来根据索引查找题目
        findTopicByIndex(index) {
            let currentIndex = 0;
            for (const topicType of this.examDetail.content) {
                for (const topic of topicType.list) {
                    if (currentIndex === index) {
                        return {
                            ...topic,
                            type: topicType.type
                        };
                    }
                    currentIndex++;
                }
            }
            return null;
        },
        jumpToQuestion(index) {
            const topicContent = this.$refs.topicContent;
            if (topicContent) {
                const questions = topicContent.querySelectorAll('.topic_detail');
                if (questions[index]) {
                    questions[index].scrollIntoView({ behavior: "smooth", block: "start" });
                    // 同时更新当前选中题号
                    this.currentQuestionIndex = index;
                }
            }
        },
        setCurrentQuestionByTypeIndex(typeIndex, topicIndex) {
            let index = 0;
            for (let i = 0; i < typeIndex; i++) {
                index += this.examDetail.content[i].list.length;
            }
            index += topicIndex;
            this.currentQuestionIndex = index;
        },
        formatAnswer(answer, type) {
            switch (type) {
            case "singleSelect":
                return answer;
            case "multiSelect":
                return Array.isArray(answer) ? answer.join('、') : answer;
            default:
                return answer;
            }
        },
        handleImageOrderChange(imageList, evt) {
            const { oldIndex, newIndex } = evt;
            if (oldIndex !== newIndex) {
                // 手动执行数组元素的移动操作
                const itemToMove = imageList[oldIndex];
                // 从数组中移除
                imageList.splice(oldIndex, 1);
                // 在新位置插入
                imageList.splice(newIndex, 0, itemToMove);

                // 更新 vuex 中的数据
                const uploadedPaper = this.$store.state.homework.uploadedPaper;
                let updatedPaper;

                if (!uploadedPaper) {
                    updatedPaper = { paperInfo: this.examDetail };
                } else {
                    updatedPaper = cloneDeep(uploadedPaper);
                    updatedPaper.paperInfo = this.examDetail;
                }

                this.$store.commit('homework/setUploadPaper', updatedPaper);
            } else {
                console.log('NO CHANGE');
            }
        }
    },
}

</script>
<style lang="scss">
.cloud_exam_detail{
    .exam_detail_left {
        flex-wrap: wrap;

        .exam_detail_author{
            .el-input{
                width: 150px;
            }

            .text-editor {
                width: 150px;
            }
        }

        .student_info {
            width: 100%;
            margin-top: 10px;
            display: flex;
            align-items: center;

            .el-input{
                margin-left: 10px;
                width: 120px;
                font-size: 15px;
                input{
                    padding: 0 10px;
                    text-align: center;
                }
            }
        }
    }

    .topic_content{
        flex:1;
        overflow: auto;
        background: #ebeff2;
        padding: 20px;
        border-radius: 6px;

        .exam-title-wrapper {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 6px;
            padding: 10px 120px 10px 20px;
            display: flex;
            align-items: center;

            .title-tip-label {
                margin-right: 4px;
                white-space: nowrap;
            }

            .text-editor {
                font-size: 18px;
                font-weight: bold;
                flex: 1;
            }

            .editable-content {
                input {
                    font-size: 18px;
                    font-weight: bold;
                }
            }
        }
    }
    .topic_summary{
        margin-bottom: 10px;
        line-height: 2.5;
        font-size: 16px;
        font-weight: bold;
    }
    .topic_tip{
        color:#000;
        margin-bottom: 10px;
        background: #fff7ec;
        padding: 14px 20px;
        border-radius: 6px;
        font-weight: bold;
        font-size: 15px;
    }
    .topic_detail{
        position: relative;
        font-size: 16px;
        background-color: #fff;
        border-radius: 6px;
        padding: 20px;
        margin: 30px 0;
    }
    div.topic_detail:last-of-type{
        border-bottom:none;
    }

    .content-container {
        display: flex;
        gap: 20px;
        align-items: flex-start; /* 防止子项在交叉轴上拉伸 */
    }

    .topic_list {
        flex: 1;
    }

    .topic_content .progress-indicator {
        position: sticky;
        bottom: 120px;
        right: 1%;
        align-self: flex-start;
        margin: 0 1%;
        z-index: 10;
    }
}
</style>
