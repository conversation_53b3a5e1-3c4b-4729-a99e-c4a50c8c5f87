import api from './api'
// const getBaseUrl=()=>{
//     let systemConfig=window.vm.$store.state.systemConfig;
//     let ajaxServer=systemConfig.server_type.protocol+systemConfig.server_type.host+systemConfig.server_type.port;
//     return ajaxServer;
// }
const v2InterfaceFactory=(method,data)=>{
    return api
        .post('/v2/api',{
            data:{
                method:method,
                bizContent:data
            }
        })
        .then(res=>{
            return res
        },(error)=>{
            console.log('error',error)
            return error
        })
}
const interfaceList = [
    'login',
    'logout',
    'query_login_config',
    'modify_personal_fancy',
    'AdminLogin',
    'query_history_version',
    'device_identify',
    'query_international_code_list',
    'AdminRegisterApplyQuery',
    'AdminRegisterApplyVerify',
    'AdminUserManageQuery',
    'AdminUserManageSetUserRole',
    'AdminUserManageSetUserStatus',
    'AdminUserManageResetUserPassword',
    'AdminHospitalManageQuery',
    'AdminHospitalManageAddHospital',
    'AdminHospitalManageUpdateHospital',
    'AdminHospitalManageDeleteHospital',
];
const v2InterfaceList = {
    'getImageCode':'verify.code.get.img.code',
    'encryptPassword':'user.encrypt.pwd',
    'getLoginToken':'user.get.login.token',
    'getVerityCodeByToken':'verify.code.send.by.login.token',
    'loginByToken':'user.login.by.token',
    'registerV2':'user.register',
    'getVerityCodeToMobile':'verify.code.send.by.mobile',
    'getVerityCodeToEmail':'verify.code.send.by.email',
    'resetPasswordByCode':'user.change.password.by.code',
    'loginAndBindAccount':'user.login.and.bind.account.by.token',
    'logoff':'user.destroy.by.token',
    'checkAccount':'user.exist.status',
    'getLoginTokenByQrCode':'user.get.login.token.by.qrcode',
    'getLoginQrCodeId':'user.create.login.qrcode',
    'getLoginTokenByCode':'user.get.login.token.by.code',
    'getWebLiveInfo':'live.broadcast.visit',
    'bindReferralCode':'user.bind.referral.code',
    'safeAuth':'user.get.safe.auth',
    'bindSocialAccount':'user.bind.social.account',
    'updateLoginName':'user.update.login.name',
    'loginByTraceless':'verify.code.get.aliyun.afs.code',
    'searchOrganization':'organization.search',
    'searchHospital': 'hospital.search',
    'createOrganization':'organization.create',
    'getAllTags':'tag.get.user.all.tags',
    'addFavorite':'resource.user.add.favorite',
    'cancelFavorite':'resource.user.cancel.favorite',
    'getSpriteImageListByResourceId':'resource.get.spriteImageList.by.id',//通过资源id获取视频雪碧图
    'updatePublicStatus':'resource.update.public',//通过资源id修改是否公开
    'getUserFavorite':'resource.get.user.favorite',//获取好友收藏列表
    'resourceVideoClip':'resource.video.clip',//视频云剪辑接口
    'exportResourceVideoClip':'resource.video.clip.export',//视频云剪辑接口--导出
    'getVideoClipProjectId':'resource.video.clip.get.projectId',//视频云剪辑接口--获取projectId
    'checkActionPermissions':'user.check.action.permissions',//检查操作权限 {action:'conference.update.recording',businessData:{}}
    'likeResourceAction':'resource.user.like',//点赞资源
    'unLikeResourceAction':'resource.user.unlike',//取消点赞资源
    'getConfigAnnouncement':'config.announcement',//获取服务器的下发的紧急通知
    'getAiAnalyzeTypes':'ai.analyze.get.types',//ai配置信息获取
    'requestAiAnalyzeWithUrl':'ai.analyze.with.url',//根据url条件参数获取数据--以图搜图
    'requestStartAiAnalyze':'ai.analyze.start.analyze',//请求ai分析
    'getReportInfo':'resource.report.info',//获取报告信息
    'setReportInfo':'resource.report.set',//保存报告信息
    'editReportInfo':'resource.report.edit',//编辑报告信息
    'getResourceDetail':'resource.details',//资源详细信息
    'updatePatientExternalId':'patient.update.external_id',//更新多中心病人id
    'startAIAnalyzeByLive':'ai.analyze.start.agora.live.analyze.mq',//直播时，打开实时分析
    'stopAIAnalyzeByLive':'ai.analyze.stop.live.analyze.mq',//直播时，关闭实时分析
    'getPaperList':'paper.list',//我的试卷
    'checkIsTeacher':'paper.is.teacher',//判断作业模块是否老师
    'getPaperDetail':'paper.detail',//获取试卷详情
    'sharePaper':'paper.share',//分享试卷
    'assignmentPaper':'paper.assignment.to.group',//布置作业
    'getIncompleteList':'paper.unfinished.answer.list',//待完成列表
    'getAssignmentList':'paper.assignment.list.by.uid',//我布置的列表
    'getFinishedList':'paper.finished.sheet.list',//我完成的列表
    'getCorrectingList':'paper.assignment.list.by.teacher',//待批改的列表
    'getanswerSheetDetail':'paper.answer.sheet.detail',//获取答题卡详情
    'getanswerSheetList':'paper.answer.sheet.list.by.assignmentID',//获取答题卡列表
    'submitAnswer':'paper.answer.sheet.student.submit',// 提交答题卡
    'submitCorrect':'paper.answer.sheet.teacher.submit',// 提交批改
    'lockAnswerSheet':'paper.answer.sheet.keep.lock',// 锁定批改状态
    'unlockAnswerSheet':'paper.answer.sheet.unlock',// 解锁批改状态
    'getDepartmentByName': 'hospital.department.get.list', // 查询医院科室
    'getHomeworkSummary': 'paper.assignment.summary', // 查询作业统计
    'transmitHomework': 'paper.assignment.transmit', // 转发作业
    'getanswerSheetByAssignmentID':'paper.answer.sheet.by.assignmentID',// 查询是否有权限查看作业
    //'getAssignmentInfo':'paper.assignment.info',// 查询作业详情
    'exportHomework':'paper.answer.sheet.export',// 导出作业统计
    'updateHomework':'paper.assignment.update',// 修改考试
    'revokeHomework':'paper.assignment.delete',// 作废作业
    'savePaper':'paper.create',// 保存试卷
    'deletePaper':'paper.delete',// 删除试卷
    'getUncorrectedList':'paper.teacher.todo.answer.list',//获取待批改列表数量
    'getNewDeviceFailure':'equipment.server.get.new.alarm.list',//获取未处理设备故障
    'getAllDeviceFailure':'equipment.server.get.all.alarm.list',//获取某设备的所有故障
    'resolveDeviceFailure':'equipment.server.update.alarm.resolve',//处理某设备的故障
    'getPacsConsultationInfo':'association.pacs.get.consultation.info',//解析PACS关联会话数据
    'createAiConversation':'aiConversation.create',//创建ai会话
    'askAiQuestion': 'aiConversation.ask',//ai会话提问
    'stopAskAiQuestion': 'aiConversation.stop.ask',//停止ai会话
    'deleteAiConversation': 'aiConversation.delete',//删除AI会话记录
    'getRecentConversationList': 'aiConversation.recent.list',//获取ai会话列表
    'getConversationHistory': 'aiConversation.history',//获取ai会话历史
    'getAiPracticeCase': 'aiBook.get.case',//获取ai练习案例
    'submitAiPracticeCaseAnswer': 'aiBook.test.submit',//提交练习答案
    'getAiPracticeStatistics':'aiBook.user.statistics',//获取练习统计数据
    'getAiPracticeTestList': 'aiBook.test.list',//获取练习测试列表
    'getAiPracticeTestDetail': 'aiBook.case.test.detail',//获取练习测试详情
    'getIworksDetail': 'exam.get.iworks.protocol',//获取iworks详情
    'getTrainingCountryList': 'training.country.list',//获取智教培国家
    'getTrainingList':'training.list',//获取智教培考试列表
    'getTrainingStudentInfoByUuid':'training.student.info.by.uuid',//获取智教培学生信息
    'applyTrainingStudent':'training.student.apply',//提交智教培学生认证信息
    'getTestListByTrainingId':'training.student.test.list',//获取智教培学生考核列表
    'getTrainingTestInfoByTestId':'training.student.get.testInfo',//获取智教培学生考核详情
    'getTrainingTestAnswerHistory':'training.student.answer.history',//获取智教培学生考核作答历史
    'getTrainingStudentInfo':'training.student.info',//获取智教培学生考核详情
    'submitTrainingTestAnswer':'training.student.submit.answer',//在线答题 提交作答
    'submitTrainingTestFile':'training.student.submit.file',//附件上传 提交作答文件
};
const interfaceFactory = (method,data) => {
    return api
        .post(method,{
            data:data
        })
        .then(res=>{
            return res
        },(error)=>{
            console.log('error',error)
            throw error
        })
}
const service={
    uploadLiveCoverImage:(formData)=>{
        let config={
            headers:{
                'Content-Type':'multipart/form-data',
            },
        }
        return api
            .post('/v2/upload/liveCoverImg',formData,config)
            .then(res=>{
                return res
            },(error)=>{
                console.log('error',error)
                return error
            })
    },
    getChannelInfoByVisitor:(url,data)=>{
        return api
            .post(url+'/v2/api/visitorToken',{
                data
            })
            .then(res=>{
                return res
            },(error)=>{
                console.log('error',error)
                return error
            })
    },
    getRegionFunctions:(data)=>{
        return api
            .get('/v2/permission',{
                params:data
            })
            .then(res=>{
                return res
            },(error)=>{
                console.log('error',error)
                return error
            })
    },
    getCurrentSubscribeUidList:(url,data)=>{
        return api
            .get(url+'/v2/api/getCurrentSubscribeUidList',{
                data
            })
            .then(res=>{
                return res
            },(error)=>{
                console.log('error',error)
                return error
            })
    },
    getBuildTime:(data)=>{
        return api
            .get('/buildTime',{
                params:data
            })
            .then(res=>{
                return res
            },(error)=>{
                console.log('error',error)
                return error
            })
    },
    createPaper(data) {
        return api.post('/api/paper/create', data)
    },
}
interfaceList.forEach(api=>{
    service[api] = (data)=>{
        return interfaceFactory(`/${api}`,data);
    }
})
for(let key in v2InterfaceList){
    service[key] = (data)=>{
        return v2InterfaceFactory(v2InterfaceList[key],data);
    }
}
export default service
