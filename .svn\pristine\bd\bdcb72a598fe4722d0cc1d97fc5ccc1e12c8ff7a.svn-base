import Vue from 'vue'
import Tool from '@/common/tool'
import VueRouter from 'vue-router'
import Root from '../pages/root.vue'
import Login from '../pages/login.vue'
import Index from '../pages/index.vue'

import AddFriend from '../pages/addFriend.vue'
// const AddFriend = resolve => require(['../pages/addFriend.vue'], resolve)
import GalleryController from '../pages/galleryController.vue'
import GroupSetting from '../pages/groupSetting.vue'
// const GroupSetting = resolve => require(['../pages/groupSetting.vue'], resolve)
import AddAttendee from '../pages/addAttendee.vue'
// const AddAttendee = resolve => require(['../pages/addAttendee.vue'], resolve)
import DeleteAttendee from '../pages/deleteAttendee.vue'
// const DeleteAttendee = resolve => require(['../pages/deleteAttendee.vue'], resolve)
import Applys from '../pages/applys.vue'
// const Applys = resolve => require(['../pages/applys.vue'], resolve)
import SearchGroup from '../pages/searchGroup.vue'
// const SearchGroup = resolve => require(['../pages/searchGroup.vue'], resolve)
import AddGroup from '../pages/addGroup.vue'
// const AddGroup = resolve => require(['../pages/addGroup.vue'], resolve)
//废弃组件 import International from '../pages/international.vue'
import PersonalSetting from '../pages/personalSetting.vue'
// const PersonalSetting = resolve => require(['../pages/personalSetting.vue'], resolve)
import Favorites from '../pages/favorites.vue'
// const Favorites = resolve => require(['../pages/favorites.vue'], resolve)
import VisitingCard from '../pages/visitingCard.vue'
// import CloudStatistics from '../pages/cloudStatistics.vue'
const CloudStatistics = () => import(/* webpackPrefetch: true */ '../pages/cloudStatistics.vue')
import VersionInfo from '../pages/versionInfo.vue'
// const VersionInfo = resolve => require(['../pages/versionInfo.vue'], resolve)
// import ScanRoomManage from '../pages/scanRoomManage.vue'
const ScanRoomManage = () => import(/* webpackPrefetch: true */ '../pages/scanRoomManage.vue')
// import ExportFile from '../pages/exportFile.vue'
const ExportFile = () => import(/* webpackPrefetch: true */ '../pages/exportFile.vue')
const TVWallWeb = () => import(/* webpackPrefetch: true */ '../pages/tvWallAgoraWeb.vue')
// import RegisterScanroom from '../pages/registerScanroom.vue'
const RegisterScanroom = () => import(/* webpackPrefetch: true */ '../pages/registerScanroom.vue')
// import EquipmentDetection from '../pages/equipmentDetection.vue'
const EquipmentDetection = () => import(/* webpackPrefetch: true */ '../pages/equipmentDetection.vue')
// import BackgroundManage from '../pages/backgroundManage.vue'
const BackgroundManage = () => import(/* webpackPrefetch: true */ '../pages/backgroundManage.vue')
import Conference from '../pages/conference.vue'
// import Report from '../pages/report.vue'
const reportControler = () => import(/* webpackPrefetch: true */ '../pages/reportControler.vue')
// import Library from '../pages/library.vue'
const Library = () => import(/* webpackPrefetch: true */ '../pages/library.vue')
import SystemSetting from '../pages/systemSetting.vue'
// const SystemSetting = resolve => require(['../pages/systemSetting.vue'], resolve)
import TransferGroup from '../pages/transferGroup.vue'
// const TransferGroup = resolve => require(['../pages/transferGroup.vue'], resolve)
import ResetMobile from '../pages/resetMobile.vue'
// const ResetMobile = resolve => require(['../pages/resetMobile.vue'], resolve)
const ResetLoginName = () => import(/* webpackPrefetch: true */ '../pages/resetLoginName.vue')
// import TvWallSetting from '../pages/tvWallSetting.vue'
const TvWallSetting = () => import(/* webpackPrefetch: true */ '../pages/tvWallSetting.vue')
const ReservedConference = () => import(/* webpackPrefetch: true */ '../pages/reservedConference.vue')
const ExamManager = () => import(/* webpackPrefetch: true */ '../pages/examManager.vue')
const NewExam = () => import(/* webpackPrefetch: true */ '../pages/newExam.vue')
const ImportExamImage = () => import(/* webpackPrefetch: true */ '../pages/importExamImage.vue')
const GroupSetWall = () => import(/* webpackPrefetch: true */ '../pages/groupsetWall.vue')
const ChatHistorySearchList = () => import(/* webpackPrefetch: true */ '../pages/chatHistorySearchList.vue')
// const chatHistoryWindow = resolve => require(['../pages/chatHistoryWindow.vue'], resolve)
const TaskManager = () => import(/* webpackPrefetch: true */ '../pages/taskManager.vue')
const InviteRegistration = () => import(/* webpackPrefetch: true */ '../pages/inviteRegistration.vue')
const Clip = () => import(/* webpackPrefetch: true */ '../pages/clip.vue')
// const EditGroupset = resolve => require(['../pages/editGroupset.vue'], resolve)
import EditGroupset from '../pages/editGroupset.vue'
import EditGroupsetManager from '../pages/editGroupsetManager.vue'

const CaseDatabase = () => import(/* webpackPrefetch: true */ '../pages/caseDatabase.vue')
const WebLive = () => import(/* webpackPrefetch: true */ '../pages/webLive.vue')
const GroupsetAvatar = () => import(/* webpackPrefetch: true */ '../pages/groupsetAvatar.vue')
const MultiCenter = () => import(/* webpackPrefetch: true */ '../pages/multicenter.vue')
const HFRMCAdmin = () => import(/* webpackPrefetch: true */ '../pages/HFRMulticenter/admin/index.vue')
const HFRMCUserManagement = () => import(/* webpackPrefetch: true */ '../pages/HFRMulticenter/admin/UserManagement.vue')
const HFRMCDataView = () => import(/* webpackPrefetch: true */ '../pages/HFRMulticenter/admin/dataView.vue')
const HFRMCAssignment = () => import(/* webpackPrefetch: true */ '../pages/HFRMulticenter/assignment/index.vue')
const MCAnnotation = () => import(/* webpackPrefetch: true */ '../pages/HFRMulticenter/annotation/index.vue')
const MCJudge = () => import(/* webpackPrefetch: true */ '../pages/HFRMulticenter/judge/index.vue')
const HFRMCNormalAdmin = () => import(/* webpackPrefetch: true */ '../pages/HFRMulticenter/admin/normalAdmin.vue')
const ThyroidMCAdmin = () => import(/* webpackPrefetch: true */ '../pages/thyroidMulticenter/admin/index.vue')
const ThyroidMCUserManagement = () => import(/* webpackPrefetch: true */ '../pages/thyroidMulticenter/admin/UserManagement.vue')
const ThyroidMCDataView = () => import(/* webpackPrefetch: true */ '../pages/thyroidMulticenter/admin/dataView.vue')
const ThyroidMCNormalAdmin = () => import(/* webpackPrefetch: true */ '../pages/thyroidMulticenter/admin/normalAdmin.vue')
const ThyroidMCAssignment = () => import(/* webpackPrefetch: true */ '../pages/thyroidMulticenter/assignment/index.vue')
const MyocardialMCAdmin = () => import(/* webpackPrefetch: true */ '../pages/myocardialMulticenter/admin/index.vue')
const MyocardialMCUserManagement = () => import(/* webpackPrefetch: true */ '../pages/myocardialMulticenter/admin/UserManagement.vue')
const MyocardialMCDataView = () => import(/* webpackPrefetch: true */ '../pages/myocardialMulticenter/admin/dataView.vue')
const MyocardialMCNormalAdmin = () => import(/* webpackPrefetch: true */ '../pages/myocardialMulticenter/admin/normalAdmin.vue')
const MyocardialMCAssignment = () => import(/* webpackPrefetch: true */ '../pages/myocardialMulticenter/assignment/index.vue')
// const GroupsetSetting = resolve => require(['../pages/groupsetSetting.vue'], resolve)
import GroupsetSetting from '../pages/groupsetSetting.vue'
import GroupsetManager from '../pages/groupsetManager.vue'
const EditOrganization = () => import(/* webpackPrefetch: true */ '../pages/editOrganization.vue')
// const PublicFavorite = resolve => require(['../pages/publicFavorite.vue'], resolve)
import PublicFavorite from '../pages/publicFavorite.vue'
const CloudVideoEditChild = () => import(/* webpackPrefetch: true */ '../pages/cloudVideoEdit/cloudVideoEditChild.vue')
const obstetricQCMCAdmin = () => import(/* webpackPrefetch: true */ '../pages/obstetricQCMulticenter/admin/index.vue')
const obstetricQCMCUserManagement = () => import(/* webpackPrefetch: true */ '../pages/obstetricQCMulticenter/admin/UserManagement.vue')
const obstetricQCMCDataView = () => import(/* webpackPrefetch: true */ '../pages/obstetricQCMulticenter/admin/dataView.vue')
const obstetricQCMCPresetDataView = () => import(/* webpackPrefetch: true */ '../pages/obstetricQCMulticenter/admin/presetData.vue')
const obstetricQCMCQuestionFeedback = () => import(/* webpackPrefetch: true */ '../pages/obstetricQCMulticenter/admin/questionFeedback.vue')
// const obstetricQCMCNormalAdmin = resolve=>require(['../pages/thyroidMulticenter/admin/normalAdmin.vue'],resolve)

// const obstetricQCMCNormalAdmin = resolve=>require(['../pages/obstetricQCMulticenter/admin/normalAdmin.vue'],resolve)
// const obstetricQCMCAssignment = resolve=>require(['../pages/obstetricQCMulticenter/assignment/index.vue'],resolve)
// const GroupManage = resolve=>require(['../pages/groupManage.vue'],resolve)
// const GroupManagers = resolve=>require(['../pages/groupManagers.vue'],resolve)
// const EditGroupManager = resolve=>require(['../pages/editGroupManager.vue'],resolve)
// const GroupJoinVerify = resolve=>require(['../pages/groupJoinVerify.vue'],resolve)
import GroupManage from '../pages/groupManage.vue'
import GroupManagers from '../pages/groupManagers.vue'
import EditGroupManager from '../pages/editGroupManager.vue'
import GroupJoinVerify from '../pages/groupJoinVerify.vue'
// const ExamImageList = resolve => require(['../pages/examImageList.vue'], resolve)
import DrAiStatistics from '../pages/drAnalyzeStatistics.vue'

import ReferralCode from '../pages/referralCode.vue'
const GenericMulticenterCreate = () => import(/* webpackPrefetch: true */ '../pages/genericMulticenter/admin/create.vue')
const GenericMulticenterSetting = () => import(/* webpackPrefetch: true */ '../pages/genericMulticenter/admin/setting.vue')
const GenericMCAdmin = () => import(/* webpackPrefetch: true */ '../pages/genericMulticenter/admin/index.vue')
const GenericMCUserManagement = () => import(/* webpackPrefetch: true */ '../pages/genericMulticenter/admin/UserManagement.vue')
const GenericMCDataView = () => import(/* webpackPrefetch: true */ '../pages/genericMulticenter/admin/dataView.vue')
const GenericMCNormalAdmin = () => import(/* webpackPrefetch: true */ '../pages/genericMulticenter/admin/normalAdmin.vue')
const GenericMCAssignment = () => import(/* webpackPrefetch: true */ '../pages/genericMulticenter/assignment/index.vue')
const CloudExam = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/cloudExam.vue')
const CloudExamDetail = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/exam.vue')
const CloudExamCorrecting = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/correctingExam.vue')
const CloudExamSetting = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/examSetting.vue')
const ExamStatistics = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/examStatistics.vue')
const PracticeOverview = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/practiceOverview.vue')
const PracticeHistory = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/practiceHistory.vue')
const PracticeDetail = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/practiceDetail.vue')
const PracticeAnswerAction = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/practiceAnswerAction.vue')
const AiMain = ()=> import(/* webpackPrefetch: true */ '../pages/aiChat/aiMain.vue')
const AiChat = ()=> import(/* webpackPrefetch: true */ '../pages/aiChat/aiChat.vue')
const UltrasoundReportQCIndex = ()=> import(/* webpackPrefetch: true */ '../pages/aiChat/ultrasoundReportQC/index.vue')
const UltrasoundReportQCReportDetail = ()=> import(/* webpackPrefetch: true */ '../pages/aiChat/ultrasoundReportQC/ReportDetail.vue')
const UltrasoundReportQCAdminOverview = ()=> import(/* webpackPrefetch: true */ '../pages/aiChat/ultrasoundReportQC/adminOverview.vue')
const UltrasoundReportQCChartOverview = ()=> import(/* webpackPrefetch: true */ '../pages/aiChat/ultrasoundReportQC/chartOverview.vue')
const SmartTechTraining = ()=> import(/* webpackPrefetch: true */ '../pages/smartTechTraining/index.vue')
const TrainingMain = ()=> import(/* webpackPrefetch: true */ '../pages/smartTechTraining/trainingMain.vue')
const ExamProject = ()=> import(/* webpackPrefetch: true */ '../pages/smartTechTraining/examProject.vue')
const UploadTestOverview = ()=> import(/* webpackPrefetch: true */ '../pages/smartTechTraining/uploadTestOverview.vue')
const OnlineTestOverview = ()=> import(/* webpackPrefetch: true */ '../pages/smartTechTraining/onlineTestOverview.vue')
const CorrectingTestOverview = ()=> import(/* webpackPrefetch: true */ '../pages/smartTechTraining/correctingTestOverview.vue')
const SmartTechTrainingExam = ()=> import(/* webpackPrefetch: true */ '../pages/smartTechTraining/exam.vue')
const SmartTechTrainingStudentOverview = ()=> import(/* webpackPrefetch: true */ '../pages/smartTechTraining/studentOverview.vue')
const SmartTechTrainingGradingReview = ()=> import(/* webpackPrefetch: true */ '../pages/smartTechTraining/gradingReview.vue')
const SmartTechTrainingStudentManagement = ()=> import(/* webpackPrefetch: true */ '../pages/smartTechTraining/studentManagement.vue')
const SmartTechTrainingSupervisorManagement = ()=> import(/* webpackPrefetch: true */ '../pages/smartTechTraining/supervisorManagement.vue')
import PacsLogin from '../pages/pacsLogin.vue'
import Init from '../pages/init.vue'
Vue.use(VueRouter)
const whiteList = ['/login','/webLive','/cloudVideoEditChild','pacs_login']
const cloudExamPage = {
    path:'cloud_exam',
    name:'cloud_exam',
    component:CloudExam,
    children:[
        {
            path:'exam/:type/:id',
            name:'cloud_exam_detail',
            component:CloudExamDetail,
            children:[
                {
                    path:'exam_setting/:group_id',
                    name:'exam_setting',
                    component:CloudExamSetting
                }
            ]
        },
        {
            path:'correcting_exam/:role',
            name:'correcting_exam',
            component:CloudExamCorrecting,
            children:[
                {
                    path:'exam/:type/:id',
                    name:'cloud_exam_detail',
                    component:CloudExamDetail
                },
                {
                    path:'exam_statistics/:id',
                    name:'exam_statistics',
                    component:ExamStatistics
                },
                {
                    path:'exam_setting',
                    name:'exam_setting',
                    component:CloudExamSetting
                }
            ]
        },
        {
            path:'exam_statistics/:id',
            name:'exam_statistics',
            component:ExamStatistics
        }
    ]
}
const globalDialogRouters = [
    {
        path:'search_group',
        name:'search_group',
        component:SearchGroup,
    },
    {
        path:'version_info',
        name:'version_info',
        component:VersionInfo,
    },
    {
        path:"invite_registration",
        name:"invite_registration",
        component:InviteRegistration
    },
    {
        path:'system_setting',
        name:'system_setting',
        component:SystemSetting,
    },
    {
        path:'case_database',
        name:'case_database',
        component:CaseDatabase
    },
    {
        path:'add_group',
        name:'add_group',
        component:AddGroup,
    },
    {
        path:'add_friend',
        name:'addFriend',
        component:AddFriend,
        children:[
            {
                path:'visiting_card',
                name:'visiting_card',
                component:VisitingCard,
            },
        ]
    },
    {
        path:'personal_setting',
        name:'personal_setting',
        component:PersonalSetting,
        children:[
            {
                path:'reset_mobile/:type',
                name:'reset_mobile',
                component:ResetMobile,
            },
            {
                path:'reset_login_name',
                name:'reset_login_name',
                component:ResetLoginName,
            },
            {
                path:'edit_organization',
                component:EditOrganization
            }
        ]
    },
    {
        path:"multicenter",
        name:"multiCenter",
        component:MultiCenter,
        children:[
            {
                path:"hfr_multicenter/admin",
                name:"admin",
                component:HFRMCAdmin,
                children:[
                    {
                        path:'user_management',
                        name:'user_management',
                        component:HFRMCUserManagement
                    },
                    {
                        path:'data_view',
                        name:'data_view',
                        component:HFRMCDataView
                    }
                ]
            },
            {
                path:"hfr_multicenter/assignment",
                name:"assignment",
                component:HFRMCAssignment,
            },
            {
                path:"hfr_multicenter/annotation",
                name:"annotation",
                component:MCAnnotation,
            },
            {
                path:"hfr_multicenter/judge",
                name:"judge",
                component:MCJudge,
            },
            {
                path:"hfr_multicenter/normal_admin",
                name:"normal_admin",
                component:HFRMCNormalAdmin,
            },
            {
                path:"thyroid_multicenter/admin",
                name:"admin",
                component:ThyroidMCAdmin,
                children:[
                    {
                        path:'user_management',
                        name:'user_management',
                        component:ThyroidMCUserManagement
                    },
                    {
                        path:'data_view',
                        name:'data_view',
                        component:ThyroidMCDataView
                    }
                ]
            },
            {
                path:"thyroid_multicenter/normal_admin",
                name:"normal_admin",
                component:ThyroidMCNormalAdmin,
            },
            {
                path:"thyroid_multicenter/assignment",
                name:"assignment",
                component:ThyroidMCAssignment,
            },
            {
                path:"myocardial_strain_multicenter/admin",
                name:"admin",
                component:MyocardialMCAdmin,
                children:[
                    {
                        path:'user_management',
                        name:'user_management',
                        component:MyocardialMCUserManagement
                    },
                    {
                        path:'data_view',
                        name:'data_view',
                        component:MyocardialMCDataView
                    }
                ]
            },
            {
                path:"myocardial_strain_multicenter/normal_admin",
                name:"normal_admin",
                component:MyocardialMCNormalAdmin,
            },
            {
                path:"myocardial_strain_multicenter/assignment",
                name:"assignment",
                component:MyocardialMCAssignment,
            },
            {
                path:"obstetric_qc_multicenter/admin",
                name:"admin",
                component:obstetricQCMCAdmin,
                children:[
                    {
                        path:'user_management',
                        name:'user_management',
                        component:obstetricQCMCUserManagement
                    },
                    {
                        path:'data_view',
                        name:'data_view',
                        component:obstetricQCMCDataView
                    },
                    {
                        path: 'preset_data',
                        name: 'preset_data',
                        component: obstetricQCMCPresetDataView,

                    },
                    {
                        path: 'question_feedback',
                        name: 'question_feedback',
                        component: obstetricQCMCQuestionFeedback,

                    },

                ]
            },
            {
                path:"obstetric_qc_multicenter/normal_admin",
                name:"normal_admin",
                component:obstetricQCMCDataView,
            },
            {
                path:"generic_multicenter/create/:type",
                name:"create_generic_multicenter",
                component:GenericMulticenterCreate,
            },
            {
                path:"generic_multicenter/setting",
                name:"generic_multicenter_setting",
                component:GenericMulticenterSetting,
            },
            {
                path:"generic_multicenter/admin",
                name:"generic_multicenter_admin",
                component:GenericMCAdmin,
                children:[
                    {
                        path:'user_management',
                        name:'generic_multicenter_user_management',
                        component:GenericMCUserManagement
                    },
                    {
                        path:'data_view',
                        name:'generic_multicenter_data_view',
                        component:GenericMCDataView
                    },
                    {
                        path:"setting_management",
                        name:"generic_multicenter_setting",
                        component:GenericMulticenterSetting,
                    },
                ]
            },
            {
                path:"generic_multicenter/assignment",
                name:"generic_multicenter_assignment",
                component:GenericMCAssignment,
            },
            {
                path:"generic_multicenter/normal_admin",
                name:"generic_multicenter_normal_admin",
                component:GenericMCNormalAdmin,
            },
            // const obstetricQCMCAdmin = resolve=>require(['../pages/obstetricQCMulticenter/admin/index.vue'],resolve)
            // const obstetricQCMCUserManagement = resolve=>require(['../pages/obstetricQCMulticenter/admin/UserManagement.vue'],resolve)
            // const obstetricQCMCDataView = resolve=>require(['../pages/obstetricQCMulticenter/admin/dataView.vue'],resolve)
            // const obstetricQCMCNormalAdmin = resolve=>require(['../pages/obstetricQCMulticenter/admin/normalAdmin.vue'],resolve)
            // const obstetricQCMCAssignment = resolve=>require(['../pages/obstetricQCMulticenter/assignment/index.vue'],resolve)
        ]
    },
    {
        path:'favorites',
        name:'favorites',
        component:Favorites,
        children:[
            {
                path:'gallery',
                name:'favorites_gallery',
                component:GalleryController,
                meta:{
                    noneComment:true,
                    noneChat:true,
                    fromFavorites:true
                }

            },
            {
                path:"clip",
                name:"clip",
                component:Clip
            }
        ]
    },
    cloudExamPage,
]

const practiceOverviewPage = {
    path:'practice_overview',
    name:'practice_overview',
    component:PracticeOverview,
    children:[
        {
            path:'practice_history',
            name:'practice_history',
            component:PracticeHistory
        },
        {
            path:'practice_detail/:id',
            name:'practice_detail',
            component:PracticeDetail
        },
        {
            path:'practice_answer_action',
            name:'practice_answer_action',
            component:PracticeAnswerAction
        }
    ]
}

//解决vue-router3.0重复访问同一路由导致的报错问题
let originalPush = VueRouter.prototype.push
let originalReplace  = VueRouter.prototype.replace

VueRouter.prototype.push = function push(location){
    return originalPush.call(this,location).catch(e=>e)
}
VueRouter.prototype.replace = function push(location){
    return originalReplace.call(this,location).catch(e=>e)
}
const router= new VueRouter({
    routes: [
        {
            path: '/',
            name: 'Root',
            component: Root
        },
        {
            path: '/init',
            name: 'Init',
            component: Init
        },
        {
            path: '/login',
            name: 'Login',
            component: Login,
            children:[
                {
                    path:'referral_code',
                    name:'referral_code',
                    component:ReferralCode,
                }
            ]
        },
        {
            path: '/webLive/:id',
            name: 'WebLive_deprecated',
            component: WebLive
        },
        {

            path: '/cloudVideoEditChild',
            name: 'CloudVideoEditChild',
            component: CloudVideoEditChild
        },
        {
            path: '/pacs_login',
            name: 'PacsLogin',
            component: PacsLogin
        },
        {
        	path:'/index/chat_window/:cid',
        	name:'index',
        	component:Index,
            meta:{
                inChatWindow:true
            },
            children:[
                {
                    path:'ai_main',
                    name:'AiMain',
                    component:AiMain,
                    redirect: '/index/chat_window/:cid/ai_main/ai_chat',
                    children:[
                        {
                            path:'ai_chat',
                            name:'AiChat',
                            component:AiChat,
                        },
                        practiceOverviewPage,
                        {
                            path:'ultrasound_report_qc_index',
                            name:'UltrasoundReportQCIndex',
                            component:UltrasoundReportQCIndex,
                            children: [
                                {
                                    path:'adminOverview',
                                    name:'UltrasoundReportQCAdminOverview',
                                    component:UltrasoundReportQCAdminOverview,
                                },
                                {
                                    path:'chartOverview',
                                    name:'UltrasoundReportQCChartOverview',
                                    component:UltrasoundReportQCChartOverview,
                                    children:[
                                        {
                                            path:'report_detail/:id',
                                            name:'ultrasoundReportQCDetail',
                                            component:UltrasoundReportQCReportDetail,
                                        }
                                    ]
                                },

                            ]
                        }
                    ]
                },
                {
                    path:'smart_tech_training',
                    name:'SmartTechTraining',
                    component:SmartTechTraining,
                    children:[
                        {
                            path:'training_main/:role/:trainingId',
                            name:'TrainingMain',
                            component:TrainingMain,
                            children:[
                                {
                                    path:'exam_project',
                                    name:'SmartTechTrainingExamProject',
                                    component:ExamProject,
                                    children:[
                                        {
                                            path:'upload_test_overview/:testId',
                                            name:'SmartTechTrainingExamProject_UploadTestOverview',
                                            component:UploadTestOverview,
                                        },
                                        {
                                            path:'online_test_overview/:testId',
                                            name:'SmartTechTrainingExamProject_OnlineTestOverview',
                                            component:OnlineTestOverview,
                                            children:[
                                                {
                                                    path:'exam/:pager_type',
                                                    name:'SmartTechTrainingExamProject_Exam',
                                                    component:SmartTechTrainingExam,
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    path:'student_overview',
                                    name:'SmartTechTrainingStudentOverview',
                                    component:SmartTechTrainingStudentOverview,
                                },
                                {
                                    path:'grading_review',
                                    name:'SmartTechTrainingGradingReview',
                                    component:SmartTechTrainingGradingReview,
                                    children:[
                                        {
                                            path:'upload_test_overview/:testId',
                                            name:'SmartTechTrainingGradingReview_UploadTestOverview',
                                            component:UploadTestOverview,
                                        },
                                        {
                                            path:'online_test_overview/:testId',
                                            name:'SmartTechTrainingGradingReview_OnlineTestOverview',
                                            component:OnlineTestOverview,
                                            children:[
                                                {
                                                    path:'exam/:pager_type',
                                                    name:'SmartTechTrainingGradingReview_Exam',
                                                    component:SmartTechTrainingExam,
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    path:'student_management',
                                    name:'SmartTechTrainingStudentManagement',
                                    component:SmartTechTrainingStudentManagement,
                                },
                                {
                                    path: 'supervisor_management',
                                    name: 'SmartTechTrainingSupervisorManagement',
                                    component: SmartTechTrainingSupervisorManagement,
                                }

                            ]
                        }
                    ]
                },
                {
                    path:'gallery',
                    name:'gallery',
                    component:GalleryController,
                    meta:{
                        keepAlive:true
                    },
                    children:[
                        {
                            path:'visiting_card',
                            name:'gallery_visiting_card',
                            component:VisitingCard,
                            meta:{
                                keepAlive:true
                            },
                        },
                        {
                            path:'equipment_detection/:type',
                            name:'equipment_detection',
                            component:EquipmentDetection,
                            meta:{
                                keepAlive:true
                            },
                        },
                        {
                            path:'report',
                            name:'report',
                            component:reportControler,
                            meta:{
                                keepAlive:true
                            },
                        },
                    ]
                },
                {
                    path:'equipment_detection/:type',
                    name:'equipment_detection',
                    component:EquipmentDetection,
                },
                {
                    path:'dr_ai_statistics',
                    name:'dr_ai_statistics',
                    component:DrAiStatistics,
                    children:[]
                },
                {
                    path:'edit_group_setting',
                    name:'edit_group_setting',
                    component:GroupSetting,
                    children:[
                        {
                            path:"group_manage",
                            name:'group_manage',
                            component:GroupManage,
                            children:[
                                {
                                    path:"transfer_group",
                                    name:'transfer_group',
                                    component:TransferGroup
                                },
                                {
                                    path:"group_managers",
                                    name:'group_managers',
                                    component:GroupManagers,
                                    children:[
                                        {
                                            path: 'edit_manager/:type',
                                            name: 'edit_manager',
                                            component: EditGroupManager,
                                        },
                                    ]
                                },
                                {
                                    path:"join_verify",
                                    name:'join_verify',
                                    component:GroupJoinVerify,
                                    children:[
                                        {
                                            path:'visiting_card',
                                            name:'visiting_card',
                                            component:VisitingCard,
                                        },
                                    ]
                                },
                            ]
                        }
                    ]
                },

                {
                    path:'add_attendee',
                    name:'add_attendee',
                    component:AddAttendee,
                },
                {
                    path:'delete_attendee',
                    name:'delete_attendee',
                    component:DeleteAttendee,
                },
                {
                    path:'applys',
                    name:'applys',
                    component:Applys,
                },
                {
                    path:'live_management',
                    name:'live_management',
                    // component:LiveManagement,
                },
                ...globalDialogRouters,
                {
                    path:'visiting_card',
                    name:'visiting_card',
                    component:VisitingCard,
                    meta:{
                        fromGroup:true,
                    },
                },
                {
                    path:'public_favorite/:uid',
                    name:'public_favorite',
                    component:PublicFavorite,
                    children:[
                        {
                            path:'gallery',
                            name:'public_favorites_gallery',
                            component:GalleryController,
                            meta:{
                                noneComment:true,
                                noneChat:true,
                                fromFavorites:true
                            }

                        },
                    ]
                },
                {
                    path:'cloud_statistics/:type',
                    name:'cloud_statistics',
                    component:CloudStatistics,
                },
                {
                    path:'scan_room_manage',
                    name:'scan_room_manage',
                    component:ScanRoomManage,
                },
                {
                    path:'export_file',
                    name:'export_file',
                    component:ExportFile,
                },
                {
                    path:'tv_wall_web',
                    name:'tv_wall_web',
                    component:TVWallWeb,
                    meta:{
                        inTvWall:true
                    },
                    children:[
                        {
                            path:'conference/:cid',
                            name:'tv_wall_conference',
                            component:Conference,
                            meta:{
                                inConference:true,
                                inTvWall:true
                            }
                        },
                        {
                            path:'gallery',
                            name:'gallery',
                            component:GalleryController,
                            meta:{
                                inTvWall:true
                            }
                        },
                        {
                            path:'equipment_detection/:type',
                            name:'equipment_detection',
                            component:EquipmentDetection,
                        },
                        {
                            path:'tv_wall_setting',
                            name:'tv_wall_setting',
                            component:TvWallSetting,
                        },
                    ]
                },
                {
                    path:'equipment_detection/:type',
                    name:'equipment_detection',
                    component:EquipmentDetection,
                },
                {
                    path:'register_scanroom',
                    name:'register_scanroom',
                    component:RegisterScanroom,
                },
                {
                    path:'background_manage',
                    name:'background_manage',
                    component:BackgroundManage,
                },
                {
                    path:'conference/:cid',
                    name:'conference',
                    component:Conference,
                    meta:{
                        inConference:true
                    },
                    children:[
                        {
                            path:'equipment_detection/:type',
                            name:'equipment_detection',
                            component:EquipmentDetection,
                        },
                    ]
                },
                {
                    path:'library',
                    name:'library',
                    component:Library,
                    children:[
                        {
                            path:'gallery',
                            name:'library_gallery',
                            component:GalleryController,
                            meta:{
                                noneComment:true,
                                noneChat:true,
                                fromFavorites:true
                            }

                        }
                    ]
                },
                {
                    path:'reserved_conference',
                    name:'reserved_conference',
                    component:ReservedConference,
                },
                {
                    path:'task_manager',
                    name:'task_manager',
                    component:TaskManager,
                    children:[
                        {
                            path:'gallery',
                            name:'media_transfer_gallery',
                            component:GalleryController,
                            meta:{
                                noneComment:true,
                                noneChat:true
                            }
                        },
                        {
                            path:'exam_manager',
                            name:'exam_manager',
                            component:ExamManager,
                            children:[
                                {
                                    path:'new_exam',
                                    name:'new_exam',
                                    component:NewExam,
                                },
                                {
                                    path:'import_exam_image',
                                    name:'import_exam_image',
                                    component:ImportExamImage,
                                }
                            ]
                        }
                    ]
                },
                {
                    path:'groupset_wall/:groupset_id',
                    name:'groupset_wall',
                    component:GroupSetWall,
                    children:[
                        {
                            path:'gallery',
                            name:'gallery',
                            component:GalleryController,
                            children:[
                                {
                                    path:'report',
                                    name:'report',
                                    component:reportControler,
                                    meta:{
                                        keepAlive:true
                                    },
                                },
                            ]
                        },
                        {
                            path:'exam_mode/gallery',
                            name:'groupset_exam_mode_gallery',
                            component:GalleryController,
                            meta:{
                                noneComment:true,
                                noneChat:true,
                            }
                        },
                        {
                            path:'groupset_setting',
                            name:'groupset_setting',
                            component:GroupsetSetting,
                        },
                        {
                            path:'groupset_manager',
                            name:'groupset_manager',
                            component:GroupsetManager,
                        },
                        {
                            path:'edit_groupset/:type/:step',
                            name:'edit_groupset',
                            component:EditGroupset
                        },
                        {
                            path:'edit_groupset_manager/:action',
                            name:'edit_groupset_manager',
                            component:EditGroupsetManager
                        },
                        {
                            path:'groupset_avatar',
                            name:'groupset_avatar',
                            component:GroupsetAvatar
                        },
                        {
                            path:'tv_wall_web',
                            name:'tv_wall_web',
                            component:TVWallWeb,
                            meta:{
                                inTvWall:true
                            },
                            children:[
                                {
                                    path:'conference/:cid',
                                    name:'tv_wall_conference',
                                    component:Conference,
                                    meta:{
                                        inConference:true,
                                        inTvWall:true
                                    }
                                },
                                {
                                    path:'gallery',
                                    name:'gallery',
                                    component:GalleryController,
                                    meta:{
                                        inTvWall:true
                                    }
                                },
                                {
                                    path:'equipment_detection/:type',
                                    name:'equipment_detection',
                                    component:EquipmentDetection,
                                },
                                {
                                    path:'tv_wall_setting',
                                    name:'tv_wall_setting',
                                    component:TvWallSetting,
                                },
                            ]
                        },
                    ]
                },
                // {
                //     path:"clip_gallery",
                //     name:"clip_gallery",
                //     component:GalleryController,
                //     meta:{
                //         noneComment:true,
                //         noneChat:true
                //     }
                // },
                {
                    path:"clip",
                    name:"clip",
                    component:Clip
                },
                {
                    path:'tv_wall_web',
                    name:'tv_wall_web',
                    component:TVWallWeb,
                    meta:{
                        inTvWall:true
                    },
                    children:[
                        {
                            path:'conference/:cid',
                            name:'tv_wall_conference',
                            component:Conference,
                            meta:{
                                inConference:true,
                                inTvWall:true
                            }
                        },
                        {
                            path:'gallery',
                            name:'gallery',
                            component:GalleryController,
                            meta:{
                                inTvWall:true
                            }
                        },
                        {
                            path:'equipment_detection/:type',
                            name:'equipment_detection',
                            component:EquipmentDetection,
                        },
                        {
                            path:'tv_wall_setting',
                            name:'tv_wall_setting',
                            component:TvWallSetting,
                        },
                    ]
                },
                {
                    path:'chat_history_search_list',
                    name:'chat_history_search_list',
                    component:ChatHistorySearchList,
                    children:[
                        {
                            path:'gallery',
                            name:'history_message_gallery',
                            component:GalleryController,
                            meta:{
                                noneComment:true,
                                noneChat:true,
                            }
                        },
                        {
                            path:'visiting_card',
                            name:'visiting_card',
                            component:VisitingCard,
                        },
                        cloudExamPage
                    ]
                },
                {
                    path: 'exam_setting',
                    component: CloudExam,
                    children: [
                        {
                            path: '',
                            name: 'exam_setting',
                            component: CloudExamSetting
                        }
                    ]
                }
            ]
        },
    ]
})
router.beforeEach((to,from,next)=>{
    console.log(to,from);
    // 检查是否是iframe内的路由变化
    if (window.self !== window.top) {
        // 如果是在iframe内，直接放行
        next()
        return
    }    // 检查是否完成初始化
    const isInit = Tool.isInit()
    if(to.path.startsWith('/init')){
        next()
        return
    }
    if(!isInit){
        if(whiteList.includes(to.path)){
            next(`/init?previousPath=${encodeURIComponent(to.path)}`)
        }else{
            next(`/init`)
        }

        return
    }
    for(let i = 0;i<whiteList.length;i++){
        if(to.path.indexOf(whiteList[i])>-1){
            next()
            return
        }
    }
    const loginToken=Tool.getToken()

    if(!loginToken){
        next('/login')
        return
    }
    // 会议相关路由检查
    if(from.path && from.path.indexOf('/conference') > -1) {
        let whitePath = ['/live_address_page']
        if(Number(window.vm.$root.currentLiveCid)) {
            if((whitePath.findIndex(item => to.path.includes(item)) > -1) ||
           (whitePath.findIndex(item => from.path.includes(item)) > -1)) {
                next()
            } else {
                next(false)
            }
        } else {
            next()
        }
    } else {
        next()
    }
})
router.afterEach(()=>{
    //用于跨路由嵌套层级的跳转
    if (window.directPath) {
        router.replace(window.directPath);
        window.directPath=''
    }
})
router.onError((error) => {
    console.log('router load error3:',error);
    window.vm.$alert(window.vm.$store.state.language.chunk_load_error);

})
export default router;
