<template>
    <div class="avatar_wrapper" :style="{ width: radius + 'px', height: radius + 'px' }"
        :class="{ 'is_private': onlineState === -1 && is_public === 0 }">
        <img :src="showUrl" @error="errorHandler">
        <template v-if="showOnlineState">
            <span class="online_icon" v-show="onlineState === 1"></span>
            <span class="offline_icon" v-show="onlineState === 0"></span>
        </template>
    </div>
</template>
<script>
import base from '../lib/base';
import {
    addRootToUrl,
    getLocalAvatar,
    getDefaultImg,
} from '../lib/common_base';
import { cloneDeep } from 'lodash'
import Tool from '@/common/tool'
export default {
    name: 'mrAvatar',
    mixins: [base],
    components: {},
    props: {
        radius: {
            type: Number,
            default: 46
        },
        url: {
            type: String,
            default: ''
        },
        onlineState: {
            type: Number,
            default: -1
        },
        showOnlineState: {
            type: Boolean,
            default: false
        },
        is_public: {
            type: Number,
            default: 1
        }
    },
    computed: {

    },
    data() {
        return {
            showUrl: ''
        }
    },
    created() {
        let url = this.url
        url = this.limitImageSize(url)
        this.showUrl = url
    },
    mounted() {
    },
    methods: {
        errorHandler() {
            let defaultSrc = "static/resource_pc/images/b2-1.png";
            this.showUrl = defaultSrc;
        }
    },
}
</script>
<style lang="scss" scoped>
.avatar_wrapper {
    position: relative;
    flex-shrink: 0;
    border-radius: 50%;
    overflow: hidden;

    &.is_private {
        border: 2px solid #ffa9a3;
    }

    &.groupset_icon {
        width: 40%;
        height: 40%;
        position: absolute;
        right: -10%;
        top: -10%;
    }

    &>img {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        object-fit: contain;

    }

    .online_icon {
        width: 20%;
        height: 20%;
        position: absolute;
        right: 5%;
        bottom: 5%;
        background: #00c59d;
        border-radius: 50%;
    }

    .offline_icon {
        width: 20%;
        height: 20%;
        position: absolute;
        right: 5%;
        bottom: 5%;
        background: #aaa;
        border-radius: 50%;
    }
}
</style>
