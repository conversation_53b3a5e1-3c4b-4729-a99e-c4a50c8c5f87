<template>
    <div class="smart_tech_exam_container">
        <div class="custom_header">
            <div class="back_btn" @click="back">
                <i class="el-icon-arrow-left"></i>
                <span>{{ lang.back_button }}</span>
            </div>
        </div>
        <div class="custom_body">
            <div class="correct_exam_detail">
                <div class="exam_detail_left">
                    <!-- 答题模式，可编辑学生信息 -->
                    <div v-if="type === CLOUD_TEST_TYPE.ANSWER" class="student_info">
                        <div class="student_info_row">
                            <div class="student_info_label">{{ lang.student_name }}:&nbsp;</div>
                            <el-input
                                v-model="studentName"
                                :placeholder="this.studentName ? this.studentName : lang.input_enter_tips"
                                size="small"
                            ></el-input>
                        </div>
                        <div class="student_info_row student_info_row_org">
                            <div class="student_info_label">{{ lang.admin_hospital_name }}:&nbsp;</div>
                            <el-input
                                v-model="studentOrg"
                                :placeholder="this.studentOrg ? this.studentOrg : lang.input_enter_tips"
                                size="small"
                            ></el-input>
                        </div>
                    </div>
                </div>
                <div class="exam_detail_right">
                    <template v-if="type === CLOUD_TEST_TYPE.VIEW">
                        <el-button class="primary_btn" @click="shareExam" size="large" type="default">{{
                            lang.share_paper
                        }}</el-button>
                        <el-button class="error_btn" @click="deletePaper" size="large" type="default">{{
                            lang.homework.delete_paper
                        }}</el-button>
                        <el-button class="gradient_btn" @click="arrangeExam">{{ lang.assign_homework }}</el-button>
                    </template>
                    <!-- <template v-else-if="type === 2 || type === 3">
                        <el-button class="primary_btn" v-if="type === 2" @click="save" size="large" type="default">{{lang.save_txt}}</el-button>
                        <el-button class="error_btn" @click="submit">{{lang.submit_btn}}</el-button>
                    </template> -->
                    <template v-else-if="type === CLOUD_TEST_TYPE.EDIT">
                        <el-button class="error_btn" @click="back" size="large" type="default">{{
                            lang.cancel_btn
                        }}</el-button>
                        <el-button class="primary_btn" @click="saveExam" size="large" type="default">{{
                            lang.save_txt
                        }}</el-button>
                    </template>
                    <div v-else-if="type === CLOUD_TEST_TYPE.VIEW_RESULT" class="exam_detail_item">
                        <span>{{ lang.paper_results }}:</span>
                        <span class="assignment_score">{{ currentAssignment.score }}</span>
                        <span>{{ lang.point_tip }}</span>
                    </div>
                </div>
            </div>
            <div ref="topicContent" class="topic_content" v-if="examContents.length>0">
                <div class="exam-title-wrapper" v-if="type === CLOUD_TEST_TYPE.EDIT">
                    <span class="title-tip-label">{{ lang.homework.exam_title_tip }}：</span>
                    <!-- 编辑模式，可编辑试卷标题 -->
                    <text-field
                        :value="examTitle"
                        :isEdit="true"
                        :placeholder="lang.input_enter_tips"
                        @change="(val) => handleFieldChange(examTitle, 'title', val)"
                    />
                </div>
                <div class="content-container">
                    <div class="topic_list">
                        <div v-for="(topicType, index) of examContents" :key="index" class="topic_list_item">
                            <div class="topic_summary">
                                {{ lang.topic_type[topicType.type] }}（{{ topicType.summaryText }}）
                            </div>
                            <pre
                                v-if="type !== CLOUD_TEST_TYPE.EDIT && topicType.type === 'operation'"
                                class="topic_tip"
                                v-html="lang.homework_operation_step"
                            ></pre>
                            <div
                                class="topic_detail"
                                v-for="(topic, j_index) of topicType.list"
                                :key="j_index"
                                @click="setCurrentQuestionByTypeIndex(index, j_index)"
                            >
                                <short-answer-topic
                                    v-if="topicType.type === 'shortAnswer'"
                                    :topic="topic"
                                    :type="type"
                                    :topicTypeIndex="index"
                                    :topicIndex="j_index"
                                    :disableModify="disableModify"
                                    @delete-image="deleteImage"
                                    @upload-click="() => uploadStart(index, j_index, '-1')"
                                    @image-order-changed="handleImageOrderChange"
                                    @update-value="handleUpdateValue"
                                    @update-pass-status="handleUpdatePassStatus"
                                    :isPassMode="true"
                                />
                                <operation-topic
                                    v-else-if="topicType.type === 'operation'"
                                    :topic="topic"
                                    :type="type"
                                    :topicTypeIndex="index"
                                    :topicIndex="j_index"
                                    :disableModify="disableModify"
                                    @delete-image="deleteImage"
                                    @upload-click="(subIndex) => uploadStart(index, j_index, subIndex)"
                                    @image-order-changed="handleImageOrderChange"
                                    @update-pass-status="handleUpdatePassStatus"
                                    @open-collect="openCollect"
                                    @close-collect="closeCollect"
                                    :isPassMode="true"
                                />

                                <input
                                    v-show="false"
                                    ref="uploadComponent"
                                    type="file"
                                    :accept="acceptFileTypes"
                                    multiple
                                    @change="handleFileChange($event)"
                                />
                            </div>
                        </div>
                    </div>
                    <!-- 答题、批改模式，显示进度条 -->
                    <progress-indicator
                        v-if="(type === CLOUD_TEST_TYPE.ANSWER || type === CLOUD_TEST_TYPE.CORRECT) && examContents.length>0"
                        :progressList="progressList"
                        @submit="submit"
                        :topicType="type"
                        :totalScore="currentTotalScore"
                        @jump="jumpToQuestion"
                        :isPassMode="true"
                        :showSaveBtn="false"
                    />
                </div>
            </div>
            <!-- <div class="topic_footer">
                <el-pagination
                  background
                  :current-page="topicStep"
                  :layout="layout"
                  :page-size="1"
                  :pager-count="15"
                  :total="currentAssignment.questionCount"
                  @current-change="handleCurrentChange"
                  class="topic_footer_pagination"
                />
            </div> -->
        </div>
        <router-view></router-view>
    </div>
</template>

<script>
import base from "../../lib/base";
import service from "../../service/service.js";
import { formatDurationTime, findServiceId } from "../../lib/common_base";
import moment from "moment";
import { uploadFile } from "@/common/oss/index";
import Tool from "@/common/tool.js";
import { cloneDeep } from "lodash";
import { CLOUD_TEST_TYPE } from "../../lib/constants";

// 导入自定义组件'
import ProgressIndicator from "../cloudExam/components/progressIndicator.vue";
import ShortAnswerTopic from "../cloudExam/components/ShortAnswerTopic.vue";
import OperationTopic from "../cloudExam/components/OperationTopic.vue";

export default {
    mixins: [base],
    name: "smartTechExam",
    components: {
        ProgressIndicator,
        ShortAnswerTopic,
        OperationTopic,
    },
    data() {
        return {
            paperId: 0,
            type: null, // 查看类型 2：答卷 3：批卷 4：查看结果
            cid: 0, // 群id ，0为全局进入
            examContents: [],
            currentAssignment: {},
            useTime: 0,
            interval: null,
            uploadIndex: [0, 0, 0],
            collectIndex: [0, 0, 0],
            fileTransferAssistant: null,
            confirmSave: false,
            studentName: "",
            studentOrg: "",
            currentQuestionIndex: 0,
            CLOUD_TEST_TYPE: CLOUD_TEST_TYPE,
            userRole: "",
            trainingID: "",
            examTitle: "",
        };
    },
    filters: {
        showData(ts) {
            return moment(ts).format("YYYY-MM-DD HH:mm:ss z");
        },
        useTime(duration) {
            return formatDurationTime(duration);
        },
    },
    computed: {
        title() {
            return this.examTitle || this.lang.homework.exam_detail;
        },
        disableModify() {
            return this.type !== CLOUD_TEST_TYPE.ANSWER;
        },
        acceptFileTypes() {
            const imageTypes = this.getSupportImageType().join(",.").toLowerCase();
            const videoTypes = this.getSupportVideoType().join(",.").toLowerCase();
            return `.${imageTypes},.${videoTypes}`;
        },
        progressList() {
            if (!this.examContents) {
                return [];
            }
            const res = this.getAnswer();
            const answers = res.answer;
            let progress = [];

            for (let i = 0; i < answers.length; i++) {
                let isCompleted = false;
                let baseStatus = "none";
                let isPassed = null;

                if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                    // 答题模式
                    const topic = this.findTopicByIndex(i);
                    if (topic) {
                        if (topic.type === "operation") {
                            // 判断实操题：检测子题中是否有上传图片且已停止采集
                            isCompleted = topic.subTopic.some((sub) => sub.value && sub.value.length > 0);
                        } else {
                            isCompleted = typeof answers[i].value === "string" && answers[i].value.trim() !== "";
                        }
                    }
                } else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                    // 批改模式
                    const topic = this.findTopicByIndex(i);
                    if (topic) {
                        isCompleted = typeof topic.correctScore !== "undefined" && topic.correctScore !== null;
                        // 添加通过/不通过状态
                        isPassed = topic.isPassed;
                    }
                }
                baseStatus = isCompleted ? "done" : "none";
                // 如果该题为当前题，则显示 current 样式
                let status = i === this.currentQuestionIndex ? "current" : baseStatus;
                progress.push({
                    status: status,
                    completed: isCompleted,
                    isPassed: isPassed,
                });
            }
            return progress;
        },
        currentTotalScore() {
            if (this.type !== CLOUD_TEST_TYPE.CORRECT || this.examContents.length === 0) {
                return 0;
            }
            let totalScore = 0;
            this.examContents.forEach((topicType) => {
                topicType.list.forEach((item) => {
                    // 只累加已经有correctScore的题目分数
                    if (item.correctScore !== null && item.correctScore !== undefined && item.correctScore !== "") {
                        totalScore += Number(item.correctScore);
                    }
                });
            });
            return totalScore;
        }
    },
    created() {
        this.userRole = this.$route.params.role;
        this.cid = parseInt(this.$route.params.cid) || 0;
        this.paperId = this.$route.params.testId;
        this.type = parseInt(this.$route.params.pager_type);
        this.trainingID = this.$route.params.trainingId;
        this.currentAssignment = cloneDeep(this.$route.params.testData);
        this.getTrainingStudentInfo();

        console.log(this.currentAssignment, "currentAssignment");
    },
    mounted() {
        if (this.type === CLOUD_TEST_TYPE.ANSWER) {
            // 考生获取答题卡
            this.getanswerSheetDetail();
        } else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
            // 老师批卷获取答题卡
            this.getCorrectDetail();
        } else if (this.type === CLOUD_TEST_TYPE.VIEW_RESULT) {
            // 考生查看详情
            this.getanswerSheetDetail();
        }
    },
    beforeRouteLeave(to, from, next) {
        // 在离开组件前提示保存
        if (this.type !== CLOUD_TEST_TYPE.ANSWER || this.confirmSave) {
            next();
            return;
        }
        setTimeout(() => {
            // 不加延时会闪没
            this.$confirm(this.lang.unsave_tip, this.lang.tip_title, {
                confirmButtonText: this.lang.save_txt,
                cancelButtonText: this.lang.cancel_button_text,
                type: "warning",
            })
                .then(() => {
                    let { answer } = this.getAnswer();
                    this.submitAnswer(false, answer);
                    this.back();
                })
                .catch(() => {
                    this.back();
                });
        }, 100);
        this.confirmSave = true;
        next(false);
    },
    destroyed() {
        clearInterval(this.interval);
        this.closeFileTransferListener();
        if (this.type === CLOUD_TEST_TYPE.CORRECT) {
            this.unlockAnswerSheet();
        }
    },
    methods: {
        // 添加辅助方法来根据索引查找题目
        findTopicByIndex(index) {
            let currentIndex = 0;
            for (const topicType of this.examContents) {
                for (const topic of topicType.list) {
                    if (currentIndex === index) {
                        return {
                            ...topic,
                            type: topicType.type,
                        };
                    }
                    currentIndex++;
                }
            }
            return null;
        },
        getSupportImageType() {
            return Tool.getSupportImageType();
        },
        getSupportVideoType() {
            return Tool.getSupportVideoType();
        },
        getTopicTypeSummary(topicType) {
            console.log('topicType',topicType);
            let summary = this.lang.topic_summary_no_score;
            summary = summary.replace("{a}", topicType.count);
            return summary;
        },
        deleteImage(imageList, index) {
            imageList.splice(index, 1);

            // 触发计算属性更新
            if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                this.$forceUpdate();
            }
        },
        getCorrectDetail() {
            this.getanswerSheetDetail();
            // 获取批改详情先检查锁，已被别的批改老师锁定则退出
            // service
            //     .lockAnswerSheet({
            //         answerSheetID: this.paperId,
            //     })
            //     .then((res) => {
            //         if (res.data.error_code === 0) {
            //             this.getanswerSheetDetail();
            //         } else {
            //             this.back();
            //         }
            //     });
        },
        getanswerSheetDetail() {
            // service.getanswerSheetDetail({
            //     answerSheetID: this.paperId
            // }).then(res => {

            // })
            const data = {
                error_code: 0,
                message: "success",
                data: {
                    _id: "682b0f5286e740b3374d52c9",
                    paperID: "67b97e8d4544ce1dd181c112",
                    assignmentID: "682b0f5286e740b3374d52c5",
                    gid: [5245],
                    uid: 58,
                    studentName: "",
                    studentOrg: "",
                    score: 0,
                    teacherID: 0,
                    answer: [],
                    scoreDetail: [],
                    status: 0,
                    submitAt: 0,
                    scoreAt: 0,
                    dueTime: 1748620800000,
                    useTime: 0,
                    createdAt: "2025-05-19T11:00:34.469Z",
                    updatedAt: "2025-05-19T11:00:34.469Z",
                    assignmentInfo: {
                        _id: "682b0f5286e740b3374d52c5",
                        paperID: "67b97e8d4544ce1dd181c112",
                        gid: [5245],
                        uid: 200,
                        teachers: [200],
                        dueTime: 1748620800000,
                        maxRetry: 1,
                        resultCanChecked: true,
                        useTimeList: [],
                        status: 1,
                        paperInfo: {
                            title: "自主命题02",
                            uid: 200,
                            author: "shuxuezhang10",
                            score: 40,
                            questionCount: 8,
                            createdAt: "2025-02-22T07:36:45.704Z",
                            updatedAt: "2025-02-22T07:36:45.704Z",
                        },
                        createdAt: "2025-05-19T11:00:34.438Z",
                    },
                    paperInfo: {
                        _id: "67b97e8d4544ce1dd181c112",
                        title: "自主命题02",
                        uid: 200,
                        author: "shuxuezhang10",
                        score: 40,
                        questionCount: 8,
                        content: [
                            {
                                type: "shortAnswer",
                                totalScore: 10,
                                count: 2,
                                list: [
                                    {
                                        id: 5,
                                        index: 5,
                                        title: "简答题1",
                                        score: 5,
                                        type: "shortAnswer",
                                        imageList: [],
                                        subTopic: [],
                                        value: "超声心动图检查可以清晰显示心脏结构和功能，对心脏瓣膜病变、心肌病变和先天性心脏病的诊断有重要价值。",
                                        answerRecords: [
                                            {
                                                content: "超声心动图检查可以清晰显示心脏结构。",
                                                submitTime: "2025-05-18T10:30:20Z",
                                                user: "学生",
                                            },
                                            {
                                                content:
                                                    "超声心动图检查可以清晰显示心脏结构和功能，对心脏瓣膜病变的诊断有重要价值。",
                                                submitTime: "2025-05-19T08:45:15Z",
                                                user: "学生",
                                            },
                                            {
                                                content:
                                                    "超声心动图检查可以清晰显示心脏结构和功能，对心脏瓣膜病变、心肌病变和先天性心脏病的诊断有重要价值。",
                                                submitTime: "2025-05-20T09:15:30Z",
                                                user: "学生",
                                                comment:
                                                    "答案完整准确，清晰描述了超声心动图检查的作用和价值，包含了所有关键点。",
                                                isPassed: true,
                                                score: 5,
                                            },
                                        ],
                                    },
                                    {
                                        id: 6,
                                        index: 6,
                                        title: "简答题2",
                                        score: 5,
                                        type: "shortAnswer",
                                        imageList: [],
                                        subTopic: [],
                                        value: "腹部超声检查可以评估肝脏、胆囊、胰腺、脾脏和肾脏等器官的大小、形态和回声特点，有助于发现肿瘤、结石、炎症等病变。",
                                        answerRecords: [
                                            {
                                                content: "腹部超声检查可以评估肝脏、胆囊等器官。",
                                                submitTime: "2025-05-18T11:20:10Z",
                                                user: "学生",
                                            },
                                            {
                                                content:
                                                    "腹部超声检查可以评估肝脏、胆囊、胰腺、脾脏和肾脏等器官的大小和形态。",
                                                submitTime: "2025-05-19T14:30:25Z",
                                                user: "学生",
                                            },
                                            {
                                                content:
                                                    "腹部超声检查可以评估肝脏、胆囊、胰腺、脾脏和肾脏等器官的大小、形态和回声特点，有助于发现肿瘤、结石、炎症等病变。",
                                                submitTime: "2025-05-20T10:05:40Z",
                                                user: "学生",
                                                comment:
                                                    "回答全面准确，详细说明了腹部超声检查的功能和临床应用价值，覆盖了所有要点。",
                                                isPassed: true,
                                                score: 5,
                                            },
                                        ],
                                    },
                                ],
                            },
                            {
                                type: "operation",
                                totalScore: 20,
                                count: 2,
                                list: [
                                    {
                                        id: 7,
                                        index: 7,
                                        title: "超声胃残余量监测",
                                        score: 10,
                                        type: "operation",
                                        description: "",
                                        imageList: [],
                                        subTopic: [
                                            {
                                                title: "胃窦标准切面(图片)",
                                                description:
                                                    "1.图片需要包含体表标记。 2.图片清晰显示标准切面要求的器官和组织结构：肝左叶、腹主动脉、胃窦区，采取双径法测量胃窦的双径。 3.考核APP内在该图片注释中说明胃残余量计算的方法和计算结果。",
                                                value: [],
                                                score: 10,
                                                imageList: [],
                                                collecting: false,
                                                answerRecords: [
                                                    {
                                                        images: [],
                                                        submitTime: "2025-05-18T11:20:10Z",
                                                    },
                                                    {
                                                        images: [],
                                                        submitTime: "2025-05-19T14:30:25Z",
                                                        comment:
                                                            "胃窦标准切面图像需要更清晰地显示肝左叶、腹主动脉和胃窦区，请重新提交并确保包含体表标记。",
                                                        isPassed: false,
                                                    },
                                                ],
                                            },
                                        ],
                                    },
                                    {
                                        id: 8,
                                        index: 8,
                                        title: "超声引导下动静脉穿刺",
                                        score: 10,
                                        type: "operation",
                                        description: "",
                                        imageList: [],
                                        subTopic: [
                                            {
                                                title: "采取轻压法区分动静脉的B超影像",
                                                description: "视频中需要包含体表标记。",
                                                value: [],
                                                score: 5,
                                                imageList: [],
                                                collecting: false,
                                                answerRecords: [
                                                    {
                                                        images: [
                                                            {
                                                                url: "https://rmtus-attachment-dev.oss-cn-shanghai.aliyuncs.com/upload/981/77/981-77-1747711914254_1/傲娇猫帝.png_thumb.jpg",
                                                                downloadUrl:
                                                                    "https://rmtus-attachment-dev.oss-cn-shanghai.aliyuncs.com/upload/981/77/981-77-1747711914254_1/傲娇猫帝.png",
                                                            },
                                                            {
                                                                url: "https://rmtus-attachment-dev.oss-cn-shanghai.aliyuncs.com/upload/981/77/981-77-1747711914254_1/傲娇猫帝.png_thumb.jpg",
                                                                downloadUrl:
                                                                    "https://rmtus-attachment-dev.oss-cn-shanghai.aliyuncs.com/upload/981/77/981-77-1747711914254_1/傲娇猫帝.png",
                                                            },
                                                        ],
                                                        submitTime: "2025-05-18T11:20:10Z",
                                                    },
                                                    {
                                                        images: [
                                                            {
                                                                url: "https://rmtus-attachment-dev.oss-cn-shanghai.aliyuncs.com/upload/981/77/981-77-1747711914254_1/傲娇猫帝.png_thumb.jpg",
                                                                downloadUrl:
                                                                    "https://rmtus-attachment-dev.oss-cn-shanghai.aliyuncs.com/upload/981/77/981-77-1747711914254_1/傲娇猫帝.png",
                                                            },
                                                        ],
                                                        submitTime: "2025-05-19T14:30:25Z",
                                                        comment:
                                                            "图像清晰展示了动静脉区分，轻压法应用正确，但建议下次拍摄时确保包含更完整的体表标记以便于定位。",
                                                        isPassed: true,
                                                    },
                                                ],
                                            },
                                            {
                                                title: "C模式下动脉脉冲多普勒频谱",
                                                description: "视频中需要包含体表标记。",
                                                value: [],
                                                score: 5,
                                                imageList: [],
                                                collecting: false,
                                            },
                                        ],
                                    },
                                ],
                            },
                        ],
                        createdAt: "2025-02-22T07:36:45.704Z",
                    },
                    studentInfo: {
                        avatar: "https://rmtus-attachment-dev.oss-cn-shanghai.aliyuncs.com/CustomPortrait/Portrait/58/553f60f0-c1d0-11ef-b08d-c5ad1494771e.png",
                        id: 58,
                        role: 4,
                        sex: 1,
                        nickname: "18804911920",
                        login_name: "cai1",
                        status: 1,
                        hospital_id: 1,
                        organization_id: 1,
                        introduction:
                            "123xx1大厦大萨达大大所大大所大所大11123xx1大厦大萨达大大所大大所大所大11123xx1大厦大萨达大大所大大所大所大11123xx1大厦大萨达大大所大大所大所大11123xx1大厦",
                        organizationInfo: {
                            id: 1,
                            name: "迈瑞第一附属医院",
                        },
                    },
                },
            };
            console.log('templateData',data);
            if (data.error_code === 0) {
                this.examContents = this.currentAssignment.paperInfo.content;
                this.examTitle = this.currentAssignment.title;
                console.log(this.examContents, "examContents");

                if (this.examContents && this.examContents.length > 0) {
                    this.examContents.forEach(topicType => {
                        this.$set(topicType, 'summaryText', this.getTopicTypeSummary(topicType));
                    });
                }

                // this.currentAssignment = data.data;
                // this.useTime = data.data.useTime;
                // this.initAnswer(this.currentAssignment.answer);
                if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                    this.useTimeInterval();
                } else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                    // this.initScoreDetail(this.currentAssignment.scoreDetail);
                    // this.teacherCorrectLock();
                } else if (this.type === CLOUD_TEST_TYPE.VIEW_RESULT) {
                    // this.initScoreDetail(this.currentAssignment.scoreDetail);
                }
            }
        },
        save() {
            if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                let { answer } = this.getAnswer();
                this.submitAnswer(false, answer, {
                    studentName: this.studentName,
                    studentOrg: this.studentOrg,
                });
            } else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                this.submitCorrect();
            }
        },
        submit() {
            if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                let { answer, completeCount } = this.getAnswer();
                let message = this.lang.submit_paper_tip;
                message = message.replace("{a}", completeCount);
                message = message.replace("{b}", this.currentAssignment.questionCount);
                this.$confirm(message, this.lang.tip_title, {
                    confirmButtonText: this.lang.submit_btn,
                    cancelButtonText: this.lang.cancel_button_text,
                })
                    .then(() => {
                        // 添加学生信息到answer对象
                        this.submitAnswer(true, answer, {
                            studentName: this.studentName,
                            studentOrg: this.studentOrg,
                        });
                    })
                    .catch(() => {});
            } else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                this.submitCorrect();
            }
        },
        submitAnswer(isFinish, answer) {
            this.confirmSave = true;
            service
                .submitAnswer({
                    answerSheetID: this.paperId,
                    useTime: this.useTime,
                    isFinish,
                    answer,
                    studentName: this.studentName,
                    studentOrg: this.studentOrg,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.$message.success(this.lang.operate_success);
                        if (isFinish) {
                            this.back();
                        }
                    }
                });
        },
        submitCorrect() {
            let correctList = [];
            this.examContents.forEach((topicType) => {
                topicType.list.forEach((topic) => {
                    correctList.push({
                        index: topic.index,
                        score: topic.correctScore || 0,
                    });
                });
            });

            service
                .submitCorrect({
                    answerSheetID: this.paperId,
                    correctList,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.$message.success(this.lang.operate_success);
                        this.back();
                    }
                });
        },
        unlockAnswerSheet() {
            service.unlockAnswerSheet({
                answerSheetID: this.paperId,
            });
        },
        teacherCorrectLock() {
            this.correctLockInterval = setInterval(() => {
                service.lockAnswerSheet({
                    answerSheetID: this.paperId,
                });
            }, 30000);
        },
        useTimeInterval() {
            this.interval = setInterval(() => {
                this.useTime += 1;
            }, 1000);
        },
        initAnswer(answer) {
            if (!this.examContents) {
                return;
            }

            this.examContents.forEach((topicType) => {
                topicType.list.forEach((topic) => {
                    if (topicType.type === "shortAnswer") {
                        // 简答题
                        const answerItem = answer.find((item) => item.index === topic.index);
                        topic.value = answerItem ? answerItem.value : "";

                        // 如果没有作答记录数组，初始化一个空数组
                        if (!topic.answerRecords) {
                            topic.answerRecords = [];
                        }

                        // 如果有新的答案且不为空，且与最后一条记录不同，则添加到作答记录中
                        if (answerItem && answerItem.value && answerItem.value.trim() !== "") {
                            const lastRecord =
                                topic.answerRecords.length > 0
                                    ? topic.answerRecords[topic.answerRecords.length - 1]
                                    : null;

                            if (!lastRecord || lastRecord.content !== answerItem.value) {
                                // 添加新的作答记录
                                topic.answerRecords.push({
                                    content: answerItem.value,
                                    submitTime: new Date().toISOString(),
                                    user: this.studentName || "学生",
                                });
                            }
                        }
                    } else if (topicType.type === "operation") {
                        // 实操题
                        topic.subTopic.forEach((subTopic) => {
                            const answerItem = answer.find((item) => item.index === topic.index);
                            if (answerItem && answerItem.subTopic) {
                                const subAnswerItem = answerItem.subTopic.find(
                                    (subItem) => subItem.index === subTopic.index
                                );
                                subTopic.value = subAnswerItem ? subAnswerItem.value : [];
                            } else {
                                subTopic.value = [];
                            }
                        });
                    }
                });
            });
        },
        initScoreDetail(scoreDetail) {
            if (!scoreDetail || !this.examContents) {
                return;
            }

            this.examContents.forEach((topicType) => {
                topicType.list.forEach((topic) => {
                    const scoreItem = scoreDetail.find((item) => item.index === topic.index);
                    if (scoreItem) {
                        topic.correctScore = scoreItem.score;
                    }
                });
            });
        },

        jumpToQuestion(index) {
            // 更新当前题目索引
            this.currentQuestionIndex = index;

            // 找到对应的题目并滚动到视图
            this.$nextTick(() => {
                const topicElements = this.$el.querySelectorAll(".topic_detail");
                if (topicElements[index]) {
                    topicElements[index].scrollIntoView({ behavior: "smooth" });
                }
            });
        },
        setCurrentQuestionByTypeIndex(typeIndex, questionIndex) {
            // 计算全局题号
            let globalIndex = 0;
            for (let i = 0; i < typeIndex; i++) {
                globalIndex += this.examContents[i].list.length;
            }
            globalIndex += questionIndex;

            this.jumpToQuestion(globalIndex);
        },
        handleUpdateValue(topic, value) {
            topic.value = value;
        },
        handleUpdatePassStatus(topic, value) {
            // 使用Vue.set确保响应式更新
            this.$set(topic, "isPassed", value);
            // 强制更新视图
            this.$nextTick(() => {
                this.$forceUpdate();
            });
        },
        getAnswer() {
            let answer = [];
            let completeCount = 0;

            if (!this.examContents) {
                return { answer, completeCount };
            }

            this.examContents.forEach((topicType) => {
                if (topicType.type === "shortAnswer") {
                    // 简答题
                    topicType.list.forEach((topic) => {
                        answer.push({
                            index: topic.index,
                            value: topic.value || "",
                        });

                        if (topic.value) {
                            completeCount++;
                        }
                    });
                } else if (topicType.type === "operation") {
                    // 实操题
                    topicType.list.forEach((topic) => {
                        let subTopicAnswer = [];
                        let hasValue = false;

                        topic.subTopic.forEach((subTopic) => {
                            subTopicAnswer.push({
                                index: subTopic.index,
                                value: subTopic.value || [],
                            });

                            if (subTopic.value && subTopic.value.length > 0) {
                                hasValue = true;
                            }
                        });

                        answer.push({
                            index: topic.index,
                            subTopic: subTopicAnswer,
                        });

                        if (hasValue) {
                            completeCount++;
                        }
                    });
                }
            });

            return { answer, completeCount };
        },
        back() {
            this.$router.back();
        },
        openFileTransfer: async function () {
            const service_type = this.systemConfig.ServiceConfig.type.FileTransferAssistant;
            let fileTransferAssistant = await findServiceId(service_type);
            if (fileTransferAssistant.cid) {
                this.openConversation(fileTransferAssistant.cid, 10, 0, (is_succ, conversation) => {
                    this.openFileTransferListener(conversation.id);
                });
            } else {
                this.$root.socket.emit(
                    "request_start_single_chat_conversation",
                    {
                        list: [fileTransferAssistant.id, this.user.uid],
                        start_type: undefined,
                        mode: this.systemConfig.ConversationConfig.mode.Single,
                        type: this.systemConfig.ConversationConfig.type.Single,
                    },
                    async (is_succ, data) => {
                        if (is_succ) {
                            this.$store.commit("conversationList/initConversation", data);
                            this.$store.commit("examList/initExamObj", data);
                            await Tool.handleAfterConversationCreated(data, "openConversation");
                            this.openFileTransferListener(data);
                        } else {
                            this.$message.error(this.lang.start_conversation_error);
                        }
                    }
                );
            }
        },
        openFileTransferListener(cid) {
            // 监听传输助手消息通知
            const controler = this.conversationList[cid].socket;
            if (!controler) {
                setTimeout(() => {
                    this.openFileTransferListener(cid);
                }, 1000);
            } else {
                this.closeFileTransferListener();
                controler.on("other_say", this.collectImage);
                this.fileTransferAssistant = controler;
            }
        },
        closeFileTransferListener() {
            if (this.fileTransferAssistant) {
                this.fileTransferAssistant.off("other_say", this.collectImage);
                this.fileTransferAssistant = null;
            }
        },
        collectImage(messageList) {
            console.log("collectImage1", messageList);
            const msg_type = this.systemConfig.msg_type;
            messageList.forEach(async (msg) => {
                if (
                    msg.msg_type === msg_type.Frame ||
                    msg.msg_type === msg_type.Cine ||
                    msg.msg_type === msg_type.EXAM_IMAGES ||
                    msg.msg_type === msg_type.OBAI
                ) {
                    //只采集单帧，多帧和聚合消息,和产科质控
                    let type = msg.msg_type;
                    if (msg.msg_type === msg_type.EXAM_IMAGES) {
                        type = msg.cover_msg_type;
                    }
                    let downloadUrl = "";
                    if (type === msg_type.Frame) {
                        downloadUrl = msg.url.replace("thumbnail.jpg", `SingleFrame.${msg.img_encode_type}`);
                    } else if (type === msg_type.Cine) {
                        downloadUrl = msg.url.replace("thumbnail.jpg", `DeviceVideo.${msg.img_encode_type}`);
                    } else if (type === msg_type.OBAI) {
                        downloadUrl = msg.url.replace("thumbnail.jpg", `ScreenShot.${msg.img_encode_type}`);
                    }
                    if (downloadUrl) {
                        const response = await fetch(downloadUrl);
                        const blob = await response.blob();
                        const file = new File([blob], `${msg.file_id}.${msg.img_encode_type}`, {
                            type: blob.type, // 你可以从blob中获取MIME类型
                        });
                        this.uploadCollectImage(file, this.collectIndex);
                        console.log("collectImage", file);
                    }
                }
            });
        },
        openCollect(topicTypeIndex, topicIndex, subIndex, subtopic) {
            try {
                const [index, j_index, subIndex] = this.collectIndex;
                this.examContents[index].list[j_index].subTopic[subIndex].collecting = false;
            } catch (e) {
            } finally {
                this.collectIndex = [topicTypeIndex, topicIndex, subIndex];
                window.vm.$set(subtopic, "collecting", true);
                this.openFileTransfer();
            }
        },
        closeCollect(topicTypeIndex, topicIndex, subIndex, subtopic) {
            this.hasClickedStopCollect = true;
            this.collectIndex = [0, 0, 0];
            window.vm.$set(subtopic, "collecting", false);
            this.closeFileTransferListener();
        },
        uploadStart(index, j_index, subIndex) {
            if (this.type === CLOUD_TEST_TYPE.EDIT && subIndex === "-1") {
                this.uploadIndex = [index, j_index];
            } else {
                this.uploadIndex = [index, j_index, subIndex];
            }
            // this.uploadIndex = [index, j_index, subIndex];
            this.$refs.uploadComponent[0].value = ""; //处理无法连续上传相同文件
            this.$refs.uploadComponent[0].click();
        },
        handleFileChange(e) {
            let files = e.target.files;
            let key,
                uploadList = [];
            for (const file of files) {
                if (file.size > 20 * 1024 * 1024) {
                    this.$message.error(`${this.lang.upload_max_text}20M`);
                    return;
                }
                if (file.size == 0) {
                    this.$message.error(`${this.lang.upload_min_text}0M`);
                    return;
                }

                // 检查文件格式
                const fileExt = file.name.split(".").pop().toLowerCase();
                // const isImage = file.type.startsWith('image/')
                // const isVideo = file.type.startsWith('video/')

                //Chromeium类浏览器flv识别有问题
                // console.log('fileType',file.type.split('/')[0])
                // console.log('fileExt',fileExt)
                // if (!Tool.getSupportVideoType().includes(fileExt) && !Tool.getSupportImageType().includes(fileExt)) {
                //     this.$message.error(this.lang.supply_exam_image.err_tip.unsupported_file_format)
                //     return
                // }
                this.uploadCollectImage(file, this.uploadIndex);
            }
        },
        uploadCollectImage(file, uploadIndex) {
            let dir = new Date().getTime() + parseInt(Math.random() * 1000 + 1000, 10); // 目录
            var date = new Date();
            var time =
                date.getFullYear() +
                "-" +
                (date.getMonth() < 9 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1) +
                "-" +
                (date.getDate() < 10 ? "0" + date.getDate() : date.getDate());
            let filePath = `homework/operationImage/${time}/${dir}/${file.name}`;
            let uploadConfig = this.systemConfig.serverInfo.file_upload_config;
            //OSS
            uploadFile({
                bucket: uploadConfig.ossInfo.bucket,
                filePath: filePath,
                file,
                callback: (event, data) => {
                    console.log("uploadFile", event, data);
                    if ("complete" == event) {
                        let url = uploadConfig.ossInfo.playback_https_addr + "/" + filePath;
                        const index = uploadIndex[0];
                        const j_index = uploadIndex[1];
                        const subIndex = uploadIndex[2];
                        const msg_type = Tool.getMsgType(filePath);

                        if (this.type === CLOUD_TEST_TYPE.EDIT) {
                            if (uploadIndex.length === 2) {
                                // 题目主体的图片
                                if (!this.examContents[index].list[j_index].imageList) {
                                    this.examContents[index].list[j_index].imageList = [];
                                }
                                this.examContents[index].list[j_index].imageList.push({
                                    msg_type: msg_type,
                                    url: url,
                                });
                            } else {
                                // 子题目的图片
                                if (!this.examContents[index].list[j_index].subTopic[subIndex].imageList) {
                                    this.examContents[index].list[j_index].subTopic[subIndex].imageList = [];
                                }
                                this.examContents[index].list[j_index].subTopic[subIndex].imageList.push({
                                    msg_type: msg_type,
                                    url: url,
                                });
                            }
                        } else {
                            // 原有上传逻辑
                            this.examContents[index].list[j_index].subTopic[subIndex].value.push({
                                msg_type: msg_type,
                                url: url,
                            });
                        }
                    } else if ("error" == event) {
                        this.$message.error(this.lang.upload_file_error_text);
                    }
                },
            });
        },
        uploadAndAddToList(file, topic, subIndex) {
            const isImage = this.getSupportImageType().some((type) =>
                file.type.toLowerCase().includes(type.toLowerCase())
            );
            const isVideo = this.getSupportVideoType().some((type) =>
                file.type.toLowerCase().includes(type.toLowerCase())
            );

            if (!isImage && !isVideo) {
                this.$message.error(this.lang.file_type_error);
                return;
            }

            // 上传文件
            uploadFile(file)
                .then((res) => {
                    if (res.url) {
                        const fileObj = {
                            msg_type: isImage ? 3 : 4,
                            url: res.url,
                            size: file.size,
                            name: file.name,
                        };

                        if (subIndex === "-1") {
                            // 添加到题目图片
                            if (!topic.imageList) {
                                topic.imageList = [];
                            }
                            topic.imageList.push(fileObj);
                        } else {
                            // 添加到子题图片
                            if (!topic.subTopic[subIndex].value) {
                                topic.subTopic[subIndex].value = [];
                            }
                            topic.subTopic[subIndex].value.push(fileObj);
                        }

                        this.$forceUpdate();
                    }
                })
                .catch((err) => {
                    this.$message.error(this.lang.upload_failed);
                });
        },
        handleImageOrderChange(imageList, evt) {
            const { oldIndex, newIndex } = evt;
            if (oldIndex !== newIndex) {
                // 手动执行数组元素的移动操作
                const itemToMove = imageList[oldIndex];
                // 从数组中移除
                imageList.splice(oldIndex, 1);
                // 在新位置插入
                imageList.splice(newIndex, 0, itemToMove);
            } else {
                console.log("NO CHANGE");
            }
        },
        getTrainingStudentInfo() {
            service
                .getTrainingStudentInfo({
                    trainingID: this.trainingID,
                })
                .then((res) => {
                    const data = res.data;
                    if (data.error_code === 0) {
                        this.studentName = data.data.name;
                        this.studentOrg = data.data.hospital;
                    }
                });
        },
    },
};
</script>

<style lang="scss" scoped>
.smart_tech_exam_container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f7f9fc;
    z-index: 10;
    .custom_header {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        background-color: #fff;
        border-bottom: 1px solid #ebeef5;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
        position: relative;
        z-index: 1;

        .back_btn {
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #303133;
            font-size: 14px;
            transition: color 0.2s ease;

            i {
                margin-right: 5px;
                font-size: 16px;
            }

            &:hover {
                color: #409eff;
            }
        }
    }

    .custom_body {
        height: 100%;
        padding: 25px;
        overflow-y: hidden;
        background-color: #f7f9fc;
        position: relative;
        display: flex;
        flex-direction: column;
        .correct_exam_detail {
            display: flex;
            color: #333;
            padding-bottom: 20px;
            .exam_detail_left {
                flex-wrap: wrap;
                flex: 4;
                display: flex;
                justify-content: space-between;
                padding: 0 20px;
                font-size: 16px;
                align-items: center;
                .exam_detail_author {
                    .el-input {
                        width: 150px;
                    }

                    .text-editor {
                        width: 150px;
                    }
                }

                .student_info {
                    width: 100%;
                    margin-top: 10px;
                    display: flex;
                    align-items: center;
                    .student_info_row {
                        display: flex;
                        align-items: center;
                    }

                    .student_info_row_org {
                        margin-left: 10%;
                    }

                    .student_info_label {
                        flex-shrink: 0;
                    }
                    .el-input {
                        margin-left: 10px;
                        width: 200px;
                        font-size: 15px;
                        input {
                            padding: 0 10px;
                            text-align: center;
                        }
                    }
                }
            }
            .exam_detail_right {
                flex: 6;
                display: flex;
                justify-content: flex-end;
                font-size: 16px;
                align-items: center;
                .exam_detail_item {
                    margin-right: 30px;
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    span {
                        margin: 0 8px;
                    }
                    .assignment_score {
                        color: #00c59d;
                        font-size: 40px;
                        font-weight: 100;
                    }
                }
                button {
                    width: 180px;
                }
            }
        }
        .topic_content {
            flex: 1;
            overflow: auto;
            background: #ebeff2;
            padding: 20px;
            border-radius: 6px;

            .exam-title-wrapper {
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 20px;
                background-color: #fff;
                border-radius: 6px;
                padding: 10px 120px 10px 20px;
                display: flex;
                align-items: center;

                .title-tip-label {
                    margin-right: 4px;
                    white-space: nowrap;
                }

                .text-editor {
                    font-size: 18px;
                    font-weight: bold;
                    flex: 1;
                }

                .editable-content {
                    input {
                        font-size: 18px;
                        font-weight: bold;
                    }
                }
            }
        }
        .topic_summary {
            margin-bottom: 10px;
            line-height: 2.5;
            font-size: 16px;
            font-weight: bold;
        }
        .topic_tip {
            color: #000;
            margin-bottom: 10px;
            background: #fff7ec;
            padding: 14px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 15px;
        }
        .topic_detail {
            position: relative;
            font-size: 16px;
            background-color: #fff;
            border-radius: 6px;
            padding: 20px;
            margin: 30px 0;
        }
        div.topic_detail:last-of-type {
            border-bottom: none;
        }

        .content-container {
            display: flex;
            gap: 20px;
            align-items: flex-start; /* 防止子项在交叉轴上拉伸 */
        }

        .topic_list {
            flex: 1;
        }

        .topic_content .progress-indicator {
            position: sticky;
            bottom: 120px;
            right: 1%;
            align-self: flex-start;
            margin: 0 1%;
            z-index: 10;
        }
    }
}
.topic_footer_pagination {
    margin-top: 20px;
    text-align: center;
}
</style>
