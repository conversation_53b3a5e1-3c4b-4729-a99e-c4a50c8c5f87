<template>
    <div class="SingleSelectTopic_wrapper">
        <div class="topic_title">
            <span class="topic-index" v-if="type === CLOUD_TEST_TYPE.EDIT || topic.index">{{topic.index}}. </span>
            <text-field
                :value="topic.title"
                :isEdit="type === CLOUD_TEST_TYPE.EDIT"
                :placeholder="lang.input_enter_tips"
                @change="(val) => handleFieldChange('title', val)"
            />
            <!-- 编辑模式，可编辑题目标题 -->
            <div class="topic_operation">
                <p class="topic_count" v-if="type === CLOUD_TEST_TYPE.VIEW || type === CLOUD_TEST_TYPE.ANSWER || type === CLOUD_TEST_TYPE.CORRECT">{{ getTopicCount }}</p>
                <template v-else-if="type === CLOUD_TEST_TYPE.EDIT">
                    <div class="topic_count">
                        <!-- 编辑模式，可设置题目分数 -->
                        <text-field
                            :value="topic.score"
                            :isEdit="true"
                            inputType="number"
                            @change="(val) => handleFieldChange('score', Number(val))"
                        />
                        {{ lang.point_tip }}
                    </div>
                </template>
                <p class="topic_score" v-else-if="type === CLOUD_TEST_TYPE.VIEW_RESULT">
                    (<span>{{ topic.correctScore }}{{ lang.point_tip }}</span
                    >/{{ topic.score }}{{ lang.point_tip }})
                </p>
                <!-- 编辑模式，可删除题目 -->
                <el-popconfirm
                    v-if="type === CLOUD_TEST_TYPE.EDIT"
                    :title="lang.homework.confirm_delete_topic"
                    @confirm="deleteTopic"
                    class="homework-delete-topic-btn"
                >
                    <el-button slot="reference" class="delete-topic-btn" type="text">
                        <i class="el-icon-delete"></i>
                    </el-button>
                </el-popconfirm>
            </div>
        </div>
        <!-- 编辑模式，在题目下方显示图片上传 -->
        <view-list
            class="topic_image_list"
            v-if="type === CLOUD_TEST_TYPE.EDIT"
            :image-list="topic.imageList"
            :disable-delete="false"
            :disable-upload="false"
            :type="type"
            @view-image="viewImage"
            @delete-image="(index) => deleteImage(index)"
            @upload-click="() => $emit('upload-click')"
            @order-changed="(evt) => handleImageOrderChange(evt)"
        />
        <!-- 非编辑模式，显示图片上传 -->
        <view-list
            class="topic_image_list"
            v-if="type !== CLOUD_TEST_TYPE.EDIT"
            :image-list="topic.imageList"
            :disable-delete="true"
            :disable-upload="true"
            :type="type"
            @view-image="viewImage"
        />
        <div class="topic_radio" v-for="option of topic.options" :key="topic.index + option.label">
            <!-- 非编辑模式，单选题显示选项 -->
            <el-radio
                v-if="type !== CLOUD_TEST_TYPE.EDIT"
                :value="topic.value"
                :label="option.label"
                border
                :disabled="disableModify"
                @change="(value) => handleFieldChange('value', value)"
            ></el-radio>
            <!-- 编辑模式，可编辑选项内容 -->
            <choice-option-field
                v-if="type === CLOUD_TEST_TYPE.EDIT"
                :index="option.label"
                :value="option.content"
                :placeholder="lang.input_enter_tips"
                :showIndex="true"
                @change="(val) => handleOptionChange(option, val)"
            />
            <span v-else @click="toggleRadio(option)">{{ option.content }}</span>
        </div>

        <template v-if="type === CLOUD_TEST_TYPE.CORRECT">
            <div class="correcting_item_wrapper">
                <div class="correcting_item">
                    <div
                        class="full_score"
                        @click="correctSelect(1)"
                        :class="{ active: topic.correctScore === topic.score }"
                    >
                        {{ lang.correct_tip }}
                    </div>
                    <div class="empty_score" @click="correctSelect(0)" :class="{ active: topic.correctScore === 0 }">
                        {{ lang.error_tip }}
                    </div>
                </div>
                <!-- 批改模式，显示参考答案 -->
                <div class="reference-answer-display" v-if="topic.answer">
                    <span class="ref-label">{{ lang.homework.reference_answer }}: </span>
                    <span class="ref-content">{{ topic.answer }}</span>
                </div>
            </div>
        </template>

        <div v-if="type === CLOUD_TEST_TYPE.EDIT" class="reference-answer">
            <!-- 编辑模式，可编辑参考答案 -->
            <span class="ref-label">{{ lang.homework.reference_answer }}: </span>
            <single-select-field
                :value="topic.answer"
                :options="topic.options"
                :placeholder="lang.homework.select_option_placeholder"
                @change="(val) => handleFieldChange('answer', val)"
            />
        </div>
        <image-viewer ref="imageViewer"></image-viewer>
    </div>
</template>

<script>
import base from "../../../lib/base";
import viewList from "./viewList.vue";
import imageViewer from "../../../MRComponents/imageViewer";
import TextField from './TextField.vue';
import ChoiceOptionField from './ChoiceOptionField.vue';
import SingleSelectField from './SingleSelectField.vue';
import { CLOUD_TEST_TYPE } from '../../../lib/constants';
export default {
    name: "SingleSelectTopic",
    components: {
        viewList,
        imageViewer,
        TextField,
        ChoiceOptionField,
        SingleSelectField,
    },
    mixins: [base],
    props: {
        topic: {
            type: Object,
            required: true,
        },
        type: {
            type: Number,
            required: true,
        },
        topicTypeIndex: {
            type: Number,
            required: true,
        },
        topicIndex: {
            type: Number,
            required: true,
        },
        disableModify: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            CLOUD_TEST_TYPE: CLOUD_TEST_TYPE,
        };
    },
    computed: {
        getTopicCount() {
            let topicCount = this.lang.topic_count;
            topicCount = topicCount.replace("{a}", this.topic.score);
            return topicCount;
        },
    },
    methods: {
        handleFieldChange(field, value) {
            this.$emit("field-change", this.topic, field, value);
        },
        handleOptionChange(option, value) {
            this.$emit("option-change", option, "content", value);
        },
        deleteTopic() {
            this.$emit("delete-topic", this.topicTypeIndex, this.topicIndex);
        },
        viewImage(imageList, index) {
            this.$refs.imageViewer.init(imageList, index);
        },
        deleteImage(index) {
            this.$emit("delete-image", this.topic.imageList, index);
        },
        toggleRadio(option) {
            if (this.disableModify) {
                return;
            }
            this.$emit("field-change", this.topic, "value", option.label);
        },
        correctSelect(type) {
            let score = 0;
            if (type === 0) {
                score = 0;
            } else if (type === 1) {
                score = this.topic.score;
            }
            this.$emit("field-change", this.topic, "correctScore", score);
        },
        handleImageOrderChange(evt) {
            this.$emit("image-order-changed", this.topic.imageList, evt);
        },
    },
};
</script>

<style lang="scss" scoped>
.SingleSelectTopic_wrapper {
    .topic_title {
        display: flex;
        align-items: center;

        :deep(.delete-topic-btn) {
            margin-left: 10px;
            padding: 0;
            font-size: 18px;
            color: #f56c6c;

            &:hover {
                color: #fff;
                background: #ff4d4d;
            }
        }
        .topic_operation {
            display: flex;
            align-items: center;
            margin-left: 10px;
            padding-right: 10px;
        }
    }

    .topic_image_list {
        margin-left: 20px;
    }

    .topic_radio {
        margin: 16px 0;
        display: flex;
        align-items: center;
        position: relative;
        cursor: pointer;

        :deep(.el-radio.is-bordered),
        :deep(.el-checkbox.is-bordered) {
            position: absolute;
            left: 0;
            top: 50%;
            width: 40px;
            height: 40px;
            line-height: 50px;
            border-radius: 50%;
            background: #fff;
            border: 1px solid #eceff2;
            padding: 0;
            line-height: 40px;
            text-align: center;
            transform: translateY(-50%);

            .el-radio__label,
            .el-checkbox__label {
                padding: 0;
            }
        }

        :deep(.el-radio__input),
        :deep(.el-checkbox__input) {
            display: none;
        }

        :deep(.el-radio) + span,
        :deep(.el-checkbox) + span {
            background: #ebeff2;
            flex: 1;
            padding: 10px 60px;
            border-radius: 20px;
            margin-right: 20px;
            line-height: 20px;
            color: #000;
        }

        :deep(.el-radio__input.is-checked + .el-radio__label),
        :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
            color: #fff;
        }

        :deep(.el-radio.is-checked),
        :deep(.el-checkbox.is-checked) {
            background-color: #00c59d;
        }

        :deep(.el-radio.is-checked + span),
        :deep(.el-checkbox.is-checked + span) {
            background-color: #cdf3eb;
        }

        :deep(.is-disabled + span) {
            cursor: not-allowed;
        }
    }

    .correcting_item_wrapper {
        border-top: 1px dashed #606266;
        display: flex;
        gap: 10px;

        .reference-answer-display {
            position: relative;
            top: 40px;
            left: 30px;
            display: grid;
            grid-template-columns: auto 1fr;
            align-items: start;

            .ref-label {
                color: #666;
                margin-right: 8px;
                white-space: nowrap;
            }

            .ref-content {
                color: #ff9900;
                word-break: break-word;
            }
        }
    }

    .correcting_item {
        position: static;
        display: flex;
        flex-direction: row;
        gap: 10px;

        > div {
            width: 60px;
            height: 60px;
            margin: 20px 0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fff;
            text-align: center;
            cursor: pointer;
            box-sizing: border-box;
        }

        .full_score {
            color: #00c59d;
            border: 1px solid #00c59d;

            &.active {
                color: #fff;
                background-color: #00c59d;
            }
        }

        .empty_score {
            color: #f00;
            border: 1px solid #f00;

            &.active {
                color: #fff;
                background-color: #f00;
            }
        }
    }

    .reference-answer {
        display: flex;
        align-items: center;
        margin-top: 10px;

        .ref-label {
            margin-right: 8px;
            flex-shrink: 0;
        }
    }

    .topic-index {
        white-space: nowrap;
        margin-right: 8px;
    }

    .topic_count {
        display: flex;
        color: #999;
        align-items: center;
        flex-shrink: 0;
        :deep(.editable-content) {
            display: inline-block;
            flex: initial;
            margin-right: 0px;

            input {
                width: 32px;
                height: 32px;
                padding: 0 4px;
                text-align: right;

                // 隐藏number类型输入框的上下箭头
                &::-webkit-outer-spin-button,
                &::-webkit-inner-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }
                &[type="number"] {
                    -moz-appearance: textfield;
                }
            }
        }
    }

    .topic_score {
        span {
            color: red;
        }
    }
}
</style>
