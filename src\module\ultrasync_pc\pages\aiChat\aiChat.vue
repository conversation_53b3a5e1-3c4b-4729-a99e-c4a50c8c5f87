<template>
    <div class="ai-chat">
        <!-- 使用新的组件替换旧的 div.function-bar -->
        <ai-chat-function-menu
            :chat-list="chatList"
            :current-chat-id="currentChatId"
            @create-new-chat="createNewChat"
            @switch-chat="switchChat"
            @delete-chat="deleteChat"
            @open-practice="openClinicalThinkingPractice"
            @leave-ai="handleLeaveAI"
            class="ai-chat-function-menu"
        />
        <!-- <div class="chat-sidebar" v-show="showSidebar">
            <div class="chat-list">
                <div v-for="chat in chatList" :key="chat.id"
                    :class="['chat-item', { active: currentChatId === chat.id }]" @click="switchChat(chat.id)">
                    <i class="el-icon-chat-dot-square"></i>
                    <span class="chat-title">{{ chat.title }}</span>
                    <i class="el-icon-delete delete-icon" @click.stop="deleteChat(chat.id)"
                        v-if="chatList.length > 1"></i>
                </div>
            </div>
        </div> -->

        <div class="chat-main">
            <WelcomeComponent @animation-completed="handleAnimationCompleted" :text="lang.welcome_to_realm_imaging_intelligence"
                v-show="shouldShowWelcome" class="welcome-screen" />
            <div v-show="isCurrentNewChat && isWelcomeCompleted" class="welcome-container">
                <div class="welcome-avatar">
                    <mr-avatar :url="`static/resource_pc/images/ai_logo_large_6.png`" :radius="200"
                        :showOnlineState="false" class="avatar"></mr-avatar>
                </div>
                <div class="chat-input welcome-input" v-if="currentChatId">
                    <div class="input-wrapper">
                        <el-input
                            v-model="inputMessage"
                            type="textarea"
                            :placeholder="`${lang.input_talk_about_tips_Shift_Enter}`" @keyup.enter.native.exact="sendMessage"
                             maxlength="1000"
                             :autosize="{
                                minRows:1,
                                maxRows:3
                             }"
                            @contextmenu.native="callTextAreaMenu($event)"
                            ref="welcome_input_textarea" />
                    </div>
                    <div class="chat-options">
                        <div class="left-options">
                            <el-checkbox v-model="deepThinking">{{ lang.deep_thinking }}</el-checkbox>
                            <el-checkbox v-model="webSearch">联网搜索</el-checkbox>
                        </div>
                        <div class="right-options">
                            <div class="input-actions">
                                <!-- <el-button icon="el-icon-video-pause" @click="stopAskQuestion" v-if="isWaitingResponse">暂停生成</el-button> -->
                                <el-button :class="['send-btn', { 'is-sending': isWaitingResponse }]" circle type="primary"
                                    @click="isWaitingResponse ? stopAskQuestion(currentChatId) : sendMessage()"
                                    :loading="isAborting">
                                    <i :class="isWaitingResponse ? 'el-icon-video-pause' : 'el-icon-s-promotion'" v-show="!isAborting"></i>
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="recommendation-buttons">
                    <div class="hint-text">{{lang.you_could_say_tips}}</div>
                    <div class="button-row">
                        <div class="rec-button" @click="
                            handleRecommendationClick('显微造影（SR CEUS）技术有什么临床价值和意义？')
                            ">
                            显微造影（SR CEUS）技术有什么临床价值和意义？
                        </div>
                        <div class="rec-button" @click="handleRecommendationClick('针对肥胖患者，怎样提高超声图像的清晰度？')">
                            针对肥胖患者，怎样提高超声图像的清晰度？
                        </div>
                    </div>
                </div>
            </div>

            <template v-if="!isCurrentNewChat">
                <div class="chat-messages" ref="messageContainer" @scroll="handleScroll">
                    <div v-for="(msg, index) in messages" :key="index" :class="['message', msg.type]">
                        <div class="message-info">
                            <mr-avatar :url="msg.type === 'user' ? getLocalAvatar(user) : aiAvatar" :radius="40"
                                :showOnlineState="false" :key="msg.type === 'user' ? user.avatar : 'ai'"
                                class="avatar"></mr-avatar>
                            <div class="message-time">{{ msg.time }}</div>
                        </div>
                        <div class="message-content-wrapper">
                            <div class="message-content" v-if="msg.type === 'ai'">
                                <span v-if="msg.status === 'thinking'" class="thinking-tips">{{
                                    `${lang.thinking}...`
                                    }}</span>
                                <span v-if="msg.status === 'answering'" class="answering-tips">{{
                                    `${lang.answering}...`
                                    }}</span>
                                <div class="reasoning-content" v-if="msg.hasReasoningContent">
                                    <html-renderer :html="renderMarkdown(msg.reasoningContent)"/>

                                    <i class="el-icon-loading" v-if="msg.status === 'thinking'"></i>
                                </div>
                                <div class="answer-content">
                                    <html-renderer :html="renderMarkdown(msg.content)"/>
                                    <i class="el-icon-loading" v-if="msg.status === 'answering'"></i>
                                    <div v-if="msg.status === 'completed_stop_by_user'">
                                        <span class="stop-by-user-tips">[{{ lang.user_has_terminated_answering
                                            }}]</span>
                                    </div>
                                    <div class="message-actions"
                                        v-if="msg.status === 'completed' || msg.status === 'completed_stop_by_user'">
                                        <el-button size="mini" icon="el-icon-document-copy"
                                            @click="copyContent(msg.content)">{{ lang.copy }}</el-button>
                                    </div>
                                </div>
                            </div>
                            <div class="message-content" v-else>
                                <html-renderer :html="renderMarkdown(msg.content)"/>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chat-input" v-if="currentChatId">
                    <div class="input-wrapper">
                        <el-input v-model="inputMessage"
                            type="textarea"
                            :placeholder="`${lang.input_talk_about_tips_Shift_Enter}`" @keyup.enter.native.exact="sendMessage"
                             maxlength="1000"
                             :autosize="{
                                minRows:1,
                                maxRows:3
                             }"
                            @contextmenu.native="callTextAreaMenu($event)"
                            ref="chat_input_textarea" />
                    </div>
                    <div class="chat-options">
                        <div class="left-options">
                            <el-checkbox v-model="deepThinking">{{ lang.deep_thinking }}</el-checkbox>
                            <el-checkbox v-model="webSearch">联网搜索</el-checkbox>
                        </div>
                        <div class="right-options">
                            <div class="input-actions">
                            <!-- <el-button icon="el-icon-video-pause" @click="stopAskQuestion" v-if="isWaitingResponse">暂停生成</el-button> -->
                            <el-button :class="['send-btn', { 'is-sending': isWaitingResponse }]" circle type="primary"
                                @click="isWaitingResponse ? stopAskQuestion(currentChatId) : sendMessage()"
                                :loading="isAborting">
                                <i :class="isWaitingResponse ? 'el-icon-video-pause' : 'el-icon-s-promotion'" v-show="!isAborting"></i>
                            </el-button>
                        </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
        <!-- 在最上层添加关闭按钮 -->
        <!-- <div class="close-button" @click="back">
               <div class="close-icon">
                   <span></span>
                   <span></span>
               </div>
               <div class="button-glow"></div>
           </div> -->
    </div>
</template>

<script>
import base from "../../lib/base";
import { getLocalAvatar } from "../../lib/common_base";
import "highlight.js/styles/github.css";
import "katex/dist/katex.min.css";
import moment from "moment";
import AIChatService from "@/common/aiChatService";
import WelcomeComponent from "./welcome.vue";
import HtmlRenderer from '@/components/vNodeRender/htmlRender.vue';
import Tool from '@/common/tool.js';
import AiChatFunctionMenu from './aiChatFunctionMenu.vue';

export default {
    mixins: [base],
    name: "AiChatPage",
    computed: {
        messages() {
            return this.chatMessages[this.currentChatId] || [];
        },
        isCurrentNewChat() {
            // 添加对 chatList 的直接引用来确保响应式
            const listLength = this.chatList.length;
            return (
                listLength > 0 &&
                this.chatList[0]?.id === this.currentChatId &&
                (!this.chatMessages[this.currentChatId] || this.chatMessages[this.currentChatId].length === 0)
            );
        },
        shouldShowWelcome() {
            const isWelcomeAnimationShown = this.$store.state.dynamicGlobalParams.isWelcomeAnimationShown
            if(isWelcomeAnimationShown){
                this.isWelcomeCompleted = true;
            }
            return !isWelcomeAnimationShown;
        },
    },
    components: {
        WelcomeComponent,
        HtmlRenderer,
        AiChatFunctionMenu
    },
    data() {
        return {
            getLocalAvatar,
            inputMessage: "",
            chatList: [],
            currentChatId: 0,
            chatMessages: {}, // 存储每个会话的消息
            deepThinking: false,
            webSearch: false,
            aiAvatar: "static/resource_pc/images/ai_logo_small_6.png", // 添加AI头像
            isWaitingResponse: false,
            isUserScrolling: false, // 添加新变量追踪用户是否在滚动
            lastScrollTop: 0, // 记录上次滚动位置
            loadedChats: new Set(), // 新增：记录已加载历史记录的会话ID
            aiChatService: null,
            showSidebar: false, // 添加控制侧边栏显示的状态
            isWelcomeCompleted: false,
            isAborting: false,
            isCreatingChat: false,
        };
    },
    created() {
        this.aiChatService = new AIChatService();
        this.createNewChat();
        this.getRecentConversationList();
    },
    mounted() {
        // 添加点击监听器
        document.addEventListener("click", this.handleGlobalClick);
        document.addEventListener("click", this.handleOutsideClick);
        this.$root.eventBus.$on("ai-chat-link-click", this.handleLinkClick);
    },
    beforeDestroy() {
        // 移除点击监听器
        document.removeEventListener("click", this.handleGlobalClick);
        document.removeEventListener("click", this.handleOutsideClick);
        this.$root.eventBus.$off("ai-chat-link-click");
        this.stopAskQuestion(this.currentChatId);
    },
    methods: {
        formatMessageTime(timestamp) {
            const messageTime = moment(timestamp);
            const today = moment().startOf("day");

            if (messageTime.isSame(today, "day")) {
                // 今天的消息只显示时分秒
                return messageTime.format("HH:mm:ss");
            } else {
                // 昨天及以前的消息显示完整的年月日时分秒
                return messageTime.format("YYYY-MM-DD HH:mm:ss");
            }
        },
        async createNewChat() {
            // 如果正在创建中，不能再次调用这个函数
            if (this.isCreatingChat) {
                return;
            }
            this.isCreatingChat = true;
            if(this.isGeneratingAIResponse()){
                await this.stopAskQuestion(this.currentChatId);
            }
            try {
                // 检查是否已经存在空白未使用的对话
                const existingEmptyChat = this.chatList.find((chat) => {
                    return this.chatMessages[chat.id]?.length === 0;
                });

                // 如果存在空白对话，直接切换到该对话
                if (existingEmptyChat) {
                    this.switchChat(existingEmptyChat.id, true);
                    return;
                }

                // 如果没有空白对话，创建新对话
                const data = await this.aiChatService.createAiConversation();
                const newId = data.conversationID;
                this.chatList.unshift({
                    id: newId,
                    title: this.lang.new_chat,
                });
                this.$set(this.chatMessages, newId, []);
                this.loadedChats.add(newId);
                this.switchChat(newId, true);
            } finally {
                this.isCreatingChat = false;
            }
        },
        async getRecentConversationList() {
            const data = await this.aiChatService.getRecentConversationList();
            data.forEach((item) => {
                this.chatList.push({
                    id: item.conversationID,
                    title: item.question,
                });
            });
        },
        renderMarkdown(content) {
            if (!content) {
                return "";
            }
            return this.aiChatService.renderMarkdown(content);
        },
        async switchChat(chatId, newConversation = false) {
            this.showSidebar = false;
            if (chatId === this.currentChatId) {
                return;
            }
            // 如果请求还在进行中,终止请求
            this.stopAskQuestion(this.currentChatId);
            this.currentChatId = chatId;
            this.inputMessage = "";

            // 只在首次加载时获取历史记录
            if (!this.loadedChats.has(chatId) && !newConversation) {
                this.$set(this.chatMessages, chatId, []);
                const data = await this.aiChatService.getConversationHistory(chatId);
                // 处理每条历史记录
                data.forEach((record) => {
                    // 添加AI回答消息
                    if ((record.answer && record.answer.trim()) || record.status === 2) {
                        let aiMessage = {
                            type: "ai",
                            content: record.answer,
                            reasoningContent: record.reasoning_content || "",
                            hasReasoningContent: !!record.reasoning_content,
                            time: this.formatMessageTime(record.updatedAt),
                            status: "completed",
                        };
                        if (record.status === 2) {
                            this.$set(aiMessage, "status", "completed_stop_by_user");
                        }
                        this.chatMessages[chatId].unshift(aiMessage);
                    }
                    // 添加用户消息
                    this.chatMessages[chatId].unshift({
                        type: "user",
                        content: record.question,
                        time: this.formatMessageTime(record.createdAt),
                        status: "completed",
                    });
                });
                this.loadedChats.add(chatId);
                this.$nextTick(() => {
                    this.scrollToBottom(true);
                });
            }
            this.$nextTick(() => {
                this.scrollToBottom(true);
            });
        },

        async deleteChat(chatId) {
            const index = this.chatList.findIndex((chat) => chat.id === chatId);
            if (index !== -1) {
                this.chatList.splice(index, 1);
                this.$delete(this.chatMessages, chatId);

                // 如果删除的是当前会话，需要切换到其他会话
                if (this.currentChatId === chatId && this.chatList.length > 0) {
                    // 优先选择有消息内容的会话
                    let targetChat = this.chatList.find(chat => 
                        this.chatMessages[chat.id] && this.chatMessages[chat.id].length > 0
                    );
                    // 如果没有有内容的会话，就选择第一个
                    if (!targetChat) {
                        targetChat = this.chatList[0];
                    }
                    this.switchChat(targetChat.id);
                }
            }
            // 删除 chat 后，将等待响应状态设为 false
            this.isWaitingResponse = false;
            this.aiChatService.deleteAiConversation(chatId);
        },
        clearMessages() {
            this.messages = [];
        },
        async sendMessage(event) {
            if (this.inputMessage.trim() === "" || this.isWaitingResponse) {
                return;
            }
            const messageContent = this.inputMessage.trim();

            const userMessage = {
                type: "user",
                question: messageContent,
                conversationID: this.currentChatId,
                content: messageContent,
                time: this.formatMessageTime(new Date()),
            };

            if (!this.chatMessages[this.currentChatId]) {
                this.chatMessages[this.currentChatId] = [];
            }

            this.chatMessages[this.currentChatId].push(userMessage);
            // 如果是当前会话的第一条消息,更新会话标题
            if (this.chatMessages[this.currentChatId].length === 1) {
                const chat = this.chatList.find((chat) => chat.id === this.currentChatId);
                if (chat) {
                    chat.title = messageContent;
                }
            }
            this.inputMessage = "";
            this.scrollToBottom(true);

            this.isWaitingResponse = true;
            try {
                await this.getAIResponse(userMessage);
            } finally {
                this.isWaitingResponse = false;
            }
        },
        async getAIResponse(userMessage) {
            try {
                await this.askQuestion(userMessage.question, userMessage.conversationID);
            } catch (error) {
                console.error("Error:", error);
                // 使用 Vue.set 或 this.$set 来确保响应式更新
                this.$set(userMessage, "content", this.lang.sorry_an_error_occurred_please_try_again);
                this.$set(userMessage, "status", "error");
                // 或者直接更新整个消息数组
                this.chatMessages[userMessage.conversationID] = [...this.chatMessages[userMessage.conversationID]];
            }
        },
        async askQuestion(question, conversationID) {
            try {
                if (!this.chatMessages[conversationID]) {
                    this.$set(this.chatMessages, conversationID, []);
                }

                const aiMessage = {
                    type: "ai",
                    status: "thinking",
                    reasoningContent: "",
                    content: "",
                    time: this.formatMessageTime(new Date()),
                    hasReasoningContent: false,
                };
                this.chatMessages[conversationID].push(aiMessage);

                await this.aiChatService.askQuestion({
                    question,
                    conversationID,
                    deepThinking: this.deepThinking,
                    webSearch: this.webSearch,
                    onThinking: (reasoningContent) => {
                        if(aiMessage.status === "completed_stop_by_user"){
                            return
                        }
                        if (reasoningContent) {
                            if (!aiMessage.hasReasoningContent) {
                                this.$set(aiMessage, "hasReasoningContent", true);
                            }
                            let content = aiMessage.reasoningContent + reasoningContent;
                            this.$set(aiMessage, "reasoningContent", content);
                        }
                        this.$set(aiMessage, "status", "thinking");
                        this.$nextTick(() => this.scrollToBottom());
                    },
                    onAnswering: (content) => {
                        if(aiMessage.status === "completed_stop_by_user"){
                            return
                        }
                        let newContent = aiMessage.content + content;
                        this.$set(aiMessage, "content", newContent);
                        this.$set(aiMessage, "status", "answering");
                        this.$nextTick(() => this.scrollToBottom());
                    },
                    onCompleted: () => {
                        if(aiMessage.status === "completed_stop_by_user"){
                            return
                        }
                        this.$set(aiMessage, "status", "completed");
                        this.$nextTick(() => this.scrollToBottom());
                    },
                    onError: (message, error) => {
                        console.error(message, error);
                        if(aiMessage.status === "completed_stop_by_user"){
                            return
                        }
                        let content = aiMessage.content + `\n[${this.lang.server_request_exception_tips}]`;
                        this.$set(aiMessage, "content", content);
                        this.$set(aiMessage, "status", "error");
                        this.$nextTick(() => this.scrollToBottom());
                    },
                    onAbort: () => {
                        if(aiMessage.status === "completed_stop_by_user"){
                            return
                        }
                        this.$set(aiMessage, "status", "completed_stop_by_user");
                        this.$nextTick(() => this.scrollToBottom());
                    },
                });
            } catch (error) {
                console.error("Error:", error);
                // 添加安全检查，确保消息数组存在
                const messages = this.chatMessages[conversationID];
                if (messages && Array.isArray(messages)) {
                    const aiMessage = messages.findLast(
                        (msg) => msg.type === "ai" && msg.status !== "completed"
                    );
                    if (aiMessage) {
                        let content = aiMessage.content + `\n[${this.lang.server_request_exception_tips}]`;
                        this.$set(aiMessage, "content", content);
                        this.$set(aiMessage, "status", "error");
                    }
                }
            }
        },
        async stopAskQuestion(conversationID) {
            if(!conversationID){
                return
            }
            // 添加安全检查，确保消息数组存在
            const messages = this.chatMessages[conversationID];
            if (!messages || !Array.isArray(messages)) {
                return;
            }
            
            const aiMessage = messages.findLast(
                (msg) => msg.type === "ai" && msg.status !== "completed"
            );
            if (aiMessage) {
                console.log("stopAskQuestion", conversationID,aiMessage);
                this.$set(aiMessage, "status", "completed_stop_by_user");
            }
            this.isAborting = true;
            await this.aiChatService.abort();


            this.isAborting = false;

        },
        async copyContent(content) {
            // 如果是点击代码块的复制按钮
            if (content.startsWith("```") && content.endsWith("```")) {
                content = content.replace(/^```[\s\S]*?\n/, "").replace(/```$/, "");
            }
            await Tool.copyToClipboard(content);
            this.$message.success(this.lang.copy_text_success);

        },
        handleScroll() {
            // 如果不在底部，标记用户正在滚动
            const container = this.$refs.messageContainer;
            const isScrolledToBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 100; // 添加100px的缓冲区

            this.isUserScrolling = !isScrolledToBottom;
            this.lastScrollTop = container.scrollTop;
        },
        scrollToBottom(force) {
            this.$nextTick(() => {
                const container = this.$refs.messageContainer;
                // 只有当用户没有主动滚动或者之前已经在底部时才自动滚动
                if (container && (!this.isUserScrolling || force)) {
                    container.scrollTop = container.scrollHeight;
                }
            });
        },
        handleOutsideClick(event) {
            // 检查点击是否在侧边栏和功能栏之外
            const sidebar = document.querySelector(".chat-sidebar");
            const functionBar = document.querySelector(".function-bar");
            if (this.showSidebar && sidebar && !sidebar.contains(event.target) && !functionBar.contains(event.target)) {
                this.showSidebar = false;
            }
        },
        toggleSidebar(event) {
            // 阻止事件冒泡，防止触发外部点击事件
            if (event) {
                event.stopPropagation();
            }
            this.showSidebar = !this.showSidebar;
        },
        openClinicalThinkingPractice() {
            this.$router.push(this.$route.path + "/practice_overview");
        },
        handleLinkClick(href) {
            window.open(href, "_blank");
        },
        handleAnimationCompleted() {
            this.isWelcomeCompleted = true;
            this.$store.commit('dynamicGlobalParams/updateDynamicGlobalParams', { isWelcomeAnimationShown: true });
        },
        handleLeaveAI() {
            this.back();
        },
        handleRecommendationClick(question) {
            this.inputMessage = question;
            this.sendMessage();
        },
        //是否正在生成AI代码
        isGeneratingAIResponse() {
            const currentMessages = this.chatMessages[this.currentChatId] || [];
            const lastMessage = currentMessages[currentMessages.length - 1];
            return lastMessage && lastMessage.type === 'ai' && lastMessage.status === 'answering';
        },
        callTextAreaMenu(event) {
            // if(Tool.checkAppClient('Cef')) {
            //     event.preventDefault();

            //     // 获取当前活动的文本区域
            //     const activeTextarea = this.isCurrentNewChat ?
            //         this.$refs.welcome_input_textarea?.$refs.textarea :
            //         this.$refs.chat_input_textarea?.$refs.textarea;

            //     if (!activeTextarea) {
            //         return;
            //     }

            //     // 保存当前光标位置
            //     const lastCursorPosition = activeTextarea.selectionStart;

            //     this.$root.eventBus.$emit('showTextAreaMenu', {
            //         event: event,
            //         textarea: activeTextarea,
            //         textContent: this.inputMessage,
            //         updateFunc: (newText) => {
            //             console.log('updateFunc',newText)
            //             // 更新文本内容
            //             this.inputMessage = newText;
            //             // 记住最后的光标位置供后续使用
            //             this.$nextTick(() => {
            //                 if (activeTextarea) {
            //                     activeTextarea.selectionStart = lastCursorPosition;
            //                     activeTextarea.selectionEnd = lastCursorPosition;
            //                 }
            //             });
            //         }
            //     });
            // }
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync_pc/style/aiChat.scss';
.ai-chat {
    height: 100%;
    display: flex;
    background-color: #f5f7fa;
    position: relative;
    .chat-sidebar {
        position: absolute;
        left: 60px;
        height: 100%;
        z-index: 1;
        width: 250px;
        background-color: #ffffff;
        border-right: 1px solid #e4e7ed;
        display: flex;
        flex-direction: column;
        padding: 16px;
        will-change: opacity;
        transition: left 0.3s ease;

        &.expanded {
            left: 200px;
        }

        .chat-list {
            flex: 1;
            overflow-y: auto;

            .chat-item {
                display: flex;
                align-items: center;
                padding: 10px;
                cursor: pointer;
                border-radius: 4px;
                margin-bottom: 4px;
                color: #303133;
                transition: all 0.3s;
                position: relative;

                &:hover {
                    background: rgba(0, 0, 0, 0.05);

                    .delete-icon {
                        opacity: 1;
                    }
                }

                &.active {
                    background: rgba(33, 150, 243, 0.1);
                }

                i {
                    margin-right: 8px;
                    font-size: 16px;
                }

                .chat-title {
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-size: 14px;
                    padding-right: 10px;
                }

                .delete-icon {
                    opacity: 0;
                    transition: opacity 0.3s;
                    position: absolute;
                    right: 0;
                    color: #ff4d4f;

                    &:hover {
                        color: #ff7875;
                    }
                }
            }
        }
    }
    .ai-chat-function-menu{
        flex-shrink: 0;
        background-color: #ffffff;
    }
    .chat-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 20px;
        padding-left: 0;
        gap: 20px;
        background-color: #ffffff;
        position: relative;
        overflow: hidden;
        .welcome-screen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 2;
        }

        .welcome-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: #303133;
            padding-bottom: 60px;
            opacity: 1;
            .welcome-avatar {
                margin-bottom: 60px;
                border-radius: 50%;
                box-sizing: content-box;
                opacity: 0;
                animation: avatarFadeIn 2s ease-out forwards;
                position: relative;

                //    padding: 2px;
                //    background: rgba(199, 0, 10, 1);
                &::after {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    border-radius: 50%;
                    width: 100%;
                    height: 100%;
                    //    animation: darkPulse 3s ease-in-out infinite;
                    // box-shadow: 0 0 20px rgba(199, 0, 10, 0.5);
                    z-index: -1;
                }

                .avatar_wrapper {
                    border-radius: 0;
                }
            }

            .chat-input,
            .recommendation-buttons {
                opacity: 0;
                transform: translateY(60px);
                animation: contentFadeIn 0.8s ease-out forwards;
                animation-delay: 0.6s;
            }
        }

        .recommendation-buttons {
            margin-top: 20px;
            width: 100%;
            max-width: 800px;
            display: flex;
            flex-direction: column;
            gap: 12px;
            font-size: 15px;
            .hint-text {
                font-size: 15px;
                color: #666;
                text-align: left;
                text-indent: 20px;
            }

            .button-row {
                display: flex;
                gap: 12px;
                justify-content: flex-start;
                flex-wrap: wrap;
                flex-direction: column;
                align-items: flex-start;
            }
            .rec-button {
                color: #666;
                width: fit-content;
                padding: 8px 12px;
            }
        }

        @keyframes avatarFadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes avatarGlow {
            0% {
                box-shadow: 0 0 15px rgba(199, 0, 10, 0.5);
                transform: scale(1);
            }

            50% {
                box-shadow: 0 0 30px rgba(199, 0, 10, 1);
                transform: scale(1);
            }

            100% {
                box-shadow: 0 0 15px rgba(199, 0, 10, 0.5);
                transform: scale(1);
            }
        }

        @keyframes contentFadeIn {
            from {
                opacity: 0;
                transform: translateY(60px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            border-radius: 4px;

            .message {
                margin-bottom: 15px;
                max-width: 70%;
                display: flex;
                flex-direction: column;

                &.user {
                    margin-left: auto;
                    align-items: flex-end;

                    .message-info {
                        flex-direction: row-reverse;
                    }

                    .message-content {
                        background: $ai-theme-gradient;
                        color: white;
                        box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);

                        :deep(p) {
                            white-space: pre-wrap;
                            word-break: break-word;
                        }
                    }
                }

                &.ai {
                    .message-content {
                        .thinking-tips {
                            font-size: 12px;
                            color: #666;
                        }

                        .answering-tips {
                            font-size: 12px;
                            color: #666;
                        }

                        background: #f5f7fa;
                        border: 1px solid #e4e7ed;
                        color: #303133;
                        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

                        .reasoning-content {
                            background: #e4e7ed;
                            border: 1px solid #e4e7ed;
                            color: #606266;
                            padding: 10px;
                        }

                        .answer-content {
                            position: relative;

                            :deep(pre) {
                                background-color: #f6f8fa;
                                padding: 16px;
                                border-radius: 6px;
                                margin: 10px 0;
                                overflow-x: auto;
                            }

                            :deep(code) {
                                font-family: Monaco, Consolas, Courier New, monospace;
                            }

                            :deep(p) {
                                margin: 8px 0;
                            }

                            :deep(table) {
                                border-collapse: collapse;
                                margin: 10px 0;

                                th,
                                td {
                                    border: 1px solid #ddd;
                                    padding: 8px;
                                }
                            }
                        }

                        .message-actions {
                            margin-top: 8px;
                            display: flex;
                            justify-content: flex-end;

                            .el-button {
                                padding: 5px 10px;
                                font-size: 14px;
                            }
                        }
                    }
                }

                .message-content-wrapper {
                    margin-bottom: 4px;
                    margin-top: 6px;
                    user-select: text;
                    font-size: 16px;
                    .thinking-indicator {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        color: #666;
                        margin-bottom: 8px;
                    }
                }

                .message-content {
                    padding: 8px 12px;
                    border-radius: 4px;
                    line-height: 1.5;
                    box-shadow: 1px 1px 4px 2px rgb(0 0 0 / 10%);
                    display: inline-block;
                    max-width: 100%;
                }

                .message-info {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .message-time {
                        font-size: 12px;
                        color: #909399;
                    }
                }
            }
        }

        .chat-input {
            margin-top: auto;
            width: 100%;
            background: #ffffff;
            padding: 16px 16px 6px;
            border-radius: 19px;
            border: 1px solid #E9E9E9;
            box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.08);

            &.welcome-input {
                width: 100%;
                max-width: 800px;
                margin-top: 5px;
                padding: 34px 28px 0px;
                background: #FFFFFF;
                border: 1px solid #E9E9E9;
                box-shadow: 0 0 12px 0 rgba(0,0,0,0.08);
                border-radius: 19px;
            }

            .chat-options {
                display: flex;
                gap: 12px;
                justify-content: space-between;
                padding: 12px 0;
                .left-options {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                }
                :deep(.el-checkbox) {
                        height: 32px;
                        margin-right: 0;

                        .el-checkbox__label {
                            padding: 0 16px;
                            height: 100%;
                            line-height: 32px;
                            border-radius: 16px;
                            font-size: 15px;
                            transition: all 0.3s;
                            background: #EEEEEE;
                            color: #000000;

                            &:hover {
                                border-color: #2196f3;
                                color: #2196f3;
                            }
                        }

                        &.is-checked .el-checkbox__label {
                            background: rgba(33, 150, 243, 0.2);
                            border-color: #2196f3;
                            color: #2196f3;
                        }

                        // 隐藏默认的复选框
                        .el-checkbox__input {
                            display: none;
                        }
                    }
                .right-options {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                }
            }

            .input-wrapper {
                :deep(.el-textarea) {
                    font-size: 16px;
                    .el-textarea__inner {
                        resize: none;
                        background: #ffffff;
                        border: none;
                        border-bottom: 1px solid #D8D8D8;
                        border-radius: 0;
                        color: #303133;
                        transition: all 0.3s ease;
                        padding-left: 0;
                        padding-right: 0;
                        &::placeholder {
                            color: #D8D8D8;
                        }
                    }
                }
            }

            .input-actions {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                .send-btn {
                    transition: all 0.3s ease;
                    width: 40px;
                    height: 40px;
                    padding: 0;
                    background: $ai-theme-gradient;
                    border: none;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    &:hover {
                        background: $ai-theme-gradient;
                    }

                    i {
                        transition: all 0.3s ease;
                        font-size: 18px;
                    }

                    &.is-sending {
                        background: $ai-theme-gradient;

                        &:hover {
                            background: $ai-theme-gradient;
                        }
                    }
                }
            }
        }

        :deep(.code-block-wrapper) {
            margin: 1em 0;
            border-radius: 6px;
            background: #f5f7fa;
            overflow: hidden;
            border: 1px solid #e4e7ed;

            .code-block-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 16px;
                background: #ebeef5;
                border-bottom: 1px solid #e4e7ed;

                .code-block-header__lang {
                    color: #909399;
                    font-size: 13px;
                }

                .code-copy-btn {
                    padding: 4px 12px;
                    background: #fff;
                    border: 1px solid #dcdfe6;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 12px;
                    color: #606266;
                    transition: all 0.3s;

                    &:hover {
                        background: #f5f7fa;
                        color: #409eff;
                        border-color: #409eff;
                    }
                }
            }

            .code-block-body {
                margin: 0;
                padding: 16px;
                overflow-x: auto;
                background: #f6f8fa;
                font-size: 13px;
                line-height: 1.6;
            }
        }

        @keyframes darkPulse {
            0% {
                box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.4);
            }

            70% {
                box-shadow: 0 0 0 20px rgba(0, 0, 0, 0);
            }

            100% {
                box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
            }
        }
    }

    // 自定义滚动条样式
    .chat-messages,
    .chat-list {
        &::-webkit-scrollbar {
            width: 4px;
        }

        &::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 2px;

            &:hover {
                background: rgba(33, 150, 243, 0.5);
            }
        }
    }

    // 修改为渐变动画
    .sidebar-enter-active,
    .sidebar-leave-active {
        transition: opacity 0.3s ease;
    }

    .sidebar-enter-from,
    .sidebar-leave-to {
        opacity: 0;
    }

    .function-bar {
        &.expanded {
            .function-group {
                .function-item {
                    width: 100%;
                    justify-content: flex-start;
                    padding-left: 20px;
                }
            }
        }
    }

    // 添加过渡动画样式
    .fade-enter-active {
        transition: opacity 0.3s ease 0.3s; // 0.3s 是动画时长，0.3s 是延迟时间
    }

    .fade-leave-active {
        transition: opacity 0.2s ease;
    }

    .fade-enter-from,
    .fade-leave-to {
        opacity: 0;
    }

    .close-button {
        position: absolute;
        top: 20px;
        right: 20px;
        width: 48px; // 增大按钮尺寸
        height: 48px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(5px);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        z-index: 10;
        border: 1px solid rgba(255, 255, 255, 0.2);

        &:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05); // 减小放大效果

            .close-icon {
                span {
                    background: #2196f3;
                    box-shadow: 0 0 8px #2196f3;
                }
            }
        }

        .close-icon {
            position: relative;
            width: 24px; // 增大关闭图标尺寸
            height: 24px;

            span {
                position: absolute;
                width: 100%;
                height: 3px; // 增加线条粗细
                background: #606266;
                border-radius: 3px;
                transition: all 0.3s ease;

                &:first-child {
                    transform: rotate(45deg);
                }

                &:last-child {
                    transform: rotate(-45deg);
                }
            }
        }
    }

    .rec-button {
        padding: 4px 12px;
        background: #ffffff;
        border: 1px solid #E0E8F2;
        border-radius: 6px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        color: #666;
        font-size: 15px;

        i {
            font-size: 20px;
            color: #666;
        }

        &:hover {
            border-color: #2196f3;
            color: #2196f3;
            background: rgba(33, 150, 243, 0.05);

            i {
                color: #2196f3;
            }
        }

        &.highlight {
            background: rgba(33, 150, 243, 0.1);
            border-color: #2196f3;
            color: #2196f3;

            i {
                color: #2196f3;
            }

            &:hover {
                background: rgba(33, 150, 243, 0.15);
            }
        }
    }
}

// 为历史记录子菜单添加滚动条样式
.function-bar.expanded .function-bar-top {
    &::-webkit-scrollbar {
        width: 4px;
    }

    &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 2px;

        &:hover {
            background: rgba(33, 150, 243, 0.5);
        }
    }
}
</style>
