<template>
    <div class="ultrasound-chartOverView">
        <router-view />
        <search-form class="ultrasound-chartOverView-search-form" />
        <div class="ultrasound-chartOverView-container">
            <div class="chart-rank-container">
                <div class="chart-container">
                    <chart-display />
                </div>
                <div class="rank-container">
                    <ranking-list />
                </div>
            </div>
            <report-table />
        </div>
    </div>
</template>

<script>
import SearchForm from "./SearchForm.vue";
import ChartDisplay from "./ChartDisplay.vue";
import RankingList from "./RankingList.vue";
import ReportTable from "./ReportTable.vue";
export default {
    name: "ultrasoundChartOverView",
    components: {
        SearchForm,
        ChartDisplay,
        RankingList,
        ReportTable,
    },
    data() {
        return {
            role: "admin", //admin 主任  normal 医生
        };
    },
    mounted() {
        if (this.$route.params && this.$route.params.cardData) {
            console.log('接收到的 GaugeCard 数据:', JSON.parse(JSON.stringify(this.$route.params.cardData)));
        }
    }
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/aiChat.scss";
.ultrasound-chartOverView {
    padding: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    position: relative;
    .ultrasound-chartOverView-search-form {
        flex-shrink: 0;
    }
    .ultrasound-chartOverView-container {
        flex: 1;
        overflow: auto;
        .chart-rank-container {
            display: flex;
            margin-bottom: 20px;
            gap: 20px;
            height: 500px;
            .chart-container {
                flex: 1;
                min-width: 0; // 防止内容溢出
            }

            .rank-container {
                width: 350px;
                flex-shrink: 0;
            }
        }
    }
}
</style>
