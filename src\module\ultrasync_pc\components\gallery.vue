<template>
    <div class="gallery_wrapper" v-show="isShowGallery">
        <canvas style="display: none" id="ai_canvas"></canvas>
        <canvas style="display: none" id="ai_mul_view_canvas"></canvas>
        <div class="gallery" ref="gallery">
            <div class="gallery_body" v-loading="isLoading">
                <i class="iconfont iconsearchclose" :class="{ nonechat: noneChat }" @click="clickCloseGallery"></i>
                <div class="title_bar" v-show="noneChat"></div>
                <div class="left">
                    <div class="top" ref="topSwiper">
                        <div class="open_file" v-show="index == -1">
                            <img
                                :src="
                                    getResourceTempStatus(currentFile.resource_id, 'loaded')
                                        ? getResourceTempStatus(currentFile.resource_id, 'realUrl')
                                        : currentFile.url
                                "
                                v-if="currentFile.url || currentFile.realUrl"
                            />
                            <div v-else class="view_not_uploaded">
                                {{ lang.view_not_uploaded }}
                            </div>
                        </div>
                        <pdf-reader ref="fileReader" :url="readingFile.url" v-if="isShowFileReader"></pdf-reader>
                        <div class="mui-slider-item" :key="currentFile.resource_id + currentFile.file_id">
                            <div
                                class="ai_search_suggest"
                                v-show="showAISearchSuggest && false"
                                @click="searchInCaseData()"
                                :title="lang.searc_in_case_database"
                            >
                                {{ lang.ai_search_suggest }}
                            </div>
                            <template v-if="checkResourceType(currentFile) === 'image'">
                                <div class="">
                                    <div
                                        class="loading_span"
                                        v-if="currentFile.url && !getResourceTempStatus(currentFile.resource_id, 'loaded')"
                                        v-loading="true"
                                    ></div>
                                    <img
                                        v-if="currentFile.url"
                                        @mouseup="mouseupImage"
                                        @contextmenu="handleContextMenu($event, currentFile)"
                                        :src="isStructImage ? drawImageSrc(currentFile) : realUrl(currentFile)"
                                        class="preview"
                                        draggable="false"
                                        @error="setErrorImage(currentFile)"
                                    />
                                    <div v-else class="view_not_uploaded">
                                        {{ lang.view_not_uploaded }}
                                    </div>
                                </div>
                            </template>
                            <template
                                v-else-if="
                                    checkResourceType(currentFile) === 'video' ||
                                    checkResourceType(currentFile) === 'review_video'
                                "
                            >
                                                        <div
                            class="main_video"
                            v-if="!isCef && getResourceTempStatus(currentFile.resource_id, 'mainVideoSrc')"
                        >
                            <videoPlayer
                                :videoSrc="getResourceTempStatus(currentFile.resource_id, 'mainVideoSrc')"
                                :enableReporting="isReviewVideo(currentFile)"
                                :fileInfo="currentFile"
                                @play="onVideoPlay"
                                @pause="onVideoPause"
                                @ended="onVideoEnded"
                                @reportTime="onVideoReportTime"
                                @error="onVideoError"
                            ></videoPlayer>
                        </div>
                                <!-- <div class="loading_span" v-loading="!getResourceTempStatus(currentFile.resource_id,'loaded')"></div> -->
                                <!-- <video v-if="!isCef" :src=""  id="main_player"  playsinline controls crossorigin="anonymous">
                                </video> -->
                                <!-- <video   controls @error="playVideoError()"></video> -->
                                <!-- <video v-if="!isCef&&currentFile.img_has_gesture_video" :poster="getRenderImageUrl(currentFile)" :src="getResourceTempStatus(currentFile.resource_id,'gestrueVideoSrc')" class="gesture_video" controls></video> -->
                            </template>
                            <!-- <template v-else-if="checkResourceType(currentFile) === 'review_video'">
                                <object v-show="!isCef" class="review main_video" type="application/x-vlc-plugin" pluginspage="http://www.videolan.org/" events="false">
                                    <param name="mrl" :value="getResourceTempStatus(currentFile.resource_id,'mainVideoSrc')">
                                    <param name="volume" value="50">
                                    <param name="autoplay" value="true">
                                    <param name="loop" value="false">
                                    <param name="fullscreen" value="true">
                                    <param name="controls" value="true">
                                </object>
                                <object v-show="!isCef" class="gesture_video" type="application/x-vlc-plugin" pluginspage="http://www.videolan.org/" events="false">
                                    <param name="mrl" :value="getResourceTempStatus(currentFile.resource_id,'gestrueVideoSrc')">
                                    <param name="volume" value="50">
                                    <param name="autoplay" value="true">
                                    <param name="loop" value="false">
                                    <param name="fullscreen" value="true">
                                    <param name="controls" value="true">
                                </object>
                            </template> -->
                            <template v-else-if="currentFile.msg_type == systemConfig.msg_type.File">
                                <img
                                    :src="'static/resource_pc/images/file_icon/' + currentFile.file_type + '.png'"
                                    class="preview"
                                    draggable="false"
                                    @click="openFileReader"
                                />
                            </template>
                            <template v-else>
                                <img src="static/resource_pc/images/poster2.jpg" class="preview" draggable="false" />
                            </template>
                        </div>
                        <i
                            v-if="galleryList.length > 2 && index !== 0"
                            @click="prevImage"
                            class="iconfont iconright1"
                        ></i>
                        <i
                            v-if="galleryList.length > 2 && index < galleryList.length - 1"
                            @click="nextImage"
                            class="iconfont iconright2"
                        ></i>
                    </div>
                    <div class="thumb_wrap">
                        <div class="thumb_loading" v-show="index === -1" v-loading="true"></div>
                        <div ref="thumb_scroll_wrap" class="thumb_scroll_wrap">
                            <vue-slide :key="'thumb_' + cid" class="thumb_slide" ref="thumb_slide" v-if="isShowGallery">
                                <div
                                    :style="{ width: galleryList.length * 157 + 'px', 'min-width': '100%' }"
                                    @mousewheel.prevent.stop
                                    class="clearfix"
                                >
                                    <div
                                        v-for="(file, f_index) in galleryList"
                                        class="thumb_item"
                                        :class="{ current_thumb: f_index == index }"
                                        @mousedown="mousedownThumb($event, f_index)"
                                        @contextmenu.prevent="handleContextMenu($event, file)"
                                        @mouseup="mouseupThumb($event, f_index)"
                                        :key="file.resource_id + file.file_id + f_index"
                                    >
                                        <span
                                            class="comment_number"
                                            v-show="
                                                getCommentNum(
                                                    gallery.commentObj[file.resource_id] &&
                                                        gallery.commentObj[file.resource_id].comment_list
                                                )
                                            "
                                            >{{
                                                getCommentNum(
                                                    gallery.commentObj[file.resource_id] &&
                                                        gallery.commentObj[file.resource_id].comment_list
                                                )
                                            }}</span
                                        >
                                        <template
                                            v-if="
                                                showAiAnalyzeIcon(file) &&
                                                file.msg_type != systemConfig.msg_type.EXPIRATION_RES
                                            "
                                        >
                                            <span
                                                v-for="(iconObj, index) in imageStandardIcon(file)"
                                                :key="index"
                                                :class="iconObj.css"
                                                :title="iconObj.tips"
                                            >
                                                {{ iconObj.label }}
                                            </span>
                                        </template>
                                        <span
                                            v-if="newReceiveResourceIds.includes(file.resource_id)"
                                            class="unread_tip"
                                        ></span>
                                        <template v-if="checkResourceType(file) === 'image'">
                                            <img
                                                v-if="file.url"
                                                :src="file.error_image || file.url"
                                                class="preview"
                                                draggable="false"
                                                @error="setErrorImage(file)"
                                            />
                                            <template v-else>
                                                <img
                                                    src="static/resource_pc/images/default.png"
                                                    class="preview"
                                                    draggable="false"
                                                    @error="setErrorImage(file)"
                                                />
                                                <span
                                                    v-if="
                                                        functionsStatus.breastAI &&
                                                        isAiAnalyze &&
                                                        checkIworksTest(file)
                                                    "
                                                >
                                                    <span
                                                        class="icon iconfont ai_result_deletion_icon iconwenhao-yuankuang-copy"
                                                        :title="lang.view_deletion"
                                                    >
                                                    </span>
                                                </span>
                                            </template>
                                            <!-- <div  class="empty_thump">
                                                <img v-if="file.url"  class="preview" draggable="false" @error="setErrorImage(file)">
                                            </div> -->
                                            <i class="icon iconfont iconpicture"></i>
                                        </template>
                                        <template v-else-if="checkResourceType(file) === 'video'">
                                            <img :src="file.url" class="preview" draggable="false" />
                                            <i class="icon iconfont iconvideo_fill_light"></i>
                                        </template>
                                        <template
                                            v-else-if="
                                                file.msg_type == systemConfig.msg_type.RealTimeVideoReview ||
                                                file.img_type_ex == systemConfig.msg_type.RealTimeVideoReview
                                            "
                                        >
                                            <p class="review_text">{{ lang.live_playback }}</p>
                                            <p class="review_time">
                                                {{ formatTime(file.start_ts) }}<br />{{ formatTime(file.stop_ts) }}
                                            </p>
                                            <i class="icon iconfont iconvideo_fill_light"></i>
                                        </template>
                                        <template
                                            v-else-if="
                                                file.msg_type == systemConfig.msg_type.VIDEO_CLIP ||
                                                file.img_type_ex == systemConfig.msg_type.VIDEO_CLIP
                                            "
                                        >
                                            <p class="review_text">{{ lang.video_clips }}</p>
                                            <p class="review_time">{{ formatTime(file.start_ts) }}</p>
                                            <i class="icon iconfont iconvideo_fill_light"></i>
                                        </template>
                                        <template v-else-if="file.msg_type == systemConfig.msg_type.File">
                                            <img
                                                :src="'static/resource_pc/images/file_icon/' + file.file_type + '.png'"
                                                class="preview"
                                                draggable="false"
                                            />
                                        </template>
                                        <template v-else>
                                            <img
                                                src="static/resource_pc/images/poster2.jpg"
                                                class="preview"
                                                draggable="false"
                                            />
                                        </template>
                                        <p v-if="file.protocol_view_name" class="view_name">
                                            {{ file.protocol_view_name }}
                                        </p>
                                    </div>
                                </div>
                            </vue-slide>
                        </div>

                        <i @click="lastPage" class="icon iconfont iconsanjiaoxing last_page"></i>
                        <i @click="nextPage" class="icon iconfont iconsanjiaoxing next_page"></i>
                    </div>
                </div>
                <div
                    class="right"
                    @keyup.stop
                    v-if="currentFile.url && currentFile.resource_id && (!noneComment || !noneChat)"
                >
                    <div
                        class="image_detail"
                        :class="{ show: rightActiveTab == 'discover' }"
                        v-show="rightActiveTab == 'discover'"
                    >
                        <!--  <span v-show="rightTab==1"  @click="rightTab=0" class="tab_btn_left">
                            <i class="iconfont iconright1"></i>
                        </span> -->
                        <div class="image_detail_box">
                            <div class="header">{{ this.themeSubject || currentFile.resource_id }}</div>
                            <div class="patient_info">
                                <span>{{ lang.patient_name }}: {{ patientInfo.patient_name }}，</span>
                                <span>{{ lang.patient_sex }}: {{ patientInfo.patient_sex }}，</span>
                                <span>{{ lang.patient_age }}: {{ patientInfo.patient_age }}</span>
                            </div>

                            <div class="tags_warper" v-loading="operatingTags">
                                <vue-scroll class="tag_list clearfix">
                                    <span
                                        v-for="(item, t_index) of tagList"
                                        class="fl tag_item"
                                        @click="deleteTag(t_index)"
                                        :key="t_index"
                                    >
                                        {{ item.tags }}
                                        <span class="checknum">{{ item.checknum }}</span>
                                        <i class="icon iconfont iconclose"></i>
                                    </span>
                                </vue-scroll>
                                <div class="add_tags">
                                    <el-input
                                        v-model="tagText"
                                        class="tag_text"
                                        :placeholder="lang.gallery_add_tags_btn"
                                    ></el-input>
                                    <el-button
                                        class="add_custom_tag_submit"
                                        type="primary"
                                        size="mini"
                                        v-loading="addingTags"
                                        @click="addCustomTag"
                                        >{{ lang.admin_add }}</el-button
                                    >
                                    <el-popover placement="left" trigger="click" popper-class="gallery_search_tags">
                                        <div class="choose_tags_wrap" v-loading="choosingTags">
                                            <el-tabs v-model="tagTab">
                                                <el-tab-pane :label="lang.custom_tag_top" name="custom">
                                                    <div class="tag_names clearfix">
                                                        <span
                                                            v-for="(item, index) of allTags"
                                                            @click="addUserTag(index)"
                                                            :class="{ checked: item.checked == true }"
                                                            class="fl"
                                                            :key="index"
                                                        >
                                                            {{ item.caption }}
                                                        </span>
                                                    </div>
                                                </el-tab-pane>
                                                <!-- <el-tab-pane :label="lang.system_tag_top" name="system">
                                                    <div class="tag_names clearfix">
                                                        <span v-for="(item,index) of tagNames" @click="addSystemTag(index)" :class="{'checked':item.checked==true}" class="fl" :key="index">
                                                            {{item.name}}
                                                        </span>
                                                    </div>
                                                </el-tab-pane> -->
                                            </el-tabs>
                                        </div>
                                        <el-button
                                            class="choose_custom_tag_btn"
                                            type="primary"
                                            size="mini"
                                            slot="reference"
                                        >
                                            <i class="icon iconfont iconplus"></i>
                                        </el-button>
                                    </el-popover>
                                </div>
                            </div>
                            <div class="comment_wraper">
                                <vue-scroll class="comment_list">
                                    <template v-for="(comment, c_index) of commentList">
                                        <template
                                            v-if="
                                                !comment.is_private ||
                                                (comment.is_private && comment.author_id === user.uid)
                                            "
                                        >
                                            <div
                                                v-if="comment.status !== 0"
                                                class="comment_list_item clearfix"
                                                :ref="'comment_' + comment.comment_id"
                                                :class="{ high_light: comment.highLight }"
                                                :key="c_index"
                                                :id="'comment_' + comment.comment_id"
                                            >
                                                <pre @contextmenu="callTextMenu($event, comment.content, 1)">{{
                                                    comment.content
                                                }}</pre>
                                                <span class="comment_time">{{ formatTime(comment.post_ts) }}</span>
                                                <span class="comment_author">{{ comment.nickname }}</span>
                                                <span class="comment_private_text" v-if="comment.is_private">{{
                                                    lang.private_comment
                                                }}</span>
                                            </div>
                                        </template>
                                    </template>
                                </vue-scroll>
                                <el-input
                                    class="comment_text"
                                    v-model="commentText"
                                    type="textarea"
                                    :rows="3"
                                    resize="none"
                                    :placeholder="lang.gallery_navbar_comment"
                                ></el-input>
                                <div class="comment_btns clearfix">
                                    <el-button size="small" class="fr" @click="sendComment" v-loading="addingComment">{{
                                        lang.gallery_add_comment_btn
                                    }}</el-button>
                                    <el-button
                                        size="small"
                                        class="fl"
                                        v-show="
                                            false &&
                                            functionsStatus.obstetricalAI &&
                                            currentFile.mc_resource_map &&
                                            currentFile.mc_resource_map.ai_report
                                        "
                                        @click="openQCReport()"
                                        >{{ lang.obstetric_qc.result }}</el-button
                                    >
                                    <el-button
                                        v-show="checkShowOpenReport"
                                        size="small"
                                        class="fl"
                                        @click="openReport"
                                        >{{ lang.inspection_overview }}</el-button
                                    >
                                    <!-- <el-button size="small" class="fl" v-loading="exporting" @click="exportComment(currentFile)">{{lang.export_comment}}</el-button> -->
                                    <el-button
                                        v-show="!checkFavoriteStatus(currentFile)"
                                        size="small"
                                        class="fl"
                                        @click="saveFavoriteCommit(currentFile)"
                                        >{{ lang.action_favorite_text }}</el-button
                                    >
                                    <el-button
                                        v-show="checkFavoriteStatus(currentFile)"
                                        size="small"
                                        class="fl"
                                        @click="cancelFavoriteCommit(currentFile)"
                                        >{{ lang.cancel_favorite }}</el-button
                                    >
                                    <el-button
                                        size="small"
                                        :class="['fl icon-button', checkLikeAction(currentFile) ? 'like' : '']"
                                        @click="handleLikeAction(currentFile)"
                                        >{{ parseLikeText(currentFile) }}<i class="iconfont iconzan"></i
                                    ></el-button>
                                    <!-- <el-button v-show="currentFile.userFavoriteStatus" size="small" class="fl" @click="cancelFavoriteCommit(currentFile)">{{lang.cancel_favorite}}</el-button> -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="chatbox" :class="{ show: rightActiveTab == 'chat' }" v-show="rightActiveTab == 'chat'">
                        <chat-component
                            :chatType="CHAT_TYPE['GALLERY']"
                            :cid="currentFile.group_id || cid"
                            ref="chat_component"
                            :from="`gallery`"
                        ></chat-component>
                        <!--  <span v-show="rightTab==0&&!playRealtimeVideo" @click="rightTab=1" class="tab_btn_right">
                            <i class="iconfont iconright2"></i>
                        </span> -->
                    </div>
                    <div
                        class="gallery_ai_result"
                        v-show="rightActiveTab == 'ai_analyze' || rightActiveTab == 'obstetric_qc_ai'"
                    >
                        <div
                            class="gallery_ai_result"
                            v-show=" rightActiveTab == 'ai_analyze'"
                        >
                            <div class="header">{{ lang[rightTabs.ai_analyze.key] }}</div>
                            <div></div>
                            <template v-if="aiAnalyzeType != this.aiPresetData.typeIndex.obstetrical">
                                <iworks-test-image-report :currentFile="currentFile" @reloadImage="reloadImage">
                            </iworks-test-image-report>
                            </template>
                            <template v-else>
                                <galleryDialog
                                    :currentFile="currentFile"
                                    :index="index"
                                    :isMcData="false"
                                    :isShowBackButton="false"
                                    :isShowReconsider="false"
                                    :isShowQuestion="false"
                                    :tips="'results_reference_only'"
                                    :isShowDialog="true"
                                    :isAiAnalyzeType="true"
                                    v-show="true"
                                    @updataLoadingStatus="handleUpdataLoadingStatus"
                                    @reloadImage="reloadImage"
                                >
                                </galleryDialog>
                            </template>
                        </div>

                        <div
                            class="gallery_ai_result"
                            v-show="rightActiveTab == 'obstetric_qc_ai'"
                        >
                            <div class="header">
                                {{
                                    lang[rightTabs.obstetric_qc_ai.key]
                                }}
                            </div>
                            <galleryDialog
                                :currentFile="currentFile"
                                :index="index"
                                :isMcData="isObstetricQCMulticenter"
                                :isShowBackButton="false"
                                :isShowReconsider="true"
                                :isShowQuestion="true"
                                :tips="'ai_nalysis_result_tips'"
                                :isShowDialog="true"
                                :isAiAnalyzeType="false"
                                v-show="true"
                                @updataLoadingStatus="handleUpdataLoadingStatus"
                                @reloadImage="reloadImage"
                            >
                            </galleryDialog>
                        </div>
                    </div>
                </div>
                <div class="middle" v-if="currentFile.url && currentFile.resource_id && (!noneComment || !noneChat)">
                    <div class="right_tab">
                        <div class="gallery_tabs">
                            <div
                                class="gallery_tab_name"
                                v-for="tab in rightTabs"
                                :class="{ active_tab: tab.name == rightActiveTab }"
                                :key="tab.key"
                                @click="switchRightTab(tab.name)"
                                v-show="tab.enabled"
                            >
                                <i class="icon iconfont" :class="[tab.icon]" :title="lang[tab.key]"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <gallery-menu></gallery-menu>
    </div>
</template>
<script>
import base from "../lib/base";
import "swiper/dist/css/swiper.css";
import Swiper from "swiper";
import Tool from "@/common/tool.js";
import chatComponent from "./chatComponent";
import vueSlide from "vuescroll/dist/vuescroll-slide";
import galleryDialog from "./obstetricQC/galleryDialog";
import iworksTestImageReport from "./aiReport/iworksTestImageReport";
import obstetricTool from "../lib/obstetricTool";
import GalleryMenu from "./galleryMenu"; // 导入GalleryMenu组件
import {
    getRealUrl,
    switchRealUrlToBColorUrl,
    transferPatientInfo,
    getPositionOffsetFromUrl,
    cancelFavoriteCommit,
    getRecordSubject,
    getResourceTempStatus,
    checkResourceType,
    imageStandardIcon,
    checkIworksTest,
    getResourceTempState,
    getMessageAiReportFromLocal,
} from "../lib/common_base";
import { initVideoPage, getRealtimeConsultationSize, initDcm } from "../lib/common_realtimeVideo";
import { cloneDeep, trim } from "lodash";
import service from "../service/service";
import videoPlayer from "../MRComponents/videoPlayer";
import {CHAT_TYPE} from '../lib/constants'

export default {
    mixins: [base, obstetricTool],
    name: "GalleryComponent",
    components: {
        chatComponent,
        pdfReader: () => import(/* webpackPrefetch: true */ './pdfReader'),
        vueSlide,
        galleryDialog,
        iworksTestImageReport,
        videoPlayer,
        GalleryMenu, // 注册GalleryMenu组件
    },
    data() {
        return {
            CHAT_TYPE,
            getPositionOffsetFromUrl,
            switchRealUrlToBColorUrl,
            getResourceTempState,
            getRecordSubject,
            getResourceTempStatus,
            imageStandardIcon,
            checkResourceType,
            checkIworksTest,
            getMessageAiReportFromLocal,
            isShowGallery: false,
            cid: 0,
            index: -1,
            swiperLength: 0,
            sliderTimer: 0,
            retentionTimer: null, //停留时间
            showAISearchSuggest: false, //显示推送ai
            // rightTab:0,//画廊右侧显示面板，0：聊天界面；1：评论标签界面
            is_private: this.$route.meta.is_private || 0, //显示私有评论还是公开评论
            noneComment: this.$route.meta.noneComment || 0, //是否显示评论面板
            noneChat: this.$route.meta.noneChat || 0, //是否显示聊天面板
            keepAlive: this.$route.meta.keepAlive || 0, //index下的画廊才keepalive
            openFile: {},
            operatingTags: false,
            tagText: "",
            addingTags: false,
            choosingTags: false,
            tagTab: "custom",
            commentText: "",
            addingComment: false,
            mainVideoSrc: "",
            gestrueVideoSrc: "",
            mainAudioSrc: "",
            exporting: false,
            RealTimeVideoThumb: [],
            playRealtimeVideo: false,
            mousedownThumpPoint: null,
            isShowFileReader: false,
            readingFile: {},
            close_audio_for_separate_mode: false,
            videoCommitFile: null,
            videoCommitStamp: 0,
            videoCommitInterval: null,
            // isShowDialog:false,//是否显示dialog
            rightTabs: {},
            defaultRightTabs: {
                chat: { name: "chat", key: "chat_text", icon: "iconioschatbubble fx1", enabled: true },
                discover: { name: "discover", key: "discover", icon: "iconfaxianjihuo fx1", enabled: true },
                ai_analyze: { name: "ai_analyze", key: "ai_analyze", icon: "iconAI fx1", enabled: false },
                obstetric_qc_ai: { name: "obstetric_qc_ai", key: "obstetric_qc_ai", icon: "iconAI", enabled: false },
            },
            need_reload: false, //当用户切换ai数显示时需要切换
            rightActiveTab: "",
            isStructImage: true, //显示原始图像按钮
            displayColors: [],
            newReceiveResourceIds: [],
            commentList: [],
            getUserFavoriteStatus: false,
            getLikeCount: 0,
            getUserLikeStatus: false,
            isLoading: false, //是否在分析中显示加载框
            aiAnalyzeType: -1,
            isShowQuestion: false,
            isMcData: false,
            mainVideoPlayer: false,
            hasQRCode: false, // 标记当前图片是否包含二维码
            qrCodeData: null, // 存储二维码扫描结果
            qrCodeWorker: null, // 存储二维码识别 Worker 实例
            qrCodeTasks: {}, // 存储二维码识别任务
            qrCodeTaskId: 0, // 二维码识别任务 ID
        };
    },
    computed: {
        aiPresetData() {
            return this.$store.state.aiPresetData;
        },
        loadMore() {
            return this.gallery.loadMore || false;
        },
        listenerGalleryNewDataTag() {
            return this.gallery.listenerGalleryNewDataTag || "";
        },
        conversation() {
            this.serRightTabs();
            return this.conversationList[this.cid] || {};
        },
        galleryObj() {
            return this.conversation.galleryObj || {};
        },
        galleryList() {
            return this.formatGalleryList(this.gallery.list);
        },
        currentFile() {
            let file = this.galleryList[this.index] || this.openFile || {};
            if (!file.hasOwnProperty("mc_resource_map")) {
                file.mc_resource_map = false;
            }
            // file.mc_resource_map=file.mc_resource_map||false
            // console.log('file:',file)
            return cloneDeep(file);
        },
        isObstetricQCMulticenter() {
            return this.conversation.multicenter_type == "obstetric_qc_multicenter";
        },
        commentItem() {
            let resource_id = this.currentFile && this.currentFile.resource_id;
            let commentObj = this.$store.state.gallery.commentObj;
            return commentObj[resource_id] || {};
        },
        // commentList(){
        //     let list=this.commentItem.comment_list||[]
        //     let temp=[];
        //     for(let item of list){
        //         // if (item.is_private==this.is_private) {

        //         // }
        //         temp.push(item)
        //     }
        //     return temp
        // },
        patientInfo() {
            let obj = {};
            let image = this.galleryList[this.index] || {};
            obj = transferPatientInfo(image);
            let sexArr = [this.lang.male, this.lang.female, this.lang.unknown];
            if (this.currentFile.live_record_data) {
                if (this.currentFile.live_record_data.name) {
                    obj.patient_name = this.currentFile.live_record_data.name;
                }
                if (this.currentFile.live_record_data.age) {
                    obj.patient_age = this.currentFile.live_record_data.age;
                }
                if (this.currentFile.live_record_data.hasOwnProperty("gender")) {
                    obj.patient_sex = sexArr[Number(this.currentFile.live_record_data.gender)];
                }
            }
            return obj;
        },
        tagList() {
            //已添加标签去重
            let list = this.commentItem.tags_list || [];
            let tempObj = {};
            let tempArr = [];
            let uid = this.user.uid;
            for (let tag of list) {
                if (!tempObj[tag.tags]) {
                    tag.checknum = 1;
                    tempObj[tag.tags] = Object.assign({}, tag);
                } else {
                    tag.checknum += 1;
                    tempObj[tag.tags].checknum += 1;
                }
                if (tag.sender_id == uid) {
                    tempObj[tag.tags].selfCheck = true;
                }
            }
            for (let tag in tempObj) {
                tempArr.push(tempObj[tag]);
            }
            return tempArr;
        },
        allTags() {
            let allList = this.$store.state.gallery.tagTopInfo.allTagTop || [];
            let result = [];
            let obj = {};
            for (let tag of allList) {
                tag.checked = false;
                result.push(tag);
            }
            for (let item of this.tagList) {
                if (item.selfCheck) {
                    for (let tag of result) {
                        if (item.tags == tag.caption) {
                            tag.checked = true;
                            break;
                        }
                    }
                }
            }
            return result;
        },
        // resourceUnreview(){
        //     return this.$store.state.resourceUnreview
        // },
        EnableOpenReport() {
            // 是否允许查看报告
            return (
                this.$store.state.systemConfig.serverInfo.report &&
                this.$store.state.systemConfig.serverInfo.report.enable
            );
        },
        defaultSetIndex() {
            return this.$store.state.gallery.index;
        },
        themeSubject() {
            return this.getRecordSubject(this.currentFile);
        },
        mcOptionsObj() {
            let listObj = {};
            let optionList = Object.values(this.$store.state.multicenter.optionList);
            if (optionList && optionList.length > 0) {
                optionList.forEach((option) => {
                    listObj[option.id] = option.more_details;
                });
                return listObj;
            } else {
                return null;
            }
        },
        isAiAnalyze() {
            return this.conversation.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze;
        },
        isDrAiAnalyze() {
            return this.conversation.service_type == this.systemConfig.ServiceConfig.type.DrAiAnalyze;
        },
        checkShowOpenReport() {
            return (
                this.currentFile.exam_id &&
                this.currentFile.img_encode_type &&
                (this.currentFile.img_encode_type.toUpperCase() === "PNG" ||
                    this.currentFile.img_encode_type.toUpperCase() === "JPG") &&
                this.EnableOpenReport
            );
        },
    },
    watch: {
        // galleryList:{
        //     handler(cur,prev){
        //         this.$nextTick(()=>{
        //             // this.receiveImage=false
        //             // if(Array.isArray(cur)&&Array.isArray(prev)){
        //             //     if (cur.length==0&&prev.length==1) {
        //             //         //画廊中最后一张图片被删除时自动关闭画廊
        //             //         if (prev[0].group_id==this.cid) {
        //             //             this.closeGallery();
        //             //             return ;
        //             //         }
        //             //     }
        //             // }
        //             // if (Array.isArray(cur)&&cur.length>1&&cur.length-this.swiperLength==1) {
        //             //     if (cur[0].group_id==prev[0].group_id) {
        //             //     //同一个群内新增一张图片时表示接收到新图片消息
        //             //         this.receiveImage=true
        //             //     }
        //             // }
        //             if(this.swiperLength!==cur.length){
        //                 this.updateSwiper()
        //             }

        //             this.swiperLength=cur.length;
        //         })
        //     },
        //     immediate:true
        // }
        "currentFile.mc_resource_map": {
            handler(newValue, oldValue) {
                if (!this.isShowGallery) {
                    return;
                }
                if (newValue.ai_report && oldValue.ai_report) {
                    if (newValue.ai_report.finshed) {
                        let new_isReconsider = newValue.isReconsider || false;
                        let new_reconsider_report = newValue.reconsider_report || {};
                        let old_reconsider_report = oldValue.reconsider_report || {};

                        if (new_reconsider_report.finshed != old_reconsider_report.finshed) {
                            this.preLoad(this.index, true);
                        }
                    }
                }
            },
            immediate: true,
            deep: true,
        },
        "currentFile.ai_analyze": {
            handler(newValue, oldValue) {
                if (!this.isShowGallery) {
                    return;
                }
                if (newValue && newValue.report && this.index >= 0 && this.galleryList.length > 0) {
                    this.preLoad(this.index, true);
                }
            },
            immediate: true,
            deep: true,
        },
        "$store.state.gallery.openFile": {
            handler(val) {
                if (val && val.resource_id) {
                    this.$nextTick(() => {
                        this.initGalleryPage();
                    });
                }
            },
        },
        "$store.state.gallery.commentObj": {
            handler(val) {
                if (!this.isShowGallery) {
                    return;
                }
                if (val && this.currentFile && this.currentFile.resource_id && val[this.currentFile.resource_id]) {
                    if (
                        val[this.currentFile.resource_id].ai_analyze_report &&
                        val[this.currentFile.resource_id].ai_analyze_report.status
                    ) {
                        setTimeout(() => {
                            if (this.index >= 0) {
                                this.preLoad(this.index, true);
                                if (this.index + 1 < this.galleryList.length) {
                                    this.preLoad(this.index + 1, true);
                                }
                            }
                        }, 50);
                    }
                }
            },
        },
    },
    created() {
        this.serRightTabs();
        // 初始化二维码识别 Worker
        this.initQRCodeWorker();
    },
    mounted() {
        this.$nextTick(() => {
            // 画廊中聊天窗点击其他图片
            // this.$root.eventBus.$off('initGalleryPage').$on('initGalleryPage',this.initGalleryPage);
            this.$root.eventBus.$off("destroyGallery").$on("destroyGallery", this.destroyGallery);
            this.$root.eventBus.$off("destroyLive").$on("destroyLive", this.destroyLive);
            //定位到评论或标签
            this.$root.eventBus.$off("positionToComment").$on("positionToComment", this.positionToComment);
            this.$root.eventBus.$off("closeGallery").$on("closeGallery", this.closeGallery);
            this.$root.eventBus.$off("prevImage").$on("prevImage", this.prevImage);
            this.$root.eventBus.$off("nextImage").$on("nextImage", this.nextImage);
            this.$root.eventBus.$off("hideRealTimeVideo").$on("hideRealTimeVideo", this.hideRealTimeVideo);
            this.$root.eventBus.$off("showRealTimeVideo").$on("showRealTimeVideo", this.showRealTimeVideo);
            this.$root.eventBus.$off("realtimeConsultationSize").$on("realtimeConsultationSize", (cb) => {
                this.$nextTick(() => {
                    let container = this.$refs.topSwiper;
                    let rect = getRealtimeConsultationSize(container);
                    cb && cb(rect);
                });
            });
            this.$root.eventBus.$off("notify_clip_parameter").$on("notify_clip_parameter", this.notifyClipParameter);
            this.$root.eventBus
                .$off("listenerGalleryNewData")
                .$on("listenerGalleryNewData", this.listenerGalleryNewData);
        });
    },
    beforeDestroy() {
        // 终止二维码识别 Worker
        if (this.qrCodeWorker) {
            this.qrCodeWorker.terminate();
            this.qrCodeWorker = null;
        }
    },
    methods: {
        //是否在分析中显示加载框
        handleUpdataLoadingStatus(isLoading) {
            this.isLoading = isLoading;
        },
        //是否重新加载
        reloadImage(isStructImage, displayColors, isDisplayBColor) {
            this.isStructImage = isStructImage;
            this.displayColors = displayColors;
            this.isDisplayBColor = isDisplayBColor;
            this.preLoad(this.index, true);
        },
        //右侧快捷按钮
        serRightTabs() {
            this.rightTabs = cloneDeep(this.defaultRightTabs);
            this.aiAnalyzeType = -1;
            this.isMcData = false;
            //产科质控
            if (this.currentFile.group_id || this.currentFile.gmsg_id || this.currentFile.sender_id) {
                if (this.currentFile.mc_resource_map && this.currentFile.mc_resource_map.ai_report) {
                    this.rightTabs.obstetric_qc_ai.enabled = this.functionsStatus.obstetricalAI && true;
                    if (!this.rightActiveTab) {
                        this.rightActiveTab = "obstetric_qc_ai";

                    }
                    this.aiAnalyzeType = this.aiPresetData.typeIndex.obstetrical;
                    this.isMcData = this.currentFile.msg_type == this.systemConfig.msg_type.AI_ANALYZE ? false : true;
                    if( this.rightActiveTab == "obstetric_qc_ai"){
                        this.aiAnalyzeType = this.aiPresetData.typeIndex.obstetrical;
                    }
                }
            }

            if (this.currentFile.group_id || this.currentFile.gmsg_id || this.currentFile.sender_id) {
                //AI分析
                let ai_analyze_report = this.getMessageAiReportFromLocal(this.currentFile);
                let storeItem = this.$store.state.gallery.commentObj[this.currentFile.resource_id];
                if (storeItem && storeItem.ai_analyze_report) {
                    ai_analyze_report = storeItem.ai_analyze_report;
                }
                this.rightTabs.ai_analyze.enabled =
                    this.functionsStatus.breastAI &&
                    ((this.conversationList[this.cid] &&
                        this.conversationList[this.cid].service_type ==
                            this.systemConfig.ServiceConfig.type.AiAnalyze) ||
                        (this.currentFile && this.currentFile.ai_analyze_id) ||
                        (ai_analyze_report && ai_analyze_report.type));
                //  && (
                //     storeItem&&storeItem.ai_analyze_report&&storeItem.ai_analyze_report.type!=undefined&&storeItem.ai_analyze_report.type!=this.aiPresetData.typeIndex.breastSearch
                // )
                // this.rightTabs.ai_analyze.enabled = this.functionsStatus.breastAI && this.currentFile.ai_analyze_id
                if (this.rightTabs.ai_analyze.enabled && !this.rightActiveTab) {
                    this.rightActiveTab = "ai_analyze";
                }
                if(this.rightActiveTab=='ai_analyze'){
                    this.aiAnalyzeType = ai_analyze_report?.type ||this.rightActiveTab
                }
            }
            if (this.currentFile.gmsg_id || this.currentFile.group_id || this.currentFile.sender_id) {
                for (let name in this.rightTabs) {
                    if (this.rightActiveTab) {
                        if (name == this.rightActiveTab && !this.rightTabs[name].enabled) {
                            this.rightActiveTab = "discover";
                            break;
                        }
                    } else {
                        this.rightActiveTab = "discover";
                        break;
                    }
                }
            }
        },

        switchRightTab(name) {
            let list = ["ai_analyze", "obstetric_qc_ai"];
            if (
                this.rightActiveTab != name &&
                this.index >= 0 &&
                (list.indexOf(this.rightActiveTab) > -1 || list.indexOf(name) > -1)
            ) {
                this.preLoad(this.index, true);
                this.need_reload = true;
            }
            this.rightActiveTab = name;
            if(name === 'chat'){
                this.$refs.chat_component.shouldScrollBottom()
            }
        },
        initGalleryPage() {
            this.cid = this.$route.query.cid || this.$route.params.cid;
            this.index = -1;
            this.openFile = this.gallery.openFile;
            this.RealTimeVideoThumb = [];
            this.isInGallery = true;
            // this.rightTab=0
            this.is_private = this.$route.meta.is_private || 0;
            this.noneComment = this.$route.meta.noneComment || 0;
            this.noneChat = this.$route.meta.noneChat || 0;
            this.keepAlive = this.$route.meta.keepAlive || 0;
            this.isShowFileReader = false;
            this.isDisplayBColor = false;
            // if (this.conversation.is_show_video_btn||this.currentFile.file_id ==='temp') {
            //     this.RealTimeVideoThumb.push({
            //         msg_type:this.systemConfig.msg_type.RealTimeVideo,
            //         file_id:'temp',
            //         loaded:true
            //     })
            // }
            this.$nextTick(() => {
                this.positionIndex();
            });
            //初始化画廊之后执行跳转到画廊前挂载的回调
            this.$root.galleryCallback && this.$root.galleryCallback();
            this.$root.galleryCallback = null;
            this.isShowGallery = true;
            this.$store.commit("gallery/sortTags");
            this.serRightTabs();
        },
        initSwiper() {
            this.positionIndex();
        },
        updateSwiper() {
            this.updateIndex();
        },
        slideTop(index) {
            try {
                this.index = index;
                // this.rightTab=0;
                this.slideThumb(index);
                this.changeHandler(index);
            } catch (error) {
                console.error(error);
            }
        },
        slideThumb(index) {
            console.log(index, "slideThumb");
            let thumb_slide = this.$refs.thumb_slide;
            let thumb_scroll_wrap = this.$refs.thumb_scroll_wrap;
            let scroll_width = thumb_scroll_wrap.clientWidth;
            let left = index * 157 - scroll_width / 2 + 78;
            this.$nextTick(() => {
                thumb_slide && thumb_slide.scrollTo({ x: left }, 150);
            });
        },
        lastPage() {
            let thumb_slide = this.$refs.thumb_slide;
            let left = thumb_slide.getPosition().scrollLeft;
            let thumb_scroll_wrap = this.$refs.thumb_scroll_wrap;
            let scroll_width = thumb_scroll_wrap.clientWidth;
            left -= scroll_width;
            if (left === 0) {
                left = -1;
            }
            this.$nextTick(() => {
                thumb_slide && thumb_slide.scrollTo({ x: left }, 150);
            });
        },
        nextPage() {
            let thumb_slide = this.$refs.thumb_slide;
            let left = thumb_slide.getPosition().scrollLeft;
            let thumb_scroll_wrap = this.$refs.thumb_scroll_wrap;
            let scroll_width = thumb_scroll_wrap.clientWidth;
            left += scroll_width;
            if (left === 0) {
                left = -1;
            }
            this.$nextTick(() => {
                thumb_slide && thumb_slide.scrollTo({ x: left }, 150);
            });
        },
        updateIndex() {
            console.log("updateIndex", this.index);
            if (this.index == -1) {
                //文件数组不存在，由watch触发时定位下标
                this.positionIndex();
            } else {
                this.slideTop(this.index);
            }
        },
        changeHandler(index) {
            console.log("changeHandler", index);
            this.displayColors = [];
            this.isDisplayBColor = false;
            this.isStructImage = true
            this.index = index;
            let file = this.galleryList[index];
            if (!file) {
                return;
            }
            this.clearResourceDetail();
            this.getResourceDetail();
            //快速划过不预加载图片
            clearTimeout(this.sliderTimer);
            this.sliderTimer = setTimeout(() => {
                this.preLoad(index, this.need_reload);
                this.need_reload = false;
                if (index - 1 >= 0) {
                    this.preLoad(index - 1);
                }
                if (index + 1 < this.galleryList.length) {
                    this.preLoad(index + 1);
                }
                if (this.galleryList.length - index <= 3) {
                    //切换到最后三张时加载更多
                    if (this.loadMore) {
                        this.$store.state.gallery.loadMoreCallback();
                    }
                }
            }, 300);
            clearTimeout(this.retentionTimer);
            if (this.currentFile.resource_id) {
                if (this.$store.state.gallery.commentObj[this.currentFile.resource_id]) {
                    let item = this.$store.state.gallery.commentObj[this.currentFile.resource_id];
                    if ("showAISearchSuggest" in item) {
                        this.showAISearchSuggest = item.showAISearchSuggest;
                    } else {
                        this.showAISearchSuggest = false;
                        // this.retentionTimer=setTimeout(()=>{
                        //     this.serarchInCaseDatabaseWithBackground()
                        // },800)
                    }
                } else {
                    this.showAISearchSuggest = false;
                    // this.retentionTimer=setTimeout(()=>{
                    //     this.serarchInCaseDatabaseWithBackground()
                    // },800)
                }
            }
            this.playRealtimeVideo = false;
            this.initDcmIfNeed(file);
            this.initVideoPageIfNeed(file);
            this.initFileReaderIfNeed(file);
            this.newReceiveResourceIds = this.newReceiveResourceIds.filter((item) => item !== file.resource_id);
            this.serRightTabs();

            // 如果是图片类型，在图片切换时检测二维码
            if (this.checkResourceType(file) === 'image') {
                this.detectQRCode(file);
            } else {
                // 如果不是图片类型，重置二维码检测状态
                this.hasQRCode = false;
                this.qrCodeData = null;
            }
        },
        realUrl(imageObj) {
            let realUrl = getRealUrl(imageObj);
            if (this.isDisplayBColor) {
                realUrl = this.switchRealUrlToBColorUrl(realUrl);
            }
            return realUrl;
        },
        drawImageSrc() {
            if (this.isStructImage && this.displayColors.length < 1) {
                //无结构框
                let url = this.getRenderImageUrl(this.currentFile);
                if (this.isDisplayBColor) {
                    url = this.switchRealUrlToBColorUrl(url);
                }
                return url;
            } else {
                //有结构框
                return this.getRenderImageUrl(this.currentFile, "realUrlStruct");
            }
        },
        loadImageList(realUrl,nowTime){
            return new Promise((resolve, reject) => {
                const realImage = new Image();
                realImage.onerror = reject;
                realImage.onload = () => resolve(realImage);
                realImage.setAttribute("crossOrigin", "anonymous");
                realImage.src = `${realUrl}?temp=${nowTime}`;
            });
        },
        async combinationImage (imageList,offset){
            return new Promise((resolve, reject) => {
                if(offset.length>2){
                    let max_width = 0
                    let max_height = 0
                    let off_width = offset[0]
                    let off_height = offset[1]

                    const canvas = document.getElementById("ai_mul_view_canvas");
                    const ctx = canvas.getContext('2d');
                    for(let i=0;i<offset.length;i++){
                        if(i%2==0){
                            max_width  = max_width>offset[i] + imageList[i%2].naturalWidth? max_width : offset[i] + imageList[i%2].naturalWidth
                            max_height = max_height> offset[i+1] + imageList[i%2].naturalHeight? max_height : offset[i+1] + imageList[i%2].naturalHeight
                        }
                    }
                    canvas.width = max_width- off_width;
                    canvas.height  = max_height - off_height ;
                    for(let i=0;i<offset.length;i++){
                        if(i%2==0){
                            ctx.drawImage(imageList[i%2], offset[i]-off_width, offset[i+1] - off_height );
                        }
                    }
                    const mergedDataUrl = canvas.toDataURL('image/png');
                    const newRealImage = new Image();
                    newRealImage.onload = () => resolve(newRealImage);
                    newRealImage.src = mergedDataUrl;
                }else{
                    resolve(imageList[0])
                }
            })
        },
        preLoad(index, need_reload = false) {
            var that = this;
            var imageObj = cloneDeep(this.galleryList[index]);
            let force = need_reload || this.isObstetricQCMulticenter
            if (!force) {
                if (this.getResourceTempStatus(imageObj.resource_id, "loaded") || imageObj.url == "") {
                    return;
                }
                if (this.galleryList[index].preloading) {
                    //图片未加载完成5s内重复触发不执行，5s后如果仍未加载完成可再次触发
                    setTimeout(() => {
                        this.galleryList[index].preloading = false;
                    }, 5000);
                    return;
                }
                this.galleryList[index].preloading = true;
            }
            // if(this.galleryList[index].hasOwnProperty('loadingTime')){
            //     this.galleryList[index].loadingTime = this.galleryList[index].loadingTime+1
            // }else{
            //     this.galleryList[index].loadingTime = 0
            // }
            // if(this.galleryList[index].loadingTime>5){ //尝试加载5次还无法加载 则不再加载
            //     return
            // }

            let realUrl = imageObj.url;
            let tempRealUrl = "";
            let tempLength = this.RealTimeVideoThumb.length;
            realUrl = getRealUrl(imageObj);
            if (this.isDisplayBColor) {
                realUrl = this.switchRealUrlToBColorUrl(realUrl);
            }
            if (
                (this.checkResourceType(imageObj) === "video" || this.checkResourceType(imageObj) === "review_video") &&
                !this.getResourceTempStatus(imageObj.resource_id, "loaded")
            ) {
                let mainVideoSrc = "";
                let gestrueVideoSrc = "";
                let mainAudioSrc = "";
                if (
                    imageObj.msg_type == this.systemConfig.msg_type.Video ||
                    imageObj.img_type_ex == this.systemConfig.msg_type.Video
                ) {
                    mainVideoSrc = imageObj.url.replace(imageObj.thumb, "");
                } else if (
                    imageObj.msg_type == this.systemConfig.msg_type.Cine ||
                    imageObj.img_type_ex == this.systemConfig.msg_type.Cine
                ) {
                    if (this.getResourceTempStatus(imageObj.resource_id, "mainVideoSrc")) {
                        //存在mainVideoSrc直接播放该地址
                        mainVideoSrc = this.getResourceTempStatus(imageObj.resource_id, "mainVideoSrc");
                    } else if (imageObj.mainVideoSrc) {
                        mainVideoSrc = imageObj.mainVideoSrc;
                    } else {
                        mainVideoSrc = imageObj.url.replace("thumbnail.jpg", "DeviceVideo.");
                        mainVideoSrc += imageObj.img_encode_type;
                        gestrueVideoSrc =
                            imageObj.url.replace("thumbnail.jpg", "GestureVideo.") + imageObj.img_encode_type;
                    }
                } else if (this.checkResourceType(imageObj) === "review_video") {
                    mainVideoSrc = imageObj.ultrasound_url;
                }
                // if(imageObj.img_has_gesture_video){
                //     //存在手势视频

                // }
                if (imageObj.img_has_gesture_video) {
                    gestrueVideoSrc = imageObj.gesture_url;
                }
                mainAudioSrc = imageObj.voice_url || "";
                that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                    resource_id: imageObj.resource_id,
                    data: {
                        mainVideoSrc: mainVideoSrc,
                        gestrueVideoSrc: gestrueVideoSrc,
                        mainAudioSrc: mainAudioSrc,
                    },
                });
            }

            if (
                force ||
                this.checkResourceType(imageObj) === "image" ||
                (this.checkResourceType(imageObj) === "video" &&
                    !this.getResourceTempStatus(imageObj.resource_id, "loaded"))
            ) {
                //预加载大图
                let resource_id = imageObj.resource_id;
                let item = this.$store.state.gallery.commentObj[resource_id];
                // if (item&&item.ai_analyzing) {
                //     //是AI分析图片且未返回结果
                //     setTimeout(()=>{
                //         this.galleryList[index].preloading=false;
                //         this.preLoad(index);
                //     },100)
                //     return
                // }
                const nowTime = Date.now();
                if (this.systemConfig.serverInfo.network_environment === 1) {
                    realUrl = Tool.replaceInternalNetworkEnvImageHost(realUrl);
                }
                let offset = [];
                let grayImageList = [];
                (async ()=>{
                    if (that.isDisplayBColor && imageObj.msg_type == that.systemConfig.msg_type.OBAI) {
                        offset = await that.getPositionOffsetFromUrl(realUrl);
                        for(let i=0;i<offset.length;i++){
                            if(i%2==0){
                                grayImageList.push(
                                    this.loadImageList( this.switchRealUrlToBColorUrl(realUrl,i/2+1),nowTime)
                                )
                            }
                        }
                    }
                    if(grayImageList.length<1){
                        grayImageList =[this.loadImageList( realUrl,nowTime)]
                    }
                    Promise.all(grayImageList).then(async images => {
                        let canvasUrl = "";
                        const realImage = await this.combinationImage(images,offset)
                        canvasUrl = that.drawCanvasToImage(imageObj, realImage);
                        if (!canvasUrl) {
                            let offset = [0, 0];
                            if (that.isDisplayBColor && imageObj.msg_type == that.systemConfig.msg_type.OBAI) {
                                offset = await that.getPositionOffsetFromUrl(realUrl);
                            }
                            canvasUrl = that.drawCanvasToImageForMCResource(offset, imageObj, realImage);
                            if (!canvasUrl) {
                                canvasUrl = that.drawCanvasToImageForStruct(offset, imageObj, realImage);
                            }
                        }

                        if (!canvasUrl) {
                            that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                                resource_id: imageObj.resource_id,
                                data: {
                                    realUrl,
                                    loaded: true,
                                },
                            });
                        }

                        // 如果是图片类型，在图片加载完成后检测二维码
                        if (that.checkResourceType(imageObj) === "image" && that.index === index) {
                            that.detectQRCode(imageObj);
                        }
                    }).catch(error => {
                        console.log("preloadError", error, that.galleryList[index - tempLength]);
                    });
                })()
            }
        },
        clearGalleryStatus() {
            this.$store.commit("gallery/setGallery", {
                list: [],
                openFile: null,
                loadMore: false,
                loadMoreCallback: async () => {},
                listenerGalleryNewDataTag: "",
            });
            this.newReceiveResourceIds = [];
        },
        closeGallery() {
            this.clearGalleryStatus();
            if (!this.keepAlive) {
                this.back();
            } else {
                this.$router.replace(`/index/chat_window/${this.cid}`);
            }
        },
        clickCloseGallery() {
            //手动点击关闭
            this.clearGalleryStatus();
            if (!this.keepAlive) {
                if (this.$route.name.includes("gallery")) {
                    this.back();
                } else {
                    this.destroyGallery();
                }
            } else {
                if (this.$route.path.indexOf("live_management") > 0) {
                    this.$router.replace(`/index/chat_window/live_management`);
                } else {
                    this.$router.replace(`/index/chat_window/${this.cid}`);
                }
            }
        },
        destroyGallery() {
            this.isLoading = false;
            this.isInGallery = false;
            this.isShowGallery = false;
            // this.isShowDialog=false;
            this.isShowFileReader = false;
            this.destroyLive();
            this.endVideoCommit();
        },
        destroyLive() {
            window.CWorkstationCommunicationMng.exitUltrasoundDesktop({});
            this.playRealtimeVideo = false;
            // this.$root.eventBus.$emit('closeRecieveDestop', {close_audio_for_separate_mode:this.close_audio_for_separate_mode,cid:this.cid})
            this.close_audio_for_separate_mode = false;
            this.$root.eventBus.$emit("recoverMonitorWall"); //展示电视墙小播放器
            // if (this.playRealtimeVideo) {

            // }
            if (!this.isCef) {
                //浏览器关闭画廊停止播放视频
                let videos = document.querySelectorAll("video");
                for (let video of videos) {
                    video.pause();
                }
            }
        },
        playVideoError() {
            this.showVideoErrorTips();
        },
        positionToComment(msg) {
            setTimeout(() => {
                // this.rightTab=1;
                if (msg.msg_type == this.systemConfig.msg_type.COMMENT) {
                    for (let comment of this.commentList) {
                        if (comment.comment_id == msg.comment_id) {
                            this.$store.commit("gallery/updateHighLightComment", msg);
                            let dom = this.$refs["comment_" + comment.comment_id][0];
                            dom.scrollIntoViewIfNeeded(false);
                            break;
                        }
                    }
                }
            }, 0);
        },
        positionIndex() {
            if (!this.openFile.file_id && !this.openFile.ai_analyze) {
                //未设置打开文件时不定位
                if (!this.openFile.protocol_view_guid && !this.openFile.protocol_view_name) {
                    return;
                }
            }
            for (let i = 0; i < this.galleryList.length; i++) {
                let file_id =
                    this.openFile.file_id ||
                    (this.openFile.ai_analyze && this.openFile.ai_analyze.messages[0].file_id) ||
                    this.openFile.resource_id;
                let resource_id =
                    this.openFile.resource_id ||
                    (this.openFile.ai_analyze && this.openFile.ai_analyze.messages[0].resource_id) ||
                    this.openFile.resource_id;
                if (!resource_id) {
                    if (file_id == this.galleryList[i].file_id) {
                        this.index = i;
                        break;
                    }
                } else {
                    if (resource_id == this.galleryList[i].resource_id) {
                        this.index = i;
                        break;
                    }
                }
            }
            console.log("positionIndex", this.index);
            if (this.index > -1) {
                this.slideTop(this.index);
            }
        },
        mouseupImage(event) {
            this.initDcmIfNeed(this.currentFile);

            // 移除这里的二维码检测代码
        },
        mousedownThumb(event, index) {
            this.mousedownThumpPoint = {
                x: event.x,
                y: event.y,
            };
        },
        mouseupThumb(event, index) {
            let offsetX = this.mousedownThumpPoint.x - event.x;
            let offsetY = this.mousedownThumpPoint.y - event.y;
            if (Math.abs(offsetX) < 20 && Math.abs(offsetY) < 20) {
                if (index == this.index) {
                    this.initDcmIfNeed(this.currentFile);
                    return;
                }
                this.slideTop(index);
            }
        },
        deleteTag(tagIndex) {
            if (!Tool.checkSpeakPermission(this.currentFile.group_id, this.user.uid)) {
                this.$notify.error(this.lang.no_speak_permission);
                return;
            }
            //todo
            let tag = this.tagList[tagIndex];
            if (!tag.selfCheck) {
                this.$notify.error(this.lang.cannot_delete_tag);
                return;
            }
            for (let item of this.commentItem.tags_list) {
                if (item.tags == tag.tags && item.sender_id == this.user.id) {
                    //选中原标签数组中自己发的那条
                    tag = item;
                    break;
                }
            }
            var that = this;
            this.operatingTags = true;
            let resource_id = this.currentFile.resource_id;
            let img_id = this.currentFile.img_id;
            let cid = this.currentFile.group_id;
            var controller = this.conversationList[cid].socket;
            var sendDate = {
                resource_id: resource_id,
                img_id: img_id,
                tag: tag.tags,
            };
            controller.emit("del_tags", sendDate, function (is_succ, data) {
                if (is_succ) {
                    that.$store.commit("gallery/deleteTagList", data);
                    that.$store.commit("gallery/reduceTagCount", data);
                } else {
                    that.$notify.error("del_tags error");
                }
                that.operatingTags = false;
            });
        },
        addCustomTag() {
            let str = this.tagText;
            str = str.replace(/&nbsp;/g, "");
            let regexp = /<div>/;
            if (regexp.test(str)) {
                this.$notify.error(this.lang.tag_text_has_newline);
                this.tagText = "";
                return;
            }
            if (str.length == 0) {
                this.$notify.error(this.lang.tag_text_null);
                return;
            } else {
                let find_in_list = false;
                for (let item of this.tagList) {
                    if (item.tags == str && item.selfCheck == true) {
                        find_in_list = true;
                        break;
                    }
                }
                if (find_in_list) {
                    //加的标签在tag_list里存在(用户给这张图片已添加过标签)
                    this.$notify.error(this.lang.tag_repeat_add);
                    return;
                } else {
                    //加的标签在tag_list里没有，但是在tagList存在，则模拟点击事件
                    for (let item of this.allTags) {
                        if (item.caption == str) {
                            this.addTag(item);
                            this.tagText = "";
                            return;
                        }
                    }
                }
                var that = this;
                let resource_id = this.currentFile.resource_id;
                let img_id = this.currentFile.img_id;
                let cid = this.currentFile.group_id;
                var controller = this.conversationList[cid].socket;
                var sendData = {
                    resource_id: resource_id,
                    img_id: img_id,
                    sender_id: this.user.uid,
                    tag: str,
                };
                this.addingTags = true;
                controller.emit("add_custom_tags", sendData, function (is_succ, data) {
                    that.addingTags = false;
                    if (is_succ) {
                        if (data == "add_tag_duplication") {
                            that.$notify.error(that.lang.tag_repeat_add);
                        } else {
                            that.$store.commit("gallery/addCustomTag", data);
                            that.$store.commit("gallery/addTagList", data);
                            //增加自定义标签仅增加tags_list，不改变常用标签信息
                            //data.name = data.tags;
                        }
                    } else {
                        that.$notify.error("add_custom_tags error");
                    }
                });
                this.tagText = "";
            }
        },
        addUserTag(tagIndex) {
            if (!Tool.checkSpeakPermission(this.currentFile.group_id, this.user.uid)) {
                this.$notify.error(this.lang.no_speak_permission);
                return;
            }
            let item = this.allTags[tagIndex];
            this.addTag(item);
        },
        // addSystemTag(tagIndex){
        //     if(!Tool.checkSpeakPermission(this.currentFile.group_id, this.user.uid)){
        //         this.$notify.error(this.lang.no_speak_permission);
        //         return;
        //     }
        //     let item=this.tagNames[tagIndex];
        //     this.addTag(item);
        // },
        addTag(item) {
            if (item.checked) {
                return;
            } else {
                //todo
                //tag是tag_list中的数据结构
                var that = this;
                this.choosingTags = true;
                let resource_id = this.currentFile.resource_id;
                let img_id = this.currentFile.img_id;
                let cid = this.currentFile.group_id;
                var controller = this.conversationList[cid].socket;
                var sendDate = {
                    resource_id: resource_id,
                    img_id: img_id,
                    tag: item.caption,
                    is_private: this.is_private,
                };
                controller.emit("add_tags", sendDate, function (is_succ, data) {
                    if (is_succ) {
                        that.$store.commit("gallery/addTagList", data);
                        that.$store.commit("gallery/addTagCount", data);
                    } else {
                        that.$notify.error("add_tags error");
                    }
                    that.choosingTags = false;
                });
            }
        },
        sendComment() {
            let str = this.commentText;
            if (str.length == 0) {
                this.$notify.error(this.lang.comment_text_null);
                return;
            } else {
                var that = this;
                let resource_id = this.currentFile.resource_id;
                let img_id = this.currentFile.img_id;
                let cid = this.currentFile.group_id;
                var controller = this.conversationList[cid].socket;
                let sendDate = {
                    resource_id: resource_id,
                    img_id: img_id,
                    parent_id: 0,
                    content: str,
                    is_private: this.is_private ? 1 : 0,
                };
                this.addingComment = true;
                controller.emit("add_comment", sendDate, function (is_succ, data) {
                    that.addingComment = false;
                    if (is_succ) {
                        // that.$store.commit('gallery/addCommentToList',data);
                        that.commentList.push(data);
                        setTimeout(() => {
                            //延时等待新加评论渲染到页面
                            let comment_id = that.commentList[that.commentList.length - 1].comment_id;
                            let dom = that.$refs["comment_" + comment_id][0];
                            dom.scrollIntoViewIfNeeded(false);
                        }, 0);
                    } else {
                        that.$notify.error("add_comment error");
                    }
                });
                this.commentText = "";
            }
        },
        async initVideoPageIfNeed(file) {
            let container = this.$refs.topSwiper;
            const { mainVideoSrc, gestrueVideoSrc, mainAudioSrc, isPlayReview } = await initVideoPage({
                file,
                container,
                cid: this.cid,
            });
            if (!this.isShowGallery) {
                this.destroyGallery();
                return;
            }
            this.endVideoCommit();
            
            // 桌面端环境：使用原有逻辑
            if (this.isCef && isPlayReview) {
                this.startVideoCommit(file);
            }
            // 浏览器环境：检查是否为回放视频，如果是则由videoPlayer组件自动处理上报
            else if (!this.isCef && this.isReviewVideo(file)) {
                console.log('Browser environment: Video reporting will be handled by videoPlayer component');
                // 浏览器环境下不需要手动调用startVideoCommit，videoPlayer组件会自动处理
            }
            
            this.mainVideoSrc = mainVideoSrc || this.mainVideoSrc;
            this.gestrueVideoSrc = gestrueVideoSrc || this.gestrueVideoSrc;
            this.mainAudioSrc = mainAudioSrc || this.mainAudioSrc;
        },
        initFileReaderIfNeed(file) {
            this.isShowFileReader = false;
            this.$nextTick(() => {
                if (file.msg_type == this.systemConfig.msg_type.File) {
                    this.isShowFileReader = true;
                    this.readingFile = file;
                    // this.$nextTick(()=>{
                    //     this.$refs.fileReader.initPage();
                    // })
                } else if (
                    file.msg_type == this.systemConfig.msg_type.Frame &&
                    file.img_encode_type &&
                    file.img_encode_type.toUpperCase() == "PDF"
                ) {
                    this.isShowFileReader = true;
                    const pdfFile = this.parsePdf(file);
                    this.readingFile = pdfFile;
                    // this.$nextTick(()=>{
                    //     this.$refs.fileReader.initPage();
                    // })
                }
            });
        },
        exportComment(file) {
            let cid = this.currentFile.group_id;
            var controller = this.conversationList[cid].socket;
            let obj = {
                img_id: file.img_id,
                resource_id: file.resource_id,
            };
            this.exporting = true;
            controller.emit("export_comment", obj, (is_succ, data) => {
                this.exporting = false;
                if (is_succ) {
                    this.hideRealTimeVideo();
                    window.open(data + "?msg_id=" + file.gmsg_id, "_self");
                } else {
                    this.$notify.error(this.lang.export_comment_fail);
                }
            });
        },
        hideRealTimeVideo() {
            //在画廊内调用时隐藏播放器显示
            if (this.isInGallery) {
                if (
                    this.checkResourceType(this.currentFile) === "video" ||
                    this.checkResourceType(this.currentFile) === "review_video"
                ) {
                    window.CWorkstationCommunicationMng.hideRealTimeVideo({});
                }
            }
        },
        showRealTimeVideo() {
            //在画廊内调用时恢复播放器显示
            if (this.isInGallery) {
                if (
                    this.checkResourceType(this.currentFile) === "video" ||
                    this.checkResourceType(this.currentFile) === "review_video"
                ) {
                    window.CWorkstationCommunicationMng.showRealTimeVideo({
                        type: 4,
                    });
                }
            }
        },
        drawCanvasToImage(imageObj, realImage) {
            let resource_id = imageObj.resource_id;
            let item = this.$store.state.gallery.commentObj[resource_id];

            let mark_list = imageObj.mark_list;
            if (
                imageObj.ai_analyze_report &&
                imageObj.ai_analyze_report.mark_list &&
                imageObj.ai_analyze_report.mark_list[resource_id]
            ) {
                mark_list = imageObj.ai_analyze_report.mark_list[resource_id];
            }
            if (
                item &&
                item.ai_analyze_report &&
                item.ai_analyze_report.mark_list &&
                item.ai_analyze_report.mark_list[resource_id]
            ) {
                mark_list = item.ai_analyze_report.mark_list[resource_id];
            }
            if (!mark_list || mark_list.length === 0 || !this.functionsStatus.breastAI) {
                //存在描迹则返回描迹图片，否则返回
                return "";
            }
            var canvas = document.getElementById("ai_canvas");
            var context = canvas.getContext("2d");
            canvas.width = realImage.width;
            canvas.height = realImage.height;
            context.drawImage(realImage, 0, 0);
            context.strokeStyle = "#f00";
            context.lineWidth = 2;
            for (let shape of mark_list) {
                if (shape.point.length == 0) {
                    continue;
                }
                context.beginPath();
                if (shape.type == 1) {
                    //直线
                    context.moveTo(shape.point[0].x, shape.point[0].y);
                    context.lineTo(shape.point[1].x, shape.point[1].y);
                    context.stroke();
                } else if (shape.type == 2) {
                    //矩形
                    let width = shape.point[1].x - shape.point[0].x;
                    let height = shape.point[1].y - shape.point[0].y;
                    context.strokeRect(shape.point[0].x, shape.point[0].y, width, height);
                    context.stroke();
                } else if (shape.type == 3) {
                    //椭圆
                    let radiusX = shape.point[1].x - shape.point[0].x;
                    let radiusY = shape.point[1].y - shape.point[0].y;
                    context.ellipse(
                        shape.point[0].x,
                        shape.point[0].y,
                        radiusX,
                        radiusY,
                        (Math.PI / 180) * shape.deg,
                        0,
                        Math.PI * 2,
                        true
                    );
                    context.stroke();
                } else if (shape.type == 4) {
                    //描迹
                    context.moveTo(shape.point[0].x, shape.point[0].y);
                    for (let i = 1; i < shape.point.length; i++) {
                        context.lineTo(shape.point[i].x, shape.point[i].y);
                    }
                    context.lineTo(shape.point[0].x, shape.point[0].y);
                    context.stroke();
                }
                context.closePath();
            }
            let base64 = canvas.toDataURL("image/jpeg");
            this.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                resource_id: imageObj.resource_id,
                data: {
                    drawedCanvas: true,
                    realUrl: base64,
                    loaded: true,
                },
            });
            return base64;
        },
        drawCanvasToImageForStruct(offset, imageObj, realImage) {
            let ai_analyze_report = this.getMessageAiReportFromLocal(imageObj);
            let report = null;
            if (ai_analyze_report && ai_analyze_report.clips && ai_analyze_report.clips[imageObj.resource_id]) {
                report = ai_analyze_report.clips[imageObj.resource_id][0];
            }
            let aiPresetData = null;
            if(ai_analyze_report&& ai_analyze_report.type==this.aiPresetData.typeIndex.abdomen){
                aiPresetData =   this.$store.state.aiPresetData?.iworksAbdomenTest.views||{}
            }
            if(ai_analyze_report&& ai_analyze_report.type==this.aiPresetData.typeIndex.cardiac){
                aiPresetData =   this.$store.state.aiPresetData?.cardiacViews.views||{}
            }
            if (report && this.functionsStatus.breastAI && aiPresetData) {

                const child_id = report.child_id&& report.child_id >= 0 && aiPresetData[report.child_id]? report.child_id : -1
                let viewObj = aiPresetData[report.id];
                if(child_id>-1){
                    viewObj = aiPresetData[child_id];
                }
                let dispalyStructs = [];
                if (viewObj) {
                    for (let i in viewObj.details) {
                        let struct = viewObj.details[i];
                        for (let j in report.struct) {
                            let item = report.struct[j];
                            let isShow = true;
                            if (item.type == struct.id && isShow) {
                                dispalyStructs.push(item);
                            }
                        }
                    }
                    return this.setCanvasByPosition(offset, imageObj, realImage, dispalyStructs);
                } else {
                    return "";
                }
            } else {
                return "";
            }
        },
        drawCanvasToImageForMCResource(offset, imageObj, realImage) {
            if (!this.functionsStatus.obstetricalAI || !imageObj) {
                return;
            }
            let ai_report = null;
            let mc_resource_map = null;
            let item = this.$store.state.gallery.commentObj[imageObj.resource_id];
            if(this.isAiAnalyze||this.rightActiveTab == "ai_analyze"){
                mc_resource_map = this.getMcResourceMap(imageObj, true);
            }else if(this.rightActiveTab == "obstetric_qc_ai"){
                // this.isObstetricQCMulticenter
                mc_resource_map = mc_resource_map|| this.getMcResourceMap(imageObj, false);
            }else{
                mc_resource_map =imageObj.mc_resource_map? imageObj.mc_resource_map : this.getMcResourceMap(imageObj, true);
            }
            const multicenter = this.$store.state.multicenter;
            let  mc_id = 0;
            let  mc_op_id = 0;
            let  mc_options = null;
            if( multicenter.obstetricEarlyPregnancy&&
            multicenter.obstetricEarlyPregnancy.mcOpId&&
            multicenter.optionList){
                mc_op_id = multicenter.obstetricEarlyPregnancy.mcOpId;
                mc_options = multicenter.optionList[mc_op_id];
                mc_id = mc_options&&mc_options.mc_id;
            }

            var canvas = document.getElementById("ai_canvas");
            var context = canvas.getContext("2d");
            canvas.width = realImage.width;
            canvas.height = realImage.height;
            context.drawImage(realImage, 0, 0);
            if (
                mc_options&&
                mc_op_id&&
                mc_id &&
                this.functionsStatus.obstetricalAI &&
                mc_resource_map &&
                mc_resource_map.ai_report &&
                mc_resource_map.ai_report.finshed &&
                mc_resource_map.ai_report.report&&
                this.mcOptionsObj &&
                this.mcOptionsObj[mc_op_id]
            ) {
                ai_report = mc_resource_map.ai_report;
            }
            if (mc_resource_map && ai_report ) {
                let isStructDect = false;
                if (ai_report && ai_report.report && ai_report.report.isSuccess) {
                    let viewListObj = this.mcOptionsObj[mc_resource_map.mc_op_id].listObj;
                    ai_report = ai_report.report;
                    let strucs = ai_report.structure;
                    let dispalyStructs = [];
                    if (viewListObj[ai_report.view_type]) {
                        let allowedStrucs = viewListObj[ai_report.view_type].detail;
                        allowedStrucs.forEach((allowed) => {
                            strucs.forEach((detect) => {
                                if (allowed.id == detect.type) {
                                    dispalyStructs.push(detect);
                                }
                            });
                        });
                    }
                    let colors = this.aiPresetData.colors;

                    context.strokeStyle = "#f00";
                    context.lineWidth = 3;
                    if (viewListObj[ai_report.view_type]) {
                        let allowedStrucs = viewListObj[ai_report.view_type].detail;
                        let i = 0;
                        let colorsObj = {};
                        viewListObj[ai_report.view_type].item.forEach((item) => {
                            let detail_ids = item.detail_ids;
                            if (detail_ids && detail_ids.length > 0) {
                                let color = colors[i];
                                let old_color = "";
                                detail_ids.forEach((detail_id) => {
                                    if (colorsObj[detail_id]) {
                                        old_color = colorsObj[detail_id];
                                    }
                                });
                                if (!old_color) {
                                    i = i + 1;
                                }
                                detail_ids.forEach((detail_id) => {
                                    if (old_color) {
                                        colorsObj[detail_id] = old_color;
                                    } else {
                                        colorsObj[detail_id] = color;
                                    }
                                });
                            }
                        });
                        let haveSetItem = (ai_report.item || []).reduce((h, v) => {
                            h[v.type] = h[v.type] || [];
                            h[v.type].push(v);
                            return h;
                        }, {});

                        viewListObj[ai_report.view_type].item.forEach(async (item) => {
                            let detail_ids = item.detail_ids;
                            if (true) {
                                if (detail_ids && detail_ids.length > 0) {
                                    // let offset = [0, 0];

                                    detail_ids.forEach((detail_id) => {
                                        dispalyStructs.forEach((detect) => {
                                            let position = detect.position;
                                            position = [
                                                position[0] - offset[0],
                                                position[1] - offset[1],
                                                position[2] - offset[0],
                                                position[3] - offset[1],
                                            ];
                                            isStructDect = isStructDect || position.length > 0;
                                            if (
                                                detail_id == detect.type &&
                                                haveSetItem[item.id] &&
                                                haveSetItem[item.id].length > 0
                                            ) {
                                                let color = colorsObj[detail_id];
                                                if (
                                                    this.displayColors.indexOf(color) > -1 ||
                                                    this.displayColors.length == 0
                                                ) {
                                                    // dispalyStructs.push(detect)
                                                    context.beginPath();
                                                    context.moveTo(position[0], position[1]); //把画笔移动到指定的坐标
                                                    context.lineTo(position[2], position[1]); //绘制一条从当前位置到指定坐标(200, 50)的直线.
                                                    context.lineTo(position[2], position[3]);
                                                    context.lineTo(position[0], position[3]);
                                                    // context.lineTo(position[0], position[1]);
                                                    context.strokeStyle = color;
                                                    context.save();
                                                    context.closePath();
                                                    context.stroke(); //绘制路径。
                                                }
                                            }
                                        });
                                    });
                                }
                            }
                        });
                    }

                }
                let base64 = canvas.toDataURL("image/jpeg");
                let data = {};
                data = {
                    drawedCanvas: true,
                    realUrlStruct: base64,
                    loaded: true,
                };
                if (
                    !isStructDect ||
                    (this.isStructImage && this.displayColors.length < 1) ||
                    this.displayColors == this.aiPresetData.colors.length
                ) {
                    data.realUrl = base64;
                }
                this.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                    resource_id: imageObj.resource_id,
                    data,
                });
                return base64;
            } else {
                return "";
            }
        },
        setCanvasByPosition(offset, imageObj, realImage, dispalyStructs) {
            let colors = this.aiPresetData.colors;
            var canvas = document.getElementById("ai_canvas");
            var context = canvas.getContext("2d");
            canvas.width = realImage.width;
            canvas.height = realImage.height;
            context.drawImage(realImage, 0, 0);
            context.strokeStyle = "#f00";
            context.lineWidth = 3;
            for (let i = dispalyStructs.length - 1; i >= 0; i--) {
                let position = dispalyStructs[i].position;
                position = [
                    position[0] - offset[0],
                    position[1] - offset[1],
                    position[2] - offset[0],
                    position[3] - offset[1],
                ];
                let color = colors[i];
                if (this.displayColors.indexOf(color) >= 0 || (this.isStructImage && this.displayColors.length < 1)) {
                    context.beginPath();
                    context.moveTo(position[0], position[1]); //把画笔移动到指定的坐标
                    context.lineTo(position[2], position[1]); //绘制一条从当前位置到指定坐标(200, 50)的直线.
                    context.lineTo(position[2], position[3]);
                    context.lineTo(position[0], position[3]);
                    context.strokeStyle = color;

                    context.save();
                    context.closePath();
                    context.stroke(); //绘制路径。
                }
            }
            let base64 = canvas.toDataURL("image/jpeg");
            let data = {};
            if (this.isStructImage && this.displayColors.length < 1) {
                data = {
                    drawedCanvas: true,
                    realUrl: base64,
                    realUrlStruct: base64,
                    loaded: true,
                };
            } else {
                data = {
                    drawedCanvas: true,
                    realUrlStruct: base64,
                    loaded: true,
                };
            }
            // console.error('data:',data)
            this.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                resource_id: imageObj.resource_id,
                data,
            });
            return base64;
        },
        //查看自控报告
        openQCReport() {
            // this.isShowDialog = true
        },
        updateMarkListIfNeed() {
            //更新了描迹检查是否要重新画描迹
            var imageObj = this.galleryList[this.index];
            if (!this.getResourceTempStatus(imageObj.resource_id, "drawedCanvas")) {
                this.preLoad(this.index);
            }
        },
        openReport() {
            this.hideRealTimeVideo();
            this.$store.commit("report/setReportFile", this.currentFile);
            this.$router.push(this.$route.fullPath + "/report");
        },
        initDcmIfNeed(file) {
            initDcm(file);
        },
        openFileReader() {},
        prevImage() {
            if (this.index != 0) {
                this.slideTop(this.index - 1);
            }
        },
        nextImage() {
            if (this.index < this.galleryList.length) {
                this.slideTop(this.index + 1);
            }
        },
        notifyClipParameter(option) {
            let that = this;

            //关闭画廊
            this.clickCloseGallery();
            setTimeout(() => {
                let image = that.currentFile;
                window.media_transfer_task_list = [
                    {
                        type: 1,
                        group_id: image.group_id,
                        resource_id: image.resource_id,

                        timespan: option.timespan || { start: 0, end: 0 },
                        image_detail_information: option.image_detail_information || [],
                    },
                ];

                var path = that.$route.fullPath;
                that.$router.push(path + "/clip");
            }, 200);
        },
        getCommentNum(commentList = []) {
            let num = 0;
            commentList.forEach((element) => {
                if (element.status !== 0) {
                    if (!element.is_private || (element.is_private && this.user.uid === element.author_id)) {
                        num++;
                    }
                }
            });
            return num;
        },
        saveFavoriteCommit(oData) {
            let data = cloneDeep(oData);
            if (this.$route.path.indexOf("live_management") > 0) {
                data.from = "live_management";
            } else {
                data.from = "chat_component";
            }
            this.$root.eventBus.$emit("openFavoriteDialog", data);
            // saveFavoriteCommit(data)
        },
        parsePdf(file) {
            let obj = {
                file_type: "pdf",
            };
            let arr = file.url.split("/");
            arr.pop();
            obj.file_name = arr.pop();
            obj.url = file.url.replace("thumbnail.jpg", "SingleFrame.pdf");
            return obj;
        },
        cancelFavoriteCommit(target) {
            cancelFavoriteCommit({
                resource_id: target.resource_id,
                cid: target.group_id,
            });
        },
        startVideoCommit(file) {
            this.videoCommitStamp = Date.now();
            this.videoCommitFile = file;
            this.videoCommitInterval = setInterval(() => {
                this.commitVideoParams();
            }, 5000);
        },
        endVideoCommit() {
            clearTimeout(this.videoCommitInterval);
            this.commitVideoParams();
            this.videoCommitFile = null;
            this.videoCommitStamp = 0;
        },
        commitVideoParams() {
            if (!this.videoCommitFile) {
                return;
            }
            let cid = this.videoCommitFile.group_id;
            let data = {
                type: 5,
                cid: cid,
                business_data: {
                    resource_id: this.videoCommitFile.resource_id,
                    uuid: this.videoCommitStamp,
                    duration: Date.now() - this.videoCommitStamp,
                },
                showErrorToast: false,
            };

            // 兼容浏览器环境和桌面端环境
            this.reportReviewEvent(data, (res) => {
                console.log("reportReviewEvent---", data);
            });
        },
        /**
         * 上报直播回放观看时间
         * 兼容桌面端和浏览器环境
         */
        reportReviewEvent(data, callback) {
            // 桌面端环境：使用window.main_screen
            if (window.main_screen && typeof window.main_screen.reportReviewEvent === 'function') {
                window.main_screen.reportReviewEvent(data, callback);
                return;
            }
            this.reportReviewEventForBrowser(data, callback);
        },
        /**
         * 浏览器环境下的接口调用实现
         */
        async reportReviewEventForBrowser(data, callback) {
            try {
                const requestData = {
                    method: 'conference.report.event',
                    bizContent: data,
                    showErrorToast: false
                };

                // 使用service模块发送请求
                const response = await service.userEventV2(requestData);
                
                if (callback && typeof callback === 'function') {
                    callback(response.data || response);
                }
            } catch (error) {
                console.error('浏览器环境下上报直播回放时间失败:', error);
                
                // 执行回调，传递错误信息
                if (callback && typeof callback === 'function') {
                    callback({
                        error_code: -1,
                        error_message: error.message
                    });
                }
            }
        },
        async handleLikeAction(currentFile) {
            let isLiked = 0;
            if (
                this.$store.state.resourceTempStatus[currentFile.resource_id] &&
                this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("isLiked")
            ) {
                isLiked = this.$store.state.resourceTempStatus[currentFile.resource_id].isLiked || 0;
            } else {
                isLiked = this.getUserLikeStatus || 0;
            }
            if (isLiked) {
                this.handleUnLikeAction(currentFile);
                return;
            }
            let likes = 0;
            if (
                this.$store.state.resourceTempStatus[currentFile.resource_id] &&
                this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("likes")
            ) {
                likes = this.$store.state.resourceTempStatus[currentFile.resource_id].likes || 0;
            } else {
                likes = this.getLikeCount || 0;
            }

            this.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                resource_id: currentFile.resource_id,
                data: {
                    isLiked: 1,
                    likes: likes + 1,
                },
            });
            const { data } = await service.likeResourceAction({ resourceID: currentFile.resource_id });
        },
        async handleUnLikeAction(currentFile) {
            let likes = 0;
            if (
                this.$store.state.resourceTempStatus[currentFile.resource_id] &&
                this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("likes")
            ) {
                likes = this.$store.state.resourceTempStatus[currentFile.resource_id].likes || 0;
            } else {
                likes = this.getLikeCount || 0;
            }
            this.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                resource_id: currentFile.resource_id,
                data: {
                    isLiked: 0,
                    likes: likes - 1,
                },
            });
            const { data } = await service.unLikeResourceAction({ resourceID: currentFile.resource_id });
        },
        checkLikeAction(currentFile) {
            let isLiked = 0;
            if (
                this.$store.state.resourceTempStatus[currentFile.resource_id] &&
                this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("isLiked")
            ) {
                isLiked = this.$store.state.resourceTempStatus[currentFile.resource_id].isLiked || 0;
            } else {
                isLiked = this.getUserLikeStatus || 0;
            }
            return isLiked;
        },
        parseLikeText(currentFile) {
            let likes = 0;
            if(this.getLikeCount){
                likes = this.getLikeCount || 0;
            }else if(this.$store.state.resourceTempStatus[currentFile.resource_id] &&this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("likes")){
                likes = this.$store.state.resourceTempStatus[currentFile.resource_id].likes || 0;
            }
            let isLiked = 0;
            if (
                this.$store.state.resourceTempStatus[currentFile.resource_id] &&
                this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("isLiked")
            ) {
                isLiked = this.$store.state.resourceTempStatus[currentFile.resource_id].isLiked || 0;
            } else {
                isLiked = this.getUserLikeStatus || 0;
            }
            if (!isLiked) {
                if (likes > 0) {
                    return likes;
                } else {
                    return `${this.lang.like_action}`;
                }
            } else {
                return likes;
            }
        },
        checkFavoriteStatus() {
            let userFavoriteStatus = false;
            let currentFile = this.galleryList[this.index];
            if (!currentFile) {
                return;
            }

            if (
                this.$store.state.resourceTempStatus[currentFile.resource_id] &&
                this.$store.state.resourceTempStatus[currentFile.resource_id].hasOwnProperty("userFavoriteStatus")
            ) {
                userFavoriteStatus =
                    this.$store.state.resourceTempStatus[currentFile.resource_id].userFavoriteStatus || false;
            } else {
                userFavoriteStatus = this.getUserFavoriteStatus || false;
            }
            return userFavoriteStatus;
        },
        serarchInCaseDatabaseWithBackground() {
            let realUrl = this.currentFile.realUrl || getRealUrl(this.currentFile);
            let islegal = Tool.isLegalForematForSearchImage({ ...this.currentFile, realUrl });
            if (
                islegal &&
                realUrl &&
                this.currentFile.resource_id &&
                this.currentFile.exam_id &&
                this.currentFile.exam_type &&
                this.currentFile.exam_type == 8
            ) {
                let value = {
                    keys: { showAISearchSuggest: false },
                    resource_id: this.currentFile.resource_id,
                };
                let condition = {
                    pageNo: 1,
                    pageSize: 1,
                    type: "BREASTSEARCH",
                    sender_id: this.user.id,
                    group_id: this.currentFile.group_id,
                    resource_id: this.currentFile.resource_id,
                    url: realUrl,
                    is_detect: true,
                };
                console.log("serarchInCaseDatabaseWithBackground condition:", condition);
                let ai_searcher_server = this.$store.state.systemConfig.serverInfo.ai_searcher_server;
                let ajaxServer =
                    ai_searcher_server.protocol +
                    ai_searcher_server.addr +
                    ":" +
                    ai_searcher_server.port +
                    "/" +
                    trim(trim(ai_searcher_server.api, "/"), "\\");
                let url = trim(trim(ajaxServer, "/"), "\\") + "/" + "find_by_image";
                service.requestAiAnalyzeWithUrl({ method: "find_by_image", condition }).then(async (res) => {
                    let data = res.data.data;
                    if (data.resource_id) {
                        let value = {
                            keys: { showAISearchSuggest: false },
                            resource_id: data.resource_id,
                        };
                        if (data.list && data.list.length > 0) {
                            if (data.resource_id == this.currentFile.resource_id) {
                                this.showAISearchSuggest = true;
                            }
                            value.keys.showAISearchSuggest = true;
                        } else {
                            if (data.resource_id == this.currentFile.resource_id) {
                                this.showAISearchSuggest = false;
                            }
                            value.keys.showAISearchSuggest = false;
                        }
                        this.$store.commit("gallery/updateCommentObjByKey", value);
                    }
                });
            }
        },
        searchInCaseData() {
            if (this.currentFile) {
                let cid = this.currentFile.group_id;
                let searchParams = {
                    type: "url", //text,url,file
                    content: this.currentFile,
                };

                let islegal = Tool.isLegalForematForSearchImage(this.currentFile);
                if (islegal) {
                    this.$store.commit("caseDatabase/updateSearchParams", searchParams);
                    this.$router.push({ path: `/index/chat_window/${cid}/case_database` });
                } else {
                    this.$message.error(this.lang.picture_is_only_jpg_jpeg_bmp_png);
                }
            }
        },
        listenerGalleryNewData(data) {
            console.log(this.listenerGalleryNewDataTag, data.tag, this.isShowGallery);
            if (data.tag === this.listenerGalleryNewDataTag && this.isShowGallery) {
                console.log(data.data);
                let list = cloneDeep(this.galleryList);
                list.unshift(data.data);
                this.$store.commit("gallery/setGallery", {
                    list,
                });
                this.index++;
                this.newReceiveResourceIds.push(data.data.resource_id);
            }
        },
        clearResourceDetail() {
            this.commentList = [];
            this.getUserFavoriteStatus = false;
            this.getLikeCount = 0;
            this.getUserLikeStatus = false;
        },
        async getResourceDetail() {
            if (!this.currentFile.resource_id || !this.currentFile.url) {
                return;
            }
            const res = await service.getResourceDetail({ resourceID: this.currentFile.resource_id });
            if (res.data.error_code === 0) {
                this.$set(this, "commentList", res.data.data.commentList);
                this.$store.commit("gallery/setTagList", {
                    resource_id: this.currentFile.resource_id,
                    tagList: res.data.data.tags,
                });
                this.getUserFavoriteStatus = res.data.data.userFavorite;
                this.getLikeCount = res.data.data.likeCount;
                this.getUserLikeStatus = res.data.data.userLikeStatus;
            }
        },
        formatGalleryList(oList) {
            let list = [];
            oList.forEach((item) => {
                if (this.getResourceTempState(item.resource_id) === 1) {
                    list.push(item);
                }
            });
            return list;
        },
        showAiAnalyzeIcon(img) {
            return (
                this.functionsStatus.breastAI ||
                this.functionsStatus.drAIAssistant ||
                this.functionsStatus.obstetricalAI ||
                true
            );
        },
        // 处理右键点击事件
        handleContextMenu(event, file) {
            event.preventDefault();
            this.$root.eventBus.$emit('showGalleryMenu', {
                event: event,
                file: file,
                hasQRCode: this.hasQRCode,
                qrCodeData: this.qrCodeData
            });
        },
        // 判断是否是回放视频
        isReviewVideo(file) {
            return this.checkResourceType(file) === 'review_video';
        },
        // 视频播放事件处理
        onVideoPlay(fileInfo) {
            console.log('Video started playing:', fileInfo.resource_id);
            // 在浏览器环境下，不需要额外处理，videoPlayer组件已经开始上报
        },
        onVideoPause(fileInfo) {
            console.log('Video paused:', fileInfo.resource_id);
            // 暂停时不需要额外处理
        },
        onVideoEnded(fileInfo) {
            console.log('Video ended:', fileInfo.resource_id);
            // 结束时不需要额外处理，videoPlayer组件会停止上报
        },
        onVideoError(data) {
            console.error('Video error:', data);
            // 播放出错时的处理
        },
        // 处理视频播放时间上报
        onVideoReportTime(reportData) {
            console.log('Received video report data:', reportData);
            this.reportReviewEvent(reportData, (res) => {
                if (res.error_code === 0) {
                    console.log('Video time report success');
                } else {
                    console.error('Video time report failed:', res.error_message);
                }
            });
        },
        // 检测二维码
        detectQRCode(file) {
            //切换图片时，重置二维码状态
            this.hasQRCode = false;
            this.qrCodeData = null;
            // 只处理图片类型
            if (this.checkResourceType(file) !== 'image' || !file.url) {
                return;
            }

            const img = new Image();
            img.crossOrigin = "Anonymous";

            const that = this;
            img.onload = () => {
                const canvas = document.createElement("canvas");
                const ctx = canvas.getContext("2d");

                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);

                try {
                    // 获取图像数据
                    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

                    // 使用 Worker 进行异步二维码识别
                    // 创建任务 ID
                    const taskId = ++that.qrCodeTaskId;

                    // 记录任务
                    that.qrCodeTasks[taskId] = {
                        resourceId: file.resource_id,
                        timestamp: Date.now()
                    };

                    // 发送消息给 Worker
                    that.qrCodeWorker.postMessage({
                        id: taskId,
                        imageData: imageData
                    });
                } catch (error) {
                    console.error("Error detecting QR code:", error);
                    that.hasQRCode = false;
                    that.qrCodeData = null;
                }
            };

            img.onerror = () => {
                that.hasQRCode = false;
                that.qrCodeData = null;
            };

            // 设置图片源
            let imgUrl = this.realUrl(file);
            img.src = imgUrl;
        },
        // 初始化二维码识别 Worker
        initQRCodeWorker() {
            if (window.Worker) {
                try {
                    // 获取服务器地址
                    const server_type = this.$store.state.systemConfig.server_type;
                    let host = server_type.protocol + server_type.host + server_type.port;
                    let workerPath = './static/resource_pc/qrCodeWorker.js';

                    if (host.indexOf("localhost") > -1 || host.indexOf("192.168") > -1) {
                        host = `https://${Tool.getHostConfig().dev}`;
                    }
                    // 创建Worker实例
                    this.qrCodeWorker = new Worker(workerPath);

                    // 将host传递给Worker
                    this.qrCodeWorker.postMessage({
                        type: 'init',
                        host: host
                    });

                    // 监听 Worker 消息
                    this.qrCodeWorker.onmessage = (event) => {
                        const data = event.data;

                        if (data.type === 'ready') {
                            console.warn('QR Code Worker !!!REPORTING FOR DUTY!!!');
                        } else if (data.type === 'result') {
                            // 处理二维码识别结果
                            const taskId = data.id;
                            const task = this.qrCodeTasks[taskId];

                            if (task) {
                                // 只有当前图片的二维码识别结果才更新状态
                                if (task.resourceId === this.currentFile.resource_id) {
                                    this.hasQRCode = data.hasQRCode;
                                    this.qrCodeData = data.qrCodeData;
                                    console.warn('二维码识别结果:', data.hasQRCode ? '找到二维码' : '未找到二维码', data.qrCodeData);
                                }
                                delete this.qrCodeTasks[taskId];
                            }
                        } else if (data.type === 'error') {
                            // 处理错误
                            console.error('QR Code Worker 错误:', data.error);
                            // 清理任务（如果有）
                            if (data.id && this.qrCodeTasks[data.id]) {
                                delete this.qrCodeTasks[data.id];
                            }
                        }
                    };

                    // 监听 Worker 错误
                    this.qrCodeWorker.onerror = (error) => {
                        console.error('QR Code Worker 错误:', error);
                    };
                } catch (error) {
                    console.error('初始化 QR Code Worker 失败:', error);
                    this.qrCodeWorker = null;
                }
            } else {
                console.warn('当前浏览器不支持 Web Worker');
            }
        },
    },
};
</script>
<style lang="scss">
.gallery_wrapper {
    position: absolute;
    background: rgba(47, 47, 47, 0.7);
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    margin: 0;
    z-index: 5001;
    display: flex;
    align-items: center;
    .gallery {
        width: 90%;
        height: 85%;
        position: relative;
        box-shadow: 10px 8px 30px #666;
        border: 1px solid #fff;
        margin: 0 auto;
        .gallery_body {
            position: absolute;
            padding: 0 !important;
            display: flex;
            min-height: 10px;
            top: 0;
            width: 100%;
            height: 100%;
            z-index: 1;

            .iconsearchclose {
                position: absolute;
                width: 30px;
                height: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                right: 0;
                top: 0;
                cursor: pointer;
                z-index: 2001;
                font-size: 12px;
                color: #fff;
                &.nonechat {
                    right: 4px;
                    top: -32px;
                    color: #fff;
                    font-size: 20px;
                    padding: 0 4px;
                }
            }
            .title_bar {
                background: #212121;
                height: 36px;
                position: absolute;
                top: -36px;
                border: 1px solid #fff;
                border-bottom: none;
                left: -1px;
                right: -1px;
            }
            .left {
                flex: 1;
                background-color: #212121;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                position: relative;
                .top {
                    flex: 1;
                    position: relative;
                    padding: 10px;
                    min-height: 0;
                    .open_file {
                        height: 100%;
                        width: 100%;
                        position: absolute;
                        top: 0;
                        background-color: #212121;
                        z-index: 99;
                        img {
                            max-width: 100%;
                            max-height: 100%;
                            position: absolute;
                            top: 0;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            margin: auto;
                        }
                    }
                    .mui-slider-item {
                        font-size: 14px;
                        position: relative;
                        display: inline-block;
                        width: calc(100% - 17px);
                        height: 100%;
                        vertical-align: top;
                        white-space: normal;
                        user-select: none;
                        .ai_search_suggest {
                            // width: 40px;
                            // height: 40px;
                            // border: 3px solid #00ffae;
                            top: 5px;
                            position: absolute;
                            right: 20px;
                            color: #00ffae;
                            text-align: center;
                            display: inline;
                            line-height: 35px;
                            border-radius: 120px;
                            font-weight: 500;
                            font-size: 15px;
                            // text-shadow: 1px 1px 11px #fefffe;
                            // box-shadow: 1px 5px 20px #888888;
                            z-index: 10;
                        }
                    }
                    .preview,
                    .main_video {
                        max-width: 100%;
                        max-height: 100%;
                        position: absolute;
                        top: 0;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        margin: auto;
                    }
                    .review {
                        width: 100%;
                    }
                    .gesture_video {
                        position: absolute;
                        width: 300px;
                        right: 10px;
                        top: 0px;
                    }
                    .loading_span {
                        position: absolute !important;
                        z-index: 9;
                        left: 50%;
                        top: 50%;
                        transform: translate(-50%, -50%);
                        width: 42px;
                        height: 42px;
                        .el-loading-mask {
                            background: none;
                        }
                    }
                    .view_not_uploaded {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        font-size: 24px;
                        color: #f9f90f;
                    }
                    .iconright1 {
                        font-size: 40px;
                        left: 20px;
                        top: 50%;
                        position: absolute;
                        color: #aaa;
                        cursor: pointer;
                        z-index: 3;
                        display: none;
                    }
                    .iconright2 {
                        font-size: 40px;
                        right: 20px;
                        top: 50%;
                        position: absolute;
                        color: #aaa;
                        cursor: pointer;
                        z-index: 3;
                        display: none;
                    }
                    &:hover .iconfont {
                        display: block;
                    }
                }
                .thumb_loading {
                    position: absolute;
                    top: 0;
                    width: calc(100% - 40px);
                    height: 100%;
                    background-color: #212121;
                    z-index: 99;
                }
                .thumb_wrap {
                    height: 120px;
                    padding: 0px 20px 4px;
                    position: relative;
                    .thumb_scroll_wrap {
                        width: 100%;
                        height: 100%;
                        user-select: none;
                        .__rail-is-horizontal {
                            height: 0 !important;
                        }
                        .thumb_slide {
                            position: relative;
                            width: 100%;
                            height: 100%;
                            z-index: 1;
                            .thumb_item {
                                float: left;
                                width: 156px;
                                height: 116px;
                                background: #000;
                                position: relative;
                                margin-right: 1px;
                                cursor: pointer;
                                &.current_thumb {
                                    border: 3px solid #599592;
                                }
                                .preview {
                                    max-width: 100%;
                                    max-height: 100%;
                                    position: absolute;
                                    top: 0;
                                    bottom: 0;
                                    left: 0;
                                    right: 0;
                                    margin: auto;
                                }
                                .review_time {
                                    font-size: 12px;
                                    text-align: center;
                                    color: #fff;
                                    position: absolute;
                                    white-space: nowrap;
                                    top: 46%;
                                    left: 50%;
                                    z-index: 2;
                                    transform: translate(-50%, -50%) scale(0.9);
                                }
                                .review_text {
                                    color: yellow;
                                    font-size: 12px;
                                    text-align: center;
                                    position: absolute;
                                    width: 100%;
                                    top: 5%;
                                }
                                .iconvideo_fill_light,
                                .iconpicture {
                                    position: absolute;
                                    bottom: 0;
                                    color: #fff;
                                    font-size: 24px;
                                    line-height: 1;
                                    z-index: 2;
                                }
                                .comment_number {
                                    position: absolute;
                                    left: 2px;
                                    top: 2px;
                                    color: #fff;
                                    background: #56c7fd;
                                    border: 1px solid white;
                                    width: 20px;
                                    border-radius: 50%;
                                    height: 20px;
                                    font-size: 12px;
                                    line-height: 20px;
                                    text-align: center;
                                    z-index: 10;
                                }
                                .qc_standard_icon {
                                    right: 3px;
                                    bottom: 0px;
                                    position: absolute;
                                    color: rgb(0, 255, 102);
                                    font-size: 10px;
                                    z-index: 10;
                                }
                                .qc_non_standard_icon {
                                    right: 3px;
                                    bottom: 0px;
                                    position: absolute;
                                    color: rgb(255, 153, 51);
                                    font-size: 10px;
                                    z-index: 10;
                                }
                                .ai_result_deletion_icon {
                                    right: 3px;
                                    bottom: 0px;
                                    position: absolute;
                                    color: rgb(255, 0, 0);
                                    font-size: 10px;
                                    z-index: 10;
                                }
                                .bg_blue {
                                    background: rgb(0, 255, 102);
                                }
                                .bg_red {
                                    background: rgb(255, 0, 0);
                                }
                                .dr_result_icon {
                                    right: 3px;
                                    bottom: 3px;
                                    position: absolute;
                                    color: rgb(241 246 243);
                                    font-size: 12px;
                                    height: 17px;
                                    width: 16px;
                                    border-radius: 7px;
                                    display: block;
                                    text-align: center;
                                    line-height: 19px;
                                    font-weight: bold;
                                }
                                .unread_tip {
                                    position: absolute;
                                    right: 0.2rem;
                                    top: 0.2rem;
                                    border-radius: 50%;
                                    background-color: #f00;
                                    width: 8px;
                                    height: 8px;
                                    z-index: 2;
                                }
                                .empty_thump {
                                    width: 100%;
                                    height: 100px;
                                    position: absolute;
                                    top: 50%;
                                    left: 50%;
                                    background-color: #2c2d2f;
                                    transform: translate(-50%, -50%);
                                }
                                .view_name {
                                    color: #fff;
                                    position: absolute;
                                    top: 6px;
                                    z-index: 9;
                                    left: 2px;
                                    transform: none;
                                    font-size: 14px;
                                    white-space: normal;
                                    text-align: left;
                                }
                            }
                            .__bar-is-horizontal,
                            .__bar-is-vertical {
                                display: none;
                            }
                        }
                    }
                    .last_page {
                        transform: rotate(90deg) scaleX(1.5);
                        position: absolute;
                        left: 0px;
                        top: 50px;
                        color: #fff;
                        font-size: 18px;
                        line-height: 16px;
                        cursor: pointer;
                    }
                    .next_page {
                        transform: rotate(270deg) scaleX(1.5);
                        position: absolute;
                        right: 0px;
                        top: 50px;
                        color: #fff;
                        font-size: 18px;
                        line-height: 16px;
                        cursor: pointer;
                    }
                }
            }
            .middle {
                width: 30px;
                display: flex;
                flex-direction: colum;
                justify-content: space-around;
                background: #212121;
                .right_tab {
                    margin-top: 25px;
                }
                .iconioschatbubble {
                    font-size: 22px;
                }
                .fx1 {
                    width: 30px;
                    height: 30px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                }
                .gallery_tab_name {
                    height: 28px !important;
                    color: #86919a;
                    margin-top: 10px;
                    padding-bottom: 4px;
                    text-align: center;
                    font-size: 17px;
                }
                .active_tab {
                    color: white;
                    font-size: 18.5px;
                }
            }
            .right {
                border-left: 1px solid #d9d9d9;
                width: 450px;
                background-color: #ecf6f6;
                // #BDCFCF;
                position: relative;
                overflow: hidden;
                .chatbox {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: 100%;
                    transition: left 1s;
                    &.show {
                        left: 0;
                        transition: left 1s;
                    }
                    .tab_btn_right {
                        position: absolute;
                        width: 26px;
                        height: 50px;
                        border-radius: 0 26px 26px 0;
                        background: rgba(0, 0, 0, 0.2);
                        left: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        color: #516562;
                        line-height: 50px;
                        cursor: pointer;
                        z-index: 1;
                        i {
                            font-size: 20px;
                            position: absolute;
                        }
                        &:hover {
                            background-color: #7bb8b3;
                            color: #fff;
                        }
                    }
                }
                .gallery_ai_result {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    .header {
                        height: 40px;
                        text-align: center;
                        background-color: #7ba7b4;
                        line-height: 40px;
                        font-size: 16px;
                        font-weight: 600;
                        color: #fff;
                    }
                }

                .image_detail {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: -100%;
                    transition: left 1s;
                    display: flex;
                    flex-direction: column;
                    &.show {
                        transition: left 1s;
                        left: 0;
                    }
                    .image_detail_box {
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                        .header {
                            height: 40px;
                            text-align: center;
                            background-color: #7ba7b4;
                            line-height: 40px;
                            font-size: 16px;
                            font-weight: bold;
                            color: #fff;
                        }
                        .patient_info {
                            font-size: 16px;
                            color: #6c878c;
                            margin-left: 5px;
                            margin-right: 5px;
                            border-bottom: 2px dashed #dadfdf;
                            padding: 4px 0px 6px;
                        }
                        .tags_warper {
                            padding: 4px;
                            border-bottom: 2px solid #e1e4e4;
                            margin: 2px;
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            .tag_list {
                                flex: 1;
                                overflow: auto;
                                .tag_item {
                                    color: #fff;
                                    background: #83a5a1;
                                    padding: 0 8px;
                                    border-radius: 8px;
                                    margin: 4px;
                                    cursor: pointer;
                                    position: relative;
                                    word-break: break-all;
                                    &:hover {
                                        background-color: #e2e2e2;
                                        .iconclose {
                                            display: block;
                                        }
                                        .checknum {
                                            background-color: #e2e2e2;
                                        }
                                    }
                                    .iconclose {
                                        display: none;
                                        position: absolute;
                                        top: 50%;
                                        left: 50%;
                                        transform: translate(-50%, -50%);
                                        color: #83a5a1;
                                        font-size: 20px;
                                    }
                                    .checknum {
                                        font-size: 12px;
                                        background: #56c7fd;
                                        border: 1px solid white;
                                        width: 16px;
                                        height: 16px;
                                        line-height: 16px;
                                        display: inline-block;
                                        text-align: center;
                                        border-radius: 50%;
                                        float: right;
                                        margin: 6px 0 6px 6px;
                                    }
                                }
                            }
                            .add_tags {
                                display: flex;
                                margin: 4px 0;
                                .tag_text {
                                }
                                .add_custom_tag_submit {
                                    height: 32px;
                                    margin: 4px 6px;
                                }
                                .choose_custom_tag_btn {
                                    height: 32px;
                                    margin: 4px 6px 4px 0;
                                }
                            }
                        }
                        .comment_wraper {
                            flex: 2;
                            display: flex;
                            flex-direction: column;
                            padding: 0 2px;
                            min-height: 0;
                            .comment_list {
                                flex: 1;
                                margin-top: 6px;
                                padding: 2px 8px;
                                overflow: auto;
                                .comment_list_item {
                                    border-top: 1px solid #aaa;
                                    padding: 4px 10px 4px 4px;
                                    & > p {
                                        font-size: 18px;
                                    }
                                    &.high_light {
                                        background: #d6e9c6;
                                    }
                                    .comment_author,
                                    .comment_time {
                                        float: right;
                                        font-size: 14px;
                                        color: #666;
                                        margin-right: 8px;
                                    }
                                    .comment_private_text {
                                        float: right;
                                        font-size: 14px;
                                        margin-right: 5px;
                                    }
                                }
                            }
                            .comment_text {
                                margin: 6px 0;
                            }
                            .comment_btns {
                                // margin-bottom:6px;
                                .icon-button {
                                    position: relative;
                                    text-align: left;
                                    padding: 9px 28px 9px 10px;
                                    &.like {
                                        color: #00c49e;
                                    }
                                    .iconzan {
                                        position: absolute;
                                        right: 10px;
                                        top: 6px;
                                    }
                                }
                                .el-button {
                                    margin-bottom: 10px;
                                }
                            }
                        }
                    }
                    .tab_btn_left {
                        position: absolute;
                        width: 26px;
                        height: 50px;
                        border-radius: 26px 0 0 26px;
                        background: rgba(0, 0, 0, 0.2);
                        right: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        color: #516562;
                        line-height: 50px;
                        cursor: pointer;
                        z-index: 2;
                        i {
                            font-size: 20px;
                            position: absolute;
                        }
                        &:hover {
                            background-color: #7bb8b3;
                            color: #fff;
                        }
                    }
                }
            }
        }
    }
}
.gallery_search_tags {
    max-height: 80%;
    overflow-x: hidden;
    overflow-y: auto;
    .choose_tags_wrap {
        width: 300px;
        .el-tabs__active-bar {
            width: 84px !important;
        }
        .tag_names {
            & > span {
                color: #fff;
                background: #83a5a1;
                padding: 0 8px;
                border-radius: 8px;
                margin: 4px;
                cursor: pointer;
                position: relative;
                font-size: 16px;
                line-height: 1.6;
                &:hover {
                    background: #bdcfcf;
                }
                &.checked {
                    background: #ccc;
                }
            }
        }
    }
}
</style>
