<!-- src/components/GaugeCard.vue -->
<template>
    <div class="gauge-card" :data-value="value + '%'">
        <div class="gauge-card-content">
            <div class="gauge-title">{{ title }}</div>
            <div class="gauge-right-section">
                <svg :width="size" :height="size / 2" :viewBox="`0 0 ${viewBoxW} ${viewBoxH}`" class="gauge-svg">
                    <!-- 1. 灰色背景半圆 -->
                    <path :d="bgPath" :stroke="bgColor" :stroke-width="thickness" fill="none" stroke-linecap="butt" />
                    <!-- 2. 彩色前景半圆（用 dasharray 截断） -->
                    <path
                        :d="bgPath"
                        :stroke="fgColor"
                        :stroke-width="thickness"
                        fill="none"
                        stroke-linecap="butt"
                        :stroke-dasharray="dashArray"
                    />
                    <!-- 中心百分比 -->
                    <text
                        :x="cx"
                        :y="textYValue"
                        text-anchor="middle"
                        alignment-baseline="middle"
                        class="value-text"
                        fill="#333"
                    >
                        {{ value }}%
                    </text>
                </svg>
                <div class="standard" v-if="standard">{{ standard }}</div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "GaugeCard",
    props: {
        value: { type: Number, required: true }, // 0~100
        title: { type: String, default: "" },
        valueLabel: { type: String, default: "" },
        standard: { type: String, default: "" },
        threshold: { type: Number, default: 95 },
        size: { type: Number, default: 120 },
        thickness: { type: Number, default: 10 },
        fgColor: { type: String, default: "#FFD93C" },
        bgColor: { type: String, default: "#E5E5E5" },
    },
    computed: {
        viewBoxW() {
            return this.size;
        },
        viewBoxH() {
            return this.size / 2;
        },
        r() {
            return (this.viewBoxW - this.thickness) / 2;
        },
        cx() {
            return this.viewBoxW / 2;
        },
        cy() {
            return this.viewBoxH;
        },
        // 那条半圆的 path，从左底到右底
        bgPath() {
            return `M ${this.cx - this.r},${this.cy}` + ` A ${this.r},${this.r} 0 0,1 ${this.cx + this.r},${this.cy}`;
        },
        // 半圆总长 L = π·r
        arcLen() {
            return Math.PI * this.r;
        },
        // dashArray = "前景长度 剩余长度"
        dashArray() {
            const v = Math.min(100, Math.max(0, this.value));
            const fgLen = this.arcLen * (v / 100);
            // 剩余的灰色段长度：arcLen - fgLen
            return `${fgLen.toFixed(2)} ${this.arcLen.toFixed(2)}`;
        },
        textYValue() {
            return this.cy - this.r * 0.25;
        },
        textYLabel() {
            return this.cy - this.r * 0.05;
        },
    },
};
</script>

<style scoped lang="scss">
.gauge-card {
    position: relative;
    width: 100%;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    padding: 16px;
    box-sizing: border-box;
    cursor: pointer;

    &::before {
        content: attr(data-value);
        position: absolute;
        right: 16px;
        top: 16px;
        font-size: 28px;
        font-weight: bold;
        color: #333;
        visibility: hidden; /* 隐藏，因为我们已经在svg内有显示 */
    }

    .gauge-card-content {
        display: flex;
        align-items: center;
        height: 100%;

        .gauge-title {
            flex: 1;
            padding-right: 20px;
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
            overflow: hidden;
            flex-wrap: wrap;
        }

        .gauge-right-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;

            .gauge-svg {
                flex-shrink: 0;

                .value-text {
                    font-size: 28px;
                    font-weight: bold;
                }
            }

            .standard {
                font-size: 12px;
                color: #909399;
                margin-top: 10px;
                text-align: center;
            }
        }
    }
}
</style>
