<template>
    <transition name="slide">
        <div class="group_qrcode_card_page second_level_page">
            <mrHeader>
                <template #title>
                    {{lang.group_qrcode_card_text}}
                </template>
            </mrHeader>
            <div class="container">
                <div class="auto_add_switch">
                    <span>{{lang.auto_add_sharer_text}}</span>
                    <div class="needsclick">
                        <van-switch v-model="isAutoAddSharer"  active-color="#00c59d" inactive-color="#D9D9D9" @click="handleSwitchClick" />
                    </div>
                </div>
                <div class="qrcode_card">
                    <div class="sub_container">
                        <div class="qr_head">
                            <mr-avatar :url="getLocalAvatar(conversation)" :origin_url="conversation.avatar" :radius="2.5" :showOnlineState="false" :key="conversation.avatar"></mr-avatar>
                            <p class="base_info">
                                {{conversation.subject}}
                            </p>
                        </div>
                        <div id="group_qr_code"></div>
                        <p class="notice">{{lang.group_qrcode_card_notice_text}} ( {{expireDate}} ) </p>
                    </div>
                </div>
                <div class="button_container">
                    <van-button type="primary" v-show="isShowButton" :loading="isGeneratingShareImage" :disabled="isGeneratingShareImage" @click="downloadQRImage">{{lang.download_to_local}}</van-button>
                    <van-button type="primary" :loading="isGeneratingShareImage" :disabled="isGeneratingShareImage" @click="shareQRImage">{{lang.share_to_conversation}}</van-button>
                </div>
            </div>
        </div>
    </transition>
</template>
<script>
import base from '../lib/base'
import {parseImageListToLocal,getLocalAvatar} from '../lib/common_base'
import Tool from '@/common/tool.js'
import moment from 'moment';
import { Switch, Button, Toast } from 'vant';
import sendMessage from '../lib/send_message'

export default {
    mixins: [base, sendMessage],
    name: 'GroupQRCodeCard',
    components: {
        VanSwitch: Switch,
        VanButton: Button
    },
    data(){
        return {
            getLocalAvatar,
            isAutoAddSharer:true, // 是否自动加分享者,
            qrcode:{},
            cid:0,
            conversation:null,
            expireDate:'',
            transmitActive: false, // 标记transmit窗口是否活跃
            qrShareImageUrl: null, // 存储预生成的二维码图片URL
            isGeneratingShareImage: false,
            isShowButton: true
        }
    },
    created(){
        this.initGroupData2()
    },
    computed:{
    },
    activated() {
        this.initGroupData2();
        this.qrShareImageUrl = null; // 每次激活时重置

        this.$nextTick(() => {
            this.initQRCode()   // 确保二维码DOM已生成
        });
    },
    mounted() {
        this.$nextTick(() => {
        })
    },
    methods:{
        waitForSocketAndSend(targetCid, imageUrl, maxAttempts, intervalMs) {
            let attempts = 0;
            const check = () => {
                // 确保会话对象和cid本身都有效
                if (targetCid && this.conversationList[targetCid] && this.conversationList[targetCid].socket) {
                    this.sendQRImage(targetCid, imageUrl)
                        .finally(() => {
                            this.$root.eventBus.$emit('destroyTransmit');
                        });
                } else {
                    attempts++;
                    // 也需要检查会话对象是否存在，且 targetCid 有效
                    if (attempts < maxAttempts && targetCid && this.conversationList[targetCid]) {
                        setTimeout(check, intervalMs);
                    } else {
                        let errorMsg = this.lang.init_conversation_err;
                        if (!targetCid || !this.conversationList[targetCid]) {
                            errorMsg += ("(1)"); // 会话数据不存在
                        }
                        Toast(errorMsg);
                        this.$root.eventBus.$emit('destroyTransmit');
                    }
                }
            };
            // 在开始检查前，确保 targetCid 和会话对象是有效的
            if (targetCid && this.conversationList[targetCid]) {
                check();
            } else {
                let errorMsg = this.lang.init_conversation_err;
                if (!targetCid) {
                    errorMsg += ("(2)");   // 目标ID无效
                } else if (!this.conversationList[targetCid]) {
                    errorMsg += ("(1)"); // 会话数据不存在
                }
                Toast(errorMsg);
                this.$root.eventBus.$emit('destroyTransmit');
            }
        },
        initQRCode() {
            return new Promise((resolve, reject) => {
                const subject = encodeURIComponent(this.conversation.subject);
                const qrElement = document.getElementById("group_qr_code");

                if (!qrElement) {
                    return reject(new Error("QR code element not found"));
                }
                qrElement.innerHTML = ''; // 清除旧的二维码

                let futureDate = moment().add(7, 'days');
                this.expireDate = futureDate.format('YYYY-MM-DD');

                window.main_screen.conversation_list[this.cid].generateInviteCode({
                    autoMakeFriend: this.isAutoAddSharer,
                }, (res) => {
                    if (res.error_code === 0) {
                        let inviteCode = res.data.inviteCode;
                        const surl = this.systemConfig.server_type.protocol + this.systemConfig.server_type.host + this.systemConfig.server_type.port + '/';
                        const text = Tool.transferLocationToCe(surl + `activity/activity.html#/qr_install_app?act=add_group&inviteCode=${inviteCode}&add_sharer=${this.isAutoAddSharer ? '1' : '0'}&subject=${subject}&uname=${this.user.username}`);

                        try {
                            this.qrcode = new window.QRCode(qrElement, {
                                text,
                                width: 200,
                                height: 200
                            });
                            // QRCode 库通常同步绘制。$nextTick 确保DOM更新后再resolve。
                            this.$nextTick(() => {
                                resolve();
                            });
                        } catch (e) {
                            reject(e);
                        }
                    } else {
                        // Toast(this.lang.generate_invite_code_failed || `邀请码生成失败: ${res.error_msg || res.error_code}`);
                        reject(new Error(`Failed to generate invite code: ${res.error_msg || res.error_code}`));
                    }
                });
            });
        },
        initGroupData2(){
            this.cid = this.$route.params.cid;
            this.conversation = this.$store.state.conversationList[this.cid]||{};
        },
        handleSwitchClick() {
            this.isAutoAddSharer = !this.isAutoAddSharer
            this.initQRCode()
        },
        // 用canvas和qrcode生成群二维码卡片
        async generateQRImage() {
            // 样式参数
            const width = 400;
            const height = 650;
            const padding = 24;
            const avatarSize = 56;
            const qrSize = 240;
            const cardRadius = 18;
            const cardShadow = 8;
            const nicknameFont = '22px Arial';
            const noticeFont = '16px Arial';
            const expireFont = '14px Arial';
            const cardBg = '#fff';
            const cardShadowColor = 'rgba(0,0,0,0.08)';
            const nicknameColor = '#222';
            const noticeColor = '#888';
            const expireColor = '#888';

            // 1. 创建canvas
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            // 清空
            ctx.clearRect(0, 0, width, height);
            // 阴影
            ctx.save();
            ctx.shadowColor = cardShadowColor;
            ctx.shadowBlur = cardShadow;
            ctx.shadowOffsetY = 2;
            ctx.fillStyle = cardBg;
            this._drawRoundRect(ctx, padding, padding, width-2*padding, height-2*padding, cardRadius);
            ctx.fill();
            ctx.restore();
            // 2. 头像
            const avatarUrl = this.getLocalAvatar(this.conversation);
            await this._drawAvatar(ctx, avatarUrl, width/2-avatarSize/2, padding+28, avatarSize, avatarSize);
            
            // 3. 昵称（支持自动换行）
            const nickname = this.conversation.subject || '';
            const maxWidth = width - (padding * 2) - 40; // 文本最大宽度
            ctx.font = nicknameFont;
            ctx.fillStyle = nicknameColor;
            ctx.textAlign = 'center';
            const lineHeight = 30; // 行高
            const textY = padding + 28 + avatarSize + 32;
            
            // 获取文本换行后的行数
            const textLines = this._wrapText(ctx, nickname, width / 2, textY, maxWidth, lineHeight);
            
            // 计算额外需要的高度（行数-1）* 行高
            const extraHeight = (textLines - 1) * lineHeight;
            
            // 4. 二维码（根据文本行数调整位置）
            const qrDomElement = document.getElementById("group_qr_code");
            let qrImageSource = null;
            if (qrDomElement) {
                // QRCode.js 通常生成一个 canvas，但也可能配置为 img
                qrImageSource = qrDomElement.querySelector('canvas') || qrDomElement.querySelector('img');
            } 
            // 将获取到的二维码图像绘制到卡片Canvas上（调整Y坐标）
            const qrY = padding + 28 + avatarSize + 60 + extraHeight;
            ctx.drawImage(qrImageSource, width/2 - qrSize/2, qrY, qrSize, qrSize);
            
            // 5. 说明文字（调整Y坐标）
            const noticeY = qrY + qrSize + 36;
            ctx.font = noticeFont;
            ctx.fillStyle = noticeColor;
            ctx.textAlign = 'center';
            ctx.fillText(this.lang.group_qrcode_card_notice_text, width/2, noticeY);
            
            // 6. 过期时间（调整Y坐标）
            ctx.font = expireFont;
            ctx.fillStyle = expireColor;
            ctx.fillText(`(${this.expireDate})`, width/2, noticeY + 26);
            
            // 导出base64
            return canvas.toDataURL('image/png');
        },
        
        // 文本自动换行函数
        _wrapText(ctx, text, x, y, maxWidth, lineHeight) {
            // 按字符拆分文本
            const chars = text.split('');
            let line = '';
            let lines = [];
            
            // 逐字符测量宽度并分行
            for (let i = 0; i < chars.length; i++) {
                const testLine = line + chars[i];
                const metrics = ctx.measureText(testLine);
                const testWidth = metrics.width;
                
                if (testWidth > maxWidth && i > 0) {
                    lines.push(line);
                    line = chars[i];
                } else {
                    line = testLine;
                }
            }
            lines.push(line); // 添加最后一行
            
            // 绘制所有行
            lines.forEach((line, index) => {
                ctx.fillText(line, x, y + (index * lineHeight));
            });
            
            return lines.length; // 返回行数
        },
        // 绘制圆角矩形
        _drawRoundRect(ctx, x, y, w, h, r) {
            ctx.beginPath();
            ctx.moveTo(x+r, y);
            ctx.lineTo(x+w-r, y);
            ctx.arcTo(x+w, y, x+w, y+r, r);
            ctx.lineTo(x+w, y+h-r);
            ctx.arcTo(x+w, y+h, x+w-r, y+h, r);
            ctx.lineTo(x+r, y+h);
            ctx.arcTo(x, y+h, x, y+h-r, r);
            ctx.lineTo(x, y+r);
            ctx.arcTo(x, y, x+r, y, r);
            ctx.closePath();
        },
        // 绘制圆角头像
        async _drawAvatar(ctx, url, x, y, w, h) {
            return new Promise(resolve => {
                const img = new window.Image();
                img.crossOrigin = 'anonymous';
                img.onload = function() {
                    ctx.save();
                    ctx.beginPath();
                    ctx.arc(x+w/2, y+h/2, w/2, 0, Math.PI*2, false);
                    ctx.closePath();
                    ctx.clip();
                    ctx.drawImage(img, x, y, w, h);
                    ctx.restore();
                    resolve();
                };
                img.onerror = function() {
                    resolve();
                };
                img.src = url;
            });
        },
        async getQrImageUrl() {
            this.isGeneratingShareImage = true;
            try {
                await this.initQRCode();
                const url = await this.generateQRImage();
                this.qrShareImageUrl = url;
                this.$root.qrCodeImageUrl = url;
                return url;
            } catch (error) {
                throw error;
            } finally {
                this.isGeneratingShareImage = false;
            }
        },
        async downloadQRImage() {
            try {
                const base64Url = await this.getQrImageUrl();

                if (Tool.checkAppClient('App')) {
                    const fileName = `${this.conversation.subject}_${this.lang.group_qrcode_card_text}.png`;
                    try {
                        Tool.createCWorkstationCommunicationMng({
                            name: "gallerySave",
                            emitName: "NotifyGallerySave",
                            params: {
                                url: base64Url,
                                filename: fileName
                            },
                            timeout: null,
                        }).then((galleryRes) => {
                            if (galleryRes.error_code == "0") {
                                Toast(this.lang.has_download_tip);
                            } else {
                                Toast(galleryRes.error_message || this.lang.save_support_fail_tip);
                            }
                        }).catch(error => {
                            console.error("App gallerySave error:", error);
                            Toast(this.lang.save_support_fail_tip);
                        });
                    } catch (error) {
                        console.error("Error calling App gallerySave:", error);
                        Toast(this.lang.save_support_fail_tip );
                    }
                } else {    // 非App环境，使用现有Web下载逻辑
                    const a = document.createElement('a');
                    a.setAttribute('download', `${this.conversation.subject}_${this.lang.group_qrcode_card_text}.png`);
                    a.setAttribute('href', base64Url);
                    a.click();
                }
            } catch (error) {
                Toast(this.lang.operate_err);
            }
        },
        async shareQRImage() {
            try {
                const url = await this.getQrImageUrl();

                this.$root.eventBus.$emit('setTransmitCallback', { callback: 'qrCodeTransmitCallback' });
                this.transmitActive = true;
                this.$root.eventBus.$emit('initTransmit');

                this.$root.eventBus.$off('qrCodeTransmitCallback').$on('qrCodeTransmitCallback', (target) => {
                    this.transmitActive = false;

                    const imageUrlForSending = this.$root.qrCodeImageUrl; // Use the confirmed available URL
                    Object.entries(this.conversationList).map(item => {
                        if (target.uid && item[1].fid === target.uid) {
                            target.cid = item[0];
                        }
                    });

                    // 定义打开会话并发送的通用逻辑
                    const openAndSend = (cidToOpen, openMethod) => {
                        openMethod(cidToOpen, (success, returnedCid) => {
                            const finalCid = cidToOpen || returnedCid;
                            if (success && finalCid && this.conversationList[finalCid]) {
                                this.waitForSocketAndSend(finalCid, imageUrlForSending, 10, 200); // 尝试10次，间隔200ms
                            } else {
                                Toast(this.lang.init_conversation_err);
                                this.$root.eventBus.$emit('destroyTransmit');
                            }
                        });
                    };

                    if (target.cid && this.conversationList[target.cid]) {
                        if (this.conversationList[target.cid].socket) {
                            // Socket 已就绪，直接发送
                            this.sendQRImage(target.cid, imageUrlForSending)
                                .finally(() => {
                                    this.$root.eventBus.$emit('destroyTransmit');
                                });
                        } else {
                            // Socket 未就绪，尝试打开并等待
                            openAndSend(target.cid, (cid, cb) => this.openConversation(cid, 13, cb));
                        }
                    } else if (target.from === 'friend' && target.uid) {
                        this.openConversationByUserId(target.uid, (success, conversationId) => {
                            if (success && conversationId && this.conversationList[conversationId]) {
                                this.waitForSocketAndSend(conversationId, imageUrlForSending, 10, 200);
                            } else {
                                let errorMsg = this.lang.init_conversation_err;
                                if (!conversationId) {
                                    errorMsg += (this.lang.cid_not_returned_from_uid || " (用户ID打开未返回会话ID)");
                                } else if (!this.conversationList[conversationId]) {
                                    errorMsg += (this.lang.conversation_not_found_from_uid || " (用户ID打开后未找到会话)");
                                }
                                Toast(errorMsg);
                                this.$root.eventBus.$emit('destroyTransmit');
                            }
                        });
                    } else if (target.cid) {
                        // 如果 target.cid 存在，但 this.conversationList[target.cid] 不存在 (被上一个if排除)
                        // 这意味着会话信息可能需要加载
                        openAndSend(target.cid, (cid, cb) => this.openConversation(cid, 13, cb));
                    } else {
                        Toast(this.lang.invalid_conversation_target || '无效的会话目标');
                        this.$root.eventBus.$emit('destroyTransmit');
                    }
                });
            } catch (error) {
                Toast(this.lang.generate_image_failed || '分享图片准备失败，请重试');
                if (this.transmitActive) {
                    this.$root.eventBus.$emit('destroyTransmit');
                    this.transmitActive = false;
                }
            }
        },
        sendQRImage(targetCid, imageUrl) {
            const profileTag = '[QRImageProfile]';
            const t0 = performance.now();
            let t1, t2, t3, t4, t5, t6;
            // 检查目标会话和socket是否存在
            const targetConversation = this.$store.state.conversationList[targetCid];
            if (!targetConversation) {
                return Promise.reject(new Error('目标会话不存在'));
            }
            if (!targetConversation.socket) {
                return Promise.reject(new Error('目标会话socket不存在'));
            }

            return new Promise((resolve, reject) => {
                // 通过Image对象加载URL
                const img = new Image();
                t1 = performance.now();
                console.log(profileTag, 'sendQRImage Image object created', (t1 - t0).toFixed(2) + 'ms');

                img.onload = () => {
                    t2 = performance.now();
                    console.log(profileTag, 'sendQRImage image loaded', (t2 - t1).toFixed(2) + 'ms');
                    const canvas = document.createElement('canvas');
                    t3 = performance.now();
                    console.log(profileTag, 'sendQRImage before toBlob', (t3 - t2).toFixed(2) + 'ms');
                    canvas.width = img.width;
                    canvas.height = img.height;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0);

                    // 将Canvas转成Blob对象
                    canvas.toBlob((blob) => {
                        t4 = performance.now();
                        console.log(profileTag, 'sendQRImage after toBlob', (t4 - t3).toFixed(2) + 'ms');
                        const fileName = `${this.conversation.subject}_${this.lang.group_qrcode_card_text}.png`;
                        const file = new File([blob], fileName, { type: 'image/png' });
                        const originalCid = this.cid;
                        this.cid = targetCid;

                        // 调用发送文件消息的方法
                        t5 = performance.now();
                        console.log(profileTag, 'sendQRImage before prepareFileMessage', (t5 - t4).toFixed(2) + 'ms');
                        this.prepareFileMessage(file, 0).then(msg => {

                            if (msg) {
                                t6 = performance.now();
                                console.log(profileTag, 'sendQRImage after prepareFileMessage', (t6 - t5).toFixed(2) + 'ms');
                                this.setSendingFile(msg, true);

                                // 再次检查socket
                                if (!this.$store.state.conversationList[targetCid] || !this.$store.state.conversationList[targetCid].socket) {
                                    this.cid = originalCid;
                                    reject(new Error('目标会话socket不存在'));
                                    return;
                                }
                                const uploadStart = performance.now();
                                this.uploadAttachment(msg, file, 1);
                                const uploadEnd = performance.now();
                                console.log(profileTag, 'sendQRImage uploadAttachment called (同步)', (uploadEnd - t6).toFixed(2) + 'ms', '总耗时', (uploadEnd - t0).toFixed(2) + 'ms');
                                Toast(this.lang.share_to_wechat_succeed);
                            }
                            this.cid = originalCid;
                            resolve(true);
                        }).catch(err => {
                            this.cid = originalCid;
                            reject(err);
                        });
                    }, 'image/png');
                };

                img.onerror = (err) => {
                    Toast('图片加载失败，请重试');
                    reject(err);
                };

                img.src = imageUrl;
            });
        }
    },
    beforeRouteLeave(to, from, next) {
        if (this.isGeneratingShareImage) {
            // 如果正在生成图片，则阻止离开
            next(false);
        } else if (this.transmitActive) {
            this.$root.eventBus.$emit('destroyTransmit');
            this.transmitActive = false;
            next(false);
        } else {
            next();
        }
    }
}

</script>
<style lang="scss">
.group_qrcode_card_page{
    .container {
        height: calc(100% - 2.95rem);
        // display: flex;
        align-items: center;
        .auto_add_switch{
            width: 80%;
            margin: 2rem auto 1rem;
            display: flex;
            justify-content: center;
            font-size:.8rem;
            &>span{
                margin-right: .5rem;
            }
        }
        .qrcode_card {
            height: 70%;
            width: 15rem;
            background: #fff;
            margin: auto;
            border-radius: 8px;
            box-shadow: 0 0 0.15rem #ccc;
            .sub_container {
                padding: .7rem;
                height: 100%;
                position:relative;
                .qr_head {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    .base_info {
                        margin-left: 0;
                        margin-top: .5rem;
                        text-align: center;
                        display: inline-block;
                        font-size: .8rem;
                        vertical-align: middle;
                        .hospital {
                            font-size: .6rem;
                            opacity: .6;
                        }
                    }
                }

                .notice {
                    font-size: .6rem;
                    text-align: center;
                    margin-top: 2rem;
                    opacity: .6;
                }
            }
        }
        .button_container {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            width: 15rem;
            margin: 20px auto;
            .van-button {
                flex: 1;
                margin: 0 5px;
            }
        }
    }

}
#group_qr_code {
    display: flex;
    justify-content: center;
    padding: 1rem 0 0 0;
}
</style>
