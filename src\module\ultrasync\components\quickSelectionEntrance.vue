<template>
    <van-popup v-model="isShow" closeable position="bottom" :style="{ height: '100%' }" get-container="body">
        <div class="quick_select_page third_level_page">
            <div class="quick_select_container">
                <div class="search_container">
                    <input
                        type="text"
                        :placeholder="lang.quick_select_search_placeholder"
                        @input="search"
                        v-model="searchText"
                    />
                </div>
                <div :class="[checkOnlyList ? 'quick_only_box' : 'quick_multiple_box']">
                    <div v-show="choosenChatList.length > 0" class="quick_recent_item">
                        <p class="title">{{ lang.recent_chat_text }}</p>
                        <div class="quick_select_list">
                            <div
                                v-for="(chatItem, index) of choosenChatList"
                                @click="handleSubmit(chatItem, 1)"
                                class="quick_select_item clearfix"
                                :key="index"
                            >
                                <mr-avatar
                                    :url="getLocalAvatar(chatItem)"
                                    :origin_url="chatItem.avatar"
                                    :showOnlineState="false"
                                    :key="chatItem.avatar"
                                ></mr-avatar>
                                <p class="fl quick_select_subject">{{ chatItem.subject }}</p>
                            </div>
                        </div>
                    </div>
                    <div v-show="choosenFriendList.length > 0" class="quick_recent_item">
                        <p class="title">{{ lang.contact_text }}</p>
                        <div class="quick_select_list">
                            <div
                                v-for="(chatItem, index) of choosenFriendList"
                                @click="handleSubmit(chatItem, 2)"
                                class="quick_select_item clearfix"
                                :key="index"
                            >
                                <mr-avatar
                                    :url="getLocalAvatar(chatItem)"
                                    :origin_url="chatItem.avatar"
                                    :showOnlineState="false"
                                    :key="chatItem.avatar"
                                ></mr-avatar>
                                <p class="fl quick_select_subject">{{ chatItem.nickname }}</p>
                            </div>
                        </div>
                    </div>
                    <div v-show="choosenGroupList.length > 0" class="quick_recent_item">
                        <p class="title">{{ lang.group }}</p>
                        <div class="quick_select_list">
                            <div
                                v-for="(chatItem, index) of choosenGroupList"
                                @click="handleSubmit(chatItem, 3)"
                                class="quick_select_item clearfix"
                                :key="index"
                            >
                                <mr-avatar
                                    :url="getLocalAvatar(chatItem)"
                                    :origin_url="chatItem.avatar"
                                    :showOnlineState="false"
                                    :key="chatItem.avatar"
                                ></mr-avatar>
                                <p class="fl quick_select_subject">{{ chatItem.subject }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <p
                    v-if="choosenChatList.length == 0 && choosenFriendList.length == 0 && choosenGroupList.length == 0"
                    class="no_search_data"
                >
                    {{ lang.no_search_data }}
                </p>
            </div>
        </div>
        <CommonDialog v-model="liveActionDialog" :showConfirmButton="false" :title="lang.quick_lauch_live_title">
            <div class="dialog_body">
                <div class="dialog-container">
                    <div class="container-title longwrap">{{ currentTarget.subject }}</div>
                    <div class="submit-button">
                        <van-button type="primary" class="submit-button" block @click="confirmSubmitLiveAction">{{
                            lang.start_live_broadcast
                        }}</van-button>
                    </div>
                </div>
            </div>
        </CommonDialog>
        <CommonDialog
            v-model="sendResourceActionDialog"
            :showConfirmButton="false"
            :title="lang.send_files"
            @cancel="closeSendResourceActionDialog"
        >
            <div class="dialog_body">
                <div class="dialog-container">
                    <div class="container-title longwrap">{{ currentTarget.subject }}</div>
                    <p v-if="!sendResourceSubmitting">
                        {{ lang.in_total_text }}{{ currentShareFileList.length }}{{ lang.file_to_be_sent }}
                    </p>
                    <p v-else>
                        {{ lang.in_total_text }}{{ currentShareFileList.length }}{{ lang.unit_file }},{{
                            lang.task_manager.media_transfer.status_list["3"]
                        }}: {{ uploadSuccessCount }}，{{ lang.task_manager.media_transfer.status_list["4"] }}:
                        {{ upLoadFailCount }}
                    </p>
                    <div class="submit-button">
                        <van-button
                            type="primary"
                            class="submit-button"
                            block
                            @click="confirmSubmitSendResourceAction"
                            :loading="sendResourceSubmitting"
                            >{{ lang.send_files }}</van-button
                        >
                    </div>
                </div>
            </div>
        </CommonDialog>
    </van-popup>
</template>
<script>
import base from "../lib/base";
import send_message from "../lib/send_message";
import { cloneDeep } from "lodash";
import { Popup, Checkbox, Button, Toast } from "vant";
import Tool from "@/common/tool";
import { getLocalAvatar } from "../lib/common_base";
import CommonDialog from "../MRComponents/commonDialog.vue";
import service from '../service/service.js'
import { getOSSToken } from '@/common/oss/index.js'
export default {
    name: "quickLaunchLiveComponent",
    mixins: [base, send_message],
    model: {
        prop: "value",
        event: "change",
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        VanPopup: Popup,
        VanButton: Button,
        CommonDialog,
    },
    data() {
        return {
            getLocalAvatar,
            isShow: false,
            choosenChatList: [],
            choosenFriendList: [],
            choosenGroupList: [],
            plainChatList: [],
            searchText: "",
            callback: "",
            isInit: false,
            liveActionDialog: false,
            sendResourceActionDialog: false,
            currentTarget: {},
            currentAction: "",
            currentShareFile: "",
            currentShareFileList: [],
            sendResourceSubmitting: false,
            currentShareExams:[],
        };
    },
    computed: {
        chatList() {
            return this.filterEnableList(this.$store.state.chatList.list);
        },
        friendList() {
            return this.filterEnableList(this.$store.state.friendList.list);
        },
        groupList() {
            return this.filterEnableList(this.$store.state.groupList);
        },
        uploadSuccessCount() {
            let count = 0;
            count = Object.values(this.sendResourceByTEAirTask).filter((item) => item.isUploaded).length;
            return count;
        },
        upLoadFailCount() {
            return Object.values(this.sendResourceByTEAirTask).filter((item) => item.isError).length;
        },
        checkOnlyList() {
            let chatNum = this.choosenChatList.length > 0 ? 1 : 0;
            let friendNum = this.choosenFriendList.length > 0 ? 1 : 0;
            let groupNum = this.choosenGroupList.length > 0 ? 1 : 0;
            if (chatNum + friendNum + groupNum === 1) {
                return true;
            } else {
                return false;
            }
        },
    },
    watch: {
        isShow: {
            handler(val) {
                if (val) {
                    this.choosenChatList = [];
                    this.plainChatList = [];
                    this.choosenChatList = cloneDeep(this.chatList);
                }
            },
            immediate: true,
        },
        chatList: {
            handler(val) {
                if (!this.searchText) {
                    this.choosenChatList = cloneDeep(val);
                }
            },
        },
    },
    mounted() {
        window.CWorkstationCommunicationMng.GetTEAirStartAction();
        this.$root.eventBus.$off("NotifyTEAirStartAction").$on("NotifyTEAirStartAction", this.NotifyTEAirStartAction);
    },
    beforeDestroy() {},
    methods: {
        search() {
            let keyword = this.searchText;
            if (keyword == "") {
                this.plainChatList = [];
                this.choosenChatList = cloneDeep(this.chatList);
                this.choosenFriendList = [];
                this.choosenGroupList = [];
            } else {
                this.choosenChatList = [];
                this.choosenFriendList = [];
                this.choosenGroupList = [];
                for (let friend of this.friendList) {
                    let friendItem = Object.assign({}, friend);
                    if (friendItem.alias) {
                        friendItem.nickname = friendItem.alias;
                    }
                    if (friendItem.nickname.indexOf(keyword) >= 0) {
                        this.choosenFriendList.push(friendItem);
                    }
                }
                for (let chatItem of this.groupList) {
                    if (chatItem.subject.indexOf(keyword) >= 0) {
                        this.choosenGroupList.push(chatItem);
                    }
                }
            }
        },
        confirmSubmitLiveAction() {
            if (this.currentTarget.cid) {
                this.openConversationToStartLive(this.currentTarget.cid, 1);
            } else if (this.currentTarget.from === "friend") {
                this.openConversationToStartLive(this.currentTarget.uid, 3);
            }
            this.liveActionDialog = false;
            this.isShow = false;
        },
        confirmSubmitSendResourceAction(){
            if (this.currentAction === "share") {
                this.sendResourceActionStart();
            } else if (this.currentAction === "sendExam") {
                this.sendExamActionStart();
            }
        },
        sendResourceActionStart() {
            if (this.currentTarget.cid) {
                this.sendResourceSubmitting = true;
                this.openConversation(this.currentTarget.cid, 13, async (is_suc, cid) => {
                    if (is_suc) {
                        try {
                            setTimeout(async () => {
                                const res = await this.sendResourceByTEAir(
                                    this.currentShareFileList,
                                    this.currentTarget.cid
                                );
                                this.sendResourceSubmitting = false;
                                this.sendResourceActionDialog = false;
                                if (res.includes(false)) {
                                    window.CWorkstationCommunicationMng.CallTEAirActionResult({
                                        action: "share",
                                        result: 0,
                                    });
                                    Toast(this.lang.send_failed);
                                } else {
                                    window.CWorkstationCommunicationMng.CallTEAirActionResult({
                                        action: "share",
                                        result: 1,
                                    });
                                    Toast(this.lang.send_success);
                                }
                            }, 0);
                        } catch (error) {
                            console.error(error);
                            this.sendResourceSubmitting = false;
                        }
                    } else {
                        this.sendResourceSubmitting = false;
                    }
                });
            } else if (this.currentTarget.from === "friend") {
                this.sendResourceSubmitting = true;
                this.openConversationByUserId(this.currentTarget.uid, async (is_suc, cid) => {
                    if (is_suc) {
                        try {
                            setTimeout(async () => {
                                await this.sendResourceByTEAir(this.currentShareFileList, this.currentTarget.cid);
                                this.sendResourceSubmitting = false;
                                this.sendResourceActionDialog = false;
                                window.CWorkstationCommunicationMng.CallTEAirActionResult({
                                    action: "share",
                                    result: 1,
                                });
                                Toast(this.lang.send_success);
                            }, 0);
                        } catch (error) {
                            console.error(error);
                            this.sendResourceSubmitting = false;
                        }
                    } else {
                        this.sendResourceSubmitting = false;
                    }
                });
            }
        },
        sendExamActionStart(){
            this.currentShareExams.forEach(async (createData) =>{
                const examRes = await service.createExam({
                    examInfo:createData.examInfo,
                    patientInfo:createData.patientInfo,
                    studyInfo:createData.studyInfo,
                })
                // const examRes = {
                //     data:{
                //         error_code:0,
                //         data:{
                //             exam_path:'PATIENT_DATA/20241105/da27991f-519c-4193-aeca-9eaf6c96db81/e9381430-ff21-4b43-aa08-5b6347d8e53c',
                //             exam_id:'123'
                //         }
                //     }
                // }
                if (examRes.data.error_code !== 0) {
                    window.CWorkstationCommunicationMng.CallTEAirActionResult({
                        action: "sendExam",
                        result: 0,
                        msg:'create_exam_error',
                        data:this.currentShareExams,
                    });
                }else{
                    this.sendResourceSubmitting = true;
                    if (this.currentTarget.cid) {
                        this.openConversation(this.currentTarget.cid, 13, (is_suc) => {
                            if (is_suc) {
                                this.notifyTEAirOSSData(this.currentTarget.cid,examRes,createData);
                            }else{
                                window.CWorkstationCommunicationMng.CallTEAirActionResult({
                                    action: "sendExam",
                                    result: 0,
                                    msg:'open_conversation_error',
                                    data:this.currentShareExams,
                                });
                            }
                        })
                    } else if (this.currentTarget.from === "friend") {
                        this.openConversationByUserId(this.currentTarget.uid, (is_suc, cid) => {
                            if (is_suc) {
                                this.NotifyTEAirOSSData(cid,examRes,createData);
                            }else{
                                window.CWorkstationCommunicationMng.CallTEAirActionResult({
                                    action: "sendExam",
                                    result: 0,
                                    msg:'open_conversation_error',
                                    data:this.currentShareExams,
                                });
                            }
                        })
                    }
                }
            })
        },
        async notifyTEAirOSSData(cid,examRes,createData){
            try{
                const examPath = examRes.data.data.examPath.replace(/\\/g,"/");
                let fileList = []
                const bucket = this.systemConfig.serverInfo.oss_consultation_file_storage_server.bucket;
                let folder = '';
                createData.examFilesInfo.forEach((examFiles) => {
                    console.log('------examFiles',examFiles);
                    console.log('------createData',createData);
                    folder = examFiles.fileInfo.TAG_ExamFiles.split('.')[0]
                    const ossPath = []
                    examFiles.uploadFiles.forEach((file,index)=>{
                        const filePath = `${examPath}/${folder}/${file}`;
                        ossPath.push(filePath);
                    })
                    fileList.push(`${examPath}/${folder}/*`);
                    examFiles.ossPath = ossPath;
                });
                const bucketName = bucket;
                const iworksPath = `${examPath}/iworks.XML`
                const iworksExecutionPath = `${examPath}/iworksExecution.XML`
                fileList.push(iworksPath)
                fileList.push(iworksExecutionPath)
                const ossData = await getOSSToken(bucketName,fileList);
                ossData.cid = cid;
                ossData.examFilesInfo = createData.examFilesInfo;
                ossData.examId = createData.examInfo.TAG_SeriesID;
                ossData.iworksPath = iworksPath;
                ossData.iworksExecutionPath = iworksExecutionPath; 
                window.CWorkstationCommunicationMng.CallTEAirActionResult({
                    action: "sendExam",
                    result: 1,
                    data:ossData
                });
            }catch(e){
                window.CWorkstationCommunicationMng.CallTEAirActionResult({
                    action: "sendExam",
                    result: 0,
                    msg:'get_oss_token_error',
                    error:e,
                    data:this.createData,
                });
            }
        },
        handleSubmit(item, type) {
            let that = this;
            let target = {};
            if (type == 1) {
                target.subject = item.subject;
                target.cid = item.cid;
                if (item.is_single_chat) {
                    target.from = "friend";
                    target.uid = item.fid;
                } else {
                    target.from = "group";
                }
            } else if (type == 2) {
                target.subject = item.nickname;
                target.id = item.id;
                target.from = "friend";
                target.uid = item.id;
            } else if (type == 3) {
                target.subject = item.subject;
                target.cid = item.id;
                target.from = "group";
            }

            // let message=this.lang.whether_to_launch_live_broadcast+target.subject;
            let message = this.lang.group_setting_whether_live_record;
            if (!item.cid && item.nickname) {
                //从联系人点击的单聊，从chatlist中寻找cid
                for (let chatItem of this.chatList) {
                    if (item.id == chatItem.fid) {
                        target.cid = chatItem.cid;
                        break;
                    }
                }
            } else if (!item.cid && item.is_single_chat == 0) {
                target.cid = item.id;
            } else {
                target.cid = item.cid;
            }

            this.currentTarget = target;
            if (this.currentAction === "live") {
                this.liveActionDialog = true;
            } else if (this.currentAction === "share") {
                this.sendResourceActionDialog = true;
            } else if (this.currentAction === "sendExam") {
                this.sendResourceActionDialog = true;
            }
        },
        openConversationToStartLive(id, start_type) {
            this.openConversation(id, start_type, (is_success, data) => {
                if (!is_success) {
                    return;
                }
                let cid = 0;
                if (typeof data === "object") {
                    cid = data.id;
                } else {
                    cid = data;
                }

                setTimeout(async () => {
                    this.$root.eventBus.$emit("chatWindowStartJoinRoom", { main: 1, aux: 1, isSender: 1 }, (is_suc) => {
                        if (!is_suc) {
                            this.back();
                            this.isShow = true;
                        }
                    });
                }, 1000);
            });
        },
        isGroupMember(group, groupList) {
            // debugger
            if (group.type !== 2) {
                // 不是群，直接false
                return false;
            }
            for (const item of groupList) {
                if (item.id == group.cid) {
                    return true;
                }
            }
            return false;
        },
        isFriend(person, friendList) {
            // debugger
            if (person.type !== 1) {
                // 不是单聊，直接false
                return false;
            }
            for (const item of friendList) {
                if (item.id == person.fid) {
                    return true;
                }
            }
            return false;
        },
        filterGroupset(ochatList, filter) {
            let chatList = cloneDeep(ochatList);
            let filterObj = filter || {};
            for (const item of chatList) {
                // 如果是群落，深度遍历
                if (item.type === 3 && item.list.length > 0) {
                    for (const chat of item.list) {
                        if (chat.cid in filterObj) {
                            // 去重
                            continue;
                        }
                        if (chat.type === 3 && item.list.length > 0) {
                            // 如果还是群落，则继续深度遍历
                            this.filterGroupset(chat.list, filterObj);
                        } else {
                            const isGroupMember = this.isGroupMember(chat, this.groupList);
                            const isFriend = this.isFriend(chat, this.friendList);
                            if (isGroupMember || isFriend) {
                                this.plainChatList.push(chat);
                                filterObj[chat.cid] = "";
                            }
                        }
                    }
                } else {
                    // 单聊或群聊，判断自己是否属于该群群成员
                    if (item.cid in filterObj) {
                        // 去重
                        continue;
                    }
                    const isGroupMember = this.isGroupMember(item, this.groupList);
                    const isFriend = this.isFriend(item, this.friendList);
                    if (isGroupMember || isFriend) {
                        this.plainChatList.push(item);
                        filterObj[item.cid] = "";
                    }
                }
            }
            return this.plainChatList;
        },
        async NotifyTEAirStartAction(res) {
            console.log(res, "NotifyTEAirStartAction");
            this.currentShareFileList = [];
            if (!res.error_code) {
                if (res.data.action === "live") {
                    this.handleAction("live");
                } else if (res.data.action === "share") {
                    this.handleAction("share");
                    // res.data.file_datas[0].fileData = `data:image/jpeg;base64,${res.data.file_datas[0].fileData}`
                    res.data.file_datas.forEach((file, index) => {
                        let dataUrl = file.fileData;
                        let fileName = file.fileName;
                        let fileType = file.fileType;
                        this.currentShareFileList.push(this.base64toFile(dataUrl, fileName, fileType));
                    });
                    console.log(this.currentShareFileList);
                } else if (res.data.action === "sendExam") {
                    // 发送检查
                    this.currentShareExams = res.data.exams;
                    this.currentShareExams.forEach((exam)=>{
                        this.currentShareFileList = this.currentShareFileList.concat(exam.examFilesInfo);
                    })
                    this.handleAction("sendExam");
                    console.log(this.currentShareFileList);
                } else if (res.data.action === "logout") {
                    this.$root.eventBus.$emit("logout");
                } else if (res.data.action === "sendExamFile") {
                    // 发送检查上传好图片后前端发送消息
                    this.sendExamFileAction(res.data);
                }
            }
        },
        handleAction(action) {
            this.currentAction = action;
            this.isShow = true;
        },
        closeSendResourceActionDialog() {
            if (this.sendResourceSubmitting) {
                return;
            }
            this.sendResourceActionDialog = false;
        },
        base64toFile(dataUrl, fileName, fileType) {
            let base64Data = dataUrl;
            var arr = base64Data.split(",");
            if (arr.length === 1) {
                //代表没解析出前缀
                base64Data = `${Tool.getBase64Prefix(fileType)},${base64Data}`;
                arr = base64Data.split(",");
            }
            this.currentShareFile = base64Data;
            var mime = arr[0].match(/:(.*?);/)[1];
            var bstr = window.atob(arr[1]);
            var n = bstr.length;
            var u8arr = new Uint8Array(n);
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n);
            }
            //return new Blob([u8arr], {type:mime});
            return new File([u8arr], fileName, { type: mime });
        },
        filterEnableList(list) {
            if (this.currentAction === "live") {
                return list.filter(
                    (item) =>
                        item.service_type === this.systemConfig.ServiceConfig.type.None &&
                        item.user_status !== this.systemConfig.userStatus.Destroy
                );
            } else {
                return list.filter((item) => item.user_status !== this.systemConfig.userStatus.Destroy);
            }
        },
        async sendExamFileAction(data){
            const examRes = await service.uploadFileInfo({
                exam_id:data.examId,
                fileInfo:data.fileInfo,
                otherInfo:data.otherInfo,
            })
            if (examRes.data.error_code !== 0) {
                window.CWorkstationCommunicationMng.CallTEAirActionResult({
                    action: "sendExamFile",
                    result: 0,
                    msg:'upload_file_info_error',
                    data:examRes,
                });
            }else{
                try{
                    const typeMap = this.systemConfig.msg_type
                    const msg_type = data.fileInfo.TAG_ImgType === 101? typeMap.Cine:typeMap.Frame;
                    let controller = this.$store.state.conversationList[data.cid].socket;
                    let msg = {
                        msg_type,
                        msg_body:{
                            exam_id:data.examId,
                            img_id:data.fileInfo.TAG_ExamFiles.split('.')[0],
                            iworksOrganID:data.fileInfo.TAG_IworksOrganID,
                            ai_image_info:data.fileInfo.ai_image_info||{},
                        },
                        device_id:data.otherInfo.device_id || 'TE Air',
                    }
                    controller.emit("send_messages", [msg], function (is_succ) {
                        if (is_succ) {
                            window.CWorkstationCommunicationMng.CallTEAirActionResult({
                                action: "sendExamFile",
                                result: 1,
                                data,
                            });
                        }else{
                            throw new Error('send_message_error')
                        }
                    });
                }catch(e){
                    window.CWorkstationCommunicationMng.CallTEAirActionResult({
                        action: "sendExamFile",
                        result: 0,
                        msg:'send_message_error',
                        data:e,
                    });
                }
            }
        },
    },
};
</script>
<style lang="scss">
.quick_select_page {
    z-index: 910;
    color: #333;
    .quick_select_container {
        background: #fff;
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .title {
            padding: 0.2rem;
            color: #000;
            .share_risk_content {
                font-size: 0.6rem;
                color: red;
            }
        }
        .search_container {
            padding: 0.3rem;
            input {
                width: 100%;
                height: 100%;
                display: block;
                font-size: 0.9rem;
                padding: 0.3rem 0.8rem;
                box-sizing: border-box;
                border: none;
                background: #eee;
                border-radius: 0.5rem;
            }
        }
        .quick_only_box {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            .quick_recent_item {
                flex: 1;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                .quick_select_list {
                    overflow: auto;
                }
            }
        }
        .quick_multiple_box {
            flex: 1;
            overflow: auto;
        }
        .quick_select_list {
            border-top: 2px solid #00c59d;
            flex: 1;
            .quick_select_item {
                padding: 0.3rem;
                border-bottom: 1px solid #ccc;
                display: flex;
                & > p {
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    width: calc(100% - 3rem);
                    margin-left: 0.4rem;
                    margin-top: 0.3rem;
                    font-size: 0.9rem;
                }
            }
        }
        .no_search_data {
            padding: 0.3rem;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            text-align: center;
        }
    }
}
.dialog_body {
    padding: 10px 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .dialog-header {
        width: 100%;
        display: flex;
        justify-content: flex-end;
    }
    .dialog-container {
        width: 100%;
        .container-title {
            text-align: center;
            font-size: 24px;
            padding: 10px 0;
        }
        .container-body {
            padding: 10px 0;
            .van-checkbox__label {
                font-size: 14px;
            }
        }
    }

    .submit-button {
        padding: 10px 0;
    }
}
</style>
