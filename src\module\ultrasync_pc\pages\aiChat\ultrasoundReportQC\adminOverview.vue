<template>
    <div class="ultrasound-report-qc-admin-overview">
        <div class="dashboard">
            <!-- 顶部筛选区 - 简化版 -->
            <div class="filter-toolbar">
                <div class="time-range-buttons">
                    <el-radio-group
                        v-model="queryForm.timeRange"
                        @change="handleTimeRangeChange"
                        class="separate-buttons">
                        <el-radio-button label="week">近1周</el-radio-button>
                        <el-radio-button label="month">近1个月</el-radio-button>
                        <el-radio-button label="quarter">近3个月</el-radio-button>
                        <el-radio-button label="halfYear">近6个月</el-radio-button>
                        <el-radio-button label="year">近1年</el-radio-button>
                    </el-radio-group>
                </div>

                <div class="date-picker-wrapper">
                    <el-date-picker
                        v-model="queryForm.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        @change="handleDateChange">
                    </el-date-picker>
                </div>

                <div class="action-buttons">
                    <el-button
                        type="primary"
                        class="query-btn"
                        @click="handleQuery">
                        查询
                    </el-button>
                </div>
            </div>
            <div
                class="dashboard-content"
                :style="{
                    '--scale-factor': scaleFactor,
                    transform: `scale(${scaleFactor})`,
                    transformOrigin: 'top left',
                    width: `calc(100% / ${scaleFactor})`,
                    height: `calc(100% / ${scaleFactor})`
                }"
                ref="dashboardContent"
            >
                <!-- 仪表盘第一行 -->
                <div class="dashboard-row" ref="firstRow">
                    <!-- 质控概况 -->
                    <div class="dashboard-section" :style="{ width: sectionWidths.qualityOverview }">
                        <div class="section-header">质控概况</div>
                        <div class="section-content">
                            <gauge-card
                                v-for="(item, index) in qualityOverviewItems"
                                :key="'quality-' + index"
                                :title="item.title"
                                :value="item.value"
                                :value-label="item.valueLabel"
                                :standard="item.standard"
                                :threshold="item.targetThreshold"
                                :fg-color="calculateFgColor(item)"
                                @click.native="handleGaugeCardClick(item)"
                            />
                        </div>
                    </div>

                    <!-- 特殊情况处理 -->
                    <div class="dashboard-section" :style="{ width: sectionWidths.specialCases }">
                        <div class="section-header">特殊情况处理</div>
                        <div class="section-content">
                            <gauge-card
                                v-for="(item, index) in specialCasesItems"
                                :key="'special-' + index"
                                :title="item.title"
                                :value="item.value"
                                :value-label="item.valueLabel"
                                :standard="item.standard"
                                :threshold="item.targetThreshold"
                                :fg-color="calculateFgColor(item)"
                                @click.native="handleGaugeCardClick(item)"
                            />
                        </div>
                    </div>

                    <!-- 报告完整性 -->
                    <div class="dashboard-section" :style="{ width: sectionWidths.reportCompleteness }">
                        <div class="section-header">报告完整性</div>
                        <div class="section-content">
                            <gauge-card
                                v-for="(item, index) in reportCompletenessItems"
                                :key="'completeness-' + index"
                                :title="item.title"
                                :value="item.value"
                                :value-label="item.valueLabel"
                                :standard="item.standard"
                                :threshold="item.targetThreshold"
                                :fg-color="calculateFgColor(item)"
                                @click.native="handleGaugeCardClick(item)"
                            />
                        </div>
                    </div>
                </div>

                <!-- 仪表盘第二行 -->
                <div class="dashboard-row" ref="secondRow">
                    <!-- 非肿块型病变评估 -->
                    <div class="dashboard-section" :style="{ width: sectionWidths.nonMassLesion }">
                        <div class="section-header">非肿块型病变评估</div>
                        <div class="section-content">
                            <gauge-card
                                v-for="(item, index) in nonMassLesionItems"
                                :key="'nonMass-' + index"
                                :title="item.title"
                                :value="item.value"
                                :value-label="item.valueLabel"
                                :standard="item.standard"
                                :threshold="item.targetThreshold"
                                :fg-color="calculateFgColor(item)"
                                @click.native="handleGaugeCardClick(item)"
                            />
                        </div>
                    </div>

                    <!-- 诊断结论规范性 -->
                    <div class="dashboard-section" :style="{ width: sectionWidths.diagnosticConclusion }">
                        <div class="section-header">诊断结论规范性</div>
                        <div class="section-content">
                            <gauge-card
                                v-for="(item, index) in diagnosticConclusionItems"
                                :key="'diagnostic-' + index"
                                :title="item.title"
                                :value="item.value"
                                :value-label="item.valueLabel"
                                :standard="item.standard"
                                :threshold="item.targetThreshold"
                                :fg-color="calculateFgColor(item)"
                                @click.native="handleGaugeCardClick(item)"
                            />
                        </div>
                    </div>

                    <!-- 临床因素考量 -->
                    <div class="dashboard-section" :style="{ width: sectionWidths.clinicalFactors }">
                        <div class="section-header">临床因素考量</div>
                        <div class="section-content">
                            <gauge-card
                                v-for="(item, index) in clinicalFactorsItems"
                                :key="'clinical-' + index"
                                :title="item.title"
                                :value="item.value"
                                :value-label="item.valueLabel"
                                :standard="item.standard"
                                :threshold="item.targetThreshold"
                                :fg-color="calculateFgColor(item)"
                                @click.native="handleGaugeCardClick(item)"
                            />
                        </div>
                    </div>
                </div>

                <!-- 仪表盘第三行 -->
                <div class="dashboard-row" ref="thirdRow">
                    <!-- 基础描述 -->
                    <div class="dashboard-section" :style="{ width: sectionWidths.basicDescription }">
                        <div class="section-header">基础描述</div>
                        <div class="section-content basic-description-section">
                            <gauge-card
                                v-for="(item, index) in basicDescriptionItems"
                                :key="'basic-' + index"
                                :title="item.title"
                                :value="item.value"
                                :value-label="item.valueLabel"
                                :standard="item.standard"
                                :threshold="item.targetThreshold"
                                :fg-color="calculateFgColor(item)"
                                class="basic-description-item"
                                @click.native="handleGaugeCardClick(item)"
                            />
                        </div>
                    </div>
                </div>

                <!-- 仪表盘第四行 -->
                <div class="dashboard-row" ref="fourthRow">
                    <!-- 技术应用规范 -->
                    <div class="dashboard-section" :style="{ width: sectionWidths.technicalApplication }">
                        <div class="section-header">技术应用规范</div>
                        <div class="section-content">
                            <gauge-card
                                v-for="(item, index) in technicalApplicationItems"
                                :key="'technical-' + index"
                                :title="item.title"
                                :value="item.value"
                                :value-label="item.valueLabel"
                                :standard="item.standard"
                                :threshold="item.targetThreshold"
                                :fg-color="calculateFgColor(item)"
                                @click.native="handleGaugeCardClick(item)"
                            />
                        </div>
                    </div>

                    <!-- BI-RADS分类准确性 -->
                    <div class="dashboard-section" :style="{ width: sectionWidths.biradsAccuracy }">
                        <div class="section-header">BI-RADS分类准确性</div>
                        <div class="section-content">
                            <gauge-card
                                v-for="(item, index) in biradsAccuracyItems"
                                :key="'birads-' + index"
                                :title="item.title"
                                :value="item.value"
                                :value-label="item.valueLabel"
                                :standard="item.standard"
                                :threshold="item.targetThreshold"
                                :fg-color="calculateFgColor(item)"
                                @click.native="handleGaugeCardClick(item)"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import GaugeCard from "./GaugeCard.vue";
import base from "../../../lib/base";
export default {
    name: "ultrasoundReportQCAdminOverview",
    mixins: [base],
    components: { GaugeCard },
    data() {
        return {
            queryForm: {
                timeRange: 'month', // 默认选择近1个月
                dateRange: [],
                hospitalId: ''
            },
            hospitalOptions: [], // 医院选项，需要从接口获取
            dateRange: "2025-01-01 至 2025-12-26",
            // 基准尺寸（1920*1080分辨率下scale为1）
            baseWidth: 1920,
            baseHeight: 1080,
            scaleFactor: 1,
            // 是否已初始化CSS变量
            cssVarInitialized: false,
            // 计算各个部分的宽度
            sectionWidths: {
                // 第一行宽度分配
                qualityOverview: "16%",
                specialCases: "50%",
                reportCompleteness: "34%",
                // 第二行宽度分配
                nonMassLesion: "16%",
                diagnosticConclusion: "50%",
                clinicalFactors: "34%",
                // 第三行宽度分配
                basicDescription: "100%",
                // 第四行宽度分配
                technicalApplication: "33%",
                biradsAccuracy: "67%",
            },
            // 质控概况数据
            qualityOverviewItems: [
                {
                    label: "报告总数",
                    title: "报告总数",
                    valueLabel: "合格率",
                    value: 85,
                    standard: "合格率>=95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
            ],
            // 特殊情况处理数据
            specialCasesItems: [
                {
                    label: "哺乳期乳腺炎规范",
                    title: "哺乳期乳腺炎规范描述率",
                    valueLabel: "",
                    value: 85,
                    standard: "哺乳期乳腺炎规范描述率>=95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
                {
                    label: "恶性病例淋巴结扫描",
                    title: "恶性病例淋巴结扫描完整率",
                    valueLabel: "",
                    value: 85,
                    standard: "恶性病例淋巴结扫描完整率>=95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
                {
                    label: "副乳腺诊断",
                    title: "副乳腺诊断准确率",
                    valueLabel: "",
                    value: 100,
                    standard: "副乳腺诊断准确率>=95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
            ],
            // 报告完整性数据
            reportCompletenessItems: [
                {
                    label: "图文一致性",
                    title: "图文一致性符合率",
                    valueLabel: "",
                    value: 85,
                    standard: "图文一致性符合率>=95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
                {
                    label: "术后/化疗后分类",
                    title: "术后/化疗后分类准确率",
                    valueLabel: "",
                    value: 100,
                    standard: "术后/化疗后分类准确率>=95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
            ],
            // 非肿块型病变评估数据
            nonMassLesionItems: [
                {
                    label: "非肿块型病变描述",
                    title: "非肿块型病变描述完整率",
                    valueLabel: "",
                    value: 85,
                    standard: "非肿块型病变描述完整率>=90%",
                    targetThreshold: 90,
                    comparisonType: "gte",
                },
            ],
            // 诊断结论规范性数据
            diagnosticConclusionItems: [
                {
                    label: "结论中'乳腺增生症'出现率",
                    title: "结论中'乳腺增生症'出现率",
                    valueLabel: "",
                    value: 85,
                    standard: "结论中'乳腺增生症'出现率<0%",
                    targetThreshold: 0,
                    comparisonType: "lt",
                },
                {
                    label: "特殊周期",
                    title: "特殊周期标注率",
                    valueLabel: "",
                    value: 85,
                    standard: "特殊周期标注率=100%",
                    targetThreshold: 100,
                    comparisonType: "eq",
                },
                {
                    label: "哺乳期导管及淤积描述",
                    title: "哺乳期导管及淤积描述完整率",
                    valueLabel: "",
                    value: 85,
                    standard: "哺乳期导管及淤积描述完整率>=95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
            ],
            // 临床因素考量数据
            clinicalFactorsItems: [
                {
                    label: "40岁以上病例升类率",
                    title: "40岁以上病例升类率",
                    valueLabel: "",
                    value: 85,
                    standard: "40岁以上病例升类率>=95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
                {
                    label: "4A类病例随访建议完整率",
                    title: "4A类病例随访建议完整率",
                    valueLabel: "",
                    value: 100,
                    standard: "4A类病例随访建议完整率>=95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
            ],

            // 基础描述数据（6个数据）
            basicDescriptionItems: [
                {
                    label: "正常乳腺",
                    title: "正常乳腺测量率",
                    valueLabel: "",
                    value: 100,
                    standard: "正常乳腺测量率≤5%",
                    targetThreshold: 5,
                    comparisonType: "lte",
                },
                {
                    label: "特殊情况下",
                    title: "特殊情况下测量率",
                    valueLabel: "",
                    value: 100,
                    standard: "特殊情况下测量率≥95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
                {
                    label: "多病灶钟点法",
                    title: "多病灶钟点法使用率",
                    valueLabel: "",
                    value: 85,
                    standard: "多病灶钟点法使用率≥95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
                {
                    label: "最大囊性结节",
                    title: "最大囊性结节描述率",
                    valueLabel: "",
                    value: 85,
                    standard: "最大囊性结节描述率≥95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
                {
                    label: "特殊囊性结节",
                    title: "特殊囊性结节单独描述率",
                    valueLabel: "",
                    value: 100,
                    standard: "特殊囊性结节单独描述率≥95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
                {
                    label: "BI-RADS 4类以上病灶",
                    title: "BI-RADS 4类以上病灶单独描述率",
                    valueLabel: "",
                    value: 85,
                    standard: "BI-RADS 4类以上病灶单独描述率≥100%",
                    targetThreshold: 100,
                    comparisonType: "gte",
                },
            ],

            // 技术应用规范数据（2个数据）
            technicalApplicationItems: [
                {
                    label: "弹性成像适应证",
                    title: "弹性成像适应证符合率",
                    valueLabel: "",
                    value: 85,
                    standard: "弹性成像适应证符合率≥95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
                {
                    label: "超声造影适应证",
                    title: "超声造影适应证符合率",
                    valueLabel: "",
                    value: 100,
                    standard: "超声造影适应证符合率≥90%",
                    targetThreshold: 90,
                    comparisonType: "gte",
                },
            ],

            // BI-RADS分类准确性数据（4个数据）
            biradsAccuracyItems: [
                {
                    label: "典型病变病理",
                    title: "典型病变病理标注率",
                    valueLabel: "",
                    value: 85,
                    standard: "典型病变病理标注率≥90%",
                    targetThreshold: 90,
                    comparisonType: "gte",
                },
                {
                    label: "分叶状病灶",
                    title: "分叶状病灶分类准确率",
                    valueLabel: "",
                    value: 100,
                    standard: "分叶状病灶分类准确率≥95%",
                    targetThreshold: 95,
                    comparisonType: "gte",
                },
                {
                    label: "BI-RADS 0类",
                    title: "BI-RADS 0类误用率",
                    valueLabel: "",
                    value: 85,
                    standard: "BI-RADS 0类误用率≤5%",
                    targetThreshold: 5,
                    comparisonType: "lte",
                },
                {
                    label: "应诊断0类",
                    title: "应诊断0类漏诊率",
                    valueLabel: "",
                    value: 100,
                    standard: "应诊断0类漏诊率≤5%",
                    targetThreshold: 5,
                    comparisonType: "lte",
                },
            ],
        };
    },
    mounted() {
        this.$nextTick(() => {
            // 初始化缩放因子
            this.updateScaleFactor();
            this.calculateSectionWidths();

            // 监听窗口大小变化，重新计算宽度和缩放因子
            window.addEventListener("resize", this.handleResize);
        });
    },
    beforeDestroy() {
        // 移除事件监听器
        window.removeEventListener("resize", this.handleResize);
    },
    methods: {
        // 处理窗口大小变化
        handleResize() {
            this.updateScaleFactor();
            this.calculateSectionWidths();
        },

        // 更新缩放因子
        updateScaleFactor() {
            // 获取当前窗口尺寸
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;

            // 计算宽度和高度的缩放比例
            const widthRatio = windowWidth / this.baseWidth;
            const heightRatio = windowHeight / this.baseHeight;

            // 取较小的比例作为缩放因子，确保内容完全显示
            this.scaleFactor = Math.min(widthRatio, heightRatio);

            // 如果缩放因子大于1，则保持为1（不放大）
            if (this.scaleFactor > 1) {
                this.scaleFactor = 1;
            }

            // 应用CSS变量
            if (this.$refs.dashboardContent) {
                this.$refs.dashboardContent.style.setProperty('--scale-factor', this.scaleFactor);

                // 第一次初始化时设置transform-origin
                if (!this.cssVarInitialized) {
                    this.cssVarInitialized = true;
                }
            }
        },

        // 计算各部分宽度
        calculateSectionWidths() {
            if (!this.$refs.firstRow) {
                return;
            }

            const containerWidth = this.$refs.firstRow.offsetWidth;

            // 第一行数据项总数
            const firstRowTotalItems =
                this.qualityOverviewItems.length + this.specialCasesItems.length + this.reportCompletenessItems.length;

            // 第二行数据项总数
            const secondRowTotalItems =
                this.nonMassLesionItems.length +
                this.diagnosticConclusionItems.length +
                this.clinicalFactorsItems.length;

            // 计算第一行每项数据占总宽度的比例
            const qualityRatio = this.qualityOverviewItems.length / firstRowTotalItems;
            const specialCasesRatio = this.specialCasesItems.length / firstRowTotalItems;
            const reportRatio = this.reportCompletenessItems.length / firstRowTotalItems;

            // 计算第二行每项数据占总宽度的比例
            const nonMassRatio = this.nonMassLesionItems.length / secondRowTotalItems;
            const diagnosticRatio = this.diagnosticConclusionItems.length / secondRowTotalItems;
            const clinicalRatio = this.clinicalFactorsItems.length / secondRowTotalItems;
        },

        /**
         * 根据比较类型和阈值计算前景色
         * @param {Object} item - 包含value、targetThreshold和comparisonType的对象
         * @returns {string} - 返回颜色代码
         */
        calculateFgColor(item) {
            const green = "#03CB00";  // 达标颜色
            const yellow = "#FFC000";  // 未达标颜色

            // 参数验证：检查item是否存在且包含必要的属性
            if (
                !item ||
                typeof item.value !== "number" ||
                typeof item.targetThreshold !== "number" ||
                !item.comparisonType
            ) {
                return yellow;  // 参数无效时返回默认颜色
            }

            // 根据不同的比较类型返回对应的颜色
            switch (item.comparisonType) {
            case "gte":  // 大于等于
                return item.value >= item.targetThreshold ? green : yellow;
            case "lte":  // 小于等于
                return item.value <= item.targetThreshold ? green : yellow;
            case "eq":   // 等于
                return item.value === item.targetThreshold ? green : yellow;
            default:    // 未知比较类型
                return yellow;
            }
        },
        handleGaugeCardClick(itemData) {
            this.$router.push({ name: 'UltrasoundReportQCChartOverview', params: { cardData: itemData } });
        },
        handleTimeRangeChange(val) {
            console.log(val);
        },
        handleQuery(){

        },
        handleDateChange(){

        },

    },
};
</script>
<style scoped lang="scss">
@import "@/module/ultrasync_pc/style/aiChat.scss";

.ultrasound-report-qc-admin-overview {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    background-color: #f5f7fa;
    .filter-toolbar {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        background: #fff;
        margin-bottom: 20px;
        border-radius: 4px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

        .time-range-buttons {
            margin-right: 20px;

            .separate-buttons {
                display: flex;
                gap: 10px;

                ::v-deep .el-radio-button {
                    margin: 0;

                    .el-radio-button__inner {
                        border-radius: 4px !important;
                        border: 1px solid #DCDFE6;
                        padding: 8px 15px;
                    }

                    &.is-active {
                        .el-radio-button__inner {
                            background: $ai-theme-gradient;
                            border-color: transparent;
                            color: #fff;
                            box-shadow: none;
                        }
                    }
                }
            }
        }

        .date-picker-wrapper {
            margin-right: 20px;

            ::v-deep .el-range-editor {
                width: 380px;

                .el-range-separator {
                    width: auto;
                }
            }
        }

        .action-buttons {
            .query-btn {
                background: $ai-theme-gradient;
                border: none;
                padding: 8px 20px;

                &:hover {
                    opacity: 0.9;
                }
            }
        }
    }


    .dashboard {
        padding: 16px 24px;
        background: #fff;
        min-height: 100vh;

        .toolbar {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 8px 0;
            border-bottom: 1px solid #eaeaea;

            button {
                margin-right: 10px;
                padding: 6px 16px;
                border: 1px solid #e0e0e0;
                background: #fff;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                color: #606266;
                transition: all 0.2s;

                &:hover {
                    border-color: #c6e2ff;
                    color: #409eff;
                }

                &.active {
                    background: #409eff;
                    border-color: #409eff;
                    color: #fff;
                }

                &.query {
                    background: #409eff;
                    border-color: #409eff;
                    color: #fff;
                    margin-left: 0;
                    position: relative;
                }
            }

            input {

                padding: 8px 12px;
                margin-right: 12px;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                font-size: 14px;

                &:focus {
                    border-color: #409eff;
                    outline: none;
                }
            }
        }

        .section {
            margin-bottom: 30px;

            .section-title {
                font-size: 16px;
                font-weight: 500;
                color: #303133;
                margin-bottom: 16px;
                position: relative;
                padding-left: 12px;

                &::before {
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    height: 16px;
                    width: 4px;
                    background-color: #409eff;
                    border-radius: 2px;
                }
            }

            .cards {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
                gap: 20px;
            }
        }
    }
}

    .dashboard-row {
        display: flex;
        margin-bottom: 10px;
        gap: 10px;
        width: 100%;

        .dashboard-section {
            background: #f2f4f7;
            padding: 10px;

            .section-header {
                font-size: 16px;
                font-weight: 500;
                color: #303133;
                position: relative;
                padding-left: 12px;
                margin-bottom: 5px;

                &::before {
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    height: 16px;
                    width: 4px;
                    background-color: #409eff;
                    border-radius: 2px;
                }
            }

            .section-content {
                display: flex;
                gap: 10px;
                height: 140px;
            }


            /* 基础描述行样式（6个卡片） */
            .basic-description-content {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                justify-content: space-between;

                .basic-description-card {
                    flex: 1;
                    min-width: 160px;
                }
            }

            /* 基础描述行卡片平均分配宽度 */
            .basic-description-section {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                gap: 10px;

                .basic-description-item {
                    flex: 1;
                    min-width: 160px;
                }
            }


            /* 技术应用规范样式（2个卡片） */
            .technical-content {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                justify-content: space-between;

                .technical-card {
                    flex: 1;
                    min-width: 160px;
                }
            }


            /* BI-RADS分类准确性样式（4个卡片） */
            .birads-content {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                justify-content: space-between;

                .birads-card {
                    flex: 1;
                    min-width: 160px;
                }
            }
        }
    }

    /* 设置日期选择器样式 */
    ::v-deep .el-date-editor {
        width: 100%;
    }
</style>
