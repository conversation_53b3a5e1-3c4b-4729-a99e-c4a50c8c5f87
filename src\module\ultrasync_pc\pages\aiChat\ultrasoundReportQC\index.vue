<template>
    <div class="ultrasound-report-qc">
        <router-view />
    </div>
</template>

<script>
export default {
    name: "ultrasoundReportQCIndex",
    components: {

    },
    data() {
        return {
            role: "admin", //admin 主任  normal 医生
        };
    },
    activated() {
        if(this.role === 'admin'){
            this.$router.replace({
                name: "UltrasoundReportQCAdminOverview",
            });
        }else{
            this.$router.replace({
                name: "UltrasoundReportQCChartOverview",
            });
        }
    },
    created() {

    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/aiChat.scss";
.ultrasound-report-qc {
    overflow: hidden;
    height: 100%;
    position: relative;
}
</style>
