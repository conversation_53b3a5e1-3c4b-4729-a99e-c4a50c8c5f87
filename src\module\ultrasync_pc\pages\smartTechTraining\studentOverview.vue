<template>
    <div class="student-overview">
        <!-- 顶部统计图表区域 -->
        <div class="statistics-section">
            <div class="chart-container">
                <!-- 左侧：学员总人数环形图 -->
                <div class="chart-item">
                    <div class="chart-wrapper">
                        <!-- 新的 HTML 结构 -->
                        <div class="echart-custom-container">
                            <div class="custom-outer-ring-background"></div>
                            <div class="custom-inner-ring-display"></div>
                            <div id="totalStudentsChart" class="chart"></div>
                        </div>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-items">
                            <div v-for="(item, index) in totalStudentsData" :key="index" class="legend-item"
                                @click="handleLegendClick('totalStudents', index)"
                                :class="{ 'legend-item-disabled': !totalStudentsLegendStatus[index] }">
                                <div class="legend-item-left">
                                    <span class="legend-dot"
                                        :class="{ 'legend-dot-disabled': !totalStudentsLegendStatus[index] }" :style="{
                                            backgroundColor: chartColors[index]
                                        }"></span>
                                    <span class="legend-label"
                                        :class="{ 'text-disabled': !totalStudentsLegendStatus[index] }">{{ item.name
                                        }}</span>
                                </div>
                                <div class="legend-item-right">
                                    <p class="legend-value"
                                        :class="{ 'text-disabled': !totalStudentsLegendStatus[index] }">{{ item.value
                                        }}/{{ item.percentage }}%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：已通过人数环形图 -->
                <div class="chart-item">
                    <div class="chart-wrapper">
                        <!-- 新的 HTML 结构 -->
                        <div class="echart-custom-container">
                            <div class="custom-outer-ring-background"></div>
                            <div class="custom-inner-ring-display"></div>
                            <div id="passedStudentsChart" class="chart"></div>
                        </div>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-items">
                            <div v-for="(item, index) in passedStudentsData" :key="index" class="legend-item"
                                @click="handleLegendClick('passedStudents', index)"
                                :class="{ 'legend-item-disabled': !passedStudentsLegendStatus[index] }">
                                <div class="legend-item-left">
                                    <span class="legend-dot"
                                        :class="{ 'legend-dot-disabled': !passedStudentsLegendStatus[index] }" :style="{
                                            backgroundColor: chartColors[index]
                                        }"></span>
                                    <span class="legend-label"
                                        :class="{ 'text-disabled': !passedStudentsLegendStatus[index] }">{{ item.name
                                        }}</span>
                                </div>
                                <div class="legend-item-right">
                                    <p class="legend-value"
                                        :class="{ 'text-disabled': !passedStudentsLegendStatus[index] }">{{ item.value
                                        }}/{{ item.percentage }}%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部学员表格区域 -->
        <div class="table-section">
            <div class="table-header">
                <el-button type="primary" size="small" @click="handleExport" class="ai-theme-background export-button">
                    导出数据
                </el-button>
                <div class="query-form-container">
                    <el-form :inline="true" :model="queryForm" class="custom-query-form">
                        <el-form-item label="学员姓名:">
                            <el-input v-model="queryForm.studentName" placeholder="请输入学员姓名" size="small"></el-input>
                        </el-form-item>
                        <el-form-item label="医院/诊所:">
                            <el-input v-model="queryForm.hospital" placeholder="请输入医院/诊所" size="small"></el-input>
                        </el-form-item>
                        <el-form-item label="是否通过:">
                            <el-select v-model="queryForm.isPassed" placeholder="请选择" size="small"
                                style="width: 120px;">
                                <el-option label="全部" value=""></el-option>
                                <el-option label="已通过" value="已通过"></el-option>
                                <el-option label="未通过" value="未通过"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" size="small" @click="handleSearch" class="ai-theme-background"
                                style="width: 120px;">查询</el-button>
                            <el-button size="small" @click="handleReset" style="width: 120px;">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <div class="student-table">
                <el-table :data="studentTableData" style="width: 100%" border stripe
                    :header-cell-style="{ background: '#D7DFE1', color: '#000' }">
                    <el-table-column type="selection" min-width="50"></el-table-column>
                    <el-table-column prop="studentName" label="学员姓名" min-width="100"></el-table-column>
                    <el-table-column prop="studentId" label="学员ID" min-width="120"></el-table-column>
                    <el-table-column prop="hospital" label="医院/诊所" min-width="150"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="registerDate" label="注册日期" min-width="120"></el-table-column>

                    <el-table-column prop="requirement1" label="考核要求1名称" min-width="120">
                        <template slot-scope="scope">
                            <div class="status-tag" :class="getRequiredStatusTagClass(scope.row.requirement1)">
                                <span>{{ scope.row.requirement1 }}</span>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column prop="requirement2" label="考核要求2名称" min-width="120">
                        <template slot-scope="scope">
                            <div class="status-tag" :class="getRequiredStatusTagClass(scope.row.requirement2)">
                                <span>{{ scope.row.requirement2 }}</span>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column prop="requirement3" label="考核要求3名称" min-width="120">
                        <template slot-scope="scope">
                            <div class="status-tag" :class="getRequiredStatusTagClass(scope.row.requirement3)">
                                <span>{{ scope.row.requirement3 }}</span>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column prop="requirement4" label="考核要求4名称" min-width="120">
                        <template slot-scope="scope">
                            <div class="status-tag" :class="getRequiredStatusTagClass(scope.row.requirement4)">
                                <span>{{ scope.row.requirement4 }}</span>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column prop="isPassed" label="是否通过" min-width="100">
                        <template slot-scope="scope">
                            <div class="status-tag" :class="getRequiredStatusTagClass(scope.row.isPassed)">
                                <span>{{ scope.row.isPassed }}</span>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column prop="passDate" label="通过日期" min-width="120"></el-table-column>
                </el-table>

                <div class="pagination-container">
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as echarts from "echarts";

export default {
    name: "studenOverview",
    data() {
        return {
            queryForm: {
                studentName: '',
                hospital: '',
                isPassed: '' // 初始为空，或设为 '全部'
            },
            totalStudentsChart: null,
            passedStudentsChart: null,
            chartColors: ["#4F9DFF", "#52C41A", "#FADB14", "#FF4D4F", "#9254DE", "#13C2C2"],

            // 学员总人数数据
            totalStudentsData: [
                { name: "医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1医院1", value: 2583, percentage: 36 },
                { name: "医院2", value: 2211, percentage: 20 },
                { name: "医院3", value: 1987, percentage: 16 },
                { name: "医院4", value: 1587, percentage: 10 },
                { name: "医院5", value: 1356, percentage: 9 },
                { name: "其他", value: 1111, percentage: 9 },
            ],
            // 学员总人数图例状态（true表示显示，false表示隐藏）
            totalStudentsLegendStatus: [],

            // 已通过人数数据（暂时复用相同结构）
            passedStudentsData: [
                { name: "医院1", value: 2583, percentage: 36 },
                { name: "医院2", value: 2211, percentage: 20 },
                { name: "医院3", value: 1987, percentage: 16 },
                { name: "医院4", value: 1587, percentage: 10 },
                { name: "医院5", value: 1356, percentage: 9 },
                { name: "其他", value: 1111, percentage: 9 },
            ],
            // 已通过人数图例状态（true表示显示，false表示隐藏）
            passedStudentsLegendStatus: [],

            studentTableData: [],
            currentPage: 1,
            pageSize: 10,
            total: 0,
        };
    },
    mounted() {
        // 初始化图例状态数组，默认所有图例都是显示状态
        this.totalStudentsLegendStatus = this.totalStudentsData.map(() => true);
        this.passedStudentsLegendStatus = this.passedStudentsData.map(() => true);

        this.$nextTick(() => {
            this.initTotalStudentsChart();
            this.initPassedStudentsChart();
            this.fetchStudentTableData();
        });
    },

    beforeDestroy() {
        // 清理图表实例
        if (this.totalStudentsChart) {
            this.totalStudentsChart.dispose();
        }
        if (this.passedStudentsChart) {
            this.passedStudentsChart.dispose();
        }
    },
    methods: {
        /**
         * 处理图例点击事件
         * @param {string} chartType - 图表类型（'totalStudents' 或 'passedStudents'）
         * @param {number} index - 点击的图例索引
         */
        handleLegendClick(chartType, index) {
            if (chartType === 'totalStudents') {
                // 切换图例状态，使用Vue.set确保响应式更新
                this.$set(this.totalStudentsLegendStatus, index, !this.totalStudentsLegendStatus[index]);

                // 只获取当前激活的图例数据
                const activeData = this.totalStudentsData
                    .filter((_, i) => this.totalStudentsLegendStatus[i])
                    .map((item, i) => {
                        // 找出原始数组中的颜色索引
                        const originalIndex = this.totalStudentsData.findIndex(d => d.name === item.name);
                        return {
                            ...item,
                            itemStyle: {
                                color: this.chartColors[originalIndex],
                                borderColor: "#fff",
                                borderWidth: 2,
                            }
                        };
                    });

                // 计算新的总人数
                const totalStudents = this.calculateTotal(this.totalStudentsData, this.totalStudentsLegendStatus);

                // 完全更新图表数据
                if (this.totalStudentsChart) {
                    this.totalStudentsChart.setOption({
                        title: {
                            subtext: totalStudents.toString()
                        },
                        series: [{
                            data: activeData
                        }]
                    });
                }
            } else if (chartType === 'passedStudents') {
                // 切换图例状态，使用Vue.set确保响应式更新
                this.$set(this.passedStudentsLegendStatus, index, !this.passedStudentsLegendStatus[index]);
                console.log('图例状态更新：', this.passedStudentsLegendStatus);

                // 强制刷新页面
                this.$forceUpdate();

                // 只获取当前激活的图例数据
                const activeData = this.passedStudentsData
                    .filter((_, i) => this.passedStudentsLegendStatus[i])
                    .map((item, i) => {
                        // 找出原始数组中的颜色索引
                        const originalIndex = this.passedStudentsData.findIndex(d => d.name === item.name);
                        return {
                            ...item,
                            itemStyle: {
                                color: this.chartColors[originalIndex],
                                borderColor: "#fff",
                                borderWidth: 2,
                            }
                        };
                    });

                // 计算新的已通过人数
                const passedStudents = this.calculateTotal(this.passedStudentsData, this.passedStudentsLegendStatus);

                // 完全更新图表数据
                if (this.passedStudentsChart) {
                    this.passedStudentsChart.setOption({
                        title: {
                            subtext: passedStudents.toString()
                        },
                        series: [{
                            data: activeData
                        }]
                    });
                }
            }
        },

        initPieChart(chartId, titleText, titleSubtext, seriesName, seriesData) {
            const chartDom = document.getElementById(chartId);
            if (!chartDom) {
                return null;
            }
            let chart = echarts.init(chartDom);
            const option = {
                backgroundColor: "transparent",
                title: {
                    text: titleText,
                    subtext: titleSubtext,
                    left: "center",
                    top: "40%",
                    textStyle: {
                        fontSize: 12,
                        color: "#000000",
                        lineHeight: 16,
                        fontWeight: 700,
                        letterSpacing: "0.6px",
                    },
                    subtextStyle: { fontSize: 32, fontWeight: 400, color: "#000000", lineHeight: 36 },
                },
                tooltip: { trigger: "item", formatter: "{b}: {d}%" },
                legend: { show: false },
                series: [
                    {
                        name: seriesName,
                        type: "pie",
                        radius: ["76.5%", "88%"],
                        center: ["50%", "50%"],
                        avoidLabelOverlap: false,
                        emphasis: {
                            focus: "series",
                            blurScope: "coordinateSystem",
                            itemStyle: {
                                borderWidth: 1,
                                borderColor: "#fff",
                                shadowBlur: 5,
                                shadowColor: "rgba(0,0,0,0.2)",
                            },
                        },
                        label: { show: false },
                        labelLine: { show: false },
                        itemStyle: { borderColor: "#fff", borderWidth: 2 },
                        data: seriesData.map((item, index) => ({
                            ...item,
                            itemStyle: {
                                color: this.chartColors[index],
                                borderColor: "#fff",
                                borderWidth: 2,
                            },
                        })),
                    },
                ],
            };
            chart.setOption(option);
            window.addEventListener("resize", () => {
                chart && chart.resize();
            });
            return chart;
        },

        /**
         * 计算总人数
         * @param {Array} data - 数据数组
         * @param {Array} status - 状态数组
         * @returns {number} - 激活项的总和
         */
        calculateTotal(data, status) {
            return data
                .filter((_, i) => status[i])
                .reduce((sum, item) => sum + item.value, 0);
        },

        initTotalStudentsChart() {
            // 计算当前激活的总人数
            const totalStudents = this.calculateTotal(this.totalStudentsData, this.totalStudentsLegendStatus);

            this.totalStudentsChart = this.initPieChart(
                "totalStudentsChart",
                "学员总人数",
                totalStudents.toString(),
                "学员分布",
                this.totalStudentsData
            );
        },

        initPassedStudentsChart() {
            // 计算当前激活的已通过人数
            const passedStudents = this.calculateTotal(this.passedStudentsData, this.passedStudentsLegendStatus);

            this.passedStudentsChart = this.initPieChart(
                "passedStudentsChart",
                "已通过人数",
                passedStudents.toString(),
                "已通过分布",
                this.passedStudentsData
            );
        },

        handleExport() {
            // 导出功能预留
            console.log("导出数据功能");
        },

        handleSizeChange(newSize) {
            this.pageSize = newSize;
            this.fetchStudentTableData();
        },

        handleCurrentChange(newPage) {
            this.currentPage = newPage;
            this.fetchStudentTableData();
        },

        fetchStudentTableData() {
            // 模拟学员表格数据
            this.studentTableData = [
                {
                    studentName: "张三",
                    studentId: "1001100",
                    hospital: "深圳市人民医院",
                    registerDate: "2025/04/12",
                    requirement1: "已通过",
                    requirement2: "已通过",
                    requirement3: "已通过",
                    requirement4: "已通过",
                    isPassed: "已通过",
                    passDate: "2025/04/12",
                },
                {
                    studentName: "李四",
                    studentId: "1001101",
                    hospital: "揭阳市人民医院",
                    registerDate: "2025/04/12",
                    requirement1: "已通过",
                    requirement2: "未通过",
                    requirement3: "未通过",
                    requirement4: "已通过",
                    isPassed: "未通过",
                    passDate: "",
                },
                {
                    studentName: "王五",
                    studentId: "1001102",
                    hospital: "东莞市人民医院",
                    registerDate: "2025/04/12",
                    requirement1: "待审核",
                    requirement2: "待审核",
                    requirement3: "未提交",
                    requirement4: "未提交",
                    isPassed: "未通过",
                    passDate: "",
                },
                {
                    studentName: "王五",
                    studentId: "1001102",
                    hospital: "东莞市人民医院",
                    registerDate: "2025/04/12",
                    requirement1: "待审核",
                    requirement2: "待审核",
                    requirement3: "未提交",
                    requirement4: "未提交",
                    isPassed: "未通过",
                    passDate: "",
                },
                {
                    studentName: "王五",
                    studentId: "1001102",
                    hospital: "东莞市人民医院",
                    registerDate: "2025/04/12",
                    requirement1: "待审核",
                    requirement2: "待审核",
                    requirement3: "未提交",
                    requirement4: "未提交",
                    isPassed: "未通过",
                    passDate: "",
                },
                {
                    studentName: "王五",
                    studentId: "1001102",
                    hospital: "东莞市人民医院",
                    registerDate: "2025/04/12",
                    requirement1: "待审核",
                    requirement2: "待审核",
                    requirement3: "未提交",
                    requirement4: "未提交",
                    isPassed: "未通过",
                    passDate: "",
                },
                {
                    studentName: "王五",
                    studentId: "1001102",
                    hospital: "东莞市人民医院",
                    registerDate: "2025/04/12",
                    requirement1: "待审核",
                    requirement2: "待审核",
                    requirement3: "未提交",
                    requirement4: "未提交",
                    isPassed: "未通过",
                    passDate: "",
                },
                {
                    studentName: "王五",
                    studentId: "1001102",
                    hospital: "东莞市人民医院",
                    registerDate: "2025/04/12",
                    requirement1: "待审核",
                    requirement2: "待审核",
                    requirement3: "未提交",
                    requirement4: "未提交",
                    isPassed: "未通过",
                    passDate: "",
                },
                {
                    studentName: "王五",
                    studentId: "1001102",
                    hospital: "东莞市人民医院",
                    registerDate: "2025/04/12",
                    requirement1: "待审核",
                    requirement2: "待审核",
                    requirement3: "未提交",
                    requirement4: "未提交",
                    isPassed: "未通过",
                    passDate: "",
                },
                {
                    studentName: "王五",
                    studentId: "1001102",
                    hospital: "东莞市人民医院",
                    registerDate: "2025/04/12",
                    requirement1: "待审核",
                    requirement2: "待审核",
                    requirement3: "未提交",
                    requirement4: "未提交",
                    isPassed: "未通过",
                    passDate: "",
                }
            ];
            this.total = 100; // 模拟总数据量
        },

        getRequiredStatusTagClass(status) {
            switch (status) {
            case '已通过':
                return 'status-tag-passed-new';
            case '未通过':
                return 'status-tag-failed-new';
            case '待审核':
                return 'status-tag-pending-new';
            case '未提交':
                return 'status-tag-unsubmitted-new';
            default:
                return 'status-tag-default-new';
            }
        },

        handleSearch() {
            // Placeholder for search logic
            console.log('Search triggered with:', this.queryForm);
        },
        handleReset() {
            this.queryForm = {
                studentName: '',
                hospital: '',
                isPassed: ''
            };
            // Placeholder for re-fetching data or resetting table
            console.log('Form reset');
            // this.fetchStudentTableData(); // Potentially call this
        },
    },

    beforeDestroy() {
        // 清理图表实例
        if (this.totalStudentsChart) {
            this.totalStudentsChart.dispose();
        }
        if (this.passedStudentsChart) {
            this.passedStudentsChart.dispose();
        }
    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";

.student-overview {
    padding: 20px;
    background: #f5f5f5;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .statistics-section {
        background: #fff;
        border-radius: 8px;
        padding: 30px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        overflow-x: auto;
        flex-shrink: 0;

        .chart-container {
            display: flex;
            justify-content: space-between;
            gap: 20px;

            .chart-item {
                flex: 1;
                display: flex;
                align-items: flex-start;
                gap: 20px;
                justify-content: center;

                .chart-wrapper {
                    .echart-custom-container {
                        position: relative;
                        width: 250px;
                        height: 250px;
                    }

                    .custom-outer-ring-background {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        background-color: #ffffff;
                        box-shadow: 0 0 10px 2px rgba(0, 0, 0, 0.44);
                        z-index: 1;
                    }

                    .custom-inner-ring-display {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        width: 55%;
                        height: 55%;
                        border-radius: 50%;
                        background-color: #ffffff;
                        border: 1px solid #a9bfbe;
                        box-shadow: 0 0 10px 4px rgba(189, 189, 189, 0.3);
                        z-index: 2;
                    }

                    .chart {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background-color: transparent !important;
                        box-shadow: none !important;
                        border-radius: 0 !important;
                        z-index: 3;
                    }
                }

                .chart-legend {
                    padding-left: 30px;
                    display: flex;
                    flex-direction: column;
                    height: 100%;

                    .legend-title {
                        font-size: 18px;
                        font-weight: 600;
                        color: #202226;
                        margin-bottom: 20px;
                    }

                    .legend-items {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-around;
                        height: 100%;

                        .legend-item {
                            display: flex;
                            align-items: center;
                            justify-content: flex-start;
                            width: 100%;
                            font-size: 16px;
                            color: #202226;
                            line-height: 24px;
                            font-weight: 400;
                            min-width: 300px;
                            max-width: 500px;
                            cursor: pointer;
                            transition: all 0.3s ease;

                            .legend-item-left {
                                flex: 7;
                                margin-right: 20px;
                                display: flex;
                                flex-wrap: nowrap;
                                align-items: center;
                            }

                            .legend-item-right {
                                flex: 3;
                                min-width: 102px;
                                max-width: 150px;
                                flex-shrink: 0;
                            }

                            .legend-dot {
                                width: 10px;
                                height: 10px;
                                border-radius: 3px;
                                margin-right: 10px;
                                flex-shrink: 0;
                                display: inline-block;
                            }

                            .legend-label {
                                color: #202226;
                                margin-right: auto;
                                display: -webkit-box;
                                -webkit-line-clamp: 2;
                                -webkit-box-orient: vertical;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                word-break: break-word;
                                transition: opacity 0.3s ease;
                            }

                            .legend-item-disabled {
                                opacity: 0.6;
                                filter: grayscale(80%);
                            }

                            .text-disabled {
                                color: #999;
                                text-decoration: line-through;
                            }

                            .legend-dot {
                                transition: all 0.3s ease;
                            }

                            .legend-dot-disabled {
                                opacity: 0.3;
                                filter: grayscale(100%);
                                box-shadow: none;
                                border: 1px dashed #999;
                            }

                            .legend-data {
                                color: #333;
                                font-weight: 500;

                            }

                            .legend-value {
                                text-align: left;
                                letter-spacing: 1px;
                            }
                        }
                    }
                }
            }
        }
    }

    .table-section {
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        .table-header {
            flex-shrink: 0;

            .export-button {
                width: 120px;
                height: 36px;
                border-radius: 4px;
                font-size: 16px;
            }

            .query-form-container {
                padding: 15px;
                border-radius: 8px;
            }
        }

        .student-table {
            flex: 1;
            overflow-y: hidden;
            display: flex;
            flex-direction: column;
            min-height: 500px;
        }
    }
}

.custom-query-form .el-form-item {
    margin-bottom: 0;
    margin-right: 15px;
}

.custom-query-form .el-form-item:last-child {
    margin-right: 0;
}
</style>
