<template>
    <div class="text-editor">
        <!-- 编辑模式 -->
        <template v-if="isEditMode">
            <el-input
                v-model="localValue"
                :placeholder="placeholder"
                :type="inputType"
                :min="minValue"
                :max="maxValue"
                @input="handleNumberInput"
                @blur="handleBlur"
                class="number-input"
            />
        </template>
        <!-- 显示模式 -->
        <template v-else>
            <pre>{{value}}</pre>
        </template>
    </div>
</template>

<script>
import { CLOUD_TEST_TYPE } from '../../../lib/constants';
export default {
    name: "TextField",
    props: {
        value: {
            type: [String, Number],
            default: ""
        },
        placeholder: {
            type: String,
            default: ""
        },
        isEdit: {
            type: [Boolean, Number],
            default: false
        },
        inputType: {
            type: String,
            default: "text",
            validator: function (value) {
                return ["text", "number"].indexOf(value) !== -1;
            }
        },
        minValue: {
            type: Number,
            default: 0
        },
        maxValue: {
            type: Number,
            default: 99
        }
    },
    computed: {
        isEditMode() {
            return this.isEdit === true || this.isEdit === CLOUD_TEST_TYPE.EDIT;
        }
    },
    data() {
        return {
            localValue: this.value
        };
    },
    watch: {
        value: {
            handler(newVal) {
                this.localValue = newVal;
            },
            immediate: true
        }
    },
    methods: {
        handleBlur() {
            if(this.inputType === 'number' && !this.localValue){
                this.localValue = this.minValue
                return
            }
            if (this.inputType === 'number') {
                const numValue = parseInt(this.localValue);
                if (numValue < this.minValue) {
                    this.localValue = this.minValue;
                } else if (numValue > this.maxValue) {
                    this.localValue = this.maxValue;
                }
            }
            this.$emit("change", this.localValue);
        },
        handleNumberInput(e) {
            if(this.inputType === 'number' && !this.localValue){
                this.localValue = this.minValue
                return
            }
            if (this.inputType === 'number') {
                let value = this.localValue
                // 判断是否在范围内
                const numValue = parseInt(value);
                if (!isNaN(numValue)) {
                    if (numValue > this.maxValue) {
                        this.localValue = String(this.maxValue);
                    } else if (numValue < this.minValue) {
                        this.localValue = String(this.minValue);
                    }
                }

            }
        }
    }
}
</script>

<style lang="scss" scoped>
.text-editor {
    flex: 1;
    margin-right: 20px;
    display: flex;
    align-items: center;
    font-size: 16px;
    line-height: 20px;

    .el-input {
        font-size: 16px;
    }

    // 隐藏number类型输入框的上下箭头
    :deep(.number-input) {
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type="number"] {
            -moz-appearance: textfield;
        }
    }

    pre {
        margin: 0;
        padding: 0;
        font-family: inherit;
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow-wrap: break-word;
        max-width: 100%;
        display: inline;
    }
}
</style>
