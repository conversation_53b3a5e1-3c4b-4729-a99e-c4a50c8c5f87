<template>
    <div class="header_container">
        <notifyBar></notifyBar>
        <div class="header_bar" @mousedown="moveWindowStart" :class="{ moving: moving }">
            <div class="header_left clearfix">
                <div class="avatar_box" @click="openPersonalSetting" :title="user.role | FilterRole">
                    <mr-avatar :url="getLocalAvatar(user)" :radius="60" :showOnlineState="false"
                        :key="user.avatar"></mr-avatar>
                    <!-- <span class="tag_role">{{ user.role|FilterRole}}</span> -->
                </div>
                <p class="nickname" @click="consoleStore">{{ user.nickname }}</p>
                <el-dropdown trigger="click" :disabled="dynamicGlobalParams.tv_wall_mode" class="header-menu-dropdown"
                    placement="bottom" popper-append-to-body ref="headerMenu" v-show="showMenuPlus">
                    <i class="icon iconfont iconplus" @mousedown.stop
                        :class="{ disabled: dynamicGlobalParams.tv_wall_mode }"></i>
                    <span v-if="showHomeworkUnread" class="unread"></span>

                    <el-dropdown-menu slot="dropdown" class="header-menu">
                        <el-menu>
                            <el-menu-item index="add_friend" @click="openAddFriend">
                                <i class="icon iconfont iconuser-round-add"></i>
                                <span>{{ lang.add_friend_text }}</span>
                            </el-menu-item>

                            <el-menu-item index="add_group" @click="openAddGroup">
                                <i class="icon iconfont iconusers-medical"></i>
                                <span>{{ lang.create_group_title }}</span>
                            </el-menu-item>

                            <el-menu-item index="search_group" @click="openSearchGroup">
                                <i class="icon iconfont iconjiarubanji"></i>
                                <span>{{ lang.search_group_title }}</span>
                            </el-menu-item>

                            <el-menu-item index="live" v-if="functionsStatus.live" @click="openLiveManagement">
                                <i class="icon iconfont iconzhibo"></i>
                                <span>{{ lang.live_management }}</span>
                            </el-menu-item>

                            <el-menu-item index="multicenter" @click="openMulticenter">
                                <i class="icon iconfont iconfenbushishujuku"></i>
                                <span>{{ lang.multicenter_title }}</span>
                            </el-menu-item>

                            <el-divider></el-divider>

                            <el-menu-item index="favorites" @click="openFavorytes">
                                <i class="icon iconfont icontianchongxing-"></i>
                                <span>{{ lang.cloud_favorites }}</span>
                            </el-menu-item>
                            <el-menu-item index="cloud_exam" @click="openCloudExam">
                                <i class="icon iconfont iconzaixiankaoshi menu-icon"></i>
                                <span class="menu-text">{{ lang.my_cloud_exam }}</span>
                                <span v-if="showHomeworkUnread" class="unread"></span>
                            </el-menu-item>
                            <el-menu-item index="cloud_exam" @click="openSmartEdTechTraining">
                                <i class="icon iconfont iconzaixiankaoshi menu-icon"></i>
                                <span class="menu-text">Smart EdTech Training</span>
                            </el-menu-item>
                            <el-menu-item index="practice_overview" @click="openPracticeOverview" v-if="functionsStatus.ai">
                                <i class="icon iconfont iconclinical_thinking_practice menu-icon"></i>
                                <span class="menu-text">{{ lang.clinical_thinking_practice_title }}</span>
                            </el-menu-item>

                            <el-menu-item index="case_database" v-show="EnableAiSearch" @click="openCaseDatabase">
                                <i class="icon iconfont iconimages"></i>
                                <span>{{ lang.case_database_title }}</span>
                            </el-menu-item>

                            <el-menu-item index="personal_setting" @click="openPersonalSetting">
                                <i class="icon iconfont iconuser-cog"></i>
                                <span>{{ lang.personal_setting_title }}</span>
                            </el-menu-item>

                            <el-menu-item v-show="functionsStatus.referralCode && isShowInviteRegistration"
                                index="invite_registration" @click="inviteRegistration">
                                <i class="icon iconfont iconsend"></i>
                                <span>{{ lang.invite_registration }}</span>
                            </el-menu-item>

                            <el-menu-item index="system_setting" @click="openSystemSetting">
                                <i class="icon iconfont iconsetting"></i>
                                <span>{{ lang.setting_title }}</span>
                            </el-menu-item>

                            <el-divider></el-divider>

                            <el-menu-item v-show="false" @click="openBIDataShow">
                                <i class="icon iconfont icontongji"></i>
                                <span>{{ lang.bi_data_display }}</span>
                            </el-menu-item>

                            <el-menu-item v-show="isAdmin && showStatistic" @click="openCouldStatistics">
                                <i class="icon iconfont icontongji"></i>
                                <span>{{ lang.cloud_statistics_title }}</span>
                            </el-menu-item>

                            <el-menu-item @click="openScanRoomManage" v-show="isAdmin">
                                <i class="icon iconfont iconguanli-fill"></i>
                                <span>{{ lang.device_mng }}</span>
                            </el-menu-item>

                            <el-menu-item v-show="isAdmin" @click="openBackgroundManage">
                                <i class="icon iconfont iconguanlipeizhi"></i>
                                <span>{{ lang.background_manage_title }}</span>
                            </el-menu-item>

                            <el-divider v-show="isAdmin"></el-divider>

                            <el-menu-item @click="openEquipmentTesting" v-if="isCef">
                                <i class="icon iconfont iconhuabankaobei-"></i>
                                <span>{{ lang.equipment_testing }}</span>
                            </el-menu-item>

                            <el-menu-item @click="openVersion">
                                <i class="icon iconfont iconcopyright"></i>
                                <span>{{ lang.system_info_text }}</span>
                            </el-menu-item>

                            <el-menu-item @click="logout" id="logout_btn">
                                <i class="icon iconfont iconquit"></i>
                                <span>{{ lang.logout }}</span>
                            </el-menu-item>
                        </el-menu>
                    </el-dropdown-menu>
                </el-dropdown>
                <div class="ai-btn-wrapper" v-if="functionsStatus.ai">
                    <button class="ai-btn" @click="openAiChat" ></button>
                </div>
                <div v-show="isWorkStation" @mousedown.stop class="home_btn" @click="goToWorkStationSetting">
                    <i class="icon iconfont iconout"></i>
                </div>
                <div v-show="isShowIstation" class="istation_btn" @click="openIstation" @mousedown.stop></div>
                <div v-if="isShowMonitorWall" class="handle-silence-stream" @mousedown.stop>
                    <el-button type="primary" round plain @click="enterTVmode">{{ lang.tv_wall_mode }}</el-button>
                </div>
                <div v-if="false" class="handle-silence-stream" @mousedown.stop>
                    <el-button @click="doCEFTest" type="primary" size="medium">CEF Test</el-button>
                </div>
                <!-- <div v-if="isShowMonitorWall">

                    <div v-show="dynamicGlobalParams.tv_wall_mode" @click="exitTVmode" class="tv_mall_btn" :class="{openingMonitor:openingMonitor}">{{lang.general_mode}}</div>
                </div> -->

                <div v-if="isWorkStation && functionsStatus.tvwall" class="handle-silence-stream" @mousedown.stop>
                    <!-- <p v-if="isPushStream">{{lang.in_silent_streaming}}</p> -->
                    <SilenceStreamIcon></SilenceStreamIcon>
                </div>

                <!-- <div v-show="taskManagerStatus" class="task_manager" @click="openTaskManager">
                    <i v-show="0==taskManagerStatus" class="icon iconfont iconPC task_default"></i>
                    <i v-show="1==taskManagerStatus" class="icon iconfont iconPC task_ing"></i>
                    <i v-show="2==taskManagerStatus" class="icon iconfont iconPC task_ing"></i>
                    <i v-show="3==taskManagerStatus" class="icon iconfont iconPC task_succ"></i>
                    <i v-show="4==taskManagerStatus" class="icon iconfont iconPC task_fail"></i>
                </div> -->
                <div class="search" @mousedown.stop>
                    <div v-show="dynamicGlobalParams.tv_wall_mode" class="disabled_modal"></div>
                    <i class="icon iconfont iconsearch"></i>
                    <i @click.stop="deleteSearchText" v-show="edit" class="icon iconfont iconclose"></i>
                    <input type="text" @click="enterEdit" @keyup.enter="search('1')" v-model="searchText"
                        maxlength="100" class="search_input" />
                    <div v-show="edit" class="search_types">
                        <div @click="search('1')" class="clearfix">
                            <i class="icon iconfont iconuser"></i>
                            <span>{{ lang.search_friend_text }}</span>
                        </div>
                        <div @click="search('2')" class="clearfix">
                            <i class="icon iconfont icongroups"></i>
                            <span>{{ lang.search_group_text }}</span>
                        </div>
                        <div @click="search('3')" class="clearfix">
                            <i class="icon iconfont iconfolder"></i>
                            <span>{{ lang.search_file_text }}</span>
                        </div>
                        <div @click="search('4')" class="clearfix">
                            <i class="icon iconfont iconyidiandiantubiao18"></i>
                            <span>{{ lang.search_history_text }}</span>
                        </div>
                        <div @click="search('5')" class="clearfix">
                            <i class="icon iconfont iconioschatbubble"></i>
                            <span>{{ lang.search_recent_chat_text }}</span>
                        </div>
                        <div v-if="functionsStatus.groupset" @click="search('6')" class="clearfix">
                            <i class="icon iconfont icongroups"></i>
                            <span>{{ lang.search_groupsets_text }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="header_right">
                <div v-show="isCef" class="clearfix" @mousedown.stop>
                    <i class="icon iconfont iconsearchclose fr" @click="closeApp"></i>
                    <i class="icon iconfont iconmaximize fr" @click="maximizeApp"></i>
                    <!-- <i v-show="isMaxScreen" @click="restoreApp" class="icon iconfont iconmaximize-restore fr"></i> -->
                    <i @click="minApp" class="icon iconfont iconmin fr"></i>
                </div>
                <img @mousedown.stop src="static/resource_pc/images/logob.png" />
            </div>
            <search-more ref="searchMore" :searchText.sync="searchText"></search-more>
        </div>
    </div>
</template>
<script>
import base from "../lib/base";
import appOperateTool from "../lib/appOperateTool";
import service from "../service/service";
import Tool from "@/common/tool.js";
import searchMore from "./searchMore";
import { findServiceId, getLocalAvatar } from "../lib/common_base";
import notifyBar from "../components/notifyBar.vue";
import SilenceStreamIcon from "../components/silenceStreamIcon";
export default {
    mixins: [base, appOperateTool],
    name: "HeaderBarComponent",
    components: {
        searchMore,
        notifyBar,
        SilenceStreamIcon,
    },
    filters: {
        FilterRole(val) {
            let arrayData = {
                0: "临时用户",
                1: "普通用户",
                2: "管理员",
                3: "超级管理员",
                4: "主任",
            };
            return arrayData[val];
        },
    },
    data() {
        return {
            getLocalAvatar,
            edit: false,
            activeName: "1",
            isShowSearchDialog: false,
            searchText: "",
            friendList: [],
            groupList: [],
            fileList: [],
            historyList: [],
            searching: false,
            openingMonitor: false,
            controlUserId: 0,
            isJoining: false,
            isPushStream: false,
            showAiMain:false,
        };
    },
    computed: {
        repository() {
            return this.$store.state.repository;
        },
        showMenuPlus() {
            return !this.$route.path.includes('ai_main');
        },
        isAdmin() {
            return this.user.role == 2 || this.user.role == 3;
        },
        isMaxScreen() {
            return this.globalParams.isMaxScreen;
        },
        isWorkStation() {
            return this.isCef && Tool.ifAppWorkstationClientType(this.systemConfig.clientType);
        },
        isShowIstation() {
            return this.globalParams.istation_info.Show || 0;
        },
        isShowMonitorWall() {
            return this.functionsStatus.tvwall && this.user.role > 1 && !this.isWorkStation;
        },
        tasks() {
            return this.$store.state.taskList.media_transfer_tasks;
        },
        taskManagerStatus() {
            let status = 0;
            if (!status) {
                //Pending || Processing优先显示
                for (let i in this.tasks) {
                    let task = this.tasks[i];
                    if (1 == task.status || 2 == task.status) {
                        status = task.status;
                        break;
                    }
                }
            }

            if (!status) {
                //Fail 优先显示
                for (let i in this.tasks) {
                    let task = this.tasks[i];
                    if (4 == task.status) {
                        status = 4;
                        break;
                    }
                }
            }

            if (!status) {
                //Fail 优先显示
                for (let i in this.tasks) {
                    let task = this.tasks[i];
                    if (3 == task.status) {
                        status = 3;
                        break;
                    }
                }
            }

            return status;
        },
        isShowInviteRegistration() {
            return this.user.probationary_expiry == "";
        },
        EnableCloudStatistics() {
            // 是否允许查看云端统计
            return (
                this.$store.state.systemConfig.serverInfo.cloud_statistics &&
                this.$store.state.systemConfig.serverInfo.cloud_statistics.enable
            );
        },
        EnableAiSearch() {
            return this.functionsStatus.breastCases && !this.globalParams.isCE;
        },
        // isShowMulticenter(){
        //     if(this.$store.state.multicenter.list.length>0){
        //         if(this.$store.state.multicenter.list.length==1 && this.$store.state.multicenter.name.obstetric_qc_multicenter==this.$store.state.multicenter.list[0].type){
        //             return this.functionsStatus.obstetricalAI
        //         }else{
        //             return true
        //         }
        //     }else{
        //         return false
        //     }
        // },
        showStatistic() {
            return this.functionsStatus.cloudStatistic;
        },
        showHomeworkUnread() {
            return (
                this.$store.state.homework.globalUnfinish > 0 ||
                this.$store.state.homework.globalUnCorrect !== undefined ||
                (this.$store.state.homework.globalCorrected !== undefined && this.$store.state.homework.globalCorrected > 0)
            );
        },
    },
    mounted() {
        this.$nextTick(async () => {
            document.addEventListener("click", (e) => {
                if (e.target.className != "search_input") {
                    this.edit = false;
                }
            });
            this.$root.eventBus.$off("closeSearchDialog").$on("closeSearchDialog", () => {
                this.isShowSearchDialog = false;
            });
            this.$root.eventBus.$off("enterTVmode").$on("enterTVmode", this.enterTVmode);
            this.$root.eventBus.$off("exitTVmode").$on("exitTVmode", this.exitTVmode);
            this.$root.eventBus.$off("searchMore").$on("searchMore", (obj) => {
                this.searchText = obj.searchText;
                setTimeout(() => {
                    this.search(obj.activeName || "1");
                }, 0);
            });
            this.$root.eventBus.$off("NotifyScreenChanged").$on("NotifyScreenChanged", this.handleNotifyScreenChanged);
        });
    },
    methods: {
        //接口用来通知CEF,CEF可以根据此做一些功能自测
        doCEFTest() {
            window.CWorkstationCommunicationMng.TestForSomething();
        },
        enterEdit() {
            this.edit = true;
        },
        quitEdit() {
            setTimeout(() => {
                this.edit = false;
            }, 100);
        },
        openAddFriend() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/add_friend`);
        },
        openAddGroup() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/add_group`);
        },
        openSearchGroup() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/search_group`);
        },
        openLibrary() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/library`);
        },
        openPersonalSetting() {
            if(!this.showMenuPlus){
                return
            }
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/personal_setting`);
        },
        openSystemSetting() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/system_setting`);
        },
        openFavorytes() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/favorites`);
        },
        openCaseDatabase() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/case_database`);
        },
        openLiveManagement() {
            this.$refs.headerMenu.hide();
            Tool.loadModuleRouter(`/index/chat_window/live_management`);
        },
        logout() {
            this.$root.eventBus.$emit("unBindControllerEvent");
            setTimeout(() => {
                if (window.main_screen && window.main_screen.gateway) {
                    window.main_screen.CloseSocket();
                }
                service
                    .logout({
                        user_id: this.user.uid,
                        client_uuid: this.user.client_uuid,
                    })
                    .then((res) => { })
                    .catch((res) => { });
                this.resetApp();
            }, 0);
        },
        resetApp() {
            window.CWorkstationCommunicationMng.CloseNewWindow();
            window.CWorkstationCommunicationMng.resetApp();
            window.CWorkstationCommunicationMng.notifyDisconnectFromDoppler();
            window.localStorage.setItem("account", "");
            window.localStorage.setItem("password", "");
            window.localStorage.setItem("loginToken", "");
            window.localStorage.removeItem("local_store_device_token");
            this.$store.commit("user/updateUser", {
                new_token: "",
            });
            window.CWorkstationCommunicationMng.ClearStartupOption();
            if (window.main_screen.CMonitorWallPush && window.main_screen.CMonitorWallPush.joined) {
                window.main_screen.CMonitorWallPush.LeaveChannelSilence();
            }

            // setTimeout(()=>{
            //     window.location.reload();
            // },300)
            this.$root.eventBus.$emit("reloadRouter");
        },
        search(activeName) {
            this.$refs.searchMore.search(activeName);
        },
        deleteSearchText() {
            this.searchText = "";
        },
        openBIDataShow() {
            var that = this;
            this.$root.socket.emit(
                "get_qc_statistics_bi_url",
                {
                    groupset_id: this.groupset_id,
                },
                (is_error, data) => {
                    if (!is_error) {
                        // console.log("get token ", data.token);
                        const url = data.biUrl;
                        console.log("url: ", url);
                        if (Tool.ifBrowserClientType(that.systemConfig.clientType)) {
                            console.log("浏览器版本");
                            window.open(url);
                        } else {
                            console.log("非浏览器版本");
                            window.CWorkstationCommunicationMng.OpenNewWindow({ url: url });
                        }
                    } else {
                        console.log("openBIDataShow error ", data);
                    }
                }
            );
        },
        openCouldStatistics() {
            this.$refs.headerMenu.hide();
            const requestConfig = this.systemConfig.server_type;
            let ajaxServer = requestConfig.protocol + requestConfig.host + requestConfig.port;
            let lang = window.localStorage.getItem("lang");
            if (process.env.NODE_ENV === "production") {
                ajaxServer += "/statistic";
            } else {
                ajaxServer = window.location.origin;
            }
            const url = Tool.transferLocationToCe(
                `${ajaxServer}/statistic.html#/index/live?dataFrom=global&id=-1&token=${window.vm.$store.state.dynamicGlobalParams.token}&language=${lang}`
            );
            // window.localStorage.setItem('stat_query', JSON.stringify({dataFrom: 'global',id: -1}));
            if ([1, 5].includes(window.clientType)) {
                window.open(url, "blank");
            } else {
                window.CWorkstationCommunicationMng.OpenNewWindow({ url });
            }
        },
        openVersion() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/version_info`);
        },
        openScanRoomManage() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/scan_room_manage`);
        },
        async openEquipmentTesting() {
            if (window.main_screen.CMonitorWallPush.joined) {
                //此时如果电视墙在默认推起，则暂时先关闭
                window.main_screen.CMonitorWallPush.LeaveChannelSilenceTmp();
                await Tool.sleep(50);
            }
            this.$refs.headerMenu.hide();
            window.CWorkstationCommunicationMng.OpenNativeRtcSetting();
            // this.$store.commit('device/updateDeviceInfo',{isOpenRtcSettingDialog:true})
        },
        openBackgroundManage() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/background_manage`);
        },
        enterTVmode() {
            // this.openingMonitor=true;
            // this.$store.commit('globalParams/updateGlobalParams',{
            //     tv_wall_mode:true
            // })
            if (this.isCef) {
                Tool.loadModuleRouter("/index/chat_window/0/tv_wall_web");
            } else {
                Tool.loadModuleRouter("/index/chat_window/0/tv_wall_web");
            }

        },
        exitTVmode() {
            if (!this.openingMonitor) {
                this.$store.commit("dynamicGlobalParams/updateDynamicGlobalParams", {
                    tv_wall_mode: false,
                });
                this.back();
            }
        },
        openIstation() {
            let cid = window.vm.$route.params.cid;
            window.CWorkstationCommunicationMng.addExam(parseInt(cid || 0));
        },
        openService: async function (type = 1) {
            let service_type = 0;
            if (type == 1) {
                service_type = this.systemConfig.ServiceConfig.type.AiAnalyze;
            } else if (type == 2) {
                service_type = this.systemConfig.ServiceConfig.type.FileTransferAssistant;
            }
            let analyze = await findServiceId(service_type);
            if (analyze.cid) {
                this.openConversation(analyze.cid, 1);
            } else {
                this.openConversation(analyze.id, 3);
            }
            this.$refs.headerMenu.hide();
        },
        inviteRegistration() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/invite_registration`);
        },
        handleNotifyScreenChanged(data) {
            //分辨率发生变化时，手动操作全屏
            this.maximizeApp();
        },
        consoleStore() {
            console.info(this.$store.state);
        },
        openMulticenter() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/multicenter`);
        },
        leaveSilence() {
            this.$root.eventBus.$emit("leaveSilence", (is_suc) => {
                // if(is_suc){
                //     this.isPushStream = false
                // }
            });
        },
        goToWorkStationSetting() {
            this.returnApp();
        },
        openCloudExam(type) {
            this.$refs.headerMenu.hide();
            // let cid = this.$route.params.cid;
            // cid = 0 全局进入
            Tool.loadModuleRouter(`/index/chat_window/0/cloud_exam`);
        },
        openPracticeOverview() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/ai_main/practice_overview`);
        },
        openAiChat() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            if (this.$route.path.startsWith(`/index/chat_window/${cid}/ai_main`)) {
                this.$router.replace(`/index/chat_window/${cid}`)
            } else {
                Tool.loadModuleRouter(`/index/chat_window/${cid}/ai_main`);
            }
        },
        openSmartEdTechTraining() {
            this.$refs.headerMenu.hide();
            let cid = this.$route.params.cid;
            Tool.loadModuleRouter(`/index/chat_window/${cid}/smart_tech_training`);
        },
    },
};
</script>
<style lang="scss">
.header_bar {
    user-select: none;

    &.moving {
        cursor: move;
    }

    .header_left {
        flex: 1;

        .avatar_box {
            float: left;
            margin: 10px 6px 6px 20px;
            cursor: pointer;
            position: relative;

            .tag_role {
                position: absolute;
                right: -18px;
                bottom: 0;
                padding: 0px 12px;
                background: #fff;
                font-size: 12px;
                height: 16px;
                line-height: 16px;
            }
        }

        .nickname {
            float: left;
            font-size: 34px;
            margin-left: 16px;
            line-height: 80px;
            color: #717071;
            min-width: 50px;
            max-width: 250px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }

        .home_btn {
            float: left;
            display: flex;
            height: 100%;
            align-items: center;
            margin-right: 26px;

            i {
                font-size: 24px;
                line-height: 42px;
                color: #717071;
                cursor: pointer;

                &:hover {
                    color: #6ab3ad;
                }
            }
        }

        .istation_btn {
            float: left;
            margin: 34px 20px 20px 0px;
            width: 25px;
            height: 25px;
            background-image: url("../../../../static/resource_pc/images/a23-1.png");

            &:hover {
                background-image: url("../../../../static/resource_pc/images/a23-2.png");
            }
        }

        .task_manager {
            float: left;
            // margin: 26px 20px 20px 0px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin: 0 20px;

            i {
                font-size: 24px;
                line-height: 42px;
                color: #717071;
                cursor: pointer;
            }

            .task_ing {
                color: #458a86;
                -webkit-animation: mymove 2s infinite;
                animation: mymove 2s infinite;
            }

            .task_succ {
                color: #458a86;
            }

            .task_fail {
                color: #d33838;
            }

            @-webkit-keyframes mymove {
                50% {
                    opacity: 0.4;
                }
            }

            @keyframes mymove {
                50% {
                    opacity: 0.4;
                }
            }
        }

        .tv_mall_btn {
            float: left;
            cursor: pointer;
            margin: 24px 10px 16px;
            padding: 0 20px;
            text-align: center;
            text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.22);
            font: bold 12px/40px Arial, sans-serif;
            border-radius: 30px;
            box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.29), inset 1px 1px 1px rgba(255, 255, 255, 0.44);
            transition: all 0.15s ease;

            &:hover {
                box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.29), inset 0px 0px 2px rgba(0, 0, 0, 0.5);
                background: rgb(215, 223, 225);
                color: #fff;
            }

            &.openingMonitor:hover {
                box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.29), inset 1px 1px 1px rgba(255, 255, 255, 0.44);
                background: #d7dfe1;
                color: #000;
            }
        }

        .handle-silence-stream {
            height: 100%;
            display: flex;
            align-items: center;
            float: left;

            .SilenceStreamBtn {
                font-size: 30px;
                padding: 0;
                padding-top: 8px;
            }

            p {
                font-size: 14px;
            }
        }

        &>span,
        .header-menu-dropdown {
            margin-right: 26px;
            margin-left: 20px;
            float: left;
            display: inline-block;
            height: 100%;
            display: flex;
            align-items: center;

            i {
                font-size: 30px;
                color: #717071;
                cursor: pointer;

                &:hover {
                    color: #6ab3ad;
                }
            }

            .disabled {
                &:hover {
                    cursor: not-allowed;
                }
            }
        }

        .search {
            width: 314px;
            position: relative;
            height: 46px;
            float: right;
            margin-top: 18px;
            line-height: 46px;
            background: #ebeff1;

            .disabled_modal {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                background: rgba(160, 160, 160, 0.3);
            }

            .iconsearch {
                color: #bbcdce;
                font-size: 22px;
                position: absolute;
                left: 8px;
            }

            .iconclose {
                color: #bbcdce;
                font-size: 20px;
                position: absolute;
                right: 8px;
                cursor: pointer;
            }

            input {
                width: 100%;
                height: 100%;
                vertical-align: top;
                padding: 0px 34px;
                font-size: 20px;
                color: #666;
            }

            .search_types {
                background: #ebeff1;
                position: absolute;
                width: 100%;
                z-index: 11;

                &>div {
                    line-height: 36px;
                    height: 36px;
                    cursor: pointer;
                    font-size: 16px;

                    i {
                        float: left;
                        font-size: 22px;
                        color: #bbcdce;
                        margin: 0 8px;
                    }

                    span {
                        float: left;
                        font-size: 14px;
                    }

                    &:hover {
                        background: #cfe5e3;

                        i {
                            color: #6ab3ad;
                        }
                    }
                }
            }
        }
    }

    .header_right {
        width: 260px;
        position: relative;

        i {
            font-size: 18px;
            color: #9d9c9d;
            font-weight: bolder;
            padding: 4px 8px;
            cursor: pointer;

            &:hover {
                background: #6ab3ad;
                color: #fff;
            }
        }

        img {
            width: 200px;
            margin-top: 4px;
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    .search_component {
        .el-tabs {
            height: 100%;
            display: flex;
            flex-direction: column;

            .el-tabs__content {
                flex: 1;
                overflow: auto;

                .el-tab-pane {
                    min-height: 50px;
                }

                .result_item {
                    margin: 10px 0;
                    cursor: pointer;

                    .avatar {
                        width: 44px;
                        height: 44px;
                        border-radius: 50%;
                    }

                    .nickname {
                        font-size: 16px;
                        line-height: 44px;
                        padding-left: 52px;
                    }

                    .right {
                        height: 44px;
                        padding-left: 52px;
                        position: relative;

                        .file_name {
                            width: 64%;
                            margin: 4px 0;
                        }

                        .time {
                            position: absolute;
                            right: 8px;
                        }
                    }
                }
            }
        }
    }

    .el-popover__reference-wrapper {
        position: relative;
    }
}

.header-menu {
    background: #ebeff1 !important;
    min-width: 200px;
    margin-top: -20px !important;
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;

    // 添加滚动条样式
    &::-webkit-scrollbar {
        width: 4px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 2px;

        &:hover {
            background: #999;
        }
    }

    i {
        font-size: 30px;
        color: #717071;
        cursor: pointer;

        &:hover {
            color: #6ab3ad;
        }
    }

    .el-menu {
        background: transparent;
        border: none;

        .el-submenu__title {
            height: 50px;
            line-height: 50px;
            font-size: 18px;
            color: #000;

            &:hover {
                color: #fff;
                background: #bbcdce;

                i {
                    color: #fff;
                }
            }

            i {
                color: #bbcdce;
                font-size: 22px;
                width: 24px;
            }
        }

        .el-menu-item-group {
            .el-menu-item {
                padding-left: 54px !important;

                &:hover {
                    color: #fff;
                    background: #bbcdce;
                }
            }
        }

        .el-menu-item {
            height: 50px;
            line-height: 50px;
            font-size: 18px;
            color: #000;
            transition: none;

            &:hover {
                color: #fff;
                background: #bbcdce;

                i {
                    color: #fff;
                }
            }

            i {
                color: #bbcdce;
                font-size: 22px;
                margin-right: 11px;
                width: 26px;
                display: inline-block;
            }
        }

        .el-divider {
            margin: 0 6px;
            background-color: #ccc;
        }

        .unread {
            position: absolute;
            right: 16px;
            top: 21px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #f00;
        }
    }
}

.ai-btn-wrapper {
    float: left;
    margin: 20px 15px;
    position: relative;
    width: 130px;
    height: 40px;
}

.ai-btn {
    position: relative;
    overflow: hidden;
    background-image: url('../../../../static/resource_pc/images/ai_btn_background.png');
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
    border: none;
    cursor: pointer;
}

.ai-btn::before,
.ai-btn::after {
    content: '';
    position: absolute;
    bottom: 22px;
    left: -58px;
    width: 78px;
    height: 14px;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.4) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(135deg);
}

.ai-btn:hover::before {
    animation: shine 1s ease;
}

@keyframes shine {
    from {
        bottom: 17px;
        left: -60px;
    }
    to {
        bottom:12px;
        left: 122px;
    }
}

.ai-btn-content {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    z-index: 2;

    .ai-icon {
        width: 30px;
        height: 30px;
        object-fit: contain;
    }
}

.ai-btn-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle,
            rgba(64, 169, 255, 0.4) 0%,
            rgba(0, 225, 255, 0.2) 40%,
            transparent 70%);
    filter: blur(15px);
    opacity: 0.8;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.ai-btn-border {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(90deg,
            #00e1ff,
            #40a9ff,
            #00e1ff,
            #40a9ff);
    border-radius: 8px;
    opacity: 0.6;
    z-index: 0;
    transition: opacity 0.3s ease;
}

@keyframes borderRotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>
