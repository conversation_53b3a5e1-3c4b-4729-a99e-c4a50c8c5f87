<template>
	<div id="pdfvuer"></div>
</template>
<script>
import base from '../lib/base'
import "pdfh5/css/pdfh5.css";
import Pdfh5 from "pdfh5";
import axios from 'axios';
export default {
    mixins: [base],
    name: 'pdfReader',
    props: {
        url:{
            type:String,
            default:''
        }
    },

    components: {

    },
    watch:{
        url:{
            handler(val){
                console.error('pdfReader url',val)
                if(val){
                    this.$nextTick(()=>{
                        this.loadPdf()
                    })
                }
            },
            immediate:true
        }
    },
    data(){
        return {
            pdfh5: null,
        }
    },

    async created(){


    },
    mounted(){

    },
    beforeDestroy(){
        if(this.pdfh5){
            this.pdfh5.destroy()
        }
    },
    methods:{
        loadPdf(){
            let fileUrl = this.url
            axios
                .get(fileUrl, {
                    responseType: 'arraybuffer'
                })
                .then(res => {
                    this.pdfh5 = new Pdfh5('#pdfvuer', {
                        data: res.data,
                    });
                    //监听完成事件
                    this.pdfh5.on("complete", function (status, msg, time) {
                        console.log("状态：" + status + "，信息：" + msg + "，耗时：" + time + "毫秒，总页数：" + this.totalNum);
                    });
                });
        }
    }
}
</script>
<style lang="scss">
.pdf_reader_page{
    width: 100%;
    height: 100%;
    background: #e7eff2;
    display:flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
    padding:10px 10px 0;
    .instruction_manual_header{
        height: 48px;
        display: flex;
        align-items: center;
        .instruction_manual_back_btn{
            cursor: pointer;
            padding: 0 40px;
            text-align: center;
            text-shadow: 1px 1px 1px rgb(255 255 255 / 22%);
            font: bold 12px/40px Arial, sans-serif;
            border-radius: 30px;
            box-shadow: 1px 1px 1px rgb(0 0 0 / 29%), inset 1px 1px 1px rgb(255 255 255 / 44%);
        }
        .instruction_manual_title{
            margin-left: 20px;
        }
    }
}
</style>

