<template>
    <transition name="slide" appear @after-enter="transitionAfterEnter" @after-leave="transitionAfterLeave">
        <div
            class="chat_window second_level_page"
            ref="chatWindow"
            :class="{ is_ios: isIOS, 'live_room_fullscreen': isLiveRoomFullScreen }"
            @touchmove="touchChatWindow"
        >
            <div class="column_container">
                <LiveRoomWeb
                    :groupTitle="currentSubject"
                    ref="chatWindowLiveRoomWeb"
                    v-if="isRealBrowser && showLiveRoom"
                    @joinChannelAux="HandleJoinChannelAux"
                    @leaveChannelAux="HandleLeaveChannelAux"
                ></LiveRoomWeb>
                <LiveRoom
                    :groupTitle="currentSubject"
                    ref="chatWindowLiveRoom"
                    v-else-if="showLiveRoom"
                    @joinChannelAux="HandleJoinChannelAux"
                    @leaveChannelAux="HandleLeaveChannelAux"
                ></LiveRoom>
                <mrHeader v-show="!isConferenceAuxOnline">
                    <!-- 自定义标题 -->
                    <template #title>
                        <div class="chat_window_title">
                            <p class="longwrap">{{ currentSubject }}</p>
                            <span v-if="!conversation.is_single_chat">({{ groupLength }})</span>
                        </div>
                    </template>
                    <!-- 自定义右侧内容 -->
                    <template #right>
                        <span v-show="isShowExamType" class="transfer_panel" @click="togglePageType">
                            <i class="iconfont svg_icon_transfer icon-switch_check_model" v-show="examPageType"></i>
                            <i class="iconfont svg_icon_transfer icon-switch_chat_model" v-show="!examPageType"></i>
                        </span>
                        <span class="more_panel" @click="clickMore">
                            <svg class="svg_icon_more">
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-more"></use>
                            </svg>
                        </span>
                    </template>
                </mrHeader>
                <div class="network_unavailable" v-if="loadingConfig.networkUnavailable">
                    <i class="icon iconfont icon-warning-o"></i>
                    {{ lang.network_unavailable }}
                </div>
                <div class="unfinished_homework" v-if="!conferenceState && unfinishedHomework" @click="openCloudExam">
                    <p>
                        <i class="iconfont icon-1guangbo"></i>{{ lang.cloud_exam }}:{{
                            unfinishedHomework.assignmentInfo.paperInfo.title
                        }}
                    </p>
                    <i class="iconfont icon-close1" @click="clearUnfinishedHomework"></i>
                </div>
                <div
                    class="unfinished_homework"
                    v-if="!conferenceState && correctedHomework"
                    @click="openCloudExamCompleted"
                >
                    <p>
                        <i class="iconfont icon-1guangbo"></i>{{ lang.cloud_exam }}<span>"{{ correctedHomework }}"</span
                        >{{ lang.corrected }}
                    </p>
                    <i class="iconfont icon-close1" @click="clearCorrectedHomework"></i>
                </div>
                <template v-if="isTransitionEnd">
                    <div v-show="!examPageType" class="second_level_content chat_window_container">
                        <!-- <div class="videos">
                            <audio v-for="(item,index) of realtimeVoiceAudios" :ref="'audio_'+index" autoplay="autoplay" :id="item.volume==0?'self_sound':item.audio_id" :key="index"></audio>
                        </div> -->
                        <!-- <div v-if="unreadNumber>0" @click="toUnreadMsg" class="unread_number_btn">{{lang.skip_to_unread_text}}</div> -->
                        <!-- v-if="conferenceState&&!isConferenceAuxOnline"-->
                        <div
                            class="realtime_voice_operate"
                            @click.stop="onClickRealtimeToolBar()"
                            v-show="functionsStatus.live && conferenceState && !isConferenceAuxOnline"
                        >
                            <div class="realtime_voice_callout">
                                <div class="operate">
                                    <i class="iconfont svg_icon_accept_dial icon-phone-"></i>
                                    <p class="operate-text">{{ lang.tap_to_join_conference }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="join_verify_tip" @click="openJoinVerify" v-if="conversation.applyCount > 0">
                            <i class="iconfont icon-user_add"></i>
                            <p>{{ lang.group_join_verify_btn }}（{{ conversation.applyCount }}）</p>
                        </div>
                        <div @click="hideAllOperateBar" class="chat_content">
                            <chat-message-list
                                :chatMessageList="chatMessageList"
                                @loadTopHistory="loadTopHistory"
                                @reEditMessage="reEditMessage"
                                @getTargetCountList="getTargetCountList"
                                ref="chatMessageListCom"
                                :options="chatListOptions"
                                :chatType="CHAT_TYPE.CHAT_WINDOW"
                            ></chat-message-list>
                        </div>
                        <div v-show="isRecording" class="record_modal">
                            <div v-show="!isCancelRecording" class="recording_sound_tip">
                                <p class="time">{{ soundingTime + " s" }}</p>
                                <i v-if="countDown > 10" class="iconfont svg_icon_speak icon-androidmicrophone"></i>
                                <!-- <img v-if="countDown>10" src="static/resource/images/recording.gif"> -->
                                <span v-else>{{ countDown }}</span>
                                <p class="cancel_text">{{ lang.recording_sound_text }}</p>
                            </div>
                            <div v-show="isCancelRecording" class="cancel_record_sound_tip">
                                <!-- <p class="time">{{soundingTime+' s'}}</p> -->
                                <i v-if="countDown > 10" class="iconfont svg_icon_speak icon-androidmicrophoneoff"></i>
                                <!-- <img src="static/resource/images/record_cancel.png"> -->
                                <p class="cancel_text">{{ lang.cancel_recording_text }}</p>
                            </div>
                        </div>
                    </div>
                    <div v-show="!examPageType" @touchmove="isPreventScroll" class="chat_window_foot" ref="footer">
                        <div class="rainbow_row clearfix">
                            <div class="block1 fl"></div>
                            <div class="block2 fl"></div>
                            <div class="block3 fl"></div>
                            <div class="block4 fl"></div>
                            <div class="block5 fl"></div>
                        </div>
                        <!-- 添加引用消息显示区域 -->
                        <div v-if="quote_message" class="quoted-message-container">
                            <span class="quote-close" @click="clearQuotedMessage">×</span>
                            <div class="quote-content">
                                <span class="quote-text" v-html="quotedMessageText"></span>
                            </div>
                        </div>
                        <div class="message_input_container" @touchmove.stop>
                            <div class="content_left">
                                <i
                                    class="iconfont svg_icon_sound icon-sound1"
                                    v-show="isShowSoundBtn"
                                    @click="toggleMessageType(1)"
                                ></i>
                                <i
                                    class="iconfont svg_icon_keyboard icon-keyboard1"
                                    v-show="isShowKeyboardBtn"
                                    @click="toggleMessageType(2)"
                                ></i>
                            </div>
                            <div class="content_text" v-show="isShowSoundBtn">
                                <textarea
                                    @input="changeText"
                                    v-model="messageText"
                                    ref="message_text"
                                    @focus="prepareInput"
                                    @blur="exitInput"
                                    @paste="formatPaste"
                                    id="message_text"
                                    @compositionend="handleCompositionend"
                                    @compositionstart="handleCompositionstart"
                                ></textarea>
                                <div class="hidden_text">{{ messageText }}</div>
                            </div>
                            <div
                                :class="['hold_to_talk', { speak_up: isRecording }]"
                                v-show="isShowKeyboardBtn"
                                @touchstart="recordStart"
                                @touchend="recordEnd"
                                @touchcancel="touchCancel"
                                @touchmove="recordCancel"
                            >
                                {{ isRecording ? lang.send_recoding_text : lang.hold_to_talk }}
                            </div>
                            <div class="content_right">
                                <i class="iconfont svg_icon_emoji icon-smile1" @click.stop="showEmoji"></i>
                                <i
                                    class="iconfont svg_icon_more icon-plus2"
                                    v-show="!isShowSendBtn"
                                    @click.prevent.stop="showOperate"
                                ></i>
                                <i
                                    class="iconfont svg_icon_send send_btn android_send_btn icon-fasong"
                                    v-show="isShowSendBtn"
                                    @click.prevent.stop="handleSendMessage"
                                ></i>
                            </div>
                        </div>
                        <div class="message_operate_container" @touchmove.stop>
                            <div class="emoji_page" v-show="isShowEmoji">
                                <span
                                    @click.stop="appendEmoji(emoji)"
                                    v-for="(emoji, index) in emojiArr"
                                    :key="index"
                                    class="emoji"
                                >
                                    {{ emoji }}
                                </span>
                            </div>
                            <div class="operate_page" v-show="isShowOperate">
                                <div class="operate_item" @click.stop="uploadPicture">
                                    <img src="static/resource/images/pics.png" alt="" srcset="" />
                                    <p class="longwrap">{{ lang.chat_upload_text }}</p>
                                    <input
                                        :key="file_tag"
                                        type="file"
                                        id="upload_picture"
                                        accept="image/*,video/*,capture=camera"
                                        @change="uploadPictureStart($refs.upload_picture_input.files)"
                                        multiple="multiple"
                                        @click.stop="callFileList"
                                        style="display: none"
                                        ref="upload_picture_input"
                                    />
                                </div>
                                <div
                                    class="operate_item"
                                    @click.stop="callLiveConference($event)"
                                    v-show="isShowCallVideoBtn"
                                >
                                    <img src="static/resource/images/real_time_ultrasound.png" alt="" srcset="" />
                                    <p class="longwrap">{{ lang.conference_seeding }}</p>
                                </div>
                                <div class="operate_item" @click.stop="openCloudExam" v-if="!conferenceState">
                                    <img src="static/resource/images/homework.png" alt="" srcset="" />
                                    <p class="longwrap">{{ lang.cloud_exam }}</p>
                                    <span v-if="unfinishedHomework || uncorrectedHomework || showCorrectedUnread" class="unread"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-show="isIOS" ref="keyboardBox" style="flex-shrink: 0; height: 0"></div>
                    <exam-mode-page v-show="examPageType" ref="examModePage"></exam-mode-page>
                    <div v-if="showQrcode" class="qrcode_dialog">
                        <div class="qrcode_container">
                            <div id="live_qr_addr_connent"></div>
                            <div @click="shareLink" class="qrcode_btn" v-if="isShowShareWeChatBtn">
                                {{ lang.share_link_to_wx }}
                            </div>
                            <div @click="closeQrcode" class="qrcode_btn">{{ lang.cancel_btn }}</div>
                        </div>
                    </div>
                    <template v-if="isShowMention">
                        <mention-dialog :show.sync="isShowMention" :callback="mentionCb"></mention-dialog>
                    </template>
                </template>
            </div>
            <keep-alive :include="/group/">
                <router-view></router-view>
            </keep-alive>
            <loading-page v-if="showLoadingPage"></loading-page>
        </div>
    </transition>
</template>
<script>
import base from "../lib/base";
import iworksTool from "../lib/iworksTool";
import record from "../lib/record_sound.js";
import send_message from "../lib/send_message.js";
import examModePage from "../components/examModePage.vue";
import mentionDialog from "../components/mentionDialog.vue";
import { Toast } from "vant";
import Tool from "@/common/tool.js";
import chatMessageList from "../components/chatMessageList.vue";
import share_to_wechat from "../lib/share_to_wechat";
import LoadingPage from "../components/loadingPage.vue";
import { cloneDeep } from "lodash";
import {
    getLiveRoomObj,
    setIworksInfoToMsg,
    setWithDrawData,
    setExpirationResource,
    parseImageListToLocal,
    patientDesensitization,
    transferPatientInfo,
} from "../lib/common_base";
import { EMOJI_LIST, CHAT_TYPE } from "../lib/constants";
// 添加异步导入
const LiveRoom = () => import("../components/live/liveRoom.vue");
const LiveRoomWeb = () => import("../components/live/liveRoomWeb.vue");

export default {
    mixins: [base, record, send_message, iworksTool, share_to_wechat],
    name: "chatWindow",
    components: {
        examModePage,
        mentionDialog,
        chatMessageList,
        LoadingPage,
        LiveRoom,
        LiveRoomWeb,
    },
    data() {
        return {
            CHAT_TYPE: CHAT_TYPE,
            show: false,
            isShowEmoji: false,
            isShowSoundBtn: true,
            isShowKeyboardBtn: false,
            isShowOperate: false,
            messageText: "",
            messageTextOld: "",
            faceList: [
                ["01", "02", "03", "04", "05", "06", "07"],
                ["08", "09", "10", "11", "12", "13", "14"],
                ["15", "16", "17", "18", "19", "20", "21"],
                ["22", "23", "24", "25", "26", "27", "28"],
                ["29", "30", "31", "32", "33", "34", "35"],
                ["36", "37", "38", "39", "40", "41", "42"],
                ["43", "44", "45", "46", "47", "48", "49"],
                ["50", "51", "52", "53", "54", "55", "56"],
                ["57", "58", "59", "60", "", "", ""],
            ],
            examPageType: false,
            isIOS: false,
            file_tag: 0,
            showQrcode: false,
            isShowMention: false,
            unreadNumber: 0,
            unreadCid: 0,
            debounceType: 3,
            resourceIdList: [], // 现有资源id列表
            resourceMsgTypeList: [], // 可显示的引用资源类型
            isNeedLoadResourceStatus: false, // 是否需要更新引用资源为失效类型
            chatMessageList: [],
            oldChatMessageList: [],
            keyboardHeight: 313,
            keyboardUpTimer: null,
            keyboardDownTimer: null,
            isSendingKeyBoardFlag: false,
            chatListOptions: {
                isLoadTop: true, //是否启用下拉加载
                isLoadBottom: false, //是否启用上拉加载
            },
            lastCid: 0,
            action: "", // 进入页面之后的动作
            tipsOpenMute: false, //提醒是否开启麦克风
            debounceClearConversationStatus: Tool.debounce(this.clearConversationStatus, 500),
            isConferenceAuxOnline: 0, //是否直播中（辅流在线代表在线）
            localStreamStatus: {}, //本地流状态
            isTransitionEnd: false,
            pageInfo: {
                currentPage: 1,
                pageSize: 20,
            },
            initCtr: false,
            cid: 0,
            record_mode: true,
            live_addr: "",
            isInputLock: true,
            event: {},
            atUser: [],
            liveRoom: {},
            manualSending: false,
            oldChatMessageListLength: 0,
            emojiArr: EMOJI_LIST,
            showLiveRoom: false,
            quote_message: null, // 引用的消息
            loadTargetCountList:false,
        };
    },
    computed: {
        conversation() {
            this.showUnreadNumberBtn();
            return this.conversationList[this.cid] || {};
        },
        controller() {
            return this.conversation.socket;
        },
        isShowSendBtn() {
            return (this.messageText == "" ? false : true) && this.isShowSoundBtn && this.isInputLock;
        },
        groupLength() {
            let list = this.parseObjToArr(this.conversation.attendeeList);
            let filterList = []; //后端把所有用户都返回回来，前端只显示未退群用户
            for (let i = 0; i < list.length; i++) {
                if (list[i].attendeeState != 0) {
                    filterList.push(list[i]);
                }
            }
            return filterList.length;
        },
        deviceCtrl() {
            return this.$store.state.deviceCtrl;
        },
        isService() {
            return this.conversation.service_type != 0;
        },
        isShowExamType() {
            return (
                this.conversation.service_type == this.systemConfig.ServiceConfig.type.FileTransferAssistant ||
                this.conversation.service_type == 0 ||
                this.conversation.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze ||
                this.conversation.service_type == this.systemConfig.ServiceConfig.type.DrAiAnalyze
            );
        },
        isShowCallVideoBtn() {
            let isShow = false;
            if (this.functionsStatus.live && !this.conferenceState) {
                if (
                    !this.isService ||
                    this.conversation.service_type === this.systemConfig.ServiceConfig.type.LiveBroadCast ||
                    this.conversation.service_type === this.systemConfig.ServiceConfig.type.AiAnalyze
                ) {
                    isShow = true;
                }
                // else if(this.conversation.service_type === this.systemConfig.ServiceConfig.type.AiAnalyze){
                //     if(this.isUltraSoundMobile){
                //         isShow = true
                //     }
                // }
            }
            return isShow;
        },
        // examList(){
        //     this.updateChatMessageListByExamList(this.$store.state.examList)
        //     return this.$store.state.examList
        // },
        messageList() {
            return this.conversation.chatMessageList || [];
        },
        attendeeArray() {
            let list = this.parseObjToArr(this.conversation.attendeeList);
            let filterList = [];
            let onlineList = [];
            let offlineList = [];
            //后端把所有用户都返回回来，前端只显示未退群用户
            for (let i = 0; i < list.length; i++) {
                if (list[i].attendeeState != 0) {
                    if (list[i].state == 1) {
                        onlineList.push(list[i]);
                    } else {
                        offlineList.push(list[i]);
                    }
                }
            }
            filterList = onlineList.concat(offlineList);
            return filterList;
        },
        remarkMap() {
            return this.$store.state.friendList.remarkMap;
        },
        conferenceState() {
            let conferenceState =
                this.$store.state.liveConference[this.cid] &&
                this.$store.state.liveConference[this.cid].conferenceState;
            // if (!conferenceState) {
            //     if (!this.dissolveLiveDialogVisible) {
            //         if(Tool.checkMobileCanCloseDialog()){

            //             Tool.closeMobileDialog()
            //         }
            //     }
            // }
            return conferenceState;
        },
        //eslint-disable-next-line
        group_title() {
            if (this.conversation.service_type === 0 || this.conversation.service_type === 104) {
                return this.remarkMap[this.conversation.fid] || this.conversation.subject;
            } else if (this.conversation.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze) {
                return this.lang.ai_analyze;
            } else if (this.conversation.service_type == this.systemConfig.ServiceConfig.type.DrAiAnalyze) {
                return this.lang.dr_ai_analyze;
            } else if (this.conversation.service_type == this.systemConfig.ServiceConfig.type.FileTransferAssistant) {
                return this.lang.file_transfer_assistant;
            }
        },
        isUltraSoundMobile() {
            return this.$store.state.device.isUltraSoundMobile;
        },
        isShowShareWeChatBtn() {
            const isCE = process.env.VUE_APP_PROJECT_NOV === "CE";
            const isPlus = this.isApp;
            return !this.$store.state.device.isUltraSoundMobile && !isCE && isPlus;
        },
        currentSubject() {
            if (this.conversation.service_type === 0 || this.conversation.service_type === 104) {
                return this.remarkMap[this.conversation.fid] || this.conversation.subject;
            } else if (this.conversation.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze) {
                return this.lang.ai_analyze;
            } else if (this.conversation.service_type == this.systemConfig.ServiceConfig.type.DrAiAnalyze) {
                return this.lang.dr_ai_analyze;
            } else if (this.conversation.service_type == this.systemConfig.ServiceConfig.type.FileTransferAssistant) {
                return this.lang.file_transfer_assistant;
            } else {
                return "";
            }
        },
        loadingConfig() {
            return this.$store.state.loadingConfig;
        },
        isRealBrowser() {
            return this.systemConfig.clientType === 5;
        },
        isLiveRoomFullScreen() {
            // 检测直播间是否处于全屏状态
            if (this.isRealBrowser && this.$refs.chatWindowLiveRoomWeb) {
                return this.$refs.chatWindowLiveRoomWeb.isFullScreenByManual;
            } else if (!this.isRealBrowser && this.$refs.chatWindowLiveRoom) {
                return this.$refs.chatWindowLiveRoom.isFullScreenByManual;
            }
            return false;
        },
        isTEAir() {
            return this.$store.state.device.isTEAir;
        },
        showLoadingPage() {
            return this.isTEAir && !this.isConferenceAuxOnline;
        },
        isManager() {
            let result = false;
            let list = this.parseObjToArr(this.conversation.attendeeList);
            for (let item of list) {
                if (item.role == this.systemConfig.groupRole.manager) {
                    if (item.userid === this.user.uid) {
                        result = true;
                        break;
                    }
                }
            }
            return result;
        },
        isCreator() {
            return this.user.uid == this.conversation.creator_id;
        },
        isHost() {
            return this.LiveConferenceData.isHost;
        },
        LiveConferenceData() {
            return (
                (this.$store.state.liveConference[this.cid] &&
                    this.$store.state.liveConference[this.cid].LiveConferenceData) ||
                {}
            );
        },
        unfinishedHomework() {
            return this.$store.state.homework.conversationUnfinish[this.cid];
        },
        correctedHomework() {
            const homework = this.$store.state.homework.conversationCorrected[this.cid];
            if (
                Array.isArray(homework) &&
                homework.length > 0 &&
                homework[0].assignmentInfo &&
                homework[0].assignmentInfo.paperInfo
            ) {
                return homework[0].assignmentInfo.paperInfo.title;
            } else if (homework && homework.assignmentInfo && homework.assignmentInfo.paperInfo) {
                return homework.assignmentInfo.paperInfo.title;
            }
            return "";
        },
        uncorrectedHomework() {
            return this.$store.state.homework.conversationUnCorrect[this.cid];
        },
        showCorrectedUnread(){
            if (this.cid) {
                return this.$store.state.homework.conversationCorrected[this.cid] !== undefined
            }else{
                return this.$store.state.homework.globalCorrected !== undefined && this.$store.state.homework.globalCorrected > 0
            }
        },
        /**
         * 处理引用消息的显示文本
         * @returns {string} 格式化后的引用消息文本
         */
        quotedMessageText() {
            if (!this.quote_message) {
                return "";
            }

            const nickname = this.quote_message.nickname;
            let content = "";

            // 判断消息类型并设置对应的内容
            if (
                [
                    this.systemConfig.msg_type.Image,
                    this.systemConfig.msg_type.Frame,
                    this.systemConfig.msg_type.OBAI,
                ].includes(this.quote_message.msg_type)
            ) {
                content = `${this.lang.msg_type_image}`;
            } else if (
                [
                    this.systemConfig.msg_type.Video,
                    this.systemConfig.msg_type.Cine,
                    this.systemConfig.msg_type.RealTimeVideoReview,
                    this.systemConfig.msg_type.VIDEO_CLIP,
                ].includes(this.quote_message.msg_type)
            ) {
                content = `${this.lang.msg_type_video}`;
            } else {
                content = this.quote_message.msg_body || "";
            }

            return `${nickname} :  ${content}`;
        },
    },
    watch: {
        messageText(newValue, oldValue) {
            if (!this.conversation.is_single_chat) {
                if (newValue.length !== 0 && oldValue.length > newValue.length) {
                    let atReg = /@[\w\u4e00-\u9fa5]+\s/g;
                    let atList = [];
                    let atObj = {};
                    // @匹配
                    let atMatch = atReg.exec(oldValue);
                    while (atMatch) {
                        atObj.name = atMatch[0];
                        atObj.index = atMatch.index;
                        atList.push(atObj);
                        atObj = {};
                        atMatch = atReg.exec(oldValue);
                    }
                    let afterStr = "";
                    let count = 0;
                    if (atList.length > 0 && oldValue.slice(0, atList[0].index) == newValue.slice(0, atList[0].index)) {
                        for (let i = 0; i < atList.length; i++) {
                            let preIndex = atList[i - 1] ? atList[i - 1].index : 0;
                            let preName = atList[i - 1] ? atList[i - 1].name : "";
                            if (newValue.indexOf(atList[i].name, preIndex + preName.length) == -1) {
                                let index = atList[i].index;
                                let name = atList[i].name;
                                let length = atList[i].name.length;
                                let preStr = oldValue.slice(0, index);
                                let behindStr = oldValue.slice(index + length);
                                afterStr = preStr + behindStr;
                                this.atUser = this.atUser.filter(
                                    (item) => atList[i].name.indexOf(item.nickname) === -1
                                );
                                break;
                            }
                            count++;
                        }
                        if (count != atList.length) {
                            this.messageText = afterStr;
                            this.messageTextOld = this.messageText;
                        }
                    }
                }
            }
        },
        messageList: {
            handler(value) {
                if (this.$route.path.includes("chat_window")) {
                    this.updateChatMessageListByConversation(value);
                }
            },
            deep: true,
        },
        LiveConferenceData: {
            handler(value) {
                if (!value) {
                    return;
                }
                if (value.joinedAux) {
                    this.isConferenceAuxOnline = 1;
                } else {
                    this.isConferenceAuxOnline = 0;
                }
            },
            immediate: true,
            deep: true,
        },
        "$store.state.examList": {
            handler(value) {
                this.updateChatMessageListByExamList(value);
            },
            deep: true,
            immediate: true,
        },
    },
    created() {
        const msgType = this.$store.state.systemConfig.msg_type;
        this.resourceMsgTypeList = [msgType.Image, msgType.Video, msgType.COMMENT, msgType.TAG, msgType.Frame];
        this.pageInfo.pageSize = this.systemConfig.historyMessageNum;
    },
    activated() {
        this.$nextTick(() => {
            var that = this;
            this.initAudioContext();
            this.$root.eventBus.$off("scrollChatWindow").$on("scrollChatWindow", that.shouldScrollBottom);
            // this.$root.eventBus.$off('on_close_realtime_voice').$on('on_close_realtime_voice',that.on_close_realtime_voice);
            this.$root.eventBus
                .$off("on_force_stop_realtime_voice")
                .$on("on_force_stop_realtime_voice", that.on_force_stop_realtime_voice);
            this.$root.eventBus.$off("NotifyPhotoLibraryAccess").$on("NotifyPhotoLibraryAccess", (access_code) => {
                if (access_code) {
                    Toast(this.lang.reselect_upload_file);
                }
            });

            this.$root.eventBus.$off("notifySoundRecord").$on("notifySoundRecord", that.notifySoundRecord);
            this.$root.eventBus.$off("chatWindowKeyboardChanged").$on("chatWindowKeyboardChanged", (data) => {
                //IOS唤起软键盘后调整页面高度防止顶部条被顶出视口
                let activeInput = document.getElementById("message_text");

                if (activeInput !== document.activeElement) {
                    return;
                }
                // console.error('****************chatWindow-chatWindow',this.osName)
                if (this.isIOS && this.isApp) {
                    let keyboardHeight = data.height;
                    // iOS时才会调用此接口，android也已经实现，但不调用。
                    if (!data.keyboard) {
                        // this.$refs.keyboardBox.style.height = 0 + 'px'
                        return;
                    }
                    try {
                        Tool.createCWorkstationCommunicationMng({
                            name: "getModel",
                            emitName: "NotifyGetModel",
                            params: {},
                            timeout: null,
                        }).then((res) => {
                            let iphoneModels = ["iPhone X", "iPhone XS", "iPhone XS MAX", "iPhone XR"];
                            if (res.error_code == "0" && iphoneModels.indexOf(res.data) >= 0) {
                            }
                            this.keyboardHeight = keyboardHeight + 15;
                            this.$refs.keyboardBox.style.height = this.keyboardHeight + 10 + "px";
                        });
                    } catch (error) {
                        Toast(error);
                    }
                }
            });
            this.$root.eventBus.$off("setPageType").$on("setPageType", (cid) => {
                console.log("setPageType");
                if (this.cid != cid) {
                    return;
                }
                if (this.examPageType) {
                    //在会诊列表页面断线重连
                    this.$refs.examModePage.init();
                } else {
                    //首次打开会话
                    if (that.conversation.view_mode == 1) {
                        that.togglePageType();
                    }
                }
            });

            this.$root.eventBus.$off("unlockContentEditable").$on("unlockContentEditable", () => {
                let message_text = this.$refs.message_text;
                // message_text.contentEditable=true;
            });
            this.$root.eventBus.$off("shouldTogglePageType").$on("shouldTogglePageType", this.shouldTogglePageType);
            this.$root.eventBus.$off("attachStream").$on("attachStream", (stream, domId) => {
                this.$nextTick(() => {
                    window.rtc.attachStream(stream, domId);
                });
            });
            this.$root.eventBus.$off("setStartVoiceMsg").$on("setStartVoiceMsg", this.setStartVoiceMsg);
            this.$root.eventBus.$off("setStopVoiceMsg").$on("setStopVoiceMsg", this.setStopVoiceMsg);
            this.$root.eventBus.$off("updateStartVoiceMsg").$on("updateStartVoiceMsg", this.updateStartVoiceMsg);
            this.$root.eventBus.$off("deleteStartVoiceMsg").$on("deleteStartVoiceMsg", this.deleteStartVoiceMsg);
            this.$root.eventBus.$off("notifyStopPlay").$on("notifyStopPlay", this.notifyStopPlay);
            this.$root.eventBus
                .$off("chatWindowStartJoinRoom")
                .$on("chatWindowStartJoinRoom", this.chatWindowStartJoinRoom);
            this.$root.eventBus.$off("callLiveConference").$on("callLiveConference", this.callLiveConference);
            this.$root.eventBus.$off("receiveConference").$on("receiveConference", this.onClickRealtimeToolBar);
            this.$root.eventBus
                .$off("refreshConversationSuccessToChatWindow")
                .$on("refreshConversationSuccessToChatWindow", () => {});
            this.$root.eventBus.$off("saveAiResultWithImage").$on("saveAiResultWithImage", this.saveAiResultWithImage);
            this.$root.eventBus.$off("sendImageWithAiReport").$on("sendImageWithAiReport", this.sendImageWithAiReport);
            this.$root.eventBus.$off('notifyWithdrawChatMessage').$on('notifyWithdrawChatMessage',this.handleWithdrawChatMessage);
            this.$root.eventBus.$off('notifyDeleteChatMessages').$on('notifyDeleteChatMessages',this.handleDeleteChatMessages);
        });
    },
    deactivated() {},
    mounted() {
        console.log("22");
        window.addEventListener("resize", this.setDirection, false);
        if (this.osName == "ios") {
            this.isIOS = true;
            if (this.isIOS && !Tool.checkAppClient("Browser")) {
                document.body.addEventListener("focusin", this.keyBoardOpenFromIos);
                document.body.addEventListener("focusout", this.keyBoardCloseFromIos);
            }
        }
        // setTimeout(()=>{
        // this.$nextTick(()=>{
        setTimeout(() => {
            this.operatePushStreamByULinkerLive();
        }, 800);
        // 监听引用消息事件
        this.$root.eventBus.$off("quoteMessage").$on("quoteMessage", this.handleQuoteMessage);
    },
    beforeRouteEnter(to, from, next) {
        next();
    },
    beforeRouteLeave(to, from, next) {
        // if (Tool.checkMobileDialogShow() && Tool.checkMobileCanCloseOnPopstate()) {
        //     if (Tool.checkMobileCanCloseDialog()) {
        //         Tool.closeMobileDialog();
        //     }
        //     next(false);
        //     return;
        // }
        this.closeChatWindow(to, from, next);
    },
    beforeDestroy() {
        window.removeEventListener("resize", this.setDirection);
        if (this.isIOS) {
            document.body.removeEventListener("focusin", this.keyBoardOpenFromIos);
            document.body.removeEventListener("focusout", this.keyBoardCloseFromIos);
        }
    },
    methods: {
        operatePushStreamByULinkerLive() {
            let globalParams = this.$store.state.globalParams;
            if (
                this.conversation &&
                globalParams.operateByULinker.action &&
                globalParams.operateByULinker.action == "start"
            ) {
                let mainstreamType = globalParams.operateByULinker.mainstreamType;
                mainstreamType = mainstreamType.toLowerCase();
                let now_cid = this.$route.params.cid;
                let isAIAnalyzeLive = this.conversation.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze;

                const mobile_errors = window.CWorkstationCommunicationMng.CLiveConferenceBridgeErrorKey();
                if (now_cid && now_cid == globalParams.operateByULinker.cid) {
                    this.chatWindowStartJoinRoom(
                        { main: 1, aux: 1, isSender: 1, videoSource: mainstreamType, isAIAnalyzeLive },
                        (success, msg) => {
                            window.CWorkstationCommunicationMng.NotifyStartOrStopConferenceResult({
                                errorcode: success ? 0 : 2,
                                errormsg: success ? "" : mobile_errors.CommonError.JOIN_LIVE_ROOM_FAIL_ERROR,
                            });
                        }
                    );
                }
                this.$store.commit("globalParams/updateGlobalParams", {
                    operateByULinker: {},
                });
            }
        },
        keyBoardOpenFromIos() {
            if (!this.isIOS || this.isApp) {
                return;
            }
            if (!this.isApp) {
                this.keyboardHeight = 250;
            }

            let activeInput = document.getElementById("message_text");

            if (activeInput !== document.activeElement) {
                return;
            }
            if ("chat_window" == window.vm.$route.name) {
                //针对ios方案一
                if (!this.isSendingKeyBoardFlag) {
                    document.querySelector(".chat_window_container").style.visibility = "hidden"; // 解决ios兼容性问题
                }
                setTimeout(() => {
                    this.$refs.keyboardBox.style.height = this.keyboardHeight + 10 + "px";
                    document.querySelector(".chat_window_container").style.visibility = "visible"; // 解决ios兼容性问题
                    // this.$refs.chatMessageListCom.scrollToBottom()
                    this.isSendingKeyBoardFlag = false;
                }, 0);
            }
        },
        keyBoardCloseFromIos() {
            if (!this.isIOS) {
                return;
            }
            if (this.isApp) {
                this.isInputLock = true;
                return;
            }
            let activeInput = document.getElementById("message_text");

            if (activeInput !== document.activeElement) {
                return;
            }
            if ("chat_window" == window.vm.$route.name) {
                //针对ios方案一
                setTimeout(() => {
                    this.$refs.keyboardBox.style.height = 0;
                }, 100);
            }
        },
        reEditMessage(msg) {
            let reg = /<img[^>]*src[=\''\'\s]+[^\.]*\/([^\d]+)0?([^\D]+)\.[^\''\']+[\''\']?[^>]*>/gi;
            msg = msg.replace(reg, "[emoji:$2]");
            this.messageText += msg;
            this.messageTextOld = this.messageText;
        },
        setDirection() {
            // 屏幕旋转会导致首页swiper异常 ，强行在旋转后将TAB设置为chat
            this.$root.eventBus.$emit("toggleTab", "chat");
        },
        touchCancel() {
            let isXiaomi = navigator.userAgent.toLowerCase().match(/mi\s/i) == "mi ";
            // console.log('============>touchCancel触发,isXiaomi:',isXiaomi)
            if (isXiaomi) {
                this.recordEnd();
            } else {
                this.recordEnd();
            }
        },
        async initPage() {
            this.cid = this.$route.query.cid || this.$route.params.cid;
            this.$root.eventBus.$on(`${this.cid}_gateway_connect`, this.gateWayConnect);
            await Tool.handleAfterConversationCreated(this.cid);
            this.getDeviceList(this.cid);
            this.isShowEmoji = false;
            this.isShowOperate = false;
            this.examPageType = false;
            this.initCtr = false;
            if (this.conversation.view_mode == 1) {
                this.togglePageType();
            }
            // this.animationTimer = setTimeout(()=>{
            //     this.animationTimer = null

            // },200)
            console.log("initPage");
            let list =
                this.$store.state.conversationList[this.cid] &&
                this.$store.state.conversationList[this.cid].chatMessageList;
            if (this.cid != this.lastCid) {
                this.messageText = "";
                this.messageTextOld = this.messageText;
                if (this.$refs.message_text) {
                    this.$refs.message_text.innerHTML = "";
                }
                if (this.$store.state.conversationList.hasOwnProperty(this.cid)) {
                    this.updateChatMessageListByConversation(list, "init");
                }
            } else {
                if (list.length === this.oldChatMessageList.length) {
                    this.chatMessageList = this.oldChatMessageList;
                    if (!this.manualSending) {
                        this.shouldScrollBottom(false, 0);
                    }
                } else {
                    this.updateChatMessageListByConversation(list, "init");
                }
            }
            if (this.conversationList[this.cid].is_need_reload) {
                this.reloadHistoryList();
            } else {
                this.loadTopHistory();
            }
            setTimeout(() => {
                this.operatePushStreamByULinkerLive();
            }, 800);
        },
        showEmoji() {
            //显示表情界面
            this.toggleMessageType(2);
            let dom = this.$refs.message_text;
            this.selectionStart = dom.selectionStart;
            this.isShowEmoji = true;
            this.hideOperatePanel();
            this.shouldScrollBottom(true);
        },
        hideEmoji() {
            //隐藏表情界面
            this.isShowEmoji = false;
        },
        changeText(event) {
            if (this.isInputLock) {
                setTimeout(() => {
                    this.messageText = this.$refs.message_text.value;
                    this.messageTextOld = this.messageText;
                    // this.messageText=this.$refs.message_text.innerHTML;
                    // this.shouldScrollBottom();注释可解决IOS输入闪烁问题
                    if (!this.conversation.is_single_chat) {
                        if (event.data == "@") {
                            this.editRange = window.getSelection().getRangeAt(0);
                            let dom = this.$refs.message_text;
                            this.selectionStart = dom.selectionStart;
                            dom.blur();
                            this.isShowMention = true;
                        }
                    }
                }, 0);
            } else {
                this.messageText = this.messageTextOld;
            }
        },
        handleCompositionend() {
            this.isInputLock = true;
        },
        handleCompositionstart() {
            this.isInputLock = false;
        },
        prepareInput(e) {
            var container = document.querySelector(".message_list_container");
            var that = this;
            this.hideEmoji();
            this.hideOperatePanel();
            if (this.isIOS) {
                setTimeout(() => {
                    let footer = this.$refs["footer"];
                    footer.scrollIntoView && footer.scrollIntoView(false);
                }, 150);
            }
            this.shouldScrollBottom(true);
        },
        exitInput() {
            setTimeout(() => {
                if (this.$refs.keyboardBox) {
                    this.$refs.keyboardBox.style.height = 0;
                }
                if (Tool.checkAppClient("IOS") && !Tool.checkAppClient("Browser")) {
                    this.shouldScrollBottom(true, 0);
                }
            }, 50);
        },
        hideAllOperateBar() {
            this.hideEmoji();
            this.hideOperatePanel();
        },
        shouldScrollBottom(force = false, delay = 300) {
            if (this.$refs.chatMessageListCom) {
                this.$refs.chatMessageListCom.shouldScrollBottom(force, delay);
            }
        },
        appendEmoji(emoji) {
            //将emoji 插入最后一个div内
            if (emoji == "") {
                return;
            }
            let str = this.messageText;
            str = str.slice(0, this.selectionStart) + emoji + str.slice(this.selectionStart);
            this.messageText = str;
            this.messageTextOld = this.messageText;
            this.selectionStart += emoji.length;
        },
        async loadTopHistory() {
            let offsetLength = this.messageList.length - this.chatMessageList.length;
            if (offsetLength > 0) {
                let data = this.messageList.slice(
                    -(this.pageInfo.pageSize + this.chatMessageList.length),
                    -this.chatMessageList.length
                );
                this.pageInfo.currentPage++;
                let list = this.messageList.slice(-(this.pageInfo.pageSize * this.pageInfo.currentPage));
                this.chatMessageList = list;
                this.historyLoaded(data);
            } else {
                let start;
                if (this.chatMessageList[0]) {
                    start = this.chatMessageList[0].gmsg_id;
                } else {
                    start = 0;
                }
                let params = {
                    start: start,
                    count: this.systemConfig.historyMessageNum,
                    mode: "older",
                };
                const res = await this.conversation.socket.request("get_history_chat_messages_by_search", params);
                this.pageInfo.currentPage++;
                let data = res.messages || [];
                this.handleChatMessage(data);
                // this.conversation.socket.emit("history_chat_messages", true, data, false, this.cid);
                this.historyLoaded(data);
                this.$store.commit("conversationList/updateMessageListIsLoaded", {
                    cid: this.cid,
                    is_loaded_history_list: true,
                });
            }
        },
        async getTargetCountList(oData={},callback){
            const {count} = oData
            let start;
            if (this.chatMessageList[0]) {
                start = this.chatMessageList[0].gmsg_id;
            } else {
                start = 0;
            }
            let params = {
                start,
                count: count,
                mode: "older",
            };
            const res = await this.conversation.socket.request("get_history_chat_messages_by_search", params);
            this.loadTargetCountList = true
            this.pageInfo.currentPage++;
            let data = res.messages || [];
            this.handleChatMessage(data);
            // this.conversation.socket.emit("history_chat_messages", true, data, false, this.cid);
            this.historyLoaded(data);
            callback(data)
        },
        handleChatMessage(oData) {
            const data = cloneDeep(oData);
            patientDesensitization(data);
            parseImageListToLocal(data, "url");
            setExpirationResource(data, this.cid);
            setWithDrawData(data);
            for (let message of data) {
                message.msg_body = this.parseMessageBody(message.msg_body);
                // message.msg_body=message.msg_body.replace(/\n/g,'<br/>')
                message.patientInfo = transferPatientInfo(message);
                message.sending = false;
                message.downloading = false;
                message.sendFail = message.sendFail || false;
                if (message.msg_type == this.systemConfig.msg_type.AI_ANALYZE) {
                    parseImageListToLocal(message.ai_analyze && message.ai_analyze.messages, "url");
                }
                if (message.protocol_guid) {
                    //消息存在iworks信息
                    setIworksInfoToMsg(message);
                }
            }
            var type = "prepend";
            let obj = {
                list: data,
                cid: this.cid,
                type: type,
                is_localdb_msg: 0,
            };

            if (data[0] && data[0].been_withdrawn == 2) {
                // 被撤回的 更改msg_type
                data[0].msg_type = this.systemConfig.msg_type.WITHDRAW;
            }
            this.$store.commit("chatList/addMessageNoSort", data[0]);
            this.$store.commit("conversationList/setChatMessage", obj);
            //聊天界面获取历史消息也走这个方法

            this.$store.commit("chatList/addMessageNoSort", data[0]);
            this.$store.commit("conversationList/updateMessageListIsLoaded", {
                cid: this.cid,
                is_loaded_history_list: true,
            });
        },
        async reloadHistoryList() {
            console.error("reloadHistory");
            if (!this.conversationList[this.cid].is_need_reload) {
                return;
            }
            await Tool.handleAfterConversationCreated(this.cid);
            let start = 0;
            let params = {
                start: start,
                count: this.systemConfig.historyMessageNum,
                mode: "older",
            };
            const res = await this.conversation.socket.request("get_history_chat_messages_by_search", params);
            this.$store.commit("conversationList/updateMessageListNeedReload", {
                is_need_reload: false,
                cid: this.cid,
            });
            this.pageInfo.currentPage = 1;
            let data = res.messages || [];
            let unSendMsgList = this.chatMessageList.filter((item) => !item.gmsg_id);
            this.$store.commit("conversationList/updateMessageList", { cid: this.cid, list: unSendMsgList });
            this.handleChatMessage(data);
        },
        toggleMessageType(type) {
            if (type == 1) {
                //点击语音输入按钮切换到语音
                this.isShowKeyboardBtn = true;
                this.isShowSoundBtn = false;
                this.isShowEmoji = false;
                this.hideOperatePanel();
            } else if (type == 2) {
                //点击键盘输入按钮切换到键盘
                this.isShowKeyboardBtn = false;
                this.isShowSoundBtn = true;
                this.hideOperatePanel();
                // this.$refs.message_text.focus()
            } else {
            }
        },
        showOperate() {
            this.isShowEmoji = false;
            this.isShowOperate = true;
            this.shouldScrollBottom(true);
        },
        async onClickRealtimeToolBar() {
            if (this.conferenceState && !this.isConferenceAuxOnline) {
                await this.chatWindowStartJoinRoom({ main: 0, aux: 1, isSender: 0 });
            }
        },
        async callLiveConference() {
            if (Tool.checkAppClient("Browser")) {
                return Toast(this.lang.use_app_tip);
            }
            try {
                await Tool.queryAppPermissions(["CAMERA", "RECORD_AUDIO"]);
                if (this.isUltraSoundMobile) {
                    let isAIAnalyzeLive =
                        this.conversation.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze;
                    this.chatWindowStartJoinRoom({ main: 1, aux: 1, isSender: 1, isAIAnalyzeLive });
                } else {
                    this.chatWindowStartJoinRoom({ main: 0, aux: 1, isSender: 1 });
                }
                this.hideOperatePanel();
            } catch (error) {
                console.error(error);
            }
        },
        historyLoaded(oData) {
            let list = oData || [];
            // Vue.set(this.conversation,'chatMessageList',list.concat(this.chatMessageList))
            // this.$store.commit('conversationList/setChatMessage',{list:list,cid:this.cid,type:'prepend'})
            this.$refs.chatMessageListCom.listLoaded(list);
            if (list.length == 0) {
                if (window.main_screen.gateway.check) {
                    if (this.conversation.chatMessageList.length != 0) {
                        Toast(this.lang.loaded_all_message);
                    }
                } else {
                    Toast(this.lang.loaded_all_message_no_net);
                }
            }
        },
        openSetting() {
            //设置界面微信分享图片后点击复选框会异常focus输入框，手动设置为输入框不可用，关闭设置界面emit恢复
            let message_text = this.$refs.message_text;
            // message_text.contentEditable=false;
            this.$router.push({
                path: `${this.$route.path}/group_setting`,
            });
            // this.$router.push(this.$route.fullPath+'/group_setting');
            // this.$router.push(`/index/chat_window/${this.cid}/group_setting`)
        },
        closeChatWindow(to, from, next) {
            if (
                window.location.hash.indexOf("force=1") > -1 ||
                (to.query.hasOwnProperty("force") && to.query.force === "1")
            ) {
                //强制回退
                next(true);
                return;
            }
            if (window.livingStatus === 1) {
                console.log("closeChatWindow window.livingStatus === 1");
                next(false);
                return;
            }
            if (Tool.checkMobileDialogShow()) {
                if (Tool.checkMobileCanCloseDialog() && Tool.checkMobileCanCloseOnPopstate()) {
                    Tool.closeMobileDialog();
                }
                console.log("closeChatWindow closeMobileDialog");
                console.log(Tool.checkMobileCanCloseDialog(), Tool.checkMobileCanCloseOnPopstate());
                next(false);
                return;
            }
            if (this.isConferenceAuxOnline) {
                if (this.isRealBrowser) {
                    this.$refs.chatWindowLiveRoomWeb.clickLeaveChannel();
                } else {
                    this.$refs.chatWindowLiveRoom.clickLeaveChannel();
                }
                console.log("closeChatWindow isConferenceAuxOnline");
                next(false);
                return;
            }
            if (this.examPageType) {
                //会诊群点击后退
                this.togglePageType();
                console.log("closeChatWindow examPageType");
                next(false);
                return;
            }
            next(true);
        },
        togglePageType() {
            this.examPageType = !this.examPageType;
            if (this.examPageType) {
                this.$refs.examModePage.init();
            }
            this.shouldScrollBottom();
        },
        shouldTogglePageType(value) {
            let examPageType = value == 1 ? true : false;
            if (examPageType != this.examPageType) {
                this.togglePageType();
            }
        },
        formatPaste(e) {
            e.preventDefault();
            var text;
            var clp = (e.originalEvent || e).clipboardData;
            if (clp === undefined || clp === null) {
                text = window.clipboardData.getData("text") || "";
                if (text !== "") {
                    if (window.getSelection) {
                        var newNode = document.createElement("span");
                        newNode.innerHTML = text;
                        window.getSelection().getRangeAt(0).insertNode(newNode);
                    } else {
                        document.selection.createRange().pasteHTML(text);
                    }
                }
            } else {
                text = clp.getData("text/plain") || "";
                if (text !== "") {
                    document.execCommand("insertText", false, text);
                }
            }
        },
        openLocalPatient() {
            if (!Tool.checkSpeakPermission(this.cid, this.user.uid)) {
                Toast(this.lang.app_no_speak_permission);
                return;
            }
            this.$router.push(`/index/chat_window/${this.cid}/local_patients`);
        },
        isPreventScroll(e) {
            console.log("isPreventScroll", e);
            if (e.target.classList[0] == "content_text") {
                //滑动文本输入区域滚动条
            } else {
                e.preventDefault();
                e.stopPropagation();
            }
        },
        touchChatWindow() {
            this.$refs.message_text && this.$refs.message_text.blur();
        },
        callFileList() {
            // this.$refs.message_text.contentEditable=false;
        },
        hideOperatePanel() {
            this.isShowOperate = false;
        },

        closeQrcode() {
            this.showQrcode = false;
        },
        shareLink() {
            var subject = this.conversation.subject;
            var live_id = this.GetLiveId();
            var surl =
                "http://" +
                this.systemConfig.server_type.host +
                ":" +
                this.systemConfig.serverInfo.listen_http_live_port;
            var href = surl + "/enterLivePlayPage?live_id=" + live_id;
            var shareContent = {
                href: href,
                title: this.lang.ultrasync_live,
                content: subject,
                thumb: "",
            };
            this.shareLinkToWeChat(shareContent);
        },
        GetLiveId() {
            var live_addr = "";
            var video_list = this.conversation.video_list;
            if (video_list && video_list.length > 0) {
                for (var ii = 0; ii < video_list.length; ii++) {
                    if (video_list[ii].type == 1 && video_list[ii].live_id) {
                        live_addr = video_list[ii].live_id;
                    }
                }
            }
            return live_addr;
        },
        insertHtmlAtRefs(html) {
            // this.messageText=this.$refs.message_text.innerHTML
            var length = this.messageText.length;
            var brIndex = this.messageText.indexOf("<br></div>", length - 10);
            var divIndex = this.messageText.indexOf("</div>", length - 6);
            var temp = this.messageText;
            if (divIndex == -1) {
                temp += html;
            } else if (brIndex > -1) {
                temp = temp.slice(0, brIndex);
                temp += html + "</div>";
            } else {
                temp = temp.slice(0, divIndex);
                temp += html + "</div>";
            }
            this.messageText = temp;
            this.messageTextOld = this.messageText;
            this.$refs.message_text.innerHTML = temp;
        },
        insertHtmlAtCursor(html) {
            var range, node;
            if (window.getSelection && window.getSelection().getRangeAt) {
                let selection = window.getSelection();
                if (this.editRange) {
                    selection.collapse(this.editRange.endContainer, this.editRange.endOffset);
                    selection.addRange(this.editRange);
                    range = this.editRange;
                    this.editRange = null;
                } else {
                    range = selection.getRangeAt(0);
                }

                node = range.createContextualFragment(html);
                console.log("selection range node", selection, range, node);
                range.insertNode(node);
                selection.collapse(range.endContainer, range.endOffset);
                range.collapse(false);
            }
        },
        mentionCb(list) {
            console.log(list);
            this.atUser = this.atUser.concat(list);
            this.$refs.message_text.focus();
            let str = this.messageText;
            let atStr = "";
            list.forEach((item, index) => {
                if (index === 0) {
                    atStr += `${item.nickname} `;
                } else {
                    atStr += `@${item.nickname} `;
                }
            });

            str = str.slice(0, this.selectionStart) + atStr + str.slice(this.selectionStart);
            this.messageText = str;
            this.messageTextOld = this.messageText;

            // this.messageText=this.messageText+list.join(' @')+' '
            // this.insertHtmlAtCursor(list.join(' @')+' ')
        },
        toUnreadMsg() {
            let index = this.chatMessageList.length - this.unreadNumber;
            this.shouldScrollBottom(true);
            this.unreadNumber = 0;
        },
        showUnreadNumberBtn() {
            for (let chat of this.$store.state.chatList.list) {
                if (chat.cid == this.cid) {
                    if (chat.unread > 10) {
                        this.unreadNumber = chat.unread;
                        this.unreadCid = this.cid;
                    }
                    break;
                }
            }
            if (this.unreadCid != this.cid) {
                //切换了会话后置空未读消息按钮
                this.unreadNumber = 0;
                this.unreadCid = this.cid;
            }
        },
        clickMore() {
            this.openSetting();
        },
        updateChatMessageListByConversation(value, action) {
            if (!Array.isArray(value)) {
                return;
            }
            let list = value;
            if (action === "init") {
                list = value.slice(-(this.pageInfo.pageSize * this.pageInfo.currentPage));
            }
            let sameLength = false;
            if (this.oldChatMessageListLength === value.length) {
                sameLength = true;
            }
            console.log(sameLength, "sameLength",this.loadTargetCountList);
            this.chatMessageList = list;

            this.oldChatMessageListLength = list.length;
            if (!this.manualSending && !sameLength && !this.loadTargetCountList) {
                this.shouldScrollBottom(false, 0);
            }
            if(this.loadTargetCountList){
                this.loadTargetCountList = false
            }
            this.clearConversationStatus();
        },
        clearConversationStatus() {
            this.$store.commit("chatList/clearMention", this.cid);
            this.clearUnread(this.cid);
        },
        clearUnread(cid) {
            if (!window.main_screen.conversation_list[this.cid]) {
                return;
            }
            const say_ack_count = this.$store.state.chatList.unreadMap[cid];
            if (say_ack_count > 0) {
                window.main_screen.conversation_list[this.cid].sayAckAllMessage({}, (res) => {
                    if (res.error_code === 0) {
                        this.$store.commit("chatList/clearUnread", this.cid);
                    }
                });
            }
        },
        updateChatMessageListByExamList(value) {
            if (Object.keys(value).length === 0) {
                return;
            }
            this.shouldScrollBottom();
        },
        changeChatListResourceStatus(value) {
            if (!Array.isArray(value)) {
                return;
            }
            let resourceList = value;
            this.resourceIdList = [];
            resourceList.forEach((item) => {
                if (!this.resourceIdList.includes(item.resource_id)) {
                    if (item.resource_id !== null) {
                        this.resourceIdList.push(item.resource_id);
                    }
                }
            });
            let chatMessageList = this.chatMessageList;
            if (chatMessageList.length === 0) {
                //聊天列表还未加载，需要在加载后手动触发
                this.isNeedLoadResourceStatus = true;
                return;
            }
            for (let i = chatMessageList.length - 1; i >= 0; i--) {
                if (
                    chatMessageList[i].hasOwnProperty("resource_id") &&
                    !this.resourceIdList.includes(chatMessageList[i].resource_id) &&
                    this.resourceMsgTypeList.includes(chatMessageList[i].msg_type)
                ) {
                    if (chatMessageList[i].been_withdrawn != 1 && chatMessageList[i].been_withdrawn != 2) {
                        this.$set(
                            chatMessageList[i],
                            "msg_type",
                            this.$store.state.systemConfig.msg_type.EXPIRATION_RES
                        );
                    }
                }
            }
        },
        handleSendMessage() {
            // 如果有引用消息，则传递给sendMessage方法
            if (this.quote_message) {
                // 保存引用消息的临时副本
                const quoteMsg = this.quote_message;

                // 调用原来的sendMessage方法，传递引用消息
                if (!this.atUser.includes(this.quote_message.sender_id)) {
                    this.atUser.push({
                        uid:this.quote_message.sender_id,
                        nickname:this.quote_message.sender_name
                    })
                }
                console.log("quoteMsg", this.atUser);
                this.sendMessage(quoteMsg);
                // 清空引用消息
                this.quote_message = null;
            } else {
                // 正常调用发送消息方法
                this.sendMessage();
            }
        },
        openHistoryMessageList(message) {
            this.$router.push({
                name: "chat_history_window",
                params: {
                    message: message,
                },
            });
        },
        transitionAfterEnter(e) {
            setTimeout(() => {
                this.isTransitionEnd = true;
                this.$nextTick(() => {
                    this.action = this.$route.params.action;
                    const Fileitem = this.$route.params.Fileitem;
                    if (Fileitem && (Fileitem.gmsg_id || Fileitem.resource_id) && this.action == "gotoHistoryPage") {
                        this.openHistoryMessageList(Fileitem);
                    }
                    this.initPage();
                });
            }, 0);
        },
        transitionAfterLeave(e) {
            this.isTransitionEnd = false;
            this.chatMessageList = [];
            this.quote_message = null;
            this.atUser = [];
            this.$root.eventBus.$off(`${this.cid}_gateway_connect`);
            this.cid = 0;
            this.exitInput();
            this.pageInfo.currentPage = 1;
            this.oldChatMessageListLength = 0;
        },
        openJoinVerify() {
            this.$router.push(this.$route.fullPath + "/group_setting/group_manage/join_verify");
        },
        gateWayConnect() {
            if (Number(this.cid)) {
                this.reloadHistoryList();
            }
        },
        saveAiResultWithImage(data){
            if(data){
                const {task_id,group_id,report}  = data
                this.$store.commit("conversationList/updateAiTaskInfo", {
                    task_id: task_id,
                    list: report,
                    cid: group_id,
                });
            }

        },

        async sendImageWithAiReport(task_id) {
            console.log("sendImageWithAiReport task_id:", task_id);
            if (
                this.isUltraSoundMobile &&
                task_id &&
                this.conversationList[this.cid] &&
                this.conversationList[this.cid].aiTaskInfo[task_id]
            ) {
                let aiTaskInfo = cloneDeep(this.conversationList[this.cid].aiTaskInfo);
                console.log("sendImageWithAiReport report list:", aiTaskInfo[task_id]);
                this.$store.commit("conversationList/clearAiTaskInfo", { cid: this.cid, task_id: task_id, list: [] });
                console.log("aiTaskInfo:", aiTaskInfo);
                const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
                for (let id in aiTaskInfo[task_id]) {
                    let report = aiTaskInfo[task_id][id][0];
                    if (report && report.clip_id &&  report.type && report.Base64) {
                        const base64Data = 'data:image/jpeg;base64,'+ report.Base64;
                        delete report.Base64;
                        console.error("base64Data:", base64Data.length);
                        const blobFile = Tool.base64ImageToBlob(base64Data);
                        const fileName = this.lang[report.clip_id] + '.jpg';
                        const file = Tool.blobToFile(blobFile,fileName);
                        const ai_image_info ={
                            is_analyze: true,
                            type: report.type,
                            error: 0,
                            report: report,
                        }
                        Object.assign(file, { ai_image_info: ai_image_info });
                        await sleep();
                        this.uploadPictureStart(cloneDeep([file]));
                    }
                }
            }
        },
        async uploadAiImagesResult(data) {
            console.log("uploadAiImagesResult:", data);
            const data_ = {
                task_id:'123',
                cid: 111,
                bucketName:'rr',
                fileList:[
                    {
                        file_id:1,
                        oss_path:'ss/sd',
                        fileName:'image.jpg',
                        size:1000,
                        report:{
                            type:'1',
                            error:0,
                            clip_id:'123',
                        },
                        error:0,
                    }
                ]
            }
            const {task_id,cid,bucketName,fileList} = data;
            if (fileList && fileList.length > 0) {
                const messageList = [];
                for (const file of fileList) {
                    const {file_id,oss_path,fileName,report,error} = file;
                    if(error===0){
                        const msg = {
                            file_id,
                            group_id: cid,
                            resource_file_size:file.size,
                            msg_type: this.systemConfig.msg_type.Image,
                            url: this.systemConfig.serverInfo.oss_attachment_server.playback_https_addr +  '/' + oss_path,
                            url_local:'',
                            file_name: fileName,
                            ai_image_info: {
                                is_analyze: true,
                                type: report.type,
                                error: 0,
                                report: report,
                            },
                            is_network_resource:false,
                            network_resource:  ''
                        };
                        messageList.push(cloneDeep(msg))
                    }
                }
                // messageList
            }
        },
        chatWindowStartJoinRoom(options, callback) {
            if(this.$route.name!=='chat_window'){
                return
            }
            this.showLiveRoom = true;

            // 添加最大重试次数
            const maxRetries = 100;
            let retryCount = 0;

            const tryStartJoinRoom = () => {
                if (this.isRealBrowser && this.$refs.chatWindowLiveRoomWeb) {
                    this.$refs.chatWindowLiveRoomWeb.startJoinRoom(
                        {
                            ...options,
                            cid: this.cid,
                        },
                        callback
                    );
                } else if (!this.isRealBrowser && this.$refs.chatWindowLiveRoom) {
                    this.$refs.chatWindowLiveRoom.startJoinRoom(
                        {
                            ...options,
                            cid: this.cid,
                        },
                        callback
                    );
                } else if (retryCount < maxRetries) {
                    // 如果组件未加载完成,100ms后重试
                    retryCount++;
                    setTimeout(tryStartJoinRoom, 100);
                } else {
                    // 超过最大重试次数
                    console.error("LiveRoom component failed to load");
                    callback && callback(false, "Component load timeout");
                }
            };

            this.$nextTick(() => {
                tryStartJoinRoom();
            });
        },
        HandleJoinChannelAux() {},
        HandleLeaveChannelAux() {
            console.log("HandleLeaveChannelAux chatWindow");
            setTimeout(() => {
                this.showLiveRoom = false;
            }, 600);
        },
        openCloudExam() {
            this.$router.push({
                path: `${this.$route.path}/cloud_exam`,
                query: this.$route.query,
            });
        },
        openCloudExamCompleted() {
            this.$router.push({
                path: `${this.$route.path}/cloud_exam`,
                query: {
                    ...this.$route.query,
                    active: "exam_completed",
                },
            });
        },
        clearUnfinishedHomework() {
            let obj = {};
            obj[this.cid] = null;
            this.$store.commit("homework/updateUnfinish", obj);
        },
        clearCorrectedHomework() {
            let obj = {};
            obj[this.cid] = null;
            this.$store.commit("homework/updateCorrected", obj);
        },
        getDeviceList(cid) {
            window.main_screen.getDeviceList({ cid: cid }, (res) => {
                if (res.error_code == 0) {
                    this.$store.commit("conversationList/updateDeviceList", {
                        cid,
                        list: res.data.list,
                    });
                }
            });
        },
        /**
         * 处理引用消息事件
         * @param {Object} message - 要引用的消息对象
         */
        handleQuoteMessage(message) {
            if (!message) {
                return;
            }

            // 检查消息是否有有效的 gmsg_id
            if (!message.gmsg_id) {
                return;
            }

            // 设置被引用的消息
            this.quote_message = message;

            // 聚焦输入框并调整界面
            this.$nextTick(() => {
                if (this.$refs.message_text) {
                    this.$refs.message_text.focus();
                }
                this.isShowSoundBtn = true;
                this.isShowKeyboardBtn = false;
            });
        },

        /**
         * 清除引用消息
         */
        clearQuotedMessage() {
            this.quote_message = null;
        },
        handleWithdrawChatMessage(data){
            console.log('handleWithdrawChatMessage', data);
            if (!data || typeof data.cid === 'undefined' || !Array.isArray(data.gmsg_id_list)) {
                return;
            }
            if (String(data.cid) !== String(this.cid)) {
                return;
            }
            if (!this.quote_message || typeof this.quote_message.gmsg_id === 'undefined') {
                return;
            }
            if (data.gmsg_id_list.includes(this.quote_message.gmsg_id)) {
                this.quote_message = null;
            }
        },
        handleDeleteChatMessages(data){
            console.log('handleDeleteChatMessages', data);

            if (!data || typeof data.cid === 'undefined' || !Array.isArray(data.gmsg_id_list)) {
                return;
            }
            if (String(data.cid) !== String(this.cid)) {
                return;
            }
            if (!this.quote_message || typeof this.quote_message.gmsg_id === 'undefined') {
                return;
            }
            if (data.gmsg_id_list.includes(this.quote_message.gmsg_id)) {
                this.quote_message = null;
            }
        }
    },
};
</script>
<style lang="scss" scoped>
/* 删除原来的引用消息样式，已移至.message_input_container内部 */

.black_bg {
    background: #000;
    color: #fff;
}
.record_modal {
    .svg_icon_speak {
        display: block;
        font-size: 4rem;
        margin: 0 auto;
    }
    .cancel_record_sound_tip {
        .svg_icon_speak {
            color: rgb(248, 54, 29);
            fill: red;
        }
    }
}

.chat_window {
    display: flex;
    flex-direction: column;
    user-select: none;
    -webkit-user-select: none;
    touch-callout: none;
    -webkit-touch-callout: none;
    top: 0;
    right: 0;
    min-width: 0;
    bottom: 0;
    overflow: hidden;
    position: absolute;
    transform: translate3d(0px, 0px, 0px);
    
    /* 直播间全屏时隐藏其他内容 */
    &.live_room_fullscreen {
        /* 隐藏header */
        header {
            display: none !important;
        }
        
        /* 隐藏网络不可用提示 */
        .network_unavailable {
            display: none !important;
        }
        
        /* 隐藏作业提示 */
        .unfinished_homework {
            display: none !important;
        }
        
        /* 隐藏聊天内容和输入区域 */
        .second_level_content,
        .chat_window_foot {
            display: none !important;
        }
        
        /* 隐藏二维码对话框 */
        .qrcode_dialog {
            display: none !important;
        }
        
        /* 确保直播组件占满整个屏幕 */
        .column_container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 9997;
        }
    }
    
    .column_container {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
        position: relative;
    }
    .unfinished_homework {
        background-color: rgb(255, 247, 236);
        padding: 0.3rem 0.6rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        & > p {
            font-size: 0.7rem;
            color: #333;
        }
        .icon-1guangbo {
            margin-right: 0.4rem;
        }
        .icon-close1 {
            color: rgb(255, 177, 68);
            font-size: 0.8rem;
        }
    }
    .row_container {
        background: #000;
        color: #fff;
        display: flex;
        flex-direction: column;
        position: absolute;
        z-index: 9999;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        align-items: center;
        justify-content: space-around;
        .svn_btn {
            width: 60px;
            height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            p {
                color: #fff;
                font-size: 12px;
            }
            .svg_icon {
                width: 36px;
                height: 36px;
                fill: #fff;
            }
        }
    }
    &:before {
        display: block;
        position: absolute;
        content: "";
        width: 100%;
        height: 60px;
        top: -60px;
        background-color: #00c59d;
    }
    .chat_window_title {
        display: flex;
        padding: 0 1.4rem;
        justify-content: center;
    }
    .title {
        display: flex;
        width: 100%;
        padding: 0 3.8rem;
        box-sizing: border-box;
        font-size: 0.95rem;
        justify-content: center;
        i {
            font-size: 0.9rem;
            position: relative;
        }
        span {
            display: inline-block;
            vertical-align: bottom;
            width: 2.2rem;
            flex-shrink: 0;
        }
    }
    .exam_purpose {
        display: inline-block;
        width: 100%;
        padding: 0 2.4rem;
        box-sizing: border-box;
    }
    .videos {
        display: none;
    }
    .has_unread {
        position: absolute;
        width: 0.3rem;
        height: 0.3rem;
        background: #f00;
        top: 0.6rem;
        border-radius: 50%;
        right: 1.6rem;
    }
    //     .svg_icon_transfer{
    //         position: absolute;
    //         right: 2.55rem;
    //         width: 0.9rem;
    //         height: 0.9rem;
    //         top: 0.65rem;
    //         fill: #fff;
    //     }
    .transfer_panel {
        position: absolute;
        width: 1.7rem;
        height: 100%;
        right: 1.75rem;
        top: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        .svg_icon_transfer {
            font-size: 1rem;
        }
    }

    .icon-shenglve,
    .icon-libraryicon03 {
        font-size: 1.4rem;
        position: absolute;
        right: 0;
        width: 2.4rem;
        top: 0;
    }
    .chat_window_container {
        flex-grow: 1;
        overflow: auto;
        display: flex;
        flex-direction: column;
        // position: absolute;
        width: 100%;
        position: relative;
        // top: 2.2rem;
        // bottom: 2.6rem;
        height: 0px;
        // height: calc(100% - 2.6rem - 2.2rem);
        & ~ div {
            user-select: none;
            -webkit-user-select: none;
        }
        .unread_number_btn {
            position: absolute;
            right: 0.8rem;
            width: 5rem;
            height: 1.15rem;
            bottom: 3.35rem;
            z-index: 2;
            /* padding: 4px 12px; */
            border-radius: 0.575rem;
            color: #fff;
            /* padding: 0.3rem 0.5rem; */
            background: #ffb144;
            font-size: 0.5rem;
            text-align: center;
            line-height: 1.15rem;
            .iconup1 {
                font-size: 0.5rem;
                margin-right: 0.3rem;
            }
            &::after {
                position: absolute;
                content: "";
                display: block;
                width: 0;
                height: 0;
                border-top: 0.3rem solid #ffb144;
                border-left: 0.3rem solid transparent;
                border-right: 0.3rem solid transparent;
                right: 0.6rem;
                bottom: -0.15rem;
            }
        }
        .realtime_voice_operate {
            width: 100%;
            height: min-height;
            padding: 0.1rem 1.5rem;
            background-color: #f1f5f8;
            box-sizing: border-box;
            border-bottom: 1px solid #ddd;
            @keyframes star {
                10% {
                    opacity: 0;
                }

                90% {
                    opacity: 1;
                }
            }
            .realtime_voice_callout {
                display: flex;
                align-items: center;
                .operate {
                    display: flex;
                    align-items: center;
                    font-size: 0.7rem;
                    color: #505050;
                    p {
                    }
                    .svg_icon_attendee_setting {
                        position: relative;
                        height: 22px;
                    }
                    .unread_dot {
                        position: absolute;
                        width: 8px;
                        height: 8px;
                        background: red;
                        right: 7px;
                        border-radius: 50%;
                        top: 0;
                    }
                    i {
                        font-size: 1.3rem;
                    }
                    i,
                    .hands_wrap {
                        margin-right: 0.7rem;
                    }
                    .svg_icon_no_mute,
                    .svg_icon_mute,
                    .svg_icon_hand_up,
                    .svg_icon_hand_off {
                        margin-right: 0;
                    }
                    .svg_icon_hand_up,
                    .svg_icon_hand_off {
                        width: 0.8rem;
                    }
                    .hands_wrap {
                        position: relative;
                        .attendee_number {
                            position: absolute;
                            right: 0;
                            top: 0;
                            font-size: 0.4rem;
                            font-weight: 700;
                            color: rgb(0, 197, 157);
                        }
                        svg,
                        span {
                            vertical-align: middle;
                        }
                    }
                    .icon-dengdai- {
                        margin-right: 0.7rem;
                        color: rgb(0, 197, 157);
                    }
                    i:last-child {
                        margin-right: 0;
                    }
                    .svg_icon_accept_dial {
                        color: rgb(0, 197, 157);
                        animation: star 0.5s ease-in infinite;
                    }
                }
                .icon-microphone {
                    color: #03d600;
                }
                .icon-15 {
                    color: #d84c57;
                    font-weight: bold;
                }
                .initiator_info {
                    height: 1.95rem;
                    display: flex;
                    flex-direction: column;
                    .voice-msg-font {
                        font-size: 0.75rem;
                        padding-left: 0.5rem;
                    }
                    .is_recording {
                        margin-top: 0.2rem;
                        font-size: 0.55rem;
                        color: rgb(255, 103, 92);
                    }
                }
                // 非录制状态
                .no_record {
                    .voice-msg-font {
                        margin-top: 0.4rem; // 需要录制去掉
                    }
                }
                .round_pot {
                    float: left;
                    width: 1.2rem;
                    height: 1.2rem;
                    margin-top: 0.3rem;
                }
            }
        }
        .join_verify_tip {
            font-size: 0.7rem;
            padding: 0 0.4rem;
            background: #f1f5f8;
            display: flex;
            align-items: center;
            .icon-user_add {
                font-size: 1.3rem;
                color: #54be1e;
                margin-right: 0.3rem;
            }
        }
        .chat_content {
            flex: 1;
            overflow: auto;
        }
        .record_modal {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 20;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            line-height: 1;
            & > div {
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                bottom: 0;
                font-size: 0.9rem;
                width: 10rem;
                text-align: center;
                color: #fff;
                i {
                    width: 3.75rem;
                    height: 3.75rem;
                    margin-bottom: 1.25rem;
                }
                img {
                    width: 5rem;
                    display: block;
                    margin: 0 auto;
                }
                .time {
                    margin-bottom: 0.9rem;
                }
                .cancel_text {
                    margin-bottom: 2rem;
                }
                span {
                    font-size: 4rem;
                }
            }
            .cancel_record_sound_tip {
                .cancel_text {
                    color: #ff675c;
                }
            }
        }
    }
    .chat_window_foot {
        // position: absolute;
        // bottom: 0px;
        width: 100%;
        .message_input_container {
            display: flex;
            padding: 0.45rem 0;
            background: #fff;
            align-items: center;
            user-select: none;
            .content_left {
                margin: 0 0.25rem 0 0.8rem;
                .svg_icon_sound,
                .svg_icon_keyboard {
                    font-size: 1.75rem;
                    color: #00c59d;
                    height: 1.4rem;
                    width: 1.4rem;
                }
            }
            .content_right {
                height: 1.8rem;
                margin: 0 0.8rem 0 0.25rem;
                position: relative;
                display: flex;
                align-items: center;
                .svg_icon_emoji,
                .svg_icon_more,
                .send_btn {
                    font-size: 1.5rem;
                    color: gray;
                    line-height: 1.45rem;
                    height: 1.45rem; // 58px
                    width: 1.45rem;
                    margin-bottom: 0.05rem;
                }
                .svg_icon_emoji {
                    font-size: 1.75rem;
                    margin-right: 0.3rem;
                }
                .svg_icon_more {
                }
                .send_btn {
                    font-size: 0.7rem;
                    width: 2.5rem;
                    text-align: center;
                    border-radius: 0.2rem;
                    display: inline-block;
                }
                .svg_icon_send {
                    font-size: 1.75rem;
                    color: #00c59d;
                    height: 1.45rem;
                    width: 1.45rem;
                }
            }
            .content_text,
            .hold_to_talk {
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1;
                min-height: 1.7rem;
                max-height: 6rem;
                font-size: 0.75rem;
                box-sizing: border-box;
                background-color: rgb(170, 180, 185);
                border-radius: 1rem;
                color: #fff;
                overflow: auto;
                user-select: none;
                -webkit-user-select: none;
                position: relative;
                z-index: 0;
                img {
                    height: 1rem;
                }
                .hidden_text {
                    line-height: 1.4rem;
                    max-height: 6rem;
                    min-height: 1.8rem;
                    overflow: hidden;
                    word-break: break-all;
                }
                textarea {
                    width: 100%;
                    height: 100%;
                    resize: none;
                    border: none;
                    padding: 0.2rem 0.65rem;
                    position: absolute;
                    box-sizing: border-box;
                    font-size: 0.75rem;
                    line-height: 1.4rem;
                    color: rgb(59, 62, 63);
                    z-index: 2;
                    border-radius: 0.85rem;
                    border: 0.0125rem solid #dddddd; // 0.5px
                    box-shadow: none;
                    -webkit-appearance: none;

                    &::-webkit-scrollbar {
                        width: 0 !important;
                        height: 0 !important;
                        color: transparent;
                    }
                }
            }
            .hold_to_talk {
                text-align: center;
                user-select: none;
                webkit-user-select: none;
                padding: 0.2rem 0.65rem;
            }
            .speak_up {
                background-color: rgb(101, 109, 112);
            }
        }
        /* 优化引用消息的SCSS样式，移至输入容器内部 */
        .quoted-message-container {
            background-color: #f5f5f5;
            border-left: 0.1429rem solid #ddd;
            border-radius: 0.1429rem;
            padding: 0.381rem 0.476rem;
            font-size: 0.5714rem;
            max-width: 100%;
            box-shadow: 0 0.0476rem 0.1429rem rgba(0, 0, 0, 0.1);
            position: relative;
            padding-right: 1.19rem;
            .quote-close {
                cursor: pointer;
                color: #999;
                font-size: 0.7619rem;
                position: absolute;
                right: 0.381rem;
                top: 0.1905rem;
                z-index: 1;
            }

            .quote-content {
                width: 100%;
                padding-right: 0.7143rem; /* 为关闭按钮留出空间 */

                .quote-text {
                    color: #666;
                    word-break: break-all; /* 防止在标点符号处换行 */
                    overflow-wrap: break-word; /* 允许在单词内换行 */
                    white-space: pre-wrap; /* 保留空格，允许自动换行 */
                    display: -webkit-box;
                    -webkit-line-clamp: 2; /* 限制为两行 */
                    -webkit-box-orient: vertical;
                    line-clamp: 2;
                    overflow: hidden;
                    max-height: 3em;
                }
            }
        }
        .message_operate_container {
            background: rgb(241, 245, 248);
            line-height: 1;
            .emoji_page {
                padding: 0.3rem 0;
                max-height: 6rem;
                overflow: auto;
                .emoji {
                    font-size: 1rem;
                    cursor: pointer;
                    width: 1.7rem;
                    height: 1.7rem;
                    display: inline-block;
                    line-height: 1.7rem;
                    text-align: center;
                }
            }
            .operate_page {
                display: flex;
                overflow: hidden;
                flex-wrap: wrap;
                height: auto;
                justify-content: center;
                img {
                    width: 35%;
                    margin-bottom: 0.4em;
                }

                .operate_item {
                    position: relative;
                    height: 4.5rem;
                    text-align: center;
                    padding: 0.5rem 0 0.5rem 0;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    box-sizing: border-box;
                    // margin-right:1.86rem;
                    // width: 33.33%;
                    i {
                        width: 2.5rem;
                        height: 2.5rem;
                        margin-bottom: 0.4rem;
                    }
                    p {
                        font-size: 0.55rem;
                        color: #333;
                        flex-shrink: 0;
                        line-height: 1rem;
                    }
                    input {
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        left: 0;
                        top: 0;
                        right: 0;
                        bottom: 0;
                        z-index: 2;
                        opacity: 0;
                        border-radius: 50%;
                        -webkit-appearance: none;
                        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
                        -webkit-tap-highlight-color: transparent;
                    }
                    .unread {
                        position: absolute;
                        width: 0.3rem;
                        height: 0.3rem;
                        background: #f00;
                        border-radius: 50%;
                        top: 10%;
                        left: 70%;
                    }
                }
                .operate_item:nth-child(n + 4) {
                    margin-right: 0;
                }
            }
        }
    }
    &.is_ios {
        .edit_comment_modal {
            .edit_comment_panel {
                // top:60%;
            }
        }
    }
    .qrcode_dialog {
        position: absolute;
        top: 0;
        left: 0;
        background: rgba(0, 0, 0, 0.5);
        right: 0;
        bottom: 0;
        z-index: 11;
        text-align: center;
        .qrcode_container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 0.2rem;
            #live_qr_addr_connent {
                margin: 1rem;
                width: 10rem;
                height: 10rem;
                box-sizing: border-box;
                overflow: hidden;
                img {
                    width: 10rem;
                    height: 10rem;
                    display: block;
                    margin: 0 auto;
                }
            }
            .qrcode_btn {
                border-top: 1px solid #aaa;
                line-height: 2;
            }
        }
    }
}

header {
    // &.iosFixed{
    //     position: absolute;
    //     top: 0;
    //     left: 0;
    //     width: 100%;
    //     z-index: 99999;
    // }
}
.speechPanel {
    width: 100%;
    height: 100%;
    // border-radius: 10px 10px 0 0;
}

.hands_tips {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 3001;
    .hands_modal {
        z-index: 3001;
        background: #000;
        position: fixed;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        opacity: 0.5;
    }
    .hands_content {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate3d(-50%, -50%, 0);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 3002;
        flex-direction: column;
        .svg_content {
            width: 5rem;
            height: 5rem;
            background: #fff;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            i {
                width: 4rem;
                height: 4rem;
            }
        }
        .hands_text {
            font-size: 1rem;
            color: #fff;
            margin-top: 1rem;
        }
    }
}
</style>
