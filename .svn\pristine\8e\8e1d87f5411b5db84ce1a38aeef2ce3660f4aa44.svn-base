<template>
    <div class="smart-tech-training-main">
        <div class="smart-tech-training-container">
            <div class="custom-header">
                <div class="back-button" @click="goBack">
                    <i class="el-icon-arrow-left"></i>
                </div>
            </div>
            <div class="smart-tech-training-content">
                <div class="card-grid">
                    <div class="card" @click="handleClickProject">
                        <div class="card-image-placeholder">
                            <!-- 这里可以替换为实际的图片 -->
                        </div>
                        <div class="card-title">超声技能认证</div>
                        <div class="card-description">
                            给出这个教培项目相关的说明，如：超声教培进度管理、上传完成超声作业。
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <router-view></router-view>

        <CommonDialog
            class="project_detail"
            title="请选择您的认证项目"
            :show.sync="selectTrainingDialogVisible"
            :close-on-click-modal="true"
            width="800px"
            @closed="selectTrainingDialogVisible = false"
            :modal="false"
            :append-to-body="true"
            :footShow="false"
        >
            <div class="project-select-container">
                <!-- 第一行：表单搜索 -->
                <div class="search-filter-bar">
                    <div class="filter-selectors">
                        <el-select v-model="specialty" :placeholder="'所属专业'" size="small" clearable>
                            <el-option :label="lang.homework_type4" :value="BODY_PART.GYN"></el-option>
                            <el-option :label="lang.homework_type1" :value="BODY_PART.ABDOMEN"></el-option>
                            <el-option :label="lang.homework_type3" :value="BODY_PART.CARDIO"></el-option>
                            <el-option :label="lang.homework_type2" :value="BODY_PART.SUPERFICIAL"></el-option>
                        </el-select>
                        <el-select
                            v-model="countryCode"
                            placeholder="请选择国家/地区"
                            size="small"
                            clearable
                            class="country-select"
                        >
                            <el-option
                                v-for="item in countryOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </div>
                    <div class="search-input-area">
                        <el-input placeholder="请搜索" v-model="trainingName" size="small" clearable>
                            <el-button
                                slot="append"
                                icon="el-icon-search"
                                @click="searchTraining"
                                :loading="isLoading"
                                :disabled="isLoading"
                            ></el-button>
                        </el-input>
                    </div>
                </div>

                <!-- 第二行及以后：项目列表 -->
                <div class="project-list" v-loading="isLoading">
                    <div
                        class="project-item"
                        v-for="training in trainings"
                        :key="training._id"
                        @click="selectTraining(training)"
                    >
                        <div class="project-item-details">
                            <div class="project-item-main-content">
                                <div class="project-item-title">{{ getTrainingDisplayTitle(training) }}</div>
                            </div>
                            <div
                                class="project-item-status-badge"
                                :class="getRoleBadgeClass(training)"
                                v-if="getRoleBadgeClass(training)"
                            >
                                {{ getRoleBadgeText(training) }}
                            </div>
                        </div>
                    </div>
                    <div v-if="trainings.length === 0" class="empty-list-placeholder">暂无匹配项目</div>
                </div>
            </div>
        </CommonDialog>

        <CommonDialog
            class="registration_form_dialog"
            title="学员信息登记"
            :show.sync="registrationFormDialogVisible"
            :close-on-click-modal="false"
            width="800px"
            @closed="handleCloseRegistrationFormDialog"
            :modal="true"
            :append-to-body="true"
            :footShow="true"
            :submitText="lang.submit_btn"
            :disabledSubmit="!studentIdFetchSuccess || isSubmitting"
            @submit="submitRegistrationForm"
        >
            <div class="registration-form-container">
                <el-form :model="registrationForm" ref="registrationFormRef" label-width="100px">
                    <el-form-item label="学员ID号:" prop="studentId">
                        <el-input
                            v-model="registrationForm.studentId"
                            @blur="handleStudentIdBlur"
                            @clear="handleStudentIdClear"
                            placeholder="请输入学员ID号"
                            maxlength="100"
                            clearable
                        >
                            <template slot="suffix">
                                <i class="el-input__icon el-icon-loading" v-if="isStudentIdLoading"></i>
                                <i
                                    class="el-input__icon el-icon-success icon-green-tick"
                                    v-if="studentIdFetchSuccess && !isStudentIdLoading"
                                ></i>
                            </template>
                        </el-input>
                    </el-form-item>
                    <div class="student-id-tip" v-if="!studentIdFetchSuccess && !isStudentIdLoading && !studentIdError">
                        <p>请通过官方网站报名成功后，由网站提供的学员ID，输入后系统将会启动校验，如校验通过方可进入项目。</p>
                    </div>
                    <div class="student-id-error" v-if="studentIdError">
                        <p>{{ studentIdError }}</p>
                    </div>
                    <template v-if="showRegistrationFormDetails">
                        <el-form-item label="姓名:" prop="name">
                            <div class="info-text">{{ registrationForm.name }}</div>
                        </el-form-item>
                        <el-form-item label="性别:" prop="gender">
                            <div class="info-text">{{ registrationForm.gender }}</div>
                        </el-form-item>
                        <el-form-item label="联系地址:" prop="address">
                            <div class="info-text">{{ registrationForm.address }}</div>
                        </el-form-item>
                        <el-form-item label="手机:" prop="mobile">
                            <div class="info-text">{{ registrationForm.mobile }}</div>
                        </el-form-item>
                        <el-form-item label="邮箱:" prop="email">
                            <div class="info-text">{{ registrationForm.email }}</div>
                        </el-form-item>
                        <el-form-item label="医院名称:" prop="hospital">
                            <div class="info-text">{{ registrationForm.hospital }}</div>
                        </el-form-item>
                    </template>
                </el-form>
            </div>
        </CommonDialog>
    </div>
</template>

<script>
import base from "../../lib/base";
import Tool from "@/common/tool";
import CommonDialog from "../../MRComponents/commonDialog";
import { SMART_TECH_TRAINING_ROLE, BODY_PART, SEX } from "../../lib/constants";
import service from "../../service/service";
const getDefaultRegistrationForm = () => ({
    name: "",
    gender: "",
    hospital: "",
    address: "",
    mobile: "",
    email: "",
    studentId: "",
});

export default {
    mixins: [base],
    name: "SmartTechTraining",
    components: {
        CommonDialog,
    },
    data() {
        return {
            BODY_PART,
            selectTrainingDialogVisible: false,
            specialty: "",
            countryCode: "",
            trainingName: "",
            trainings: [],
            countryOptions: [],
            registrationFormDialogVisible: false,
            selectedTrainingForForm: null,
            registrationForm: getDefaultRegistrationForm(),
            genderOptions: [],
            addressRegionOptions: [],
            trainingRoleMap: {
                [SMART_TECH_TRAINING_ROLE.PI_TEACHER]: { text: "PI", class: "status-pi-teacher" },
                [SMART_TECH_TRAINING_ROLE.SUPERVISOR]: { text: "SUPERVISOR", class: "status-supervisor" },
                [SMART_TECH_TRAINING_ROLE.STUDENT]: { text: "学员", class: "status-student" },
            },
            formRules: {},
            isLoading: false, // 新增加载状态
            isStudentIdLoading: false,
            studentIdFetchSuccess: false,
            debouncedFetchStudentInfo: null,
            isSubmitting: false, // 添加表单提交状态
            showRegistrationFormDetails: false, // 控制是否显示除学员ID外的其他表单项
            studentIdError: "", // 存储学员ID输入框的错误信息
        };
    },
    computed: {},
    watch: {
        selectTrainingDialogVisible: {
            handler(newVal) {
                if (newVal) {
                    this.getTrainingList();
                }
            },
            immediate: true,
        },
    },
    created() {
        this.getTrainingCountryList();
        this.debouncedFetchStudentInfo = Tool.debounce(
            (val) => {
                this.fetchStudentInfoByUuidClean(val);
            },
            200,
            true
        );
        this.genderOptions = [
            { label: "男", value: SEX.MALE },
            { label: "女", value: SEX.FEMALE },
            { label: "其他", value: SEX.UNKNOWN },
        ];
        this.formRules = {
            name: [{ required: true, message: this.lang.input_enter_tips, trigger: "blur" }],
            gender: [{ required: true, message: this.lang.input_enter_tips, trigger: "blur" }],
            hospital: [{ required: true, message: this.lang.input_enter_tips, trigger: "blur" }],
            address: [{ required: true, message: this.lang.input_enter_tips, trigger: "blur" }],
            mobile: [{ required: true, message: this.lang.input_enter_tips, trigger: "blur" }],
            email: [
                { required: true, message: this.lang.input_enter_tips, trigger: "blur" },
                { type: "email", message: "邮箱格式不正确", trigger: ["blur", "change"] },
            ],
            studentId: [{ required: true, message: this.lang.input_enter_tips, trigger: "blur" }],
        };
    },
    methods: {
        getDefaultRegistrationForm,
        getTrainingDisplayTitle(training) {
            let prefix = "";
            if (training.specialty) {
                // 直接使用中文，根据之前对 BODY_PART 和 lang 的分析
                // 确保 training.specialty 的值与 BODY_PART 中的值匹配
                let specialtyName = "";
                switch (training.specialty) {
                case BODY_PART.GYN:
                    specialtyName = this.lang.homework_type4;
                    break;
                case BODY_PART.ABDOMEN:
                    specialtyName = this.lang.homework_type1;
                    break;
                case BODY_PART.CARDIO:
                    specialtyName = this.lang.homework_type3;
                    break;
                case BODY_PART.SUPERFICIAL:
                    specialtyName = this.lang.homework_type2;
                    break;
                }
                if (specialtyName) {
                    prefix += `【${specialtyName}】`;
                }
            }
            if (training.countryCode && this.countryOptions && this.countryOptions.length > 0) {
                const country = this.countryOptions.find((c) => c.value === training.countryCode);
                if (country) {
                    prefix += `【${country.label}】`;
                }
            }

            return prefix + training.name;
        },
        goBack() {
            const cid = this.$route.params.cid;
            this.$router.replace(`/index/chat_window/${cid}`);
        },
        handleClickProject(i) {
            this.selectTrainingDialogVisible = true;
        },
        getRoleBadgeClass(training) {
            if (training.roleData) {
                if (
                    training.roleData.role === SMART_TECH_TRAINING_ROLE.STUDENT &&
                    training.roleData.roleInfo === null
                ) {
                    return "status-no-role";
                }
                return this.trainingRoleMap[training.roleData.role]
                    ? this.trainingRoleMap[training.roleData.role].class
                    : "";
            }
            return "";
        },
        getRoleBadgeText(training) {
            if (training.roleData) {
                if (
                    training.roleData.role === SMART_TECH_TRAINING_ROLE.STUDENT &&
                    training.roleData.roleInfo === null
                ) {
                    return "未认证";
                }
                return this.trainingRoleMap[training.roleData.role]
                    ? this.trainingRoleMap[training.roleData.role].text
                    : "";
            }
            return "";
        },
        selectTraining(training) {
            if (!training.roleData) {
                return;
            }

            const role = training.roleData.role;
            const needsAuthentication =
                role === SMART_TECH_TRAINING_ROLE.STUDENT && training.roleData.roleInfo === null;

            if (needsAuthentication) {
                this.selectedTrainingForForm = training;
                this.registrationFormDialogVisible = true;
                console.log("选择项目进行报名:", training);
                return;
            }

            if (role === SMART_TECH_TRAINING_ROLE.PI_TEACHER || role === SMART_TECH_TRAINING_ROLE.SUPERVISOR) {
                console.log(
                    `角色${role === SMART_TECH_TRAINING_ROLE.PI_TEACHER ? "PI导师" : "导师"}，准备跳转至学员总览页面:`,
                    training
                );
                this.selectTrainingDialogVisible = false;
                Tool.loadModuleRouter({
                    name: "SmartTechTrainingStudentOverview",
                    params: {
                        ...this.$route.params,
                        trainingId: training._id,
                        role: role,
                    },
                });
            } else if (role === SMART_TECH_TRAINING_ROLE.STUDENT) {
                console.log("角色学员，准备跳转至考试项目页面:", training);
                this.selectTrainingDialogVisible = false;
                Tool.loadModuleRouter({
                    name: "SmartTechTrainingExamProject",
                    params: {
                        ...this.$route.params,
                        trainingId: training._id,
                        role: role,
                        extraData: training.roleData.roleInfo,
                    },
                });
            }
        },
        handleCloseRegistrationFormDialog() {
            this.registrationFormDialogVisible = false;
            this.clearRegistrationFormFields(true);
            this.isStudentIdLoading = false;
            this.studentIdFetchSuccess = false;
            this.showRegistrationFormDetails = false; // 重置表单显示状态
            this.studentIdError = ""; // 清除错误信息
            if (this.$refs.registrationFormRef) {
                this.$refs.registrationFormRef.clearValidate();
            }
        },
        submitRegistrationForm() {
            this.$refs.registrationFormRef.validate((valid) => {
                if (valid) {
                    if (!this.selectedTrainingForForm || !this.selectedTrainingForForm._id) {
                        this.$message.error("培训项目信息不完整，请重试");
                        return false;
                    }

                    this.isSubmitting = true; // 开始提交，设置加载状态

                    // 准备提交数据
                    const submitData = {
                        trainingID: this.selectedTrainingForForm._id,
                        uuid: this.registrationForm.studentId,
                    };

                    // 调用接口提交信息
                    service
                        .applyTrainingStudent(submitData)
                        .then((result) => {
                            const res = result.data;
                            if (res.error_code === 0) {
                                this.$message.success("学员认证信息提交成功");
                                this.registrationFormDialogVisible = false;
                                this.clearRegistrationFormFields(true); // 清空表单
                                this.getTrainingList(); // 刷新培训列表
                            } else {
                                this.$message.error(res.message || "提交失败，请重试");
                            }
                        })
                        .catch((err) => {
                            console.error("提交学员认证信息失败:", err);
                            this.$message.error("提交失败，请检查网络后重试");
                        })
                        .finally(() => {
                            this.isSubmitting = false; // 结束提交，取消加载状态
                        });
                } else {
                    console.log("Form validation failed");
                    return false;
                }
            });
        },
        getTrainingCountryList() {
            service
                .getTrainingCountryList()
                .then((result) => {
                    const res = result.data;
                    if (res.error_code === 0) {
                        // 将接口返回的数据转换为组件需要的格式
                        const countryList = res.data.map((item) => ({
                            label: item.zh, // 使用中文名称作为显示标签
                            value: item.code, // 使用国家代码作为值
                        }));

                        // 更新addressRegionOptions
                        this.addressRegionOptions = countryList;

                        // 更新countryOptions，用于filter2下拉列表
                        this.countryOptions = [...countryList];
                    }
                })
                .catch((err) => {
                    console.error("获取国家/地区列表失败:", err);
                });
        },
        getTrainingList(params = {}) {
            this.isLoading = true; // 开始加载
            this.trainings = [];
            const searchParams = {
                countryCode: this.countryCode,
                specialty: this.specialty,
                name: this.trainingName,
                ...params,
            };

            service
                .getTrainingList(searchParams)
                .then((result) => {
                    const res = result.data;
                    if (res.error_code === 0) {
                        // 更新培训列表数据
                        if (res.data && Array.isArray(res.data)) {
                            this.trainings = res.data;
                        } else {
                            this.trainings = [];
                        }
                    }
                })
                .catch((err) => {
                    console.error("获取智教培考试列表失败:", err);
                    this.trainings = []; // 发生错误时清空列表
                })
                .finally(() => {
                    this.isLoading = false; // 结束加载
                });
        },
        searchTraining() {
            this.getTrainingList();
        },
        fetchStudentInfoByUuidClean(studentId) {
            if (!studentId || String(studentId).trim() === "") {
                this.clearRegistrationFormFields(false); // 保留 studentId 输入框中的内容，但清空其他字段
                this.isStudentIdLoading = false;
                this.studentIdFetchSuccess = false;
                this.showRegistrationFormDetails = false;
                this.studentIdError = "";
                return;
            }
            this.isStudentIdLoading = true;
            this.studentIdFetchSuccess = false;
            this.studentIdError = "";
            service
                .getTrainingStudentInfoByUuid(studentId)
                .then((result) => {
                    this.isStudentIdLoading = false;
                    const res = result.data;
                    if (res.error_code === 0 && res.data) {
                        this.studentIdFetchSuccess = true;
                        this.studentIdError = "";
                        this.showRegistrationFormDetails = true;

                        this.registrationForm.name = res.data.name || "";
                        this.registrationForm.address = res.data.address || "";
                        this.registrationForm.mobile = res.data.cellphone || "";
                        this.registrationForm.email = res.data.email || "";
                        this.registrationForm.hospital = res.data.hospital || ""

                        let genderText = "其他";
                        if (res.data.sex !== undefined && res.data.sex !== null) {
                            const matchedGender = this.genderOptions.find((opt) => opt.value === res.data.sex);
                            if (matchedGender) {
                                genderText = matchedGender.label;
                            } else {
                                switch (res.data.sex) {
                                case 1:
                                    genderText = "男";
                                    break;
                                case 0:
                                    genderText = "女";
                                    break;
                                default:
                                    genderText = "其他";
                                }
                            }
                        }
                        this.registrationForm.gender = genderText;
                    } else {
                        this.studentIdFetchSuccess = false;
                        this.showRegistrationFormDetails = false;
                        this.clearRegistrationFormFields(false);
                        this.studentIdError = res.message || "获取学员信息失败或未找到该学员";
                    }
                })
                .catch((error) => {
                    this.isStudentIdLoading = false;
                    this.studentIdFetchSuccess = false;
                    this.showRegistrationFormDetails = false;
                    this.clearRegistrationFormFields(false);
                    this.studentIdError = "请求学员信息接口时发生错误";
                    console.error("获取学员信息失败:", error);
                });
        },
        handleStudentIdBlur() {
            this.studentIdFetchSuccess = false;
            this.isStudentIdLoading = false;
            this.studentIdError = ""; // 清除错误信息
            this.clearRegistrationFormFields(false);
            console.log("handleStudentIdBlur", this.registrationForm.studentId);
            if(!this.registrationForm.studentId){
                this.studentIdError = "";
                return;
            }
            this.debouncedFetchStudentInfo({
                uuid: this.registrationForm.studentId,
            });
        },
        handleStudentIdClear() {
            this.clearRegistrationFormFields(true); // 清空所有表单字段，包括学员ID
            this.isStudentIdLoading = false;
            this.studentIdFetchSuccess = false;
            this.showRegistrationFormDetails = false; // 隐藏其他表单项
            this.studentIdError = ""; // 清除错误信息
            if (this.$refs.registrationFormRef) {
                this.$refs.registrationFormRef.clearValidate(); // 清除表单校验信息
            }
        },
        clearRegistrationFormFields(clearId = true) {
            const defaultForm = getDefaultRegistrationForm();
            for (const key in this.registrationForm) {
                if (this.registrationForm.hasOwnProperty(key)) {
                    if (key === "studentId") {
                        if (clearId) {
                            this.registrationForm.studentId = defaultForm.studentId || "";
                        }
                    } else {
                        this.registrationForm[key] = defaultForm.hasOwnProperty(key) ? defaultForm[key] : "";
                    }
                }
            }
            for (const key in defaultForm) {
                if (!this.registrationForm.hasOwnProperty(key)) {
                    this.registrationForm[key] = defaultForm[key];
                } else if (key !== "studentId") {
                    this.registrationForm[key] = defaultForm[key];
                } else if (key === "studentId" && clearId) {
                    this.registrationForm.studentId = defaultForm.studentId;
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";
.smart-tech-training-main {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 20;
    display: flex;
    flex-direction: column;
    background-color: #f7f9fc;
}

.smart-tech-training-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fff;
}

.custom-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
    padding: 0 10px;
    position: relative;

    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        cursor: pointer;
        margin-right: 10px;
        font-weight: 600;
        i {
            font-size: 25px;
            color: #000;
        }

        &:hover i {
            color: #409eff;
        }
    }
}
.smart-tech-training-content {
    flex: 1;
    position: relative;
    padding: 40px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    overflow-y: auto;

    .card-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 30px;
        width: 100%;
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: transform 0.3s ease;
            cursor: pointer;
            &:hover {
                transform: translateY(-5px);
            }

            .card-image-placeholder {
                width: 100%;
                height: 300px;
                background-color: #e9f0f8;
                border-radius: 6px;
                margin-bottom: 15px;
            }

            .card-title {
                font-size: 18px;
                font-weight: bold;
                color: #303133;
                margin-bottom: 8px;
            }

            .card-description {
                font-size: 14px;
                color: #606266;
                line-height: 1.5;
            }
        }
    }
}

.project-select-container {
    display: flex;
    flex-direction: column;
    height: 500px; // 给一个固定高度或者通过父级来控制
    max-height: 70vh; // 或者使用视口高度

    .search-filter-bar {
        display: flex;
        justify-content: flex-start; /* 修改为左对齐 */
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #ebeef5;
        background-color: #fff;
        flex-shrink: 0;

        .filter-selectors {
            display: flex;
            gap: 10px;
            .el-select {
                width: 130px;
            }
            .country-select {
                /* 新增国家地区选择框的样式 */
                width: 340px;
            }
        }

        .search-input-area {
            margin-left: 10px; /* 添加间距 */
            flex: 1; /* 使搜索输入框占据剩余空间 */
            .el-input {
                width: 100%; /* 确保el-input占据父容器的全部宽度 */
            }
        }
    }

    .project-list {
        flex-grow: 1;
        overflow-y: auto;
        padding: 15px;
        background-color: #f7f9fc;

        .project-item {
            display: flex;
            align-items: center;
            background-color: #fff;
            border-radius: 6px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
            padding: 15px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: box-shadow 0.2s ease, transform 0.2s ease;
            justify-content: space-between; /* 用于主内容和状态徽章的左右布局 */

            &:hover {
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
                transform: translateY(-2px);
            }

            .project-item-details {
                flex-grow: 1;
                min-width: 0; /* 允许文本换行和溢出处理 */
                display: flex;
                justify-content: space-between;
                align-items: center;

                .project-item-main-content {
                    flex-grow: 1;
                    min-width: 0;
                }

                .project-item-title {
                    font-size: 15px;
                    font-weight: 600; /* 稍加粗 */
                    color: #303133;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }

        .project-item-status-badge {
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 4px;
            margin-left: 10px;
            flex-shrink: 0;

            &.status-no-role {
                background-color: #f4f4f5;
                color: #909399;
                border: 1px solid #e9e9eb;
            }
            &.status-pi-teacher {
                background-color: #fdf6ec;
                color: #e6a23c;
                border: 1px solid #faecd8;
            }
            &.status-supervisor {
                background-color: #ecf5ff;
                color: #409eff;
                border: 1px solid #d9ecff;
            }
            &.status-student {
                background-color: #f0f9eb;
                color: #67c23a;
                border: 1px solid #e1f3d8;
            }
        }

        .empty-list-placeholder {
            text-align: center;
            color: #909399;
            padding: 20px;
            font-size: 14px;
        }
    }
}

.registration-form-container {
    padding: 20px;
    min-height: 480px;

    .el-form-item {
        margin-bottom: 18px;
    }
    .el-select {
        /* 确保下拉选择框宽度正确 */
        width: 100%;
    }
    // 对于联系地址中的 el-select 作为 prepend
    .el-input-group__prepend .el-select .el-input {
        width: 100px; /* 根据实际需要调整 */
    }
    .form-actions {
        text-align: right;
        margin-top: 20px;
    }
    :deep(.icon-green-tick) {
        color: #67c23a; // Element UI success green
    }

    .student-id-tip {
        margin-top: -10px;
        margin-bottom: 15px;
        padding: 0 0 0 100px;
        color: #909399;
        font-size: 14px;
        line-height: 1.5;
    }
    .student-id-error {
        margin-top: -10px;
        margin-bottom: 15px;
        padding: 0 0 0 100px;
        color: #F56C6C;
        font-size: 14px;
        line-height: 1.5;
    }

    .info-text {
        min-height: 40px;
        line-height: 40px;
        color: #606266;
        padding: 0 15px;
        font-size: 14px;
    }
}
</style>
