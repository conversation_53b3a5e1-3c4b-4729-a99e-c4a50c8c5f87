<template>
    <div>
        <el-dialog
            class="cloud_video_edit_dialog"
            :visible="visible"
            :close-on-click-modal="true"
            :modal="false"
            :before-close="beforeCloseDialog"
            append-to-body
            v-loading="loading"
            element-loading-background="rgba(0, 0, 0, 1)"
            :element-loading-text="loadingText"
        >
            <iframe
                v-if="showIframe"
                :src="iframeSrc"
                frameborder="0"
                width="100%"
                height="100%"
                @load="frameLoaded"
                ref="iframe"
            ></iframe>
        </el-dialog>

        <CommonDialog
            :title="lang.export_comment"
            :show.sync="exportVisible"
            v-if="exportVisible"
            width="500px"
            height="auto"
            append-to-body
            :footShow="false"
            class="cloud_video_edit_export"
        >
            <div class="action-button-group">
                <el-button type="primary" round @click="exportResourceVideoClip" v-if="cid">{{
                    lang.send_original_chat
                }}</el-button>
                <el-button type="primary" round @click="changeGroupExport">{{ lang.reselect_chat_send }}</el-button>
                <el-button round @click="exportVisible = false">{{ lang.cancel_btn }}</el-button>
            </div>
        </CommonDialog>
        <UploadCover ref="uploadCoverDialog" @uploadSuccess="uploadSuccess"> </UploadCover>
    </div>
</template>
<script>
import base from "../../lib/base";
import service from "../../service/service";
import "../../../../common/tool";
import Tool from "../../../../common/tool";
import CommonDialog from "../../MRComponents/commonDialog.vue";
import UploadCover from "./uploadCover.vue";
export default {
    mixins: [base],
    name: "CloudVideoEditParent",
    model: {
        prop: "value",
        event: "change",
    },
    props: {
        resourceId: {
            type: [String, Number],
            default: "",
        },
        cid: {
            type: [String, Number],
            default: 0,
        },
        userId: {
            type: [String, Number],
            default: 0,
        },
        value: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        CommonDialog,
        UploadCover,
    },
    watch: {
        value: {
            handler(val) {
                this.visible = val;
            },
            immediate: true,
        },
        visible: {
            handler(val) {
                this.$emit("change", val);
            },
        },
    },
    data() {
        return {
            visible: true,
            loading: true,
            exportVisible: false,
            currentExportInfo: {},
            currentCoverImage: "",
            projectId: "",
            mediaId: "",
            delButtonClass: "",
            recallButtonClass: "",
            cutButtonClass: "",
            delButton: null,
            recallButton: null,
            recoverButton: null,
            cutButton: null,
            filePath: "",
            retryTime: 10,
            retryInterval: 3000,
            loadingText: "",
            lastVideoTracks: [],
            loopCheckTimeLineInterval: null,
            recallData: [], //撤回列表
            selectCoverIcon: [],
            iframeSrc:'',
            showIframe:false
        };
    },
    computed: {},
    created() {
        this.loading = true;
        this.loadingText = this.lang.loading_module_text;
        const server_type = this.$store.state.systemConfig.server_type
        let host = server_type.protocol + server_type.host + server_type.port
        if(window.location.href.includes('localhost')){
            this.iframeSrc = `http://localhost:8888/ultrasync_pc.html#/cloudVideoEditChild`
        }else{
            this.iframeSrc = Tool.transferLocationToCe(`${host}/pc/ultrasync_pc.html#/cloudVideoEditChild`)
        }

    },
    beforeDestroy() {
        window.removeEventListener("message", this.handleMessage);
    },
    mounted() {
        window.addEventListener("message", this.handleMessage);
        this.showIframe = true
    },
    methods: {
        async handleMessage(event) {
            console.log(event.data);
            const { data } = event;
            if (data.type === "cloudVideoEditChild") {
                if(data.hasOwnProperty('loaded')&&data.loaded){
                    this.loading = !data.loaded
                }

                if(data.hasOwnProperty('loadFailed')&&data.loadFailed){
                    this.retryTime--
                    if(this.retryTime>=1){
                        // this.setMessageToChild({retry:true})
                        this.showIframe = false
                        setTimeout(()=>{
                            this.showIframe = true
                        },300)
                    }else{
                        this.setMessageToChild({destroy:true})
                        this.visible = false
                        this.$message.error(this.lang.cloud_resources_processed);
                    }

                    this.loadingText = `${this.lang.server_resource_retried}(${this.retryTime})`;
                }
                if(data.hasOwnProperty('back')&&data.back){
                    this.visible = false
                }

                if(data.hasOwnProperty('clickCoverButton')&&data.base64Image){
                    this.$refs.uploadCoverDialog.openEditTargetModal(data.base64Image);
                }
                if(data.hasOwnProperty('exportData')){
                    try {
                        const params = {
                            ...data.exportData,
                            filePath: this.filePath,
                        }
                        console.error(params)
                        const res = await service.exportResourceVideoClip(params);
                        this.$message.success(this.lang.export_task_submitted);
                        this.visible = false
                    } catch (error) {
                        console.error(error);
                    }
                }
                if(data.hasOwnProperty('clickSelectGroupTransmit')&&data.clickSelectGroupTransmit){
                    this.$root.eventBus.$emit("openTransmit", {
                        callback: this.selectGroup
                    });
                }
            }
        },
        selectGroup(data) {
            this.setMessageToChild({
                exportResourceVideoClip:true,
                cid:data.cid
            })
        },
        frameLoaded() {
            this.setMessageToChild({
                loaded:true,
                resourceId:this.resourceId,
                userId:this.userId,
                cid:this.cid,
                languageConfig: {
                    currentLanguage: this.lang.currentLanguage,
                    langData: this.lang
                }
            })
        },
        setMessageToChild(data={}){
            const iframe = this.$refs.iframe.contentWindow;
            iframe.postMessage(
                {
                    type: "cloudVideoEditParent",
                    ...data

                },
                "*"
            );
        },
        uploadSuccess(filePath) {
            this.$message.success(this.lang.exported_video_use_cover);
            this.filePath = filePath;
            this.$refs.uploadCoverDialog.closeDialog();
        },
        beforeCloseDialog(){

        }
    },
};
</script>
<style lang="scss">
.cloud_video_edit_dialog {
    .el-dialog {
        width: 100%;
        height: 100% !important;
        margin: 0 !important;
        #cloud_video_edit {
            width: 100%;
            height: 100%;
        }
        .el-dialog__header {
            display: none !important;
        }
        .el-dialog__body {
            padding: 0 !important;
            height: 100% !important;
            overflow: hidden !important;
        }
    }
}
</style>
