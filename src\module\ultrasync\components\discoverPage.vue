<template>
    <div class="discover_page">
        <div class="discover_list">
            <div class="discover_item" v-if="functionsStatus.library&&libraryInfo&&libraryInfo.protocol && libraryInfo.addr" @tap="toRepository">
                <img src="static/resource/images/library.png" class="icon-png" />
                <p class="menue-title">{{lang.mindray_library}}</p>
            </div>
            <div class="discover_item" v-if="functionsStatus.breastCases && EnableAiSearch" @click="toCaseDatabase">
                <img src="static/resource/images/breast-search.png" class="icon-png"  />
                <p class="menue-title">{{lang.case_database_title}}</p>
            </div>
            <div class="discover_item" @click="toPracticeOverview" v-if="functionsStatus.ai">
                <img src="static/resource/images/homework_correct_action.png" class="icon-png"  />
                <p class="menue-title">{{ lang.clinical_thinking_practice_title }}</p>
            </div>
            <div class="discover_item" @click="showClub" v-if="false&&isCE">
                <img src="static/resource/images/homework_correct_action.png" class="icon-png"  />
                <p class="menue-title">Mindray Club</p>
            </div>
            <div class="discover_item" @click="showImageList" v-if="functionsStatus.ai">
                <img src="static/resource/images/homework_correct_action.png" class="icon-png"  />
                <p class="menue-title">{{ lang.files }}</p>
            </div>
        </div>
    </div>
</template>
<script>
import base from '../lib/base';
import Tool from '@/common/tool.js'
export default {
    mixins:[base],
    
    components:{
    },
    data(){
        return {
        }
    },
    computed:{
        libraryInfo(){
            return  this.$store.state.systemConfig.serverInfo.library_server;
        },
        EnableAiSearch(){
            return this.$store.state.systemConfig.serverInfo.ai_searcher_server
            &&this.$store.state.systemConfig.serverInfo.ai_searcher_server.enable&&!this.isCE
        },
    },
    watch:{
        
    },
    mounted(){
        
    },
    beforeDestroy(){
        
    },
    methods:{
        toRepository(){
            this.$router.push(`/index/repository`)
            //this.$router.push(`/index/libraryCategory`)
        },
        toCaseDatabase(){
            this.$router.push('/index/case_database')
        },
        toPracticeOverview() {
            Tool.loadModuleRouter('/index/ai_main/practice_overview');
        },
        showClub(){
            Tool.loadModuleRouter('/index/club_entry');
        },
        showImageList(){
            this.$router.push('/index/file_list');
        }
    }
}
</script>
<style lang="scss">
.discover_page{
    height:100%;
    position:relative;
    z-index:1;
    transform:translate3d(0,0,0);
    display: flex;
    flex-direction: column;
    background: #fff;
    .discover_list{
        flex-shrink: 0;
        .discover_item{
            display: flex;
            align-items: center;
            color: #00c59d;
            line-height: 1;
            padding: .6rem 1rem;
            background: #f2f6f9;
            margin-bottom: .2rem;
            p{
                font-size: 0.7rem;
                line-height: 1.2;
                word-wrap: break-word;
                padding-left:.6rem;
            }
        }
    }
}
</style>
