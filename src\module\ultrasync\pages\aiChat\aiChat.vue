<template>
    <div class="ai-chat_page">
        <keep-alive :exclude="/./">
            <router-view></router-view>
        </keep-alive>
        <div class="chat-main">
            <div v-show="isCurrentNewChat" class="welcome-container">
                <div class="welcome-avatar">
                    <mr-avatar
                        :url="`static/resource/images/ai_logo_large_6.png`"
                        :radius="5.5"
                        :showOnlineState="false"
                        class="avatar"
                    ></mr-avatar>
                </div>
                <h2 class="welcome-title">{{ lang.welcome_to_realm_imaging_intelligence }}</h2>

                <!-- 添加推荐提示文字 -->
                <div class="recommendations-title">{{ lang.you_could_say_tips }}：</div>
                <!-- 添加推荐列表 -->
                <div class="recommendations">
                    <div
                        class="recommendation-item"
                        @click="handleRecommendationClick('显微造影（SR CEUS）技术有什么临床价值和意义？')"
                    >
                        <span class="text">显微造影（SR CEUS）技术有什么临床价值和意义？</span>
                    </div>
                    <div
                        class="recommendation-item"
                        @click="handleRecommendationClick('针对肥胖患者，怎样提高超声图像的清晰度？')"
                    >
                        <span class="text">针对肥胖患者，怎样提高超声图像的清晰度？</span>
                    </div>
                </div>
            </div>
            <!-- 消息列表区域 -->
            <div class="chat-messages" ref="messageContainer">
                <div v-for="(msg, index) in messages" :key="index" :class="['message', msg.type]">
                    <div class="message-info">
                        <mr-avatar
                            :url="msg.type === 'user' ? getLocalAvatar(user) : aiAvatar"
                            :origin_url="user.avatar"
                            :radius="2.1"
                            :showOnlineState="false"
                            :key="user.avatar"
                        ></mr-avatar>
                        <div class="message-time">{{ msg.time }}</div>
                    </div>
                    <div class="message-content-wrapper">
                        <div class="message-content" v-if="msg.type === 'ai'">
                            <span v-if="msg.status === 'thinking'" class="thinking-tips">{{
                                `${lang.thinking}...`
                            }}</span>
                            <span v-if="msg.status === 'answering'" class="answering-tips">{{
                                `${lang.answering}...`
                            }}</span>
                            <div class="reasoning-content" v-if="msg.hasReasoningContent">
                                <html-renderer :html="renderMarkdown(msg.reasoningContent)" />
                                <van-loading type="spinner" size="16" v-if="msg.status === 'thinking'"></van-loading>
                            </div>
                            <div class="answer-content">
                                <html-renderer :html="renderMarkdown(msg.content)" />
                                <van-loading type="spinner" size="16" v-if="msg.status === 'answering'" />
                                <template v-if="msg.status === 'completed_stop_by_user'">
                                    <span class="stop-by-user-tips">[{{ lang.user_has_terminated_answering }}]</span>
                                </template>
                            </div>
                            <div
                                class="message-tools"
                                v-show="msg.status === 'completed' || msg.status === 'completed_stop_by_user'"
                            >
                                <div class="copy-btn" @click="copyContent(msg.content)">
                                    <i class="iconfont icon-copy" style="font-size: 15px"></i>
                                </div>
                            </div>
                        </div>

                        <div class="message-content" v-else>
                            <html-renderer :html="renderMarkdown(msg.content)" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input">
                <div class="chat-options">
                    <div class="options-left">
                        <van-checkbox v-model="deepThinking" class="custom-checkbox">{{
                            lang.deep_thinking
                        }}</van-checkbox>
                        <van-checkbox v-model="webSearch" class="custom-checkbox">联网搜索</van-checkbox>
                    </div>
                    <div class="options-right">
                        <i class="icon iconfont icon-copilot_addchat" @click="createNewChat"></i>
                        <i class="icon iconfont icon-time" @click="dialogVisible = true"></i>
                        <!-- <van-button class="history-btn" @click="handleGoToPractice">
                            <span class="icon"><i class="iconfont icon-clinical_thinking_practice"></i></span>
                            <span class="text">{{ lang.clinical_thinking_practice_title }}</span>
                        </van-button>
                        <van-button class="new-topic-btn" icon="newspaper-o" @click="createNewChat">{{
                            lang.new_chat
                        }}</van-button> -->
                    </div>
                </div>
                <div class="input-wrapper">
                    <van-field
                        v-model="inputMessage"
                        type="textarea"
                        rows="1"
                        autosize
                        :placeholder="`${lang.professional_imaging_knowledge_tips}`"
                        @keypress.enter.prevent="sendMessage"
                        class="max-three-lines"
                        maxlength="1000"
                    />
                    <div class="input-actions">
                        <template v-if="isWaitingResponse">
                            <van-button
                                icon="close"
                                round
                                size="normal"
                                plain
                                @click="stopAskQuestion(currentChatId)"
                                :loading="isAborting"
                            ></van-button>
                        </template>
                        <template v-else>
                            <van-button
                                type="primary"
                                icon="guide-o"
                                :loading="isWaitingResponse"
                                @click="sendMessage"
                            ></van-button>
                        </template>
                    </div>
                </div>
            </div>

            <!-- 侧边栏抽屉 -->
            <ai-chat-sidebar
                :dialogVisible.sync="dialogVisible"
                :chat-list="chatList"
                :current-chat-id="currentChatId"
                @chat-select="handleChatSelect"
                @delete-chat="deleteChat"
            />
        </div>
    </div>
</template>

<script>
import base from "../../lib/base";
import { getLocalAvatar } from "../../lib/common_base";
import Tool from "@/common/tool";
import "highlight.js/styles/github.css";
import "katex/dist/katex.min.css";
import { Button, Field, Checkbox, Icon, Popup, Loading } from "vant";
import dialogPopstate from "../../lib/dialogPopstate";
import moment from "moment"; // 确保已经安装并导入 moment
import AIChatService from "@/common/aiChatService";
import HtmlRenderer from "@/components/vNodeRender/htmlRender.vue";
import { Toast } from "vant";
import AiChatSidebar from "./aiChatSidebar.vue";
export default {
    mixins: [base, dialogPopstate],
    name: "AiChatPage",
    components: {
        [Button.name]: Button,
        [Field.name]: Field,
        [Checkbox.name]: Checkbox,
        [Icon.name]: Icon,
        [Popup.name]: Popup,
        [Loading.name]: Loading,
        HtmlRenderer,
        AiChatSidebar,
    },
    computed: {
        messages() {
            return this.chatMessages[this.currentChatId] || [];
        },
        isCurrentNewChat() {
            // 添加对 chatList 的直接引用来确保响应式
            const listLength = this.chatList.length;
            if(listLength === 0 || Object.keys(this.chatMessages).length === 0){
                return true;
            }else{
                return (
                    this.chatList[0]?.id === this.currentChatId &&
                    (!this.chatMessages[this.currentChatId] || this.chatMessages[this.currentChatId].length === 0)
                );
            }
        },
    },
    data() {
        return {
            getLocalAvatar,
            inputMessage: "",
            chatList: [],
            currentChatId: 1,
            chatMessages: {}, // 存储每个会话的消息
            deepThinking: false,
            webSearch: false,
            aiAvatar: "static/resource/images/ai_logo_small_6.png", // 添加AI头像
            isWaitingResponse: false,
            isUserScrolling: false, // 添加新变量追踪用户是否在滚动
            lastScrollTop: 0, // 记录上次滚动位置
            dialogVisible: false,
            loadedChats: new Set(), // 新增：记录已加载历史记录的会话ID
            aiChatService: null,
            isAborting: false,
            creatingChat: false,
        };
    },
    created() {
        this.aiChatService = new AIChatService();
        this.createNewChat();
        this.getRecentConversationList();
    },
    mounted() {
        // 添加滚动事件监听
        const messageContainer = this.$refs.messageContainer;
        messageContainer.addEventListener("scroll", this.handleScroll);
        this.$root.eventBus.$on("ai-chat-link-click", this.handleLinkClick);
    },

    beforeDestroy() {
        // 清理事件监听
        const messageContainer = this.$refs.messageContainer;
        messageContainer.removeEventListener("scroll", this.handleScroll);
        this.$root.eventBus.$off("ai-chat-link-click");
        this.dialogVisible = false;
        // 如果请求还在进行中,终止请求
        this.stopAskQuestion(this.currentChatId);
    },
    methods: {
        formatMessageTime(timestamp) {
            const messageTime = moment(timestamp);
            const today = moment().startOf("day");

            if (messageTime.isSame(today, "day")) {
                // 今天的消息只显示时分秒
                return messageTime.format("HH:mm:ss");
            } else {
                // 昨天及以前的消息显示完整的年月日时分秒
                return messageTime.format("YYYY-MM-DD HH:mm:ss");
            }
        },
        async createNewChat() {
            // 如果正在创建，直接返回
            if (this.creatingChat) {
                return;
            }
            this.creatingChat = true;
            if (this.isGeneratingAIResponse()) {
                await this.stopAskQuestion(this.currentChatId);
            }
            try {
                // 检查是否已有空白对话
                const existingEmptyChat = this.chatList.find((chat) => {
                    return this.chatMessages[chat.id]?.length === 0;
                });
                if (existingEmptyChat) {
                    this.switchChat(existingEmptyChat.id, true);
                    return;
                }
                // 创建新对话
                const data = await this.aiChatService.createAiConversation();
                const newId = data.conversationID;
                this.chatList.unshift({
                    id: newId,
                    title: this.lang.new_chat,
                });
                this.$set(this.chatMessages, newId, []);
                this.loadedChats.add(newId);
                this.switchChat(newId, true);
            } finally {
                this.creatingChat = false;
            }
        },
        async getRecentConversationList() {
            const data = await this.aiChatService.getRecentConversationList();
            data.forEach((item) => {
                this.chatList.push({
                    id: item.conversationID,
                    title: item.question,
                });
            });
        },
        getConversationHistory(conversationID) {
            return new Promise(async (resolve, reject) => {
                const data = await this.aiChatService.getConversationHistory(conversationID);
                resolve(data);
            });
        },
        renderMarkdown(content) {
            if (!content) {
                return "";
            }
            return this.aiChatService.renderMarkdown(content);
        },
        async switchChat(chatId, newConversation = false) {
            console.log("switchChat", chatId, newConversation);
            if (chatId === this.currentChatId) {
                return;
            }
            await this.aiChatService.abort();

            this.currentChatId = chatId;
            this.inputMessage = "";

            // 只在首次加载时获取历史记录
            if (!this.loadedChats.has(chatId) && !newConversation) {
                this.$set(this.chatMessages, chatId, []);
                const data = await this.getConversationHistory(chatId);
                // 初始化当前会话的消息数组

                // 处理每条历史记录
                data.forEach((record) => {
                    // 添加AI回答消息
                    if ((record.answer && record.answer.trim()) || record.status === 2) {
                        let aiMessage = {
                            type: "ai",
                            content: record.answer,
                            reasoningContent: record.reasoning_content || "",
                            hasReasoningContent: !!record.reasoning_content,
                            time: this.formatMessageTime(record.updatedAt),
                            status: "completed",
                        };
                        if (record.status === 2) {
                            this.$set(aiMessage, "status", "completed_stop_by_user");
                        }
                        this.chatMessages[chatId].unshift(aiMessage);
                    }
                    // 添加用户消息
                    this.chatMessages[chatId].unshift({
                        type: "user",
                        content: record.question,
                        time: this.formatMessageTime(record.createdAt),
                        status: "completed",
                    });
                });
                this.loadedChats.add(chatId); // 标记该会话已加载历史记录
                this.$nextTick(() => {
                    this.scrollToBottom(true);
                });
            }
            this.$nextTick(() => {
                this.scrollToBottom(true);
            });
        },

        deleteChat(chatId) {
            const index = this.chatList.findIndex((chat) => chat.id === chatId);
            if (index !== -1) {
                this.chatList.splice(index, 1);
                this.$delete(this.chatMessages, chatId);

                // 如果删除的是当前会话，切换到第一个会话
                // if (this.currentChatId === chatId && this.chatList.length > 0) {
                //     this.currentChatId = this.chatList[0].id;
                // }

                if (this.currentChatId === chatId && this.chatList.length > 0) {
                    // 优先选择有消息内容的会话
                    let targetChat = this.chatList.find(chat => 
                        this.chatMessages[chat.id] && this.chatMessages[chat.id].length > 0
                    );
                    // 如果没有有内容的会话，就选择第一个
                    if (!targetChat) {
                        targetChat = this.chatList[0];
                    }
                    this.switchChat(targetChat.id);
                }

                // 检查是否还有"新建聊天"
                const hasNewChat = this.chatList.some(chat => chat.title === this.lang.new_chat);

                // 如果没有"新建聊天"，并且没有其他会话，则创建一个新的
                console.log("hasNewChat", hasNewChat, this.chatList.length);
                if (!hasNewChat && this.chatList.length === 0) {
                    this.createNewChat();
                }
            }
            // 删除 chat 后，将等待响应状态设为 false
            this.isWaitingResponse = false;
            this.aiChatService.deleteAiConversation(chatId);
        },
        clearMessages() {
            this.messages = [];
        },

        async sendMessage() {
            if (this.inputMessage.trim() === "" || this.isWaitingResponse) {
                return;
            }
            const messageContent = this.inputMessage.trim();
            // 发送用户消息
            const userMessage = {
                type: "user",
                question: messageContent,
                conversationID: this.currentChatId,
                content: messageContent,
                time: this.formatMessageTime(new Date()),
            };

            if (!this.chatMessages[this.currentChatId]) {
                this.chatMessages[this.currentChatId] = [];
            }

            this.chatMessages[this.currentChatId].push(userMessage);
            // 如果是当前会话的第一条消息,更新会话标题
            if (this.chatMessages[this.currentChatId].length === 1) {
                const chat = this.chatList.find((chat) => chat.id === this.currentChatId);
                if (chat) {
                    this.$set(chat, "title", messageContent);
                }
            }
            this.inputMessage = "";
            this.scrollToBottom(true);

            this.isWaitingResponse = true;
            try {
                await this.getAIResponse(userMessage);
            } finally {
                this.isWaitingResponse = false;
            }
        },
        async getAIResponse(userMessage) {
            try {
                await this.askQuestion(userMessage.question, userMessage.conversationID);
            } catch (error) {
                console.error("Error:", error);
                // 使用 Vue.set 或 this.$set 来确保响应式更新
                this.$set(userMessage, "content", this.lang.sorry_an_error_occurred_please_try_again);
                this.$set(userMessage, "status", "error");
            }
        },
        async askQuestion(question, conversationID) {
            try {
                if (!this.chatMessages[conversationID]) {
                    this.$set(this.chatMessages, conversationID, []);
                }

                const aiMessage = {
                    type: "ai",
                    status: "thinking",
                    reasoningContent: "",
                    content: "",
                    time: this.formatMessageTime(new Date()),
                    hasReasoningContent: false,
                };
                this.chatMessages[conversationID].push(aiMessage);

                await this.aiChatService.askQuestion({
                    question,
                    conversationID,
                    deepThinking: this.deepThinking,
                    webSearch: this.webSearch,
                    onThinking: (reasoningContent) => {
                        if (aiMessage.status === "completed_stop_by_user") {
                            return;
                        }
                        if (reasoningContent) {
                            if (!aiMessage.hasReasoningContent) {
                                this.$set(aiMessage, "hasReasoningContent", true);
                            }
                            let content = aiMessage.reasoningContent + reasoningContent;
                            this.$set(aiMessage, "reasoningContent", content);
                        }
                        this.$set(aiMessage, "status", "thinking");
                        this.$nextTick(() => this.scrollToBottom());
                    },
                    onAnswering: (content) => {
                        if (aiMessage.status === "completed_stop_by_user") {
                            return;
                        }
                        let newContent = aiMessage.content + content;
                        this.$set(aiMessage, "content", newContent);
                        this.$set(aiMessage, "status", "answering");
                        this.$nextTick(() => this.scrollToBottom());
                    },
                    onCompleted: () => {
                        if (aiMessage.status === "completed_stop_by_user") {
                            return;
                        }
                        this.$set(aiMessage, "status", "completed");
                        this.$nextTick(() => this.scrollToBottom());
                    },
                    onError: (message, error) => {
                        if (aiMessage.status === "completed_stop_by_user") {
                            return;
                        }
                        let content = aiMessage.content + `\n[${this.lang.server_request_exception_tips}]`;
                        this.$set(aiMessage, "content", content);
                        this.$set(aiMessage, "status", "error");
                        this.$nextTick(() => this.scrollToBottom());
                    },
                    onAbort: () => {
                        if (aiMessage.status === "completed_stop_by_user") {
                            return;
                        }
                        console.log("onAbort", conversationID, aiMessage);
                        this.$set(aiMessage, "status", "completed_stop_by_user");
                        this.$nextTick(() => this.scrollToBottom());
                    },
                });
            } catch (error) {
                console.error("Error:", error);
                const aiMessage = this.chatMessages[conversationID].findLast(
                    (msg) => msg.type === "ai" && msg.status !== "completed"
                );
                if (aiMessage) {
                    let content = aiMessage.content + `\n[${this.lang.server_request_exception_tips}]`;
                    this.$set(aiMessage, "content", content);
                    this.$set(aiMessage, "status", "error");
                }
            }
        },

        //是否正在生成AI代码
        isGeneratingAIResponse() {
            const currentMessages = this.chatMessages[this.currentChatId] || [];
            const lastMessage = currentMessages[currentMessages.length - 1];
            return lastMessage && lastMessage.type === "ai" && lastMessage.status === "answering";
        },

        async stopAskQuestion(conversationID) {
            if (!conversationID || !this.chatMessages[conversationID]) {
                return;
            }
            console.log("stopAskQuestion", conversationID, this.chatMessages[conversationID]);
            const aiMessage = this.chatMessages[conversationID].findLast(
                (msg) => msg.type === "ai" && msg.status !== "completed"
            );
            if (aiMessage) {
                console.log("stopAskQuestion", conversationID, aiMessage);
                this.$set(aiMessage, "status", "completed_stop_by_user");
            }
            this.isAborting = true;
            await this.aiChatService.abort();

            this.isAborting = false;
        },

        async copyContent(content) {
            // 如果是点击代码块的复制按钮
            if (content.startsWith("```") && content.endsWith("```")) {
                content = content.replace(/^```[\s\S]*?\n/, "").replace(/```$/, "");
            }
            await Tool.copyToClipboard(content);
            Toast(this.lang.copy_text_success);
        },
        handleScroll() {
            const container = this.$refs.messageContainer;
            const isScrolledToBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 100; // 添加100px的缓冲区

            // 如果不在底部，标记用户正在滚动
            this.isUserScrolling = !isScrolledToBottom;
            this.lastScrollTop = container.scrollTop;
        },
        scrollToBottom(force = false) {
            this.$nextTick(() => {
                const container = this.$refs.messageContainer;
                // 只有当用户没有主动滚动或者之前已经在底部时才自动滚动
                if (container && (!this.isUserScrolling || force)) {
                    container.scrollTop = container.scrollHeight;
                }
            });
        },
        handleChatSelect(chatId) {
            this.switchChat(chatId);
            this.dialogVisible = false;
        },
        handleGoToPractice() {
            this.$router.push({
                name: "ai_chat_practice_overview",
            });
        },
        handleLinkClick(url) {
            Tool.openMobileDialog({
                message: `<p>${this.lang.jump_external_links_tips}</p><p>${encodeURI(url)}</p>`,
                showRejectButton: true,
                confirm: () => {
                    Tool.openLinkByDefaultBrowser(encodeURI(url));
                },
            });
        },
        handleRecommendationClick(question) {
            this.inputMessage = question;
            this.sendMessage();
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync/style/aiChat.scss';
.ai-chat_page {
    flex: 1;
    overflow: hidden;
    .chat-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: hidden;
        position: relative;
        .welcome-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 0 0.96rem;
            text-align: center;
            height: 100%;
            position: absolute;
            top: -3rem;
            left: 0;
            right: 0;
            bottom: 0;
            .welcome-avatar {
                margin-bottom: 1.5rem;
                border-radius: 50%;
                box-sizing: content-box;
                opacity: 1;
                position: relative;
                // padding: 2px;
                // background: rgba(199, 0, 10, 1);
                &::after {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    border-radius: 50%;
                    // animation: darkPulse 3s ease-in-out infinite;
                    // box-shadow: 0 0 0.96rem rgba(199, 0, 10, 1);
                    z-index: -1;
                }

                .avatar {
                    width: 3.5rem;
                    height: 3.5rem;
                    background: rgba(0, 0, 0, 0.05);
                    border-radius: 50%;
                }
            }

            .welcome-title {
                font-size: 0.9rem;
                font-weight: 600;
                margin-bottom: 1.3rem;
                color: #333;
            }

            .welcome-subtitle {
                font-size: 0.8rem;
                color: #606266;
                line-height: 1.5;
                padding: 0 0.96rem;
                max-width: 18rem;
                margin: 0 auto;
            }
            .recommendations-title {
                font-size: 0.65rem;
                color: #999;
                margin-bottom: 0.5rem;
                width: 100%;
                max-width: 20rem;
                text-align: left;
                padding-left: 0.3rem;
            }
            .recommendations {
                width: 100%;
                max-width: 20rem;
                display: flex;
                flex-direction: column;
                gap: 0.6rem;

                .recommendation-item {
                    display: flex;
                    align-items: flex-start;
                    padding: 0.3rem 0.5rem;
                    border-radius: 0.3rem;
                    cursor: pointer;
                    transition: all 0.3sease;
                    border: 1px solid #ebedf0;
                    background: #f8f9fa;
                    justify-content: flex-start;
                    text-align: left;
                    .text {
                        font-size: 0.65rem;
                        color: #666;
                        line-height: 1.5;
                        white-space: normal;
                        word-break: break-word;
                    }
                }
            }
        }
        .chat-messages {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 0.48rem;
            overflow-y: auto;

            .message {
                margin-bottom: 0.72rem;
                max-width: 90%;
                width: auto;
                display: flex;
                flex-direction: column;

                &.user {
                    margin-left: auto;
                    align-items: flex-end;
                    align-self: flex-end;

                    .message-info {
                        flex-direction: row-reverse;
                        width: 100%;
                        justify-content: flex-start;
                    }

                    .message-content-wrapper {
                        display: flex;
                        flex-direction: row;
                        justify-content: flex-end;
                        max-width: 100%;
                    }

                    .message-content {
                        background-color: #95ec69;
                        color: #000;
                        :deep(p) {
                            white-space: pre-wrap;
                            word-break: break-word;
                        }
                    }
                }

                &.ai {
                    .message-content {
                        .thinking-tips {
                            font-size: 0.577rem;
                            color: #666;
                        }
                        .answering-tips {
                            font-size: 0.577rem;
                            color: #666;
                        }
                        .answer-content {
                            .stop-by-user-tips {
                                font-size: 0.577rem;
                                color: #666;
                            }
                            .answer-wrapper {
                                position: relative;

                                .copy-icon {
                                    position: absolute;
                                    top: 0.385rem;
                                    right: 0.385rem;
                                    padding: 0.24rem;
                                    color: #666;
                                    cursor: pointer;

                                    &:hover {
                                        color: #1989fa;
                                    }
                                }
                            }
                            :deep(pre) {
                                background-color: #f6f8fa;
                                padding: 0.77rem;
                                border-radius: 0.29rem;
                                margin: 0.48rem 0;
                                overflow-x: auto;
                                max-width: 100%;
                                white-space: pre;
                                position: relative;

                                .code-block-header {
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    margin-bottom: 0.5rem;
                                    padding-bottom: 0.5rem;
                                    border-bottom: 1px solid #e1e4e8;

                                    .code-block-header__lang {
                                        color: #666;
                                        font-size: 0.7rem;
                                    }

                                    .code-copy-btn {
                                        padding: 0.2rem 0.4rem;
                                        background: #fff;
                                        border: 1px solid #ddd;
                                        border-radius: 0.2rem;
                                        cursor: pointer;
                                        font-size: 0.7rem;
                                        color: #666;
                                        transition: all 0.3s;

                                        &:hover {
                                            background: #f0f0f0;
                                            color: #1989fa;
                                            border-color: #1989fa;
                                        }
                                    }
                                }

                                .code-block-body {
                                    margin: 0;
                                    padding: 0;
                                    background: transparent;
                                }
                            }

                            :deep(code) {
                                font-family: Monaco, Consolas, Courier New, monospace;
                                white-space: pre;
                                overflow-x: auto;
                            }

                            :deep(p) {
                                margin: 0.385rem 0;
                            }

                            :deep(table) {
                                border-collapse: collapse;
                                margin: 0.48rem 0;
                                max-width: 100%;
                                table-layout: fixed;

                                th,
                                td {
                                    border: 0.048rem solid #ddd;
                                    padding: 0.385rem;
                                }
                            }

                            :deep(blockquote) {
                                border-left: 0.192rem solid #ddd;
                                margin: 0.48rem 0;
                                padding: 0 0.77rem;
                                color: #666;
                            }

                            :deep(ul),
                            :deep(ol) {
                                padding-left: 0.96rem;
                                margin: 0.48rem 0;
                            }

                            :deep(h1),
                            :deep(h2),
                            :deep(h3),
                            :deep(h4),
                            :deep(h5),
                            :deep(h6) {
                                margin: 0.77rem 0 0.385rem;
                            }
                        }

                        .reasoning-content {
                            font-size: 0.67rem;
                            color: #666;
                            font-style: italic;
                            padding: 0.385rem;
                            background-color: rgba(0, 0, 0, 0.03);
                            border-radius: 0.192rem;
                            margin-bottom: 0.385rem;
                        }

                        .message-tools {
                            margin-top: 0.2rem;
                            display: flex;
                            justify-content: flex-end;

                            .copy-btn {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                padding: 0.1rem 0.2rem;
                                border: 1px solid #dcdfe6;
                                border-radius: 0.3rem;
                                color: #606266;
                                cursor: pointer;
                                transition: all 0.3s;

                                &:hover {
                                    color: #1989fa;
                                    border-color: #1989fa;
                                }

                                &:active {
                                    opacity: 0.8;
                                }
                            }
                        }
                    }
                }

                .message-content-wrapper {
                    margin-bottom: 0.192rem;
                    margin-top: 0.29rem;
                    font-size: 0.8rem;
                    width: 100%;
                    overflow-wrap: break-word;
                    word-wrap: break-word;
                    .thinking-indicator {
                        display: flex;
                        align-items: center;
                        gap: 0.385rem;
                        color: #666;
                        margin-bottom: 0.385rem;
                    }
                }

                .message-content {
                    padding: 0.385rem 0.577rem;
                    border-radius: 0.192rem;
                    word-break: break-word;
                    line-height: 1.5;
                    box-shadow: 0.048rem 0.048rem 0.192rem 0.096rem rgb(0 0 0 / 10%);
                    display: inline-block;
                    max-width: 100%;
                    overflow-wrap: break-word;
                    word-wrap: break-word;
                    white-space: normal;
                    overflow-x: hidden;
                }

                .message-info {
                    display: flex;
                    align-items: center;
                    gap: 0.385rem;

                    .message-time {
                        font-size: 0.577rem;
                        color: #999;
                    }
                }
            }
        }
    }

    .chat-sidebar {
        height: 100%;
        padding: 0.77rem;
        background-color: #fff;
        display: flex;
        flex-direction: column;

        .chat-list {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 1rem;
            .chat-item {
                display: flex;
                align-items: center;
                padding: 0.577rem;
                margin-bottom: 0.385rem;
                border-radius: 0.192rem;

                &.active {
                    background-color: #e8f2ff;
                }

                .chat-title {
                    flex: 1;
                    margin: 0 0.385rem;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-size: 0.67rem;
                }

                .delete-icon {
                    color: #ee0a24;
                }
            }
        }
    }

    .chat-input {
        padding: 0.577rem 0.8rem;
        background-color: #fff;
        border-top: 0.048rem solid #eee;
        flex-shrink: 0;
        z-index: 1;
        position: relative;
        .input-wrapper {
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 1.2rem;
            padding: 0.3rem 0.5rem;

            :deep(.van-field) {
                background-color: transparent;
                flex: 1;
                padding: 0.24rem;
                .van-field__control {
                    min-height: 1.2rem;
                    padding: 0.3rem 0;
                }

                &.max-three-lines {
                    .van-field__control {
                        max-height: 4.5rem; /* 约等于三行文本的高度 */
                        overflow-y: auto;
                        scrollbar-width: thin;
                    }

                    .van-field__control::-webkit-scrollbar {
                        width: 0.25rem;
                    }

                    .van-field__control::-webkit-scrollbar-thumb {
                        background-color: #c8c8c8;
                        border-radius: 0.125rem;
                    }
                }
            }

            .input-actions {
                display: flex;
                align-items: center;
                .van-button {
                    @extend .ai-theme-background;
                    width: 2.14rem;
                    height: 2.14rem;
                    padding: 0;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #fff;
                }
            }
        }

        .chat-options {
            display: flex;
            gap: 0.577rem;
            margin-bottom: 0.385rem;
            .options-left {
                display: flex;
                :deep(.custom-checkbox) {
                    .van-checkbox__label {
                        background: #f5f5f5;
                        padding: 0.29rem 0.5rem;
                        border-radius: 0.8rem;
                        font-size: 0.6rem;
                        transition: all 0.3s;
                    }

                    .van-checkbox__icon {
                        display: none;
                    }

                    .van-checkbox__icon--checked {
                        + .van-checkbox__label {
                            background: #1989fa;
                            color: white;
                        }
                    }
                }
            }
            .options-right {
                display: flex;
                gap: 0.4rem;
                margin-left: auto;
                .iconfont{
                    font-size: 1.2rem;
                    margin-left: 0.6rem;
                    color: #323233;
                }
                .history-btn,
                .new-topic-btn {
                    font-size: 0.6rem;
                    padding: 0.29rem 0.4rem;
                    border-radius: 0.8rem;
                    height: auto;
                    .van-button__content {
                        line-height: 0;
                    }
                    .van-icon {
                        margin-right: 0.1rem;
                        font-size: 0.7rem;
                    }
                    .van-button__text {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        .icon {
                            font-size: 0.7rem;
                            margin-right: 0.3rem;
                        }
                    }
                }
            }
        }
    }
}

@keyframes avatarFadeIn {
    0% {
        opacity: 0;
        transform: translateY(1rem);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes avatarGlow {
    0% {
        box-shadow: 0 0 0.72rem rgba(199, 0, 10, 0.5);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 1.44rem rgba(199, 0, 10, 1);
        transform: scale(1.05);
    }
    100% {
        box-shadow: 0 0 0.72rem rgba(199, 0, 10, 0.5);
        transform: scale(1);
    }
}

@keyframes darkPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.4);
    }

    70% {
        box-shadow: 0 0 0 20px rgba(0, 0, 0, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    }
}
</style>
