<template>
    <div class="deep_bg_wrap"
        v-loading.fullscreen.lock="loginLoading"
        :element-loading-text="lang.loginLoading">
        <cookies-notification v-if="isCookiesNotificationShow"/>
        <div class="main_viewport">
            <div class="flex_container">
                <main-menu></main-menu>
                <gallery></gallery>
                <report></report>
                <transmit></transmit>
                <div class="network_unavailable" v-show="loadingConfig.networkUnavailable">{{lang.network_unavailable}}</div>
                <audio id="message_notify" src='static/resource_pc/audio/notify.wav'></audio>
                <header-bar></header-bar>
                <div class="chat_container">
                    <left-tab></left-tab>
                    <chat-window></chat-window>
                    <router-view></router-view>

                </div>
            </div>
            <span class="web_build_time" v-if="devEnv">buildTime:{{buildTime}}</span>
            <a target="_blank" href="https://beian.miit.gov.cn/#/Integrated/index" class="license" v-if="!globalParams.isCef&&!globalParams.isCE">粤ICP备05083646号-4</a>
        </div>
        <group-avatar></group-avatar>
        <user-avatar ref="user_avatar"></user-avatar>
        <safe-auth></safe-auth>
        <download-progress-bar></download-progress-bar>
        <init-organization ref="init_organization"></init-organization>
        <LivingNotifyDialog v-model="showLivingNotifyDialog" :livingGroupInfo="livingGroupInfo" @close="handleDialogClose"></LivingNotifyDialog>
        <DownLoadManager></DownLoadManager>
        <qr-scan-action></qr-scan-action>
    </div>
</template>
<script>
import headerBar from '../components/headerBar'
import leftTab from '../components/leftTab'
import mainMenu from '../components/mainMenu'
import downloadProgressBar from '../components/downloadProgressBar'
import chatWindow from '../components/chatWindow'
import transmit from '../components/transmit'
import ServiceConfig from '@/common/ServiceConfig.js'
import CMainScreen from '../lib/CMainScreen'
import CAiEngineer from '../lib/CAiEngineer'
import CFileTransferAssistanter from '../lib/CFileTransferAssistanter'
import CCentralStationProvider from '../lib/CCentralStationProvider'
import CCentralStationUser from '../lib/CCentralStationUser'
import CFeedbackQuestionAssistanter from '../lib/CFeedbackQuestionAssistanter'
import service from '../service/service'
import multiCenterService from '../service/multiCenterService'
import base from '../lib/base'
import groupsetTool from '../lib/groupsetTool'
import sendMessage from '../lib/sendMessage'
import Tool from '@/common/tool.js'
import iworksTool from '../lib/iworksTool';
import report from '../components/report.vue'
import CWorkstationCommunicationMng from '@/common/CommunicationMng/index'
import groupAvatar from '../components/groupAvatar'
import userAvatar from '../components/userAvatar'
import safeAuth from '../components/safeAuth'
import {cloneDeep} from 'lodash'
import CEvent from "@/common/CEvent";
import InitOrganization from '../components/initOrganization.vue'
import LivingNotifyDialog from '../components/live/livingNotifyDialog.vue'
import DownLoadManager from '../components/downLoadManager.vue'
import qrScanAction from '../components/qrScanAction.vue'
import {resumeAllTasks} from '@/common/oss'
import {
    setExpirationResource,
    setIworksInfoToMsg,
    parseImageListToLocal,
    htmlEscape,
    closeChatWindowIfNeed,
    sortFriendList,
    pushImageToList,
    getLocalImgUrl,
    handleAfterLogin,
    getDefaultPreferences,
    patientDesensitization,
    getSocketServer,
    parseServerInfo,
    findServiceId,
    transferPatientInfo,
    getBaseUrl,
    getLiveRoomObj,
    checkIsCreator,
    checkIsManager,
    destroyAllConference,
} from '../lib/common_base'
import cookiesNotification from '../components/cookiesNotification.vue'
export default {
    name: 'index_pc',
    mixins:[base, sendMessage,iworksTool,groupsetTool],
    components:{
        cookiesNotification,
        headerBar,
        leftTab,
        chatWindow,
        mainMenu,
        downloadProgressBar,
        transmit,
        gallery: () => import(/* webpackPrefetch: true */ '../components/gallery.vue'),
        groupAvatar,
        userAvatar,
        safeAuth,
        InitOrganization,
        LivingNotifyDialog,
        DownLoadManager,
        report,
        qrScanAction
    },
    data(){
        return {
            loadingConfig:this.$store.state.loadingConfig,
            task_index:0,
            isCookiesNotificationShow: false,
            applyingPermission:false,
            notifying:false,
            loginLoading:false,
            notify_delete_attendee_timer:null, //删除群聊人数防抖计时器
            notify_add_attendee_timer:null,//新增群聊人数防抖计时器
            autoLoginTime:0,
            autoLogonTimer:null,
            isTopFileTransferAssistant:false,//是否置顶文件传输助手(多端登录)
            hasSetCurrentList:false,
            isAutoLogging:false,//防止重复自动登录
            isFirstLoadServerInfo:false,
            showLivingNotifyDialog:false,//展示直播提示框
            livingGroupInfo:{}
        }
    },
    computed:{
        chatList(){
            return this.$store.state.chatList.list
        },
        deviceInfo(){
            return this.$store.state.device
        },
        buildTime() {
            return process.env.VUE_APP_BUILD_TIME;
        },
        devEnv(){
            return this.user.build_version === 'dev'
        }
    },
    beforeCreate(){

    },
    created(){
        this.debounceUpdateLiveCount = Tool.debounce(this.updateLiveCount,1000)
        this.debounceSortChatList = Tool.debounce(this.sortChatList,600,true)
    },
    mounted(){
        this.$nextTick(()=>{
            //恢复窗口大小
            this.isCookiesNotificationShow = Tool.checkAppClient('Browser')
            CWorkstationCommunicationMng.navigationShowNormalOrMaximize();
            var that=this;
            this.$root.eventBus.$off('notifyStartupOption').$on('notifyStartupOption', function (data) {
                window.g_extend_info = data;
                console.log('notifyStartupOption---',data)
                if (data) {
                    //第三方登录
                    window.localStorage.setItem('password','');
                    if(that.user.fromLogin){
                        //已登录
                        if (!window.main_screen) {
                            that.initPage();
                        }
                        var option = Tool.parseStartupOption(data);
                        if (that.user.outer_id && 0 < that.user.outer_id.length && that.user.outer_id == option.outer_id) {
                            //同账号,打开会话
                            that.startConversationWithStartupOption();
                        } else {
                            //不同账号,重新登录
                            that.$router.replace(`/login`);
                            that.$root.eventBus.$emit('reloadRouter')
                        }
                    }else{
                        //未登录
                        that.$router.replace(`/login`);
                        that.$root.eventBus.$emit('reloadRouter')
                    }
                } else {
                    //自登录
                    console.log('notifyStartupOption---2',that.user.fromLogin)
                    if(that.user.fromLogin){
                        that.initPage();
                    }else{
                        that.loginLoading=true
                        that.autoLogin(()=>{
                            that.initPage()
                        })
                    }
                }
            });
            //CWorkstationCommunicationMng.QueryStartupOption();
            this.$root.eventBus.$on('NotifyGetDeviceID',(param)=>{
                console.log("################### NotifyGetDeviceID index.vue ###################",param);
                this.device_id = param.device_id;
                if("GetDeviceID_only" != param.append_info){
                    CWorkstationCommunicationMng.QueryStartupOption();
                }
            })
            CWorkstationCommunicationMng.GetDeviceID({});
            this.$root.eventBus.$off('updateProgressOSS').$on('updateProgressOSS',function(data){
                if (data.error) {
                    that.$store.commit('conversationList/updateUploadFail',{
                        cid:data.cid,
                        file_id:data.file_id
                    })
                }else{
                    that.$store.commit('conversationList/updateFileProgress',{
                        msg:{
                            file_id:data.file_id,
                            group_id:data.cid
                        },
                        percent:data.progress
                    })
                    if (data.progress==100) {
                        that.updateUploadProgress(data)
                    }else{
                    }
                }
            });
            //istation转发图片
            this.$root.eventBus.$off('initMachineTransfer').$on('initMachineTransfer',that.initMachineTransfer);
            this.$root.eventBus.$off('updateMachineTransfer').$on('updateMachineTransfer',that.updateMachineTransfer);
            this.$root.eventBus.$off('finishMachineTransfer').$on('finishMachineTransfer',that.finishMachineTransfer);
            this.$root.eventBus.$off('notifyNewExamImages').$on('notifyNewExamImages',that.notifyNewExamImages);
            document.addEventListener('keyup',(event)=>{
                if (this.$route.name=='gallery') {
                    //给画廊添加的按钮事件
                    if (event.keyCode==27) {
                        this.$root.eventBus.$emit('closeGallery')
                    }
                    if (event.keyCode==37||event.keyCode==38) {
                        this.$root.eventBus.$emit('prevImage')
                    }
                    if (event.keyCode==39||event.keyCode==40) {
                        this.$root.eventBus.$emit('nextImage')
                    }
                }

            })

            // 血站-专家系统 文件交互
            this.$root.eventBus.$off('DealNotifySendFileToConversation').$on('DealNotifySendFileToConversation',this.DealNotifySendFileToConversation);
            this.$root.eventBus.$off('UpdateSendFileToConversation').$on('UpdateSendFileToConversation',this.UpdateSendFileToConversation);
            //检测音频设备的插拔事件
            this.$root.eventBus.$off('NotifyShowConfirmDialog').$on('NotifyShowConfirmDialog',that.NotifyShowConfirmDialog);
            this.$root.eventBus.$off('getCameraDevice').$on('getCameraDevice', (jsonStr) => {

                let json = jsonStr.replace(/\\/g, '/')
                const datas = JSON.parse(json).camera
                that.$store.commit('device/updateCameraDeviceListByApp', datas)
            })
            window.addEventListener('message',(event)=>{
                var option = {};
                try{
                    option = JSON.parse( event.data);
                } catch (e) {
                    option = {};
                }

                if ("iworks_statistics_loaded" == option.message) {
                    this.$root.eventBus.$emit('iworks_statistics_loaded')
                }
            }, false);
            this.$root.eventBus.$off('NotifySwitchNativeRtcSettingStatus').$on('NotifySwitchNativeRtcSettingStatus',that.NotifySwitchNativeRtcSettingStatus);
            this.$root.eventBus.$off('leaveSilence').$on('leaveSilence',that.leaveSilence);
            this.$root.eventBus.$off('openConversationFromIndexByUserId').$on('openConversationFromIndexByUserId',that.openConversationFromIndexByUserId);
            this.$root.eventBus.$off('updateLiveCount').$on('updateLiveCount',that.debounceUpdateLiveCount);
            this.$root.eventBus.$off('addExamToAnalyze').$on('addExamToAnalyze',that.addExamToAnalyze);
            this.$root.eventBus.$off('clearAndDirectToLogin').$on('clearAndDirectToLogin',that.clearAndDirectToLogin);
            this.$root.eventBus.$off('unBindControllerEvent').$on('unBindControllerEvent',that.unBindControllerEvent)
            window.addEventListener('beforeunload', function(event) {
                // 页面即将卸载时的处理逻辑
                destroyAllConference()
                // event.preventDefault(); // 阻止默认的提示框弹出
                // event.returnValue = ''; // 自定义提示语
            });
        })
    },
    destroyed(){
        this.$root.eventBus.$off('NotifyGetDeviceID');
    },
    methods:{
        initPage(){
            //自动下载
            var auto_download = localStorage.getItem('auto_download');
            if(auto_download) {
                this.$store.commit('globalParams/updateGlobalAutoDownload', JSON.parse(auto_download));
            }

            //自动推流
            var auto_push_stream = localStorage.getItem('auto_push_stream_' + this.user.id);
            if(auto_push_stream) {
                this.$store.commit('globalParams/updateGlobalAutoPushStream', JSON.parse(auto_push_stream));
            }

            //推流参数初始化
            var catch_option = localStorage.getItem('catch_option');
            if (!catch_option) {
                catch_option = JSON.stringify({image_mode:1});
                localStorage.setItem('catch_option', catch_option);
            }
            catch_option = JSON.parse(catch_option);
            window.catch_option = catch_option;
            window.CWorkstationCommunicationMng.setCatchOption(catch_option);
            //初始化
            this.initMainScreen();
            // if(this.user.enable_monitor_wall && this.user.is_into_tv_wall == 1 && Tool.ifAppConsultationClientType(window.clientType)){
            //     //客户端下，电视墙可用且设置自动进入电视墙，用户角色在主任以上
            //     if (this.user.role>1) {
            //         this.$root.eventBus.$emit('enterTVmode')
            //     }
            // }
        },
        initMainScreen(){
            var that = this;
            let socketServer=getSocketServer()
            var option = {
                uid: that.user.id,
                url: socketServer,
                client_uuid:this.user.client_uuid,
                client_type: this.systemConfig.clientType,
                service_type: this.user.service_type
            };
            window.main_screen = this.newMainScreen(option);
            var controller = window.main_screen.controller;
            controller.init(this);
            this.initMainScreenControllerEvent(controller);
            this.initDeviceFailureMap()
        },
        initNetworkData(){
            this.$store.commit('loadingConfig/updateLoaded',{
                key:'networkUnavailable',
                loaded:false
            });
            // this.$store.commit('conversationList/clearConversation');
            window.main_screen.initGateway(this.$store.state.user.client_uuid);
            var controller = window.main_screen.controller;
            controller.init(this);
            this.initMainScreenControllerEvent(window.main_screen.controller);

        },
        newMainScreen(option){
            var main_screen = null;
            if(ServiceConfig.type.AiAnalyze == option.service_type||ServiceConfig.type.DrAiAnalyze == option.service_type) {
                main_screen = new CAiEngineer(option);
            } else if(ServiceConfig.type.FileTransferAssistant == option.service_type) {
                main_screen = new CFileTransferAssistanter(option);
            } else if(ServiceConfig.type.CentralStation == option.service_type) {
                main_screen = new CCentralStationProvider(option);
            } else if(ServiceConfig.type.CentralStationUser == option.service_type) {
                main_screen = new CCentralStationUser(option);
            } else if(ServiceConfig.type.FeedbackQuestionAssistant == option.service_type) {
                main_screen = new CFeedbackQuestionAssistanter(option);
            } else {
                main_screen = new CMainScreen(option);
            }

            return main_screen;
        },
        initMainScreenControllerEvent(controller){
            this.isAutoLogging=false;
            var that = this;
            // //Gateway
            controller.on('gateway_connect',function(){
                that.socketConnectSuccess();
                that.getAllTags();
                that.updateLiveCount()
                that.getMultiCenterOptionList()
                that.getDeviceNameById()
                that.getGroupSetList()
                that.getAiAnalyzeTypes()
                controller.emit("get_consultation_image_list",{
                    start:0,
                    count:that.systemConfig.consultationImageShowNum
                },that.setConsultationImageList);

                controller.emit('get_all_hospital_name',that.setAllHospital)
                //获取所属医院
                controller.emit('get_user_info')
                controller.emit('get_version_info')
                controller.emit("init_main_screen_controller_event",controller);
                setTimeout(()=>{
                    resumeAllTasks()
                },2000)
                if (that.user.pacsCid) {
                    // PACS唤起云++登录成功后启动会话
                    that.joinAndStartConversation(that.user.pacsCid)
                }
                // 连上socket后检查头像初始化，初始化机构窗口
                that.$refs['user_avatar'].ifCreateUserAvatar()
                that.$refs['init_organization'].init()
            });
            controller.on("gateway_error",function(data){
                that.socketError(data);
            });
            controller.on("gateway_reconnecting",function(){
                that.socketReconnecting();
            });
            controller.on("gateway_reconnect_fail",function(data){
                that.socketReconnectFail(data);
            });
            controller.on("gateway_reconnect",function(){
                that.socketReconnect();
            });
            controller.on("gateway_disconnect",function(data){
                that.socketDisconnect(data);
            });
            // //MainScreen
            controller.on("recent_active_conversation_list",function(is_succ,data){
                that.setCurrentList(is_succ,data);

                // 查询是否需要把独立工作站的检查信息发送到云++
                window.CWorkstationCommunicationMng.QueryStandaloneWorkstationShareExamInfo();

            });
            controller.on("recent_active_conversation_list_last_message",function(is_succ,data){
                that.setLastMessage(is_succ,data);
                that.$store.commit('chatList/updateUnReadMap',data)
            });
            controller.on("friend_list",function(is_succ,data){
                that.setFriendList(is_succ,data);
                //获得好友列表后，判断是否有DR AI数据需要上传,等待2.5s数据存入store
                setTimeout(()=>{
                    that.autoUploadDrAiData()
                },2500)
            });
            controller.on("userAddLoginClient",function(data){
                if(data.allClientType.length>1){
                    that.isTopFileTransferAssistant = true;
                    if(that.hasSetCurrentList){//已登录的一端监听到多端登录
                        that.topFileTransferAssistant();
                    }
                }


            });
            controller.on("conversation_list",function(is_succ,data){
                that.setGroupList(is_succ,data);
            });
            controller.on("group_applys",function(is_succ,data){
                that.setGroupApplys(is_succ,data);
            });
            controller.on("friend_applys",function(is_succ,data){
                that.dealFriendApplys(is_succ,data);
            });
            controller.on("userResponseFriendApply",function(data){
                that.friendApplyResponse(data);
            });
            controller.on("userAddFriend",function(data){
                that.notifyAddFriend(data.friendInfo);
            });
            controller.on("userApplyAddFriend",function(data){
                that.setFriendApplys(data);
            });

            // //Other
            controller.on("notify_add_friend",function(data){
                that.notifyAddFriend(data);
            });
            controller.on("update_friend_info",function(data){
                that.updateFriendInfo(data);
            });
            controller.on("notify_friend_destroy",function(data){
                that.updateFriendDestroy(data);
            });
            controller.on("update_user_info",function(data){
                that.onUpdateUserInfo(data);
            });
            controller.on("update_user_portrait_img",function(data){
                that.onUpdateUserPortraitInfo(data);
            });
            controller.on("notify_start_conversation",function(is_succ,conversation,start_type,kickout_data){
                that.NotifyStartConversation(is_succ,conversation,start_type,kickout_data);
            });
            controller.on("notify_login_another",function(){
                that.notifyLoginAnother();
            });
            controller.on("notify_user_destroy",function(){
                that.notifyUserDestroy();
            });
            controller.on("server_info",function(data){
                let json=parseServerInfo(data)
                that.$store.commit('systemConfig/updateSystemConfig',{
                    serverInfo:json
                });
                if(json.network_environment === 1&&json.storageReplaceInfo.replace){
                    that.observeImageLoad(json)
                }
                //通知APP初始化服务器的配置
                window.CWorkstationCommunicationMng.initServerConfig(json);
                that.initUserConfig2App();
                if((!that.isFirstLoadServerInfo)){
                    that.isFirstLoadServerInfo = true
                    that.sendSyncAccountOrLiveToULinker()
                    that.checkAutoEnterTvWall()
                    if(Tool.checkAppClient('Cef')){
                        //查詢IStation信息
                        that.queryIStationInfo_DR()
                        that.setWhiteBoardUrl()
                        Tool.initNativeAgoraSdk(json.agora_appid).then(async ()=>{
                            setTimeout(()=>{
                                that.ifNeedAutoPushStream()
                            },1500)
                        })
                    }
                }
                // window.main_screen.CMonitorWallPush.controller.emit('startJoinRoomSilence')
            });
            controller.on("notify_delete_group",function(json_str){
                let data = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
                that.$store.commit('chatList/deleteChatList', {cid: data.cid})
                that.$store.commit('groupList/deleteGroupList', {cid: data.cid})
                that.$store.commit('conversationList/deleteConversationList', {cid: data.cid})
                that.$store.commit('notifications/deleteGroupApplyByCid', {cid: data.cid})
                //删除群相关的图像
                that.$store.commit('consultationImageList/deleteConsultationImageListByGroupID', {cid: data.cid});
                window.main_screen.conversation_list[data.cid].onResponseDeleteAttendee()
                let liveRoom = getLiveRoomObj(window.vm.$root.currentLiveCid)
                if(liveRoom){
                    liveRoom.LeaveChannelAux()
                    window.CWorkstationCommunicationMng.StopConference()
                }
                closeChatWindowIfNeed(data.cid);
                setTimeout(()=>{
                    that.$message.success(that.lang.user_exit_group_succ);
                },100)
            });

            controller.on("open_register_scan_room_view",function(data){
                var attributes = {
                    mac_addr: data.mac_addr,
                    name: "",
                    hospital_id: 0,
                    ultrasync_box_mac_addr: data.mac_addr
                };
                that.$store.commit('user/updateUser',{
                    scan_room:attributes
                })
                // todo  hanzhijie
                // that.$router.replace('/index/chat_window/0/register_scanroom')
            });

            controller.on('user_info',(is_succ, info)=>{
                info.preferences = getDefaultPreferences(info);
                that.$store.commit('user/updateUser',info)
            })
            controller.on('version_info',function(info){
                window.server_info=info;
            })
            controller.on('request_conversation_start_ultrasound_desktop',function(data){
                that.notifyStartConversationByMonitorWall(data)
            })
            controller.on('notify_download_task',function(data){
                if ("error" == data.type) {
                    that.$message.error(data.errorInfo);
                }
            })
            controller.on("notify_update_groupset_portrait",function(err, result){
                if(!err){
                    that.notifyUpdateGroupsetAvatar(result)
                }
            });

            controller.on("notify_update_media_transfer_task", function (err,result) {
                if(!err){
                    that.$store.commit('taskList/updateMediaTransferTasks',result.list);
                }
            });

            controller.on("notify_delete_media_transfer_task", function (err,result) {
                if(!err){
                    that.$store.commit('taskList/deleteMediaTransferTasks',result.list);
                }
            });

            controller.emit("query_media_transfer_tasks", {}, function (err, result) {
                if (!err) {
                    that.$store.commit('taskList/initMediaTransferTasks',result.list);
                }
            });

            controller.on("notify_exception",function(data){
                that.notifyException(data);
            });

            controller.on("NotifyStandaloneWorkstationShareExamInfo",function(data){
                that.NotifyStandaloneWorkstationShareExamInfo(data);
            });
            controller.on("receive_group_message",function(data){
                console.log('receive_group_message',data,2)
                if(!that.conversationList.hasOwnProperty(data.group_id)){
                    // that.setSayChatMessageByNoConversation()
                    that.setSayChatMessageReceiveGroupMessage(data,false)
                    if(data.msg_type === that.systemConfig.msg_type.LIVE_INVITE||data.groupInfo.service_type === that.systemConfig.ServiceConfig.type.LiveBroadCast){
                        that.debounceUpdateLiveCount()
                    }
                    that.debounceSortChatList()
                }
            });
            controller.on("notify_agora_live_start",function(data){
                that.NotifyAgoraLiveStart(data);
            });
            controller.on("notify_agora_live_stop",function(data){
                that.NotifyAgoraLiveStop(data);
            });
            controller.on("notify_update_recording",function(data){
                that.NotifyUpdateLiveRecord(data);
            });
            controller.on("notify_update_announcement",function(data){
                that.NotifyUpdateAnnouncement(data);
            });
            controller.on("notify.group.resource.delete.exam",function(data){
                console.log(data,'notify.group.resource.delete.exam')
                that.$store.commit('examList/deleteExamListItem',{
                    cid:data.groupID,
                    exam_id:data.examID,
                })
                that.$root.eventBus.$emit('deleteExamItem');
                if(Array.isArray(data.deleteResourceIDList)){
                    data.deleteResourceIDList.forEach(resource_id=>{
                        that.$store.commit("resourceTempStatus/updateResourceTempStatus",{
                            resource_id,
                            data:{
                                state:0 //被删除
                            }
                        });
                        that.$store.commit('chatList/updateLastChatMessageByResourceDelete',{
                            cid:data.groupID,
                            data:{
                                msg_type:that.systemConfig.msg_type.ResourceDelete,
                                resource_id
                            }
                        });
                    })
                }
                if(Array.isArray(data.deleteMessageIDList)){
                    that.$store.commit("conversationList/deleteChatMessagesByGmsgIdList",{
                        gmsg_id_list:data.deleteMessageIDList,
                        cid:data.groupID
                    });
                    that.$root.eventBus.$emit('notifyDeleteChatMessages',{
                        cid: data.groupID,
                        gmsg_id_list:data.deleteMessageIDList
                    })
                }
            });
            controller.on('notify.group.resource.delete.resource',(data)=>{
                if(Array.isArray(data.deleteResourceIDList)){
                    data.deleteResourceIDList.forEach(resource_id=>{
                        that.$store.commit("resourceTempStatus/updateResourceTempStatus",{
                            resource_id,
                            data:{
                                state:0 //被删除
                            }
                        });
                        if(data.deleteMessageIDList.length>0){
                            that.$store.commit('chatList/updateLastChatMessageByResourceDelete',{
                                cid:data.groupID,
                                data:{
                                    msg_type:that.systemConfig.msg_type.ResourceDelete,
                                    resource_id
                                }
                            });
                        }
                        that.$root.eventBus.$emit('deleteFileToExamList',{
                            cid: data.groupID,
                            resource_id
                        })
                    })
                }
                if(Array.isArray(data.deleteMessageIDList)){
                    that.$store.commit("conversationList/deleteChatMessagesByGmsgIdList",{
                        gmsg_id_list:data.deleteMessageIDList,
                        cid:data.groupID
                    });
                    that.$root.eventBus.$emit('notifyDeleteChatMessages',{
                        cid: data.groupID,
                        gmsg_id_list:data.deleteMessageIDList
                    })
                }
            });
            controller.on('notify_refresh_manager_groupset_list',(data)=>{
                console.log('notify_refresh_manager_groupset_list',data)
                if(data.action === 'delete' && (Number(this.$route.params.groupset_id) === data.groupSetID)){
                    this.$router.replace('/index/chat_window/0')
                }
                this.getManagerGroupsetList()
            });
            controller.on('notify_refresh_my_groupset_list',(data)=>{
                console.log('notify_refresh_my_groupset_list',data)
                if(data.action === 'delete' && (Number(this.$route.params.groupset_id) === data.groupSetID)){
                    this.$router.replace('/index/chat_window/0')
                }
                this.getGroupSetList()
            });
            //主控-AI分析报告更新
            controller.on('update_ai_analyze_report',(data)=>{
                // console.error('update_ai_analyze_report',data)
            });
            //云作业待完成列表更新通知
            controller.on('student_answer_sheet_update',(data)=>{
                if (data.type === 'add') {
                    // 新增时全局待完成数为0才更新
                    if (this.$store.state.homework.globalUnfinish === 0) {
                        this.getUnfinishedHomework(0);
                    }
                }else{
                    this.getUnfinishedHomework(0);
                }
                data.gidList.forEach(cid=>{
                    if (this.$store.state.conversationList[cid]) {
                        this.getUnfinishedHomework(cid);
                    }
                })

                // 添加对已批改作业的处理
                this.getCorrectedHomework(0);
                data.gidList.forEach(cid=>{
                    if (this.$store.state.conversationList[cid]) {
                        this.getCorrectedHomework(cid);
                    }
                })
            });
            //云作业待批改列表更新通知
            controller.on('teacher_answer_sheet_update',(data)=>{
                if (data.type === 'add') {
                    // 新增时全局待批改数为0才更新
                    // if (this.$store.state.homework.globalUnCorrect === undefined) {
                    this.getUncorrectHomework(0);
                    // }
                }else{
                    this.getUncorrectHomework(0);
                }
                data.gidList.forEach(cid=>{
                    if (this.$store.state.conversationList[cid]) {
                        this.getUncorrectHomework(cid);
                    }
                })
            });
            //同账号自己给自己发通知
            controller.on('notify_msg_from_owner',(data)=>{
                console.error('notify_msg_from_owner',data)
            });
            //设备故障更新通知
            controller.on('equipment_server_device_alram_update',(data)=>{
                let deviceFailure = this.$store.state.device.deviceFailure
                let num =deviceFailure[data.device_id] || 0;
                if (data.status === 'NEW') {
                    num++;
                }else if (data.status === 'RESOLVE') {
                    num > 0 ? num--:0;
                }
                deviceFailure[data.device_id] = num;
                this.$store.commit('device/updateDeviceFailure',deviceFailure);
            });
            this.getUnfinishedHomework(0);
            this.getUncorrectHomework(0);
            this.getCorrectedHomework(0);
            this.$store.commit('globalParams/updateGlobalParams',{
                init_main_screen_time:new Date().getTime()
            });
        },
        openConversationFromIndexByUserId(id,callback){
            this.openConversationByUserId(id,callback)
        },
        notifyUpdateGroupsetAvatar(result){
            let groupset={
                avatar:result.avatar
            }
            this.$store.commit('groupset/updateGroupsetAvatar',{
                avatar:groupset.avatar,
                id:result.groupset_id,
            })
            this.$message.success(this.lang.modify_photo_success);
            this.back();
        },
        setCurrentList(is_succ,list){
            var that=this;
            if (is_succ) {
                this.$store.commit('chatList/initChatList',list);
                this.$store.commit('loadingConfig/updateLoaded',{
                    key:'loadedChatList',
                    loaded:true
                });

                //如果多端登录，置顶文件传输助手
                if(this.isTopFileTransferAssistant){
                    this.topFileTransferAssistant()
                }
                this.hasSetCurrentList = true;

            }else{
                this.$message.error('setCurrentList error')
            }
        },
        autoLogin(callback){
            console.log('[event] index autoLogin');
            if (this.isAutoLogging) {
                console.log('repeat auto login reject');
                return
            }
            this.isAutoLogging=true;

            if (window.main_screen && window.main_screen.gateway) {
                window.main_screen.CloseSocket()
            }
            this.unBindControllerEvent()
            // var account=window.localStorage.getItem('account')||''
            // var password=window.localStorage.getItem('password')||''

            const storeToken=this.user.new_token
            const loginToken=window.localStorage.getItem('loginToken')||storeToken||''
            const account=window.localStorage.getItem('account')||''
            const password=window.localStorage.getItem('password')||''
            const language=window.localStorage.getItem('lang')||'CN'
            let local_store_device_token = window.localStorage.getItem('local_store_device_token');
            const device_id = this.deviceInfo.device_id;
            let needChangeDeviceId = false
            var token = "";
            if(local_store_device_token){
                var info = JSON.parse(local_store_device_token);
                if(info && info.device_id){
                    if(device_id != info.device_id){//设备id有修改,不能用原来的device_id和token登陆
                        window.localStorage.removeItem('local_store_device_token');
                        needChangeDeviceId = true
                    }else{
                        if(info && info.token){
                            token = info.token
                        }
                    }
                }
            }
            console.log("################### index.vue device_id ################### ", device_id);
            var that=this;
            if (loginToken!=='') {
                this.autoLoginAction({
                    action:'loginByToken',
                    loginToken:loginToken,
                    callback:callback,
                })
            }else if(account!==''&&password!==''){
                this.autoLoginAction({
                    action:'getLoginToken',
                    account:account,
                    password:password,
                    callback:callback,
                })
            }else if((needChangeDeviceId || '' == token)){
                console.log('[event] autoLogin - login');
                this.$router.replace(`/login`)
                this.$root.eventBus.$emit('reloadRouter')
                window.localStorage.setItem('password','');
                window.localStorage.setItem('loginToken','');
                this.$store.commit('user/updateUser',{
                    new_token:''
                });
                // location.reload();
            }else{
                console.log('autoLogin--',(new Date()).toLocaleString())
                let ajaxServer=''
                service.login({
                    action_form:'login_action',
                    user:{
                        name:'',
                        pwd:'',
                        device_id:device_id,
                        token:token,
                        extend_info:"",
                        type:this.systemConfig.clientType,
                        response_data_type:1,
                        language:language
                    }
                }).then((res)=>{
                    if(res.data&&res.data.uid){
                        setTimeout(()=>{
                            this.loginLoading=false
                        },300)
                        this.$store.commit('loadingConfig/updateLoaded',{
                            key:'networkUnavailable',
                            loaded:false
                        });
                        this.$store.commit('user/updateUser', res.data);
                        callback(res)
                        if (res.data.probationary_expiry) {
                            let msg=this.lang.nls_probationary_expiry_tip.replace('{1}',res.data.probationary_expiry)
                            this.$MessageBox.alert(msg)
                        }
                    }else{
                        this.isAutoLogging=false;
                        this.resetStartupOption();
                        let msg = this.lang.unknown_error;
                        if (res.data.msg && res.data.msg) {
                            msg = res.data.msg;
                        }
                        this.clearAndDirectToLogin(msg);
                    }
                },(res)=>{
                    this.isAutoLogging=false;
                    let message=this.lang.network_error_tip
                    this.$store.commit('loadingConfig/updateLoaded',{
                        key:'networkUnavailable',
                        loaded:true
                    });
                    setTimeout(() => {
                        this.autoLogin(callback)
                    },5000);
                })
            }
        },
        setLastMessage(is_succ,list){
            //设置最后一条消息记录
            if (is_succ) {
                for(let item of list){
                    item.message.msg_body=this.parseMessageBody(item.message.msg_body)
                    if(item.message.been_withdrawn === 2){
                        item.message.msg_type = this.systemConfig.msg_type.WITHDRAW
                    }else if(item.message.been_withdrawn === 1){
                        item.message.msg_type = this.systemConfig.msg_type.ResourceDelete
                    }
                    this.$store.commit('chatList/setLastMessage',item);
                }
            }else{
                this.$message.error('setLastMessage error')
            }
        },
        setConsultationImageList(is_succ,data){
            console.log('setConsultationImageList',is_succ,cloneDeep(data))
            //放置图像列表数据
            if(is_succ){
                patientDesensitization(data.consultation_image_list);
                parseImageListToLocal(data.consultation_image_list,'url')
                if(data.iworks_protocol_list){
                    this.setGalleryMessageDetail(is_succ,{iworks_protocol_list:data.iworks_protocol_list})
                }
                let consultation_image_list = data.consultation_image_list
                for (let item of  data.consultation_image_list) {
                    setIworksInfoToMsg(item);
                }
                if(is_succ != "net_error"){
                    this.$store.commit('consultationImageList/initConsultationImages',data)
                }
                this.$store.commit('loadingConfig/updateLoaded',{
                    key:'loadedFileList',
                    loaded:true
                });
            }else{
                this.$message.error('setConsultationImageList error')
            }
        },
        setFriendList(is_succ,list){
            //设置好友列表
            if (is_succ) {
                this.$store.commit('loadingConfig/updateLoaded',{
                    key:'loadedFriendList',
                    loaded:true
                });
                this.$store.commit('friendList/initFriendList',list);
                sortFriendList();
            }else{
                this.$message.error('setFriendList error')
            }
        },
        async topFileTransferAssistant(){
            //置顶文件传输助手
            let service_type=this.systemConfig.ServiceConfig.type.FileTransferAssistant
            let analyze = await findServiceId(service_type)
            console.log("文件传输助手id:",analyze);
            if (analyze.cid) {
            // 会话列表中存在文件传输助手
                this.$store.commit('chatList/setTopChat',analyze.cid);
            }else{
                //会话列表没有则新开一个会话
                var that=this;
                let fid = analyze.id;
                this.$root.socket.emit("request_start_single_chat_conversation",{
                    list:[fid,this.user.uid],
                    start_type:undefined,
                    mode:this.systemConfig.ConversationConfig.mode.Single,
                    type:this.systemConfig.ConversationConfig.type.Single
                },function(is_succ,data){
                    if (is_succ) {
                        that.$store.commit('conversationList/initConversation',data)
                        that.$store.commit('examList/initExamObj',data);
                        setTimeout(() => {
                            that.$store.commit('chatList/setTopChat',data);
                        }, 1000);//如果立即执行 setTopChat查不到该cid(data)的会话对象
                    }else{
                        this.$message.error(that.lang.start_conversation_error)
                    }
                })
            }
        },

        setGroupList(is_succ,data){
            //设置群组列表
            if (is_succ) {
                var list =this.parseObjToArr(data)
                this.$store.commit('loadingConfig/updateLoaded',{
                    key:'loadedGroupList',
                    loaded:true
                });
                this.$store.commit('groupList/initGroupList',list);
            }else{
                this.$message.error('setGroupList error')
            }
        },
        setGroupApplys(list){
            //设置入群申请
            if (list.length>0) {
                this.setDefaultImg(list);
                for(let item of list){
                    this.$store.commit('notifications/addGroupApply',item);
                }
                this.messageNotify('join_group')
            }

        },
        dealFriendApplys(data){
            //设置好友申请
            if(data.notify_type=='request_add_friend'){
                var temp=[]
                temp.push(data.param)
                this.$store.commit('notifications/addFriendApply',data);
                this.messageNotify('friend_apply')
            }else if(data.notify_type=="response_add_friend"){
                if (data.param.accept) {
                    var str=this.lang.response_accept_friend.replace('${1}',data.param.nickname)
                }else{
                    var str=this.lang.response_disaccept_friend.replace('${1}',data.param.nickname);
                    this.$store.commit('relationship/removeApplyingList',data.param)
                }
                this.$message.success(str)
            }
        },
        setFriendApplys(data){
            let json={}
            json.param=data.friendInfo;
            json.notify_id=data.applyInfo.id;
            json.description=data.applyInfo.msg_body.description;
            this.$store.commit('notifications/addFriendApply',json);
        },
        friendApplyResponse(data){
            if (data.isAgree) {
                var str=this.lang.response_accept_friend.replace('${1}',data.userInfo.nickname)
            }else{
                var str=this.lang.response_disaccept_friend.replace('${1}',data.userInfo.nickname);
                this.$store.commit('relationship/removeApplyingList',data.userInfo)
            }
            this.$message.success(str)
        },
        NotifyStartConversation(is_succ, conversation, start_type, kickout_data){
            window.Logger.save({
                message:'NotifyStartConversation',
                eventType: `socket_event`,
                data: {conversation,is_succ,start_type, kickout_data},
            });
            if(is_succ){
                //后台通知开启会话
                let cid=conversation.id
                let existConversation=this.checkExitConversation(cid)
                if(existConversation){
                    return
                }
                let existChatListItem=this.checkExistChatListItem(cid)
                if(!existChatListItem){
                    //不存在最近会话列表则加入
                    var chatItem={
                        cid:cid,
                        fid:conversation.fid,
                        is_single_chat:conversation.is_single_chat,
                        subject:conversation.subject,
                        type:conversation.type,
                        sex:conversation.sex,
                        state:conversation.state,
                        avatar:conversation.avatar||'',
                        message:{},
                        service_type:conversation.service_type||0,
                        user_status:conversation.user_status
                    }
                    this.$store.commit('chatList/addChatList',chatItem)
                }
                conversation.start_type = start_type;
                conversation.socket=conversation.controller;
                delete conversation.controller;
                this.initConversationControllerEvent(conversation.socket,existChatListItem,()=>{
                    this.$store.commit('conversationList/setConversation',conversation);
                    this.$store.commit('examList/initExamObj',cid);
                    if ((0 == conversation.is_single_chat)&&(conversation.service_type===0)) {
                        let groupTemp={
                            id:conversation.id,
                            subject:conversation.subject,
                            is_single_chat:0
                        };
                        this.setDefaultImg([groupTemp]);
                        this.$store.commit('groupList/addGroup',groupTemp);
                    }


                    // this.$root.eventBus.$emit('scrollChatWindow');
                    if (this.user.uid!=conversation.creator_id) {
                    //非会话创建者，获取本地viewmode设置
                        let settingsObj=JSON.parse(window.localStorage.getItem(`user_${this.user.uid}_viewmode`)||"{}");
                        let viewmode=settingsObj[`group_${cid}`];
                        if (viewmode!=undefined) {
                            this.$store.commit('conversationList/updateViewMode',{
                                cid:conversation.id,
                                value:viewmode
                            });
                        }
                    }
                    this.$root.eventBus.$emit('setPageType',{cid:cid});
                    this.$root.eventBus.$emit('refreshConversationSuccessToChatWindow',cid)
                    this.$root.eventBus.$emit('refreshConversationSuccessToConference',cid)
                    if(this.$root.transmitQueue['f-'+conversation.fid]){
                        this.$root.transmitQueue[cid]=this.$root.transmitQueue['f-'+conversation.fid]
                        delete this.$root.transmitQueue['f-'+conversation.fid]
                    }
                    if(this.$root.transmitQueue_StandaloneWorkstation['f-'+conversation.fid]){
                        this.$root.transmitQueue_StandaloneWorkstation[cid]=this.$root.transmitQueue_StandaloneWorkstation['f-'+conversation.fid]
                        delete this.$root.transmitQueue_StandaloneWorkstation['f-'+conversation.fid]
                    }
                    if (this.$root.transmitQueue_StandaloneWorkstation[cid]) {
                    //开启会话后有待发送的独立工作站转发消息
                        window.CWorkstationCommunicationMng.SendStandaloneWorkstationShareExamInfo(cid);
                    }
                    if (this.$root.transmitQueue[cid]) {
                    //开启会话后有待发送的转发消息
                        this.$root.eventBus.$emit('sendTransmitMessage',cid)
                    }
                    if (this.$root.auto_upload_temp[cid]) {
                        //开启会话后有DR自动转发的图片
                        this.autoUploadDR(cid)
                    }
                    if (this.$root.auto_upload_temp['DR_AI']) {
                        //开启会话后有DR_AIZ助手自动转发的图片
                        this.autoUploadDR(cid,'DR_AI')
                    }
                    if (this.$root.droc_auto_upload_temp[cid]) {
                        //开启会话后有DROC自动转发的文件
                        this.autoUploadDROC(cid)
                    }

                    if (this.$root.deleteQueue[cid]) {
                    //开启会话后有待删除的聊天消息
                        this.tryToDeleteMessages(cid,this.$root.deleteQueue[cid])
                    }
                    if (conversation.start_type.type==this.systemConfig.start_type.RequestUltrasoundDesktopByMonitorWall) {
                    //电视墙发起会话
                        this.showConversationMonitor(conversation)
                    }
                    if(conversation.is_single_chat==0&&conversation.avatar===''){ //判断是否有必要更新群头像
                        if(this.$root.updateGroupAvatarUserList[cid]){
                            this.$root.updateGroupAvatarUserListcid = undefined;
                        }
                        this.$root.eventBus.$emit('createGroupAvatar',{conversation})
                    }else if(this.$root.updateGroupAvatarUserList[cid]) {
                        let item = this.$root.updateGroupAvatarUserList[cid]
                        if(item.id == conversation.id) {
                            this.$root.eventBus.$emit('createGroupAvatar',{conversation})
                            this.$root.updateGroupAvatarUserList[cid] = undefined
                        }
                    }
                    if (start_type.type === this.systemConfig.start_type.KickoutAttendee && start_type.kickout_data) {
                        this.kickoutAttendeeHandle(start_type.kickout_data, conversation.socket)
                    }
                    //获取聊天是否为多中心
                    this.findMulticenter(conversation.id)
                    this.getConverSationGalleryData(conversation.socket,existChatListItem)
                    this.getUnfinishedHomework(conversation.id)
                    this.getUncorrectHomework(conversation.id);
                    //获取入群申请数量
                    const isCreator = checkIsCreator(conversation.id)
                    const isManager = checkIsManager(conversation.id)
                    if (isCreator||isManager) {
                        this.getApplyCount(conversation.id)
                    }
                });

            }else{
                this.$root.eventBus.$emit('createConversationFail')
            }
        },
        checkExistChatListItem(cid){
            let exit=false
            for(let chat of this.chatList){
                if(chat.cid==cid){
                    exit=true;
                    break;
                }
            }
            return exit;
        },
        getConverSationGalleryData(controller,exist){
            return new Promise((resolve,reject)=>{
                let conversation = this.$store.state.conversationList[controller.cid]
                let conversationGalleryData = conversation.galleryObj
                if(conversationGalleryData.hasOwnProperty('cid')){
                    resolve(conversationGalleryData.gallery_list)
                }else{
                    window.main_screen.conversation_list[controller.cid].getResourceList({
                        limit:this.systemConfig.consultationImageShowNum,
                        type:'all'
                    },(res)=>{
                        if(res.error_code === 0){
                            const data = {
                                cid:controller.cid,
                                gallery_list:res.data
                            }
                            this.setGalleryList(data,exist);
                            resolve(res.data)
                        }else{
                            this.$message.error('get_gallery_messages error')
                            reject(false)

                        }
                    })
                    // controller.emit("get_gallery_messages",{
                    //     start:0,
                    //     count:this.systemConfig.consultationImageShowNum
                    // },(is_succ,data)=>{

                    //     if (is_succ) {
                    //         data.cid=controller.cid;
                    //         this.setGalleryList(is_succ,data,exist);
                    //         resolve(data.gallery_list)
                    //     }else{
                    //         this.$message.error('get_gallery_messages error')
                    //         reject(false)
                    //     }
                    // });
                }

            })
        },
        initConversationControllerEvent(controller,exist,callback){
            var that=this;
            controller.on("gateway_connect",function(e){
                console.info('Conversation_gateway_connect',e)
                callback&&callback()
            });
            controller.on("gallery_messages_detail_info",function(is_succ,data){
                that.setGalleryMessageDetail(is_succ,{
                    cid:controller.cid,
                    list:data.list,
                    iworks_protocol_list:data.iworks_protocol_list
                });
            });
            controller.on("history_chat_messages",async(is_succ,data,scroll)=>{
                that.setHistoryChatMessage(is_succ,data,scroll,controller.cid);
            });
            controller.on("other_say",function(messageList){
                if(messageList&&messageList.length>0){
                    that.setSayChatMessage(messageList,controller.cid);
                    that.debounceSortChatList()
                }

            });
            controller.on("update_messages",function(messageList){
                that.updateChatMessage(messageList,controller.cid);
            });
            controller.on("update_file_transmit_progress",function(data){
                if (data.type==1) {
                    //pc客户端，下载进度不更新
                    return
                }else{
                    data.cid=controller.cid
                    that.updateUploadProgress(data)
                }

            })
            controller.on("update_conversation_attendee_state",function(data){
                that.$store.commit('conversationList/updateFriendToAttendeeList',data)

            });
            controller.on("notify_add_attendee",function(data){ // 他人加群触发
                console.log("------------------------notify_add_attendee------------------");
                that.$store.commit('conversationList/addAttendee',data)
                that.findMulticenter(controller.cid)
                // this.notify_add_attendee_timer&&clearTimeout(this.notify_add_attendee_timer)
                // this.notify_add_attendee_timer = setTimeout(()=>{
                //     let conversation = that.conversationList[controller.cid]
                //     that.$root.eventBus.$emit('createGroupAvatar',conversation)
                // },1000)

            });
            controller.on("notify_delete_attendee",function(data){
                that.kickoutAttendeeHandle(data, controller);
                that.findMulticenter(controller.cid)
            });
            controller.on("notify_edit_subject",function(is_succ,data){
                that.setNewSubject(is_succ,data,controller.cid);
            });
            controller.on("notify_add_tag",function(data){
                that.$store.commit('gallery/addTagList',data);
            });
            controller.on("notify_del_tag",function(data){
                that.$store.commit('gallery/deleteTagList',data);
            });
            controller.on("notify_add_comment",function(data){
                that.$store.commit('gallery/addCommentToList',data);
            });
            controller.on("notify_edit_public",function(is_succ,data){
                if (is_succ) {
                    data.cid=controller.cid;
                    that.$store.commit('conversationList/updateIsPublic',data);
                }
            });
            controller.on("notify_edit_view_mode",function(is_succ,data){
                if (is_succ) {
                    data.cid=controller.cid;
                    data.value=data.view_mode;
                    that.$store.commit('conversationList/updateViewMode',data);
                }
            });
            controller.on("notify_edit_record_mode",function(is_succ,data){
                if (is_succ) {
                    if(Number(window.vm.$root.currentLiveCid) !== Number(controller.cid)){ //直播中不接受变更的录制内容
                        data.cid=controller.cid;
                        data.record_mode=data.record_mode;
                        that.$store.commit('conversationList/updateIsLiveRecord',data);
                    }
                }
            });

            //更新ai结果
            controller.on('update_ai_report',function(data){
                console.log('update_ai_report：',data)
                that.$store.commit('examList/updateExamListMCAiReport',data)
                that.$store.commit('conversationList/updateMessageMCAiReport',data);
                that.$store.commit('gallery/updateGalleryMCAiReport',data);
                that.$root.eventBus.$emit('updateAiReportInMulitcenter',data)
            });

            controller.on("notify_set_admin",function(is_succ, data){
                if (is_succ) {
                    data.cid=controller.cid;
                    that.$store.commit('conversationList/updateGroupOwnerId',data);
                }
            });
            controller.on("notify_edit_announcement",function(is_succ,data){
                console.log("[event] notify_edit_announcement",is_succ,data);
                data.cid=controller.cid
                that.$store.commit('conversationList/updateAnnounce',data);
            });
            controller.on("notify_delete_chat_messages",function(is_succ,list){
                if (is_succ) {
                    for (let i in list) {
                        let item = list[i];
                        that.$store.commit("conversationList/deleteChatMessagesByGmsgIdList", {
                            cid:controller.cid,
                            gmsg_id_list:item.gmsg_id_list
                        });
                        that.$root.eventBus.$emit('notifyDeleteChatMessages',{
                            cid: controller.cid,
                            gmsg_id_list:item.gmsg_id_list
                        })
                        let resource_id_list = item.resource_id_list;
                        if(resource_id_list){
                            for (let i in resource_id_list) {
                                let resource_id=resource_id_list[i];

                                that.$store.commit("conversationList/deleteFileToConversation",{
                                    cid: controller.cid,
                                    resource_id:resource_id
                                });

                                // that.$store.commit("consultationImageList/deleteFileToConsultationImages",{
                                //     cid: controller.cid,
                                //     resource_id:resource_id
                                // });
                                that.$root.eventBus.$emit('deleteFileToExamList',{
                                    cid: controller.cid,
                                    resource_id:resource_id
                                })
                                that.$store.commit("resourceTempStatus/updateResourceTempStatus",{
                                    resource_id,
                                    data:{
                                        state:0 //被删除
                                    }
                                });

                            }
                        }
                    }
                }
            });
            controller.on("notify_withdraw_chat_message",function(is_succ,data){
                if (is_succ) {
                    that.$store.commit("conversationList/withDrawChatMessagesByGmsgIdList", {
                        cid:controller.cid,
                        gmsg_id_list:data.gmsg_id_list
                    });
                    that.$root.eventBus.$emit('notifyWithdrawChatMessage',{
                        cid: controller.cid,
                        gmsg_id_list:data.gmsg_id_list
                    })
                    let resource_id_list = data.resource_id_list;
                    if(resource_id_list){
                        for (let i in resource_id_list) {
                            let resource_id=resource_id_list[i];
                            that.$store.commit("conversationList/deleteFileToConversation",{
                                cid: controller.cid,
                                resource_id:resource_id
                            });
                            that.$root.eventBus.$emit('deleteFileToExamList',{
                                cid: controller.cid,
                                resource_id:resource_id
                            })
                            that.$store.commit("resourceTempStatus/updateResourceTempStatus",{
                                resource_id,
                                data:{
                                    state:2 //被撤回
                                }
                            });
                        }
                    }
                }
            });
            //会话--直播分析结果
            controller.on('update_ai_analyze_report',(is_succ,data)=>{
                data.cid=controller.cid
                that.updateAiAnalyzeReport(is_succ,data)
            });
            controller.on('notify_meeting_mode_request_speak',(data)=>{
                that.notiftMeetingModeRequestSpeak(data)
            });
            controller.on('notify_switch_realtime_flow',(data)=>{
                this.$root.eventBus.$emit('notifySwitchRealtimeFlow',data)
            });
            controller.on('notify_edit_report_info',(data)=>{
                this.$root.eventBus.$emit('lockReport',data)
            });
            // controller.on('notify_update_recent_consultation',(data)=>{
            //     // window.main_screen.controller.emit("get_recent_consult_info", that.setRecentConsult);
            // });
            controller.on('notify_add_conference_plan',(data)=>{
                this.$store.commit('conversationList/addConferencePlan', data);
            });
            controller.on('notify_del_conference_plan',(data)=>{
                this.$store.commit('conversationList/delConferencePlan', data);
            });
            controller.on('notify_set_attendee_preferences',(data)=>{
                data.cid=controller.cid
                this.$store.commit('conversationList/updateMuteToConversation',data)
                this.$store.commit('chatList/updateMuteToChatList',data)
            });

            controller.on('notify_delete_exams', function(is_succ,data){

            });

            controller.on("notify_exception",function(data){
                that.notifyException(data);
            });

            controller.on("notify_update_group_portrait", function (data) {
                console.log('notify_update_group_portrait',data)
                that.$store.commit('chatList/updateChatAvatarLocalUrl',{
                    imgObj:{
                        cid:controller.cid,
                    },
                    avatar:data.avatar
                })
                that.$store.commit('groupList/updateAvatarToGroupList',{
                    cid:controller.cid,
                    avatar:data.avatar
                })
                that.$store.commit('groupset/updateAvatarToGroupsetlist',{
                    cid:controller.cid,
                    avatar:data.avatar
                })
            });
            controller.on('notify_update_resource_des',(data)=>{
                console.log("updateResourceDes ", data);
                this.$store.commit('conversationList/updateResourceDes',data);
                this.$store.commit('consultationImageList/updateImageDes',data);
                this.$store.commit('userFavorites/updateFavorites',data);
            });
            //开启会话后，接受消息通知
            controller.on('receive_group_message',(data)=>{
                console.log('receive_group_message',data,1)
                that.setSayChatMessageReceiveGroupMessage(data,true);
                let condition1 = data.msg_type === that.systemConfig.msg_type.SYS_STOP_REALTIME_CONSULTATION
                let condition2 = data.msg_type === that.systemConfig.msg_type.SYS_START_REALTIME_CONSULTATION
                let condition3 = data.groupInfo.service_type === 104
                if((condition1||condition2)&&condition3){ //收到的直播开始或结束通知，并且是预约直播的群
                    that.debounceUpdateLiveCount()
                }
                that.debounceSortChatList()
            });
            controller.on('attendeesUpdate',(data)=>{
                this.$store.commit('conversationList/updateAttendeeRole',{
                    cid:controller.cid,
                    attendeeList:data,
                });
            });
            controller.on('notify_user_apply_join_group',(data)=>{
                const isCreator = checkIsCreator(controller.cid)
                const isManager = checkIsManager(controller.cid)
                if (isCreator||isManager) {
                    let applyCount = this.conversationList[controller.cid].applyCount;
                    this.$store.commit('conversationList/updateConversation',{
                        cid:controller.cid,
                        key:'applyCount',
                        value:applyCount+1,
                    });
                }
            });
            controller.on('attendeesUpdateAliasName',(data)=>{
                this.$store.commit('conversationList/updateAttendeeAliasName',{
                    uid:data.uid,
                    cid:controller.cid,
                    aliasName:data.alias_name
                })
            });
            controller.on('resourceSetName',(data)=>{
                that.$store.commit("resourceTempStatus/updateResourceTempStatus",{
                    resource_id:data.resource_id,
                    data:{
                        custom_file_name:data.custom_file_name,
                    }
                });
            });
            if(!this.$root.conversation_event.hasOwnProperty(controller.cid)){
                this.$set(this.$root.conversation_event,controller.cid,{
                    time:new Date().getTime(),
                    event:new CEvent()
                })
            }

        },
        async setHistoryChatMessage(is_succ, data, scroll,cid){
            if(is_succ){
                patientDesensitization(data)
                parseImageListToLocal(data,'url')
                setExpirationResource(data,cid)
                let last_send_ts=data[0]&&data[0].send_ts;
                for(let message of data){
                    message.msg_body=this.parseMessageBody(message.msg_body)
                    message.patientInfo=transferPatientInfo(message);
                    message.sending=false;
                    message.downloading=false;
                    message.sendFail=message.sendFail||false;
                    if (message.msg_type==this.systemConfig.msg_type.AI_ANALYZE) {
                        parseImageListToLocal(message.ai_analyze&&message.ai_analyze.messages,'url')
                    }
                    if (message.protocol_guid) {
                        //消息存在iworks信息
                        setIworksInfoToMsg(message);
                    }
                    // last_send_ts=message.send_ts;
                }
                var type = 'prepend';
                if(scroll && scroll.type == "bottom"){

                    data.unshift({
                        msg_type:this.systemConfig.msg_type.HISTORY_TIP,
                        sender_id:this.user.uid,
                        send_ts:last_send_ts,
                    })
                    type = 'splice';
                }
                let obj={
                    list:data,
                    cid:cid,
                    type:type,
                }

                //聊天界面获取历史消息也走这个方法
                this.$root.eventBus.$emit('historyLoaded',obj)
                this.$store.commit('conversationList/updateMessageListIsLoaded',{cid,is_loaded_history_list:true})
                if (0 < data.length && scroll && "bottom" == scroll.type
                    && this.$store.state.conversationList
                    && this.$store.state.conversationList[cid]
                    && this.$store.state.conversationList[cid].start_type
                    && this.systemConfig.start_type.NewChatMessage == this.$store.state.conversationList[cid].start_type.type) {
                    this.$store.commit('chatList/addMessageNoSort',data[1]);
                }
                this.$store.commit('conversationList/setChatMessage',obj)
            }else{
                this.$message.error('setHistoryChatMessage error')
            }
        },
        setGalleryList(data,exist){
            patientDesensitization(data.gallery_list)
            parseImageListToLocal(data.gallery_list,'url')
            if(data.gallery_list.length<this.systemConfig.consultationImageShowNum){
                data.gallery_index = -1
            }
            this.$store.commit('conversationList/initGalleryObj',data);
            // let conversation =  this.$store.state.conversationList[data.cid]
            // if (!exist) {
            //     if(conversation.service_type === 104){ //不处理预约直播的群文件
            //         return
            //     }
            //     //新入群时，将群内图片放入图像列表
            //     for(let message of data.gallery_list){
            //         this.$store.commit('consultationImageList/addFileToConsultationImages',message)
            //     }
            // }
        },
        judgeIfNeedAddChatList(omessage,isTop){
            if(!omessage){
                return
            }
            let cid = omessage.group_id||omessage.groupInfo.id
            if (this.conversationList[cid]&&!this.conversationList[cid].preferences.is_mute) {
                this.messageNotify('new_message')
                this.notifying=true
            }

            let is_exist_chat_List=this.checkExistChatListItem(cid)
            let message = {
                ...omessage,
                downloading: false,
                gmsg_id: omessage.gmsg_id,
                group_id: omessage.group_id,
                msg_body: "",
                msg_type: omessage.msg_type,
                sendFail: false,
                send_ts: omessage.send_ts,
                sender_id: omessage.sender_id,
                sending: false,
                liveInfo:omessage.liveInfo,
                groupInfo:omessage.groupInfo,
                ai_result:omessage.ai_result,
                avatar:omessage.senderInfo&&omessage.senderInfo.avatar,
                nickname:omessage.senderInfo&&omessage.senderInfo.nickname,
                sex:omessage.senderInfo&&omessage.senderInfo.sex,
                fid:omessage.senderInfo&&omessage.senderInfo.id
            }
            if(omessage.groupInfo.type === 2){// 群聊
                message = {...omessage.groupInfo,...message}
            }

            parseImageListToLocal([message],'url')
            patientDesensitization([message]);
            if(!is_exist_chat_List){
                //不存在最近会话列表则加入
                let is_single_chat = omessage.groupInfo.type==1?1:0
                var chatItem={
                    cid:cid,
                    is_single_chat,
                    subject:message.groupInfo.subject,
                    type:omessage.groupInfo.type,
                    state:1,
                    avatar:is_single_chat?message.avatar:message.groupInfo.avatar,
                    message:{},
                    service_type:message.groupInfo.service_type||0,
                    nickname : message.nickname||''
                }
                if(is_single_chat){
                    chatItem.fid = message.fid
                    chatItem.sex = message.sex
                }
                if(isTop){
                    this.$store.commit('chatList/addAndTopChat',chatItem)
                }else{
                    this.$store.commit('chatList/addChatList',chatItem)
                }
            }else{
                this.$store.commit('chatList/setTopChat',cid)
            }
            return message
        },
        PreHandleChatMessageByGroupMessage(omessage){
            console.log('PreHandleChatMessageByGroupMessage',omessage)
            if(!omessage){
                return
            }
            let message = this.judgeIfNeedAddChatList(omessage)
            let cid = message.group_id
            message.msg_body=this.parseMessageBody(message.msg_body)
            if (message.msg_type==this.systemConfig.msg_type.AI_ANALYZE) {
                if (message.ai_analyze&&message.ai_analyze.messages) {
                    parseImageListToLocal(message.ai_analyze.messages,'url')
                    let ignoreConsultationImages=false
                    if (this.conversationList[cid]&&(this.systemConfig.msg_type.AI_ANALYZE==this.conversationList[cid].service_type)) {
                        //AI分析图片不放入总图像列表里
                        ignoreConsultationImages=true
                    }
                    for(let item of message.ai_analyze.messages){
                        let obj=Object.assign({},item,true)
                        pushImageToList(obj,ignoreConsultationImages)
                    }
                }
            }
            if (message.msg_type==this.systemConfig.msg_type.Text) {
                if(Array.isArray(message.mentionList)){
                    if(message.mentionList.includes(this.user.uid)||message.mentionList.includes('all')){
                        this.$store.commit('chatList/setMention',message)
                        this.messageNotify('new_message')
                        this.notifying=true
                    }
                }
            }
            if (message.protocol_guid) {
                //消息存在iworks信息
                if (message.iworks_protocol) {
                    this.setIworksProtocol(message.iworks_protocol)
                }
                if (message.iworks_protocol_execution) {
                    this.setIworksProtocol(message.iworks_protocol_execution)
                }
                setIworksInfoToMsg(message);
            }
            this.$store.commit('chatList/addMessageNoSort',message)
            if (!this.isResource(message.msg_type)) {
                this.$store.commit('chatList/addUnread',{
                    group_id:message.group_id
                })
                if(Tool.checkAppClient('Cef')){
                    let liveRoom = getLiveRoomObj();
                    if (liveRoom) {
                        liveRoom.updateUnReadMsgCount()
                    }

                }
            }
            pushImageToList(message);
            this.$root.eventBus.$emit('updateExamImageListIfNeed',message)
            this.addChatMessageTask(message, false);
            this.notifying=false
            return {
                list:[message],
                cid:cid,
                type:'append',
                is_localdb_msg: 0
            }
        },
        setSayChatMessageReceiveGroupMessage(data,is_open_conversation){
            console.log(data,'setSayChatMessageReceiveGroupMessage')
            if(is_open_conversation){
                const res = this.PreHandleChatMessageByGroupMessage(data)
                if(window.vm.$store.state.conversationList[data.group_id].is_loaded_history_list){
                    this.$store.commit('conversationList/setChatMessage',res)
                }

            }else{
                this.PreHandleChatMessageByGroupMessage(data)
            }


        },
        checkMessageListDuplicate(messageList,cid){
            // if(messageList&&messageList.length>0){
            //     let list = []
            //     let lastMessage=this.$store.state.chatList.lastMessage[cid]
            //     messageList.forEach((item)=>{
            //         if(lastMessage&&lastMessage.gmsg_id){
            //             if(item.gmsg_id>lastMessage.gmsg_id){
            //                 list.push(item)
            //             }
            //         }else{
            //             let lastConversationList = this.$store.state.conversationList[cid]&&this.$store.state.conversationList[cid].chatMessageList
            //             let lastConversationListMessage = lastConversationList&&lastConversationList.length>0&&lastConversationList[lastConversationList.length-1]
            //             if(lastConversationList&&lastConversationList.length === 0){
            //                 list.push(item)
            //             }else if(lastConversationListMessage&&lastConversationListMessage.gmsg_id&&(item.gmsg_id>lastConversationListMessage.gmsg_id)) {
            //                 list.push(item)
            //             }
            //         }
            //     })
            //     return list
            // }
            // return messageList
            let chatMessageList = this.conversationList[cid].chatMessageList;
            let newMessageList = [];
            for (let message of messageList) {
                if (message.gmsg_id && message.gmsg_id != 0) {
                    let isDuplicate = chatMessageList.some(chatMessage => message.gmsg_id == chatMessage.gmsg_id);
                    if (isDuplicate) {
                        console.log("ignore message %%%%%%%%%%");
                        continue;
                    }
                }
                newMessageList.push(message);
            }
            return newMessageList;
        },
        setSayChatMessage(omessageList,cid){
            console.log('setSayChatMessage',omessageList)
            const messageList = this.checkMessageListDuplicate(omessageList,cid)
            // const messageList = omessageList;
            if (messageList.length) {
                if (this.conversationList[cid]&&!this.conversationList[cid].preferences.is_mute) {
                    this.messageNotify('new_message')
                    this.notifying=true
                }

            }
            for(let message of messageList){
                this.$store.commit('chatList/addUnread',{
                    group_id:cid
                })
                if(Tool.checkAppClient('Cef')){
                    let liveRoom = getLiveRoomObj();
                    if (liveRoom) {
                        liveRoom.updateUnReadMsgCount()
                    }

                }
                message.msg_body=this.parseMessageBody(message.msg_body)
                patientDesensitization([message]);
                message.patientInfo=transferPatientInfo(message);
                message.sending=false;
                message.sendFail=false;
                message.downloading=false;
                parseImageListToLocal([message],'url')

                if(message.liveInfo){
                    message.nickname = message.liveInfo.creator_name
                }
                if(message.sender_nickname){
                    message.nickname = message.sender_nickname.nickname
                }
                const attendee = this.conversationList[cid]?.attendeeList['attendee_'+message.sender_id]
                if(attendee){
                    message.nickname = (attendee.alias_name ? attendee.alias_name : attendee.nickname) || message.nickname
                }
                if (message.msg_type==this.systemConfig.msg_type.AI_ANALYZE) {
                    if (message.ai_analyze&&message.ai_analyze.messages) {
                        parseImageListToLocal(message.ai_analyze.messages,'url')
                        let ignoreConsultationImages=false
                        if (this.conversationList[cid]&&(this.systemConfig.msg_type.AI_ANALYZE==this.conversationList[cid].service_type)) {
                            //AI分析图片不放入总图像列表里
                            //AI分析图片放入总图像列表里
                            //ignoreConsultationImages=true
                        }
                        for(let item of message.ai_analyze.messages){
                            let obj=Object.assign({},item,true)
                            pushImageToList(obj,ignoreConsultationImages)
                        }
                    }
                }
                if (message.msg_type==this.systemConfig.msg_type.Text) {
                    if(Array.isArray(message.mentionList)){
                        if(message.mentionList.includes(this.user.uid)||message.mentionList.includes('all')){
                            this.$store.commit('chatList/setMention',message)
                            this.messageNotify('new_message')
                            this.notifying=true
                        }
                    }
                }
                if (message.protocol_guid) {
                    //消息存在iworks信息
                    if (message.iworks_protocol) {
                        this.setIworksProtocol(message.iworks_protocol)
                    }
                    if (message.iworks_protocol_execution) {
                        this.setIworksProtocol(message.iworks_protocol_execution)
                    }
                    setIworksInfoToMsg(message);
                }
                this.$store.commit('chatList/addMessageNoSort',message)
                pushImageToList(message);

                //聚合病例
                // console.log('message.msg_type:',message.msg_type)
                // if (this.systemConfig.msg_type.EXAM_IMAGES == message.msg_type) {
                //     //拿图片
                //     // let that = this
                //     // that.$root.eventBus.$emit('getExamImageListForAutoDownload',message,function(data){
                //     //     for(let image of data){
                //     //         message.imageList = [image]
                //     //         that.addChatMessageTask(message, false);
                //     //     }
                //     // })
                //     this.addChatMessageTask(message, false);
                // } else {
                //     this.addChatMessageTask(message, false);
                // }
                this.addChatMessageTask(message, false);
                this.$root.eventBus.$emit('updateExamImageListIfNeed',message)
            }
            this.notifying=false
            if(window.vm.$store.state.conversationList[cid].is_loaded_history_list){
                const res =  {
                    list:messageList,
                    cid:cid,
                    type:'append',
                    is_localdb_msg: 0
                }
                this.$store.commit('conversationList/setChatMessage',res)
            }

        },
        updateChatMessage(messageList,cid){
            for(let message of messageList){
                parseImageListToLocal([message],'url')
                this.$store.commit('conversationList/updateChatMessage',message)
                this.$store.commit('conversationList/updateConversationImage',message)
                this.$store.commit('consultationImageList/updateConsultationImage',message)
                this.$root.eventBus.$emit('updateExamImageListIfNeed',message)
            }
        },
        addChatMessageTask(message, is_history){
            console.log('addChatMessageTask:','0**----------------------')
            //DCM自动下载任务
            console.log(message)
            console.log(message.msg_type)
            console.log(this.systemConfig.msg_type.EXAM_IMAGES)
            let g_auto_download = this.globalParams.auto_download;
            if (is_history || !g_auto_download.enable) {
                return;
            }
            if (message && this.user && message.sender_id == this.user.uid) {
                return;
            }
            var task = null;
            if (this.systemConfig.msg_type.Frame == message.msg_type) {
                var img_encode_type = message.img_encode_type? message.img_encode_type.toUpperCase() : '';
                var to_download = true;
                if ('DCM' == img_encode_type && g_auto_download.Frame_DCM) {
                    to_download = true;
                } else if ('DCM' != img_encode_type && g_auto_download.Frame) {
                    to_download = true;
                }

                if (to_download) {
                    task = {
                        type: this.systemConfig.gallery_image_mode.Frame,
                        img_id: message.img_id,
                        img_list:[],
                        creator_id: message.creator_id.toString(),
                        cid: message.group_id.toString(),
                        user_id: this.user.id.toString(),

                        patient_name: message.patient_name,
                        patient_sex: message.patient_sex,
                        patient_age: message.patient_age,
                        patient_age_unit: message.patient_age_unit,
                        patient_id: message.patient_id,
                        patient_series_datetime:message.patient_series_datetime,

                        exam_type:message.exam_type,
                        exam_id:message.exam_id,
                    };

                    var url = message.url.substr(0, message.url.lastIndexOf('/') + 1);
                    task.img_list.push(url + "SingleFrame." + message.img_encode_type);
                    if (message.img_has_gesture_video) {
                        task.img_list.push(url + "CameraFrame." + message.img_encode_type);
                    }
                }
            } else if (this.systemConfig.msg_type.Cine == message.msg_type) {
                var img_encode_type = message.img_encode_type? message.img_encode_type.toUpperCase() : '';
                var to_download = false;
                if ('DCM' == img_encode_type && g_auto_download.Cine_DCM) {
                    to_download = true;
                } else if ('DCM' != img_encode_type && g_auto_download.Cine) {
                    to_download = true;
                }

                if (to_download) {
                    task = {
                        type: this.systemConfig.gallery_image_mode.Cine,
                        img_id: message.img_id,
                        creator_id: message.creator_id.toString(),
                        cid: message.group_id.toString(),
                        user_id: this.user.id.toString(),

                        img_list:[],

                        patient_name: message.patient_name,
                        patient_sex: message.patient_sex,
                        patient_age: message.patient_age,
                        patient_age_unit: message.patient_age_unit,
                        patient_id: message.patient_id,
                        patient_series_datetime:message.patient_series_datetime,

                        exam_type:message.exam_type,
                        exam_id:message.exam_id,
                    };

                    var url = message.url.substr(0, message.url.lastIndexOf('/') + 1);
                    task.img_list.push(url + "DeviceVideo." + message.img_encode_type);
                    if (message.img_has_gesture_video) {
                        task.img_list.push(url + "GestureVideo." + message.img_encode_type);
                    }
                }
            } else if (this.systemConfig.msg_type.File == message.msg_type || this.systemConfig.msg_type.Image == message.msg_type) {
                // 下载所有类型文件
                console.log('addChatMessageTask:','1**----------------------')
                var to_download = true;
                if (to_download) {
                    task = {
                        type:  message.msg_type,
                        img_id: message.img_id,
                        creator_id: message.creator_id.toString(),
                        cid: message.group_id.toString(),
                        user_id: this.user.id.toString(),
                        img_list:[],
                    };
                    task.img_list.push(message.url);
                }
                console.log('addChatMessageTask:','2**----------------------')
                console.log(task)

            }  else if (this.systemConfig.msg_type.EXAM_IMAGES == message.msg_type) {
                // 下载所有类型文件
                console.log('addChatMessageTask:','1**----------------------')
                if (message.url && message.url.length > 0) {
                    var to_download = true;
                    if (to_download ) {
                        task = {
                            type: message.msg_type,
                            img_id: message.img_id,
                            creator_id: message.creator_id.toString(),
                            cid: message.group_id.toString(),
                            user_id: this.user.id.toString(),
                            img_list:[],
                            patient_name: message.patient_name,
                            patient_sex: message.patient_sex,
                            patient_age: message.patient_age,
                            patient_age_unit: message.patient_age_unit,
                            patient_id: message.patient_id,
                            patient_series_datetime:message.patient_series_datetime,

                            exam_type:message.exam_type,
                            exam_id:message.exam_id,
                        };
                        // message.realUrl = getRealUrl(message)

                        for(let image of message.resourceList){
                            if ( image.id == message.resource_id) {
                                if (image.resource_type == 1) {
                                    //MEDIC_IMAGE
                                    message.realUrl=message.url.replace("thumbnail.jpg","SingleFrame.jpg");
                                    if ('DCM' == message.img_encode_type) {
                                        message.attachmentUrl=message.url.replace("thumbnail.jpg","SingleFrame.DCM");
                                    }
                                    if ('dcm' == message.img_encode_type) {
                                        message.attachmentUrl=message.url.replace("thumbnail.jpg","SingleFrame.dcm");
                                    }
                                    if ('PDF' == message.img_encode_type) {
                                        message.attachmentUrl=message.url.replace("thumbnail.jpg","SingleFrame.PDF");
                                    }
                                    if ('pdf' == message.img_encode_type) {
                                        message.attachmentUrl=message.url.replace("thumbnail.jpg","SingleFrame.pdf");
                                    }
                                    if ('jpg' == message.img_encode_type) {
                                        message.attachmentUrl=message.url.replace("thumbnail.jpg","SingleFrame.jpg");
                                    }
                                    if ('png' == message.img_encode_type) {
                                        message.attachmentUrl=message.url.replace("thumbnail.jpg","SingleFrame.jpg");
                                    }
                                } else if (image.resource_type == 13) {//产科ai文件
                                    // if ('ai' == message.img_encode_type.toLowerCase()) {
                                    //     message.attachmentUrl=message.url.replace("/thumbnail.jpg",".ai");
                                    // }
                                    message.attachmentUrl=message.url.replace("/thumbnail.jpg",".ai");
                                } else if (image.resource_type == 2) {
                                    //MEDIC_VIDEO
                                    if ('DCM' == message.img_encode_type) {
                                        message.attachmentUrl=message.url.replace("thumbnail.jpg","DeviceVideo.DCM");
                                    }
                                    if ('dcm' == message.img_encode_type) {
                                        message.attachmentUrl=message.url.replace("thumbnail.jpg","DeviceVideo.dcm");
                                    }
                                    if ('mp4' == message.img_encode_type) {
                                        message.attachmentUrl=message.url.replace("thumbnail.jpg","DeviceVideo.mp4");
                                    }
                                    if ('avi' == message.img_encode_type) {
                                        message.attachmentUrl=message.url.replace("thumbnail.jpg","DeviceVideo.avi");
                                    }
                                }else{
                                    message.realUrl=message.url.replace("thumbnail.jpg","SingleFrame"+message.img_encode_type);
                                }
                            }
                        }
                        // console.error(message.dicomUrl)
                        // task.img_list.push(message.realUrl)
                        if (message.attachmentUrl && message.attachmentUrl.length > 0) {
                            task.img_list.push(message.attachmentUrl)
                        }
                        if (message.realUrl && message.realUrl.length > 0) {
                            task.img_list.push(message.realUrl)
                        }
                        if (message.file_id){
                            task.img_id = message.file_id
                        }
                    }
                }
                console.log('addChatMessageTask:','2**----------------------')
                console.log(task)

            }else if (this.systemConfig.msg_type.OBAI == message.msg_type) {
                var to_download = true;
                if (to_download) {
                    task = {
                        type: this.systemConfig.gallery_image_mode.OBAI,
                        img_id: message.img_id,
                        img_list:[],
                        creator_id: message.creator_id.toString(),
                        cid: message.group_id.toString(),
                        user_id: this.user.id.toString(),

                        patient_name: message.patient_name,
                        patient_sex: message.patient_sex,
                        patient_age: message.patient_age,
                        patient_age_unit: message.patient_age_unit,
                        patient_id: message.patient_id,
                        patient_series_datetime:message.patient_series_datetime,

                        exam_type:message.exam_type,
                        exam_id:message.exam_id,
                    };
                    var url = message.url.substr(0, message.url.lastIndexOf('/'));
                    task.img_list.push(url + ".ai");
                }
            }

            if (!task) {
                return;
            }

            task.task_id = new Date().getTime() + "_" + this.task_index;
            this.task_index++;

            //通知App
            window.CWorkstationCommunicationMng.addDownloadTasks({list:[task]});
        },
        socketConnectSuccess(){
            this.autoLoginTime = 0
            this.$store.commit('loadingConfig/updateLoaded',{
                key:'networkUnavailable',
                loaded:false
            });
        },
        socketError(e){
            console.warn(e)
            this.resetApp();
            if (e=='Invalid namespace') {
                //this.$root.socket.closeSocket()
                // Toast(e)
                console.log(e)
                // this.resetStore()
                if(this.autoLoginTime>2){
                    this.$router.replace('/login')
                }else{
                    this.autoLoginTime++
                    this.autoLogin(()=>{
                        this.initNetworkData()
                        if(this.autoLogonTimer){
                            clearTimeout(this.autoLogonTimer)
                            this.autoLogonTimer = null
                        }

                    });
                }

            }else if(e=='Init connect'){
                //尝试重新建立socket连接并初始化事件
            }else{
                //尝试连接错误5次跳转到登录页
                //this.$root.socket.closeSocket()
                //this.$router.replace('/login')
            }
        },
        socketReconnecting(){
            this.$store.commit('loadingConfig/updateLoaded',{
                key:'networkUnavailable',
                loaded:true
            });
        },
        socketReconnect(){
            this.$store.commit('loadingConfig/updateLoaded',{
                key:'networkUnavailable',
                loaded:false
            });
            //重连需重新获取系统消息，需手动清除未处理系统消息
            this.$store.commit('notifications/clearFriendApply');
            this.$store.commit('notifications/clearGroupApply');
        },
        socketReconnectFail(e){
            console.log('socketReconnectFail',e)
            console.log("[error] socketReconnectFail in index");
            this.resetApp();
            console.log("[error] socketReconnectFail in index:resetApp done");
            this.autoLogin(this.initNetworkData);
            console.log("[error] socketReconnectFail in index:autoLogin done");
        },
        socketDisconnect(e){
            console.log(e)
            this.$store.commit('loadingConfig/updateLoaded',{
                key:'networkUnavailable',
                loaded:true
            });
            this.clearLiveStatus()
            if(e !== 'io client disconnect'){
                this.autoLogin(this.initNetworkData);
            }
        },
        clearLiveStatus(){
            this.$store.commit('liveConference/clearLiveConferenceStatus');
            this.showLivingNotifyDialog = false

        },
        notifyException(data) {
            this.$MessageBox.alert(this.lang.exception_to_login_again,
                "",
                {
                    confirmButtonText:this.lang.confirm_button_text,
                    type:'warning'
                }
            ).then(()=>{
                this.autoLogin(this.initNetworkData);
            }).catch(()=>{

            })
        },
        unBindControllerEvent(){
            if (window.main_screen && window.main_screen.controller) {
                window.main_screen.controller.off("gateway_connect");
                window.main_screen.controller.off("gateway_error");
                window.main_screen.controller.off("gateway_reconnecting");
                window.main_screen.controller.off("gateway_reconnect_fail");
                window.main_screen.controller.off("gateway_reconnect");
                window.main_screen.controller.off("gateway_disconnect");
                window.main_screen.controller.off("notify_login_another");
                window.main_screen.controller.off("notify_user_destroy");
                window.main_screen.controller.off("notify.user.device.sync.live");
            }
        },
        resetApp(){
            try {
                destroyAllConference()
                // this.closePusherIfNeed();
                if (Tool.ifAppWorkstationClientType(window.clientType)) {
                    //退出Istation
                    if (true == this.$store.state.globalParams.realtime_ultrasound_mode) {
                        this.$store.commit('globalParams/updateGlobalParams', {
                            realtime_ultrasound_mode:false
                        })
                        window.CWorkstationCommunicationMng.exitRealtimeUltrasoundMode({});
                    }
                }

                // if (Tool.ifAppConsultationClientType(window.clientType) && this.$route.meta.inTvWall) {
                //     this.$root.eventBus.$emit('exitTVmode');
                // }

                window.CWorkstationCommunicationMng.resetApp();
                window.CWorkstationCommunicationMng.CloseNewWindow();
            } catch (e) {
                console.log("[error] resetApp in index",e);
            }
        },
        closePusherIfNeed(){
            console.log('closePusherIfNeed')
        },
        notifyAddFriend(friend){
            var list =[]
            list.push(friend)
            this.$store.commit('friendList/addFriendList',list[0]);
            this.$store.commit('relationship/removeApplyingList',list[0])
            sortFriendList();
        },
        updateFriendInfo(data){
            //更新好友信息
            for(let hospital of this.dynamicGlobalParams.hospitals){
                if (data.hospital_id==hospital.id) {
                    data.hospital_name=hospital.hospital_name;
                    break;
                }
            }
            this.$store.commit('friendList/updateFriendToFriendList',data)
            this.$store.commit('chatList/updateFriendToChatList',data)
            this.$store.commit('conversationList/updateFriendToConversationList',data)
            sortFriendList();
            // this.$store.commit('conversationList/updateFriendToAttendeeList',data)
        },
        updateFriendDestroy(data){
            this.updateFriendInfo();
            this.$store.commit('conversationList/updateFriendToAttendeeList',data)
        },
        onUpdateUserInfo(data){
            //更新用户信息
            this.changeDefaultImg(data);
            this.$store.commit('user/updateUser',data);
            this.$store.commit('chatList/updateFriendToChatList', this.user);
            this.$store.commit('conversationList/updateFriendToConversationList',this.user);
        },
        onUpdateUserPortraitInfo(data){
            //更新用户头像信息
            // let path_local=data.avatar
            this.$store.commit('user/updateUser',{
                avatar:data.avatar,
                avatar_local:data.avatar_local
            });
            this.$store.commit('conversationList/updateFriendToAttendeeList',{
                avatar:data.avatar,
                avatar_local:data.avatar_local,
                id:this.user.id,
                state:this.user.state,
                nickname:this.user.nickname
            });
        },
        notifyLoginAnother(){
            window.Logger.save({
                message:'notifyLoginAnother',
                eventType: `socket_event`,
            });
            this.resetApp();
            setTimeout(()=>{
                this.clearAndDirectToLogin(this.lang.login_another)
            },1500)

        },
        notifyUserDestroy(){
            window.Logger.save({
                message:'notifyUserDestroy',
                eventType: `socket_event`,
            });
            this.resetApp();
            setTimeout(()=>{
                this.clearAndDirectToLogin(this.lang.account_destroy_tip)
            },1500)


        },
        clearAndDirectToLogin(msg, to_auto_login){
            window.localStorage.setItem('password','');
            window.localStorage.setItem('loginToken','');
            this.$store.commit('user/updateUser',{
                new_token:''
            });
            this.unBindControllerEvent()
            this.$router.replace(`/login`)
            if (window.main_screen&&window.main_screen.CScanRoom&&window.main_screen.CScanRoom.StorageConsultationFile) {
                //在本地实时页面由App弹框
                setTimeout(()=>{
                    window.CWorkstationCommunicationMng.notifyStopStorageConsultationFile({
                        error:1,
                        error_info:msg
                    });
                    // location.reload();
                    this.$root.eventBus.$emit('reloadRouter')
                },100)
            } else {
                setTimeout(()=>{
                    this.$MessageBox.alert(msg).then(()=>{
                        window.localStorage.setItem('loginToken','');
                        this.$root.eventBus.$emit('reloadRouter')
                        // location.reload();
                    })
                },100)
            }

            window.localStorage.removeItem('local_store_device_token');


        },
        setGalleryMessageDetail(is_succ,data){
            console.log('setGalleryMessageDetail----------------',JSON.parse(JSON.stringify(data)))
            if (is_succ) {
                //将评论信息、iworks协议放入画廊中
                for(let key in data.iworks_protocol_list){
                    let protocol=data.iworks_protocol_list[key];
                    protocol.protocolTree=[this.setProtocolTree(protocol)]
                    protocol.viewList=this.setViewList(protocol.protocolTree[0],[])
                }
                this.$store.commit('gallery/setCommentToGallery',data)
            }else{
                this.$message.error('setGalleryMessageDetail error')
            }
        },
        setNewSubject(is_succ,subject,cid){
            let chat={subject:subject}
            this.$store.commit('conversationList/updateSubjectToConversation',{
                subject:subject,
                cid:cid
            })
            this.$store.commit('chatList/updateSubjectToChatList',{
                subject:subject,
                clamp_subject:chat.clamp_subject,
                clamped:chat.clamped,
                cid:cid
            })
            this.$store.commit('groupList/updateSubjectToGroupList',{
                subject:subject,
                cid:cid
            })
        },
        setAllHospital(is_succ,data){
            if (is_succ) {
                this.$store.commit('dynamicGlobalParams/updateDynamicGlobalParams',{
                    hospitals:data
                })
            }
        },
        closeGalleryIfNeed(data){
            if (data.cid==this.$route.params.cid) {
                if (this.$route.name=='gallery'&&!data.is_show_vedio_btn) {
                    //关闭实时时，当前打开着画廊，则关闭画廊
                    this.$root.eventBus.$emit('closeGallery')
                }
            }
        },
        requestConversationToStartUltrasoundDesktopByAutoPushStream(data){
            var auto_push_stream = this.globalParams.auto_push_stream;

            if (!this.functionsStatus.live
                 ||!auto_push_stream
                 || !auto_push_stream.enable
                 || !auto_push_stream.value_type
                 || !auto_push_stream.value) {
                return;
            }

            if ("Conversation" == auto_push_stream.value_type && 0 < auto_push_stream.value) {
                var cid = auto_push_stream.value;
                this.requestGroupToStartUltrasoundDesktopByAutoPushStream({cid:cid, conversation:data});
            } else if ("FriendId" == auto_push_stream.value_type && 0 < auto_push_stream.value) {
                var fid = auto_push_stream.value;
                this.requestFriendToStartUltrasoundDesktopByAutoPushStream({fid:fid, conversation:data});
            }
        },
        requestGroupToStartUltrasoundDesktopByAutoPushStream(data){
            let cid = 0;
            let that = this;
            let conversation = null;
            if (0 == cid) {
                that.openConversation(data.cid,2,null,(is_suc)=>{
                    var input_data={
                        gid:data.cid,
                        record_mode:data.conversation.record_mode?1:0
                    }
                    let newConversation = that.conversationList[data.cid]
                    newConversation.socket.emit('edit_record_mode',input_data,function(is_succ,data){
                        if(is_succ){
                            //修改成功
                            that.$store.commit('conversationList/updateIsLiveRecord',{
                                cid:input_data.gid,
                                record_mode:input_data.record_mode
                            });
                        }else{//修改失败
                        }
                        console.log('requestGroupToStartUltrasoundDesktopByAutoPushStream:', that.$store.state.conversationList[input_data.gid])
                        that.startUltrasoundDesktopByAutoPushStream(conversation);
                    })
                    // is_suc&&this.startUltrasoundDesktopByAutoPushStream(conversation);
                });
            } else {
                this.openConversation(cid,2,null,()=>{
                    this.startUltrasoundDesktopByAutoPushStream(conversation);
                });
            }
        },
        requestFriendToStartUltrasoundDesktopByAutoPushStream(data){
            console.log('requestFriendToStartUltrasoundDesktopByAutoPushStream',data)
            let that = this;
            let cid = 0;
            // let conversation = null;
            // for (var key in this.conversationList) {
            //     if (1 == this.conversationList[key].is_single_chat
            //       && this.conversationList[key].attendeeList["attendee_" + this.user.id]
            //       && this.conversationList[key].attendeeList["attendee_" + data.fid]) {
            //         cid = key;
            //         conversation=this.conversationList[key]
            //         break;
            //     }
            // }
            // if (data.conversation && data.conversation.id != cid) {
            //     return;
            // }
            if (0 == cid) {
                this.openConversation(data.fid,3,null,(is_suc)=>{
                    var input_data={
                        gid:data.cid,
                        record_mode:data.conversation.record_mode?1:0
                    }
                    that.$root.socket.emit('edit_record_mode',input_data,function(is_succ,data){
                        if(is_succ){
                            //修改成功
                            that.$store.commit('conversationList/updateIsLiveRecord',{
                                cid:input_data.gid,
                                record_mode:input_data.record_mode
                            });
                        }else{//修改失败
                        }
                        that.startUltrasoundDesktopByAutoPushStream();
                    })
                    // is_suc&&this.startUltrasoundDesktopByAutoPushStream();
                });
            } else {
                this.openConversation(cid,2,null,()=>{
                    this.startUltrasoundDesktopByAutoPushStream();
                });
            }

        },
        startUltrasoundDesktopByAutoPushStream(conversation){
            if(!window.vm.$root.currentLiveCid){
                setTimeout(()=>{
                    this.$root.eventBus.$emit('chatWindowStartJoinRoom',{main:1,aux:1,videoSource:'doppler',isSender:1,autoPushStream:true});
                },1000)
            }
        },
        async updateAiAnalyzeReport(is_succ,data){
            if (is_succ) {
                console.log('updateAiAnalyzeReport',data)
                if(data&&data.type == this.$store.state.aiPresetData.typeIndex.breastSearch){
                    if(data.report&&data.report.list&&data.report.list.length>0){
                        this.$store.commit('gallery/updateCommentObjByKey',{ keys:{showAISearchSuggest:true},resource_id:data.resource_id});
                    }else{
                        this.$store.commit('gallery/updateCommentObjByKey',{ keys:{showAISearchSuggest:false},resource_id:data.resource_id});
                    }
                }else{
                    await this.updateReportToGallery(data)
                    this.$store.commit('conversationList/updateAiAnalyzeReport',data);
                    if(data.type == this.$store.state.aiPresetData.typeIndex.drChest){
                        //通知dr
                        window.CWorkstationCommunicationMng.UpdateAiAnalyzeReport(data);
                    }
                }
            }
        },
        async updateReportToGallery(data){
            let mark_list=data.report&&data.report.mark_list
            let report={
                ai_analyze_report:{
                    detected_tumor:data.report.detected_tumor,
                    error:data.report.error,
                    summary:data.report.summary,
                    mark_list:{},
                    clips:{},
                    ai_analyze_id:data.ai_analyze_id,
                    type:data.type,
                    status:1
                },
                type:data.type,
                group_id:data.group_id||data.cid,
                exam_id:data.exam_id,
                ai_analyze_id:data.ai_analyze_id,
            }
            if(mark_list){
                for(let key in mark_list){
                    let new_report = cloneDeep(report)
                    new_report.resource_id = key
                    new_report.ai_analyze_report.mark_list[key]=data.report.mark_list[key]
                    let clips=data.report&&data.report['clips']
                    if(clips){
                        new_report.ai_analyze_report['clips'][key]=clips[key]
                    }
                    this.$store.commit('gallery/updateAiReportToGallery',new_report);
                    this.$store.commit('examList/updateAiReportToExamList',new_report);
                }
            }else{
                let clips=data.report&&data.report['clips']
                if(clips){
                    for(let key in clips){
                        let new_report = cloneDeep(report)
                        new_report.resource_id = key
                        new_report.ai_analyze_report['clips'][key]=clips[key]
                        // console.error('updateReportToGallery',new_report)
                        this.$store.commit('gallery/updateAiReportToGallery',new_report);
                        this.$store.commit('examList/updateAiReportToExamList',new_report);
                    }
                    if(data.state=='SUCCESS'){
                        let examObj = this.$store.state.examList[data.group_id]||{};
                        let examList = examObj.list||[];
                        for (let index = 0; index < examList.length; index++) {
                            let exam = examList[index];
                            if (data.exam_id == exam.exam_id) {
                                for (let k = 0; k < exam.imageList.length; k++) {
                                    let image = exam.imageList[k]
                                    if(image.resource_id == data.resource_id){
                                        this.$root.eventBus.$emit('updateExamImageListIfNeed',image)
                                    }
                                }
                            }
                        }
                    }
                }else{
                    this.$store.commit('examList/updateAiReportToExamList',report);
                }
            }

        },

        autoUploadDrAiData(){
            console.log('autoUploadDrAiData')
            if(this.$root&&this.$root.auto_upload_temp['DR_AI']&&this.$root.auto_upload_temp['DR_AI'].length>0){
                console.log('autoUploadDrAiData',JSON.stringify(this.$root.auto_upload_temp['DR_AI']))
                let json = this.$root.auto_upload_temp['DR_AI'].shift()
                if(json&&json.ImageList){
                    this.notifyNewExamImages(json)
                }
            }
        },
        notifyNewExamImages(json){
            if(!(json&&json.ImageList&&json.ImageList.length>0)){
                return
            }
            let receiver = json.Receiver||''
            if(receiver&&this.$store.state.friendList.list.length<1){
                if(receiver=='DR_AI'){
                    this.$root.auto_upload_temp[receiver]=this.$root.auto_upload_temp[receiver]||[]
                    this.$root.auto_upload_temp[receiver].push(json)
                }
                return
            }
            if(receiver=='DR_AI'||(this.user.preferences&&this.user.preferences.auto_upload==1)){
                //DR_AI 数据
                var fid=0
                if(receiver=='DR_AI'){
                    for(let item of this.$store.state.friendList.list){
                        if (item.service_type==ServiceConfig.type.DrAiAnalyze) {
                            fid = item.id
                            break
                        }
                    }
                }else{
                    //设置了自动上传
                    var default_conversation=this.user.preferences.default_conversation||{}
                    if (default_conversation.type==0) {
                        this.autoUploadToConversation(default_conversation.cid,json)
                        return
                    }else if(default_conversation.type==2){
                        fid=default_conversation.fid
                    }
                }
                if(fid>0){
                    //转发到好友
                    for(let item of this.chatList){
                        if (item.fid==fid) {
                            this.autoUploadAction(item.cid,json,2);
                            return
                        }
                    }
                    //好友存在于好友列表中
                    for(let item of this.$store.state.friendList.list){
                        if (item.id==fid) {
                            this.openConversation(fid,3,{type:this.systemConfig.start_type.SendTo})
                            return
                        }
                    }
                }
            }
        },
        autoUploadToConversation(cid,json){
            let tag=true;
            let searchArr=this.$store.state.groupList.concat(this.chatList)
            for(let item of searchArr){
                if ((item.id||item.cid)==cid) {
                    tag=false;
                    break;
                }
            }
            if (tag) {
                //群已解散或被踢出
                return ;
            }
            this.autoUploadAction(cid,json);

        },
        autoUploadDR(cid, type=''){
            console.log('autoUploadDROC',cid, type)
            let arr=this.$root.auto_upload_temp[cid]||[];
            if(type&&this.$root.auto_upload_temp[type].length>0){
                arr=[...this.$root.auto_upload_temp[type],...arr];
                delete this.$root.auto_upload_temp[type]
            }
            console.log('autoUploadDROC list',arr)
            for(let item of arr){
                window.CWorkstationCommunicationMng.addExamImages(cid,item.ImageList)
                console.log('autoUploadDR',item,cid)
            }
            this.$root.auto_upload_temp[cid]=null;
        },
        autoUploadDROC(cid){
            console.log('autoUploadDROC',cid)
            let arr=this.$root.droc_auto_upload_temp[cid];
            for(let item of arr){
                window.CWorkstationCommunicationMng.NotifySendFileToConversation(item);
                console.log('autoUploadDROC',item,cid)
            }
            this.$root.droc_auto_upload_temp[cid]=null;
        },
        autoUploadAction(cid,json,open_type=2){
            if (this.conversationList[cid]) {
                //会话已开启直接转发
                console.log('autoUploadDR',json,cid)
                window.CWorkstationCommunicationMng.addExamImages(cid,json.ImageList)
            }else{
                //暂存数据，先开启会话
                if(this.$root.auto_upload_temp[cid]){
                    this.$root.auto_upload_temp[cid].push(json)
                }else{
                    this.$root.auto_upload_temp[cid]=[json]
                }
                this.openConversation(cid,open_type,{type:this.systemConfig.start_type.SendTo})
            }
        },
        ShowAudioDeviceChangeMsg(uid, info){
            if(Tool.ifBrowserClientType(this.systemConfig.clientType)){
                this.$root.platformToast(info, 3);
            }else{
                window.CWorkstationCommunicationMng.ShowPopupMsg({info:info});
            }
            window.DEBUG_TO_SERVER("[RT-Voice-Client-Stats] uid=" + uid + " result=" + info);
        },
        NotifyShowConfirmDialog(json){
            console.log("-----------------NotifyConfirmDialogResult--------------------------", json);
            if(json && "live_audio_exception" == json.des){
                if(1 == json.no){//选择退出会诊
                    var realtimeVideo=window.vm.$store.state.realtimeVideo||{}
                    if(2 == realtimeVideo.real_time_video_state){
                        this.$root.eventBus.$emit('StopConference')
                    }
                    if(this.systemConfig.gallery_image_mode.RealTimeVideo == realtimeVideo.gallery_image_mode){
                        this.$root.eventBus.$emit('closeGallery')
                    }
                }
            }
        },
        notiftMeetingModeRequestSpeak(data){
            let realtimeVideo=this.$store.state.realtimeVideo;
            if((1 == realtimeVideo.is_start_conference_user) || (1 == realtimeVideo.is_start_cs_window_user)|| (1 == realtimeVideo.is_start_camera_user)){
                if(1 != data.cancel){
                    window.CWorkstationCommunicationMng.conferenceMsgNotify({cid: this.cid, notify:1});
                }else{
                    window.CWorkstationCommunicationMng.conferenceMsgNotify({cid: this.cid, notify:0});
                }
            }
        },
        initUserConfig2App(){
            var user_config = {
                auto_upload: 0
            };
            if (this.user.preferences && this.user.preferences.auto_upload) {
                user_config.auto_upload = 1;
            }
            console.log('--')
            console.log('upload_config',user_config)
            console.log('--')
            window.CWorkstationCommunicationMng.initUserConfig(user_config);
        },
        resetStartupOption() {
            window.CWorkstationCommunicationMng.ClearStartupOption();
            window.g_extend_info = null;
        },
        startConversationWithStartupOption() {
            var that = this;
            if (window.g_extend_info) {
                var option = Tool.parseStartupOption(window.g_extend_info);
                if (option.cid) {
                    this.joinAndStartConversation(option.cid)
                }
            }
        },
        joinAndStartConversation(cid){
            console.log('joinAndStartConversation',cid)
            window.main_screen.controller.emit("group_contains_attendee", {
                group_id:cid
            }, (is_succ) => {
                if(is_succ){
                    setTimeout(() => {
                        this.openConversation(cid,2);
                    }, 1000);
                }else{
                    window.main_screen.applyJoinGroup({
                        mark:'',
                        gid:cid,
                        inviterID:0,
                        source:1,
                    },(res)=>{
                        if (res.error_code==0) {
                            setTimeout(() => {
                                this.openConversation(cid,2);
                            }, 1000);
                        }
                    })
                }
            });
        },
        setFileType(list){
            for(let file of list){
                let file_type=file.url.replace(/.+\./,"").toLowerCase()
                if(file.msg_type==this.systemConfig.msg_type.File){
                    file.url_local=`static/resource_pc/images/file_icon/${file_type}.png`
                    file.file_type='pdf'
                }else if(file.msg_type==this.systemConfig.msg_type.Video){
                    file.url_local=getLocalImgUrl(file.url)
                    file.file_type='mp4'
                }else{
                    file.url_local=getLocalImgUrl(file.url)
                    file.file_type='img'
                }
                file.realUrl=file.url_local
            }
        },
        // queryRepositoryReviewed(callback){
        //     window.main_screen.controller.emit("query_repository_reviewed", {}, callback);
        // },
        messageNotify(type='new_message'){
            if (this.notifying||window.livingStatus === 2) {
                //去除重复通知 ,直播中声音去掉
                return ;
            }
            document.querySelector('#message_notify').play()
            if (Tool.ifAppClientType(window.clientType)) {
                window.CWorkstationCommunicationMng.clientNotice({})
            }else{
                if (window.Notification) {
                    if (Notification.permission=='granted') {
                        this.postNotification(type)
                    } else if (Notification.permission!='denied') {
                        if (!this.applyingPermission) {
                            this.$MessageBox.confirm(this.lang.notification_reqeust_tip).then(action => {
                                Notification.requestPermission((permission)=>{
                                    if (permission=='granted') {
                                        this.postNotification(type)
                                    }
                                    this.applyingPermission=false;
                                })
                            });

                            this.applyingPermission=true;
                        }
                    }
                }
            }
        },
        postNotification(type){
            if (!document.hasFocus()) {
                var notification = new Notification(this.lang.app_name,{
                    body:this.lang.notification_body[type],
                    icon:'static/resource_pc/images/new_logo.png'
                });
            }
        },
        isResource(type){
            if (type==this.systemConfig.msg_type.Image||
            type==this.systemConfig.msg_type.Frame||
            type==this.systemConfig.msg_type.OBAI||
            type==this.systemConfig.msg_type.Cine||
            type==this.systemConfig.msg_type.Video||
            type==this.systemConfig.msg_type.RealTimeVideoReview||
            type==this.systemConfig.msg_type.VIDEO_CLIP
            ) {
                return true
            }else{
                return false;
            }
        },
        NotifyStandaloneWorkstationShareExamInfo(data){
            //判断是否在电视墙下，发起实时下，阅片下
            if(window.vm.$store.state.dynamicGlobalParams.tv_wall_mode){//在电视墙下禁止操作
                window.CWorkstationCommunicationMng.ShowConfirmDialog({
                    title: this.lang.warning_title,
                    message: this.lang.tv_wall_warning_message,
                    yes_button: this.lang.confirm_txt,
                });
                window.CWorkstationCommunicationMng.ClearStandaloneWorkstationShareExamInfo();
                return;
            }
            // if(window.vm.$store.state.realtimeVideo.desktop_started){//发起实时下
            //     window.CWorkstationCommunicationMng.ShowConfirmDialog({
            //         title: this.lang.warning_title,
            //         message: this.lang.real_time_warning_message,
            //         yes_button: this.lang.confirm_txt,
            //     });
            //     window.CWorkstationCommunicationMng.ClearStandaloneWorkstationShareExamInfo();
            //     return;
            // }

            if(this.$route.name=='gallery'){//在阅片下，转发窗口盖在最上层
                console.log("gallery");
                this.$root.eventBus.$emit('hideRealTimeVideo');
            }

            console.log("NotifyStandaloneWorkstationShareExamInfo ", data);

            this.$root.eventBus.$emit('openTransmit',{
                callback:this.generalTransmitSubmit,
                isStandaloneWorkstationShare:1,
                cid:this.$route.params.cid,
            })
        },
        generalTransmitSubmit(data){
            console.log("generalTransmitSubmit ", data);
            if(this.$route.name=='gallery'){
                this.$root.eventBus.$emit('showRealTimeVideo');
            }

            Object.entries(this.conversationList).map(item=>{ // 判断会话列表中是否有与fid对应的会话 有则直接取之前的cid
                if(data.id &&item[1].fid === data.id){
                    data.cid = item[0]
                }
            })
            this.$root.transmitQueue_StandaloneWorkstation[data.cid||'f-'+data.id]=1;
            if (this.conversationList[data.cid]) {
                //会话已开启则直接转发
                window.CWorkstationCommunicationMng.SendStandaloneWorkstationShareExamInfo(data.cid);
            }else{
                //会话未开启则开启会话
                if (data.cid) {
                    this.openConversation(data.cid,7)
                }else{
                    this.openConversation(data.id,3)
                }
            }
        },
        autoLoginAction(params){
            if (params.action==='loginByToken') {
                this.autoLoginByToken(params.loginToken,params.callback)
            } else if (params.action==='getLoginToken') {
                //兼容旧版本的本地密码登录，后替换为Token
                const tokenParams={
                    login_name:params.account,
                    password:params.password,
                    language:window.localStorage.getItem('lang')||'CN',
                    clientType:this.systemConfig.clientType,
                    deviceId:this.deviceInfo.device_id
                }
                service.getLoginToken(tokenParams).then((res)=>{
                    if (res.data.error_code===0) {
                        this.autoLoginByToken(res.data.data.token,params.callback);
                    }else{
                        this.isAutoLogging=false;
                        this.resetStartupOption();
                        this.clearAndDirectToLogin(this.lang[res.data.key]);
                    }
                }).catch(()=>{
                    this.isAutoLogging=false;
                })
            }
        },
        autoLoginByToken(loginToken,callback){
            service.loginByToken({
                token:loginToken,
                deviceInfo:{
                    device_id:this.deviceInfo.device_id,
                    client_type:this.systemConfig.clientType
                }
            }).then((res)=>{
                window.Logger.save({
                    message:'autoLoginByToken',
                    eventType: `socket_event`,
                    data: {res},
                });
                if(res.data.error_code===0){
                    setTimeout(()=>{
                        this.loginLoading=false
                    },300)
                    const user=res.data.data
                    handleAfterLogin(user,true)
                    callback&&callback();
                }else{

                    if (res.data.key==='userChangePwdNeedLogin'||res.data.key==='codeMustRequired') {
                        this.isAutoLogging=false;
                        this.resetStartupOption();
                        this.clearAndDirectToLogin(this.lang[res.data.key]);
                    }else if(res.data.key==='userTokenError'){
                        this.isAutoLogging=false;
                        this.resetStartupOption();
                    }else if (res.data.key=='userOutOfTrail') {
                        //试用期到跳转到输入推荐码界面
                        this.isAutoLogging=false;
                        this.resetStartupOption();
                        this.clearAndDirectToLogin(this.lang.userOutOfTrail);
                        setTimeout(()=>{
                            this.$router.push(`/login/referral_code?token=${loginToken}&isAutoLogin=1&isRemember=1`)
                        },300);
                        return ;
                    }else{
                        setTimeout(()=> {
                            this.autoLoginByToken(loginToken,callback)
                        },5000)
                    }
                }
            },(res)=>{
                setTimeout(()=> {
                    this.autoLoginByToken(loginToken,callback)
                },5000)
            })
        },
        updateLiveCount({needRefresh=true}={}){
            window.main_screen.getBroadcastStatusCount(null,(res)=>{
                if(!res.error_code){
                    if(needRefresh){
                        this.$root.eventBus.$emit('refreshLiveManagementList')
                    }
                    this.$store.commit('notifications/updateLiveCount',res.data)

                }
            })
        },
        NotifyAgoraLiveStart(data){
            this.$store.commit('liveConference/updateConferenceState',{
                cid:data.groupInfo.id,
                obj:{
                    conferenceState:1,
                    senderUserId:data.host_uid
                }

            })
            const info = {
                ...data.groupInfo,
                nickname:data.hostNickname,
                fid:data.host_uid,
                avatar:data.hostAvatar||data.groupInfo.avatar
            }
            this.$set(this,'livingGroupInfo',info)
            this.debounceSortChatList()
            if(!data.hasOwnProperty('liveStartTime')){ //该直播没有曾经开播时间。
                this.showLivingNotifyDialog = true
            }

        },
        getMultiCenterOptionList(){
            multiCenterService.getMultiCenterAllOptions().then((res)=>{
                if (res.data.error_code==0) {
                    this.$store.commit('multicenter/updateMCOptionList',res.data.data);
                }
            })
        },
        NotifyAgoraLiveStop(data){
            this.$store.commit('liveConference/updateConferenceState',{
                cid:data.groupInfo.id,
                obj:{
                    conferenceState:0,
                    senderUserId:0
                }
            })
            this.showLivingNotifyDialog = false

        },
        ifNeedAutoPushStream(){
            let odata  = localStorage.getItem('auto_push_stream_' + this.user.id);
            if(odata){
                let data = JSON.parse(odata)
                let isAutoPushStream = data.enable
                let lastPushStreamCid = data.value
                if(isAutoPushStream&&lastPushStreamCid){
                    this.requestConversationToStartUltrasoundDesktopByAutoPushStream(data)
                }

            }
            this.$store.commit('liveConference/updateConferenceValue',{
                autoPushReady:true,
            })
        },

        // ConferenceID = "conferenceid12345";（云++直播群的id）
        // mainstreamType="Doppler";
        // auxstreamType="Camera";
        // action="start";(开始start/离开leave/退出quit)


        // NotifyInitNativeAgoraSdk(json){
        //     if(!json.error_code){
        //         this.$store.commit('liveConference/updateConferenceValue',{nativeSdkReady:true})
        //     }
        // },
        NotifySwitchNativeRtcSettingStatus(json){
            if(!json.error_code){
                this.$store.commit('device/updateDeviceInfo',{isOpenRtcSettingDialog:json.data.status==='open'?true:false})
            }
        },
        leaveSilence(callback){
            window.main_screen.CMonitorWallPush.controller.emit('leaveSilence',(is_suc)=>{
                callback&&callback(is_suc)
            })
        },
        getDeviceNameById(){
            if(!this.isCef){
                return
            }
            if (!this.deviceInfo.device_id) {
                setTimeout(()=>{
                    this.getDeviceNameById();
                },3000)
                return ;
            }
            return new Promise((resolve,reject)=>{
                const params = {
                    deviceId:this.deviceInfo.device_id,
                }
                window.main_screen.getDeviceNameById(params,(res)=>{
                    if(res.error_code === 0){
                        this.$store.commit('device/updateDeviceInfo',{device_name:res.data.name})
                        resolve(true)
                    }else{
                        reject(res.error_msg)
                    }

                })
            })
        },
        getAllTags(){
            service.getAllTags().then((res)=>{
                if (res.data.error_code==0) {
                    this.$store.commit('gallery/addTagTopInfo', res.data.data)
                }
            })
        },
        getAiAnalyzeTypes(){
            service.getAiAnalyzeTypes().then((res)=>{
                if (res.data.error_code==0) {
                    this.$store.commit('aiPresetData/updateAiPresetData',res.data.data)
                }
            })
        },
        NotifyUpdateLiveRecord(data){
            console.log('NotifyUpdateLiveRecord',data)
            this.$store.commit("conversationList/updateChatMessageLiveRecordData", {
                group_id: data.gid,
                resource_id: data.resource_id,
                live_record_data: data.resource.more_details,
                coverUrl:data.coverUrl
            });
            this.$store.commit("consultationImageList/updateConsultationLiveRecordData", {
                group_id: data.gid,
                resource_id: data.resource_id,
                live_record_data: data.resource.more_details,
                coverUrl:data.coverUrl
            });
        },
        setWhiteBoardUrl(){
            if(Tool.checkAppClient('Cef')){
                let language = window.localStorage.getItem('lang')
                CWorkstationCommunicationMng.SetWhiteBoardUrl({
                    url:window.location.href.includes('localhost')?
                        `http://localhost:8888/whiteboard.html#/index?language=${language}`
                        :Tool.transferLocationToCe(`${getBaseUrl()}/whiteboard/whiteboard.html#/index?language=${language}`)
                })
            }

        },
        findMulticenter(cid){
            //查询群是否属于多中心
            // let controller = conversation.socket
            const controller=window.main_screen.conversation_list[cid];
            controller&&controller.getMulticenter({},(res)=>{
                if (!res.error_code) {
                    let multicenter_list = []
                    if(res.data){
                        multicenter_list = res.data.constructor == Array? [...res.data] : [res.data]
                    }
                    let data={
                        list:multicenter_list,
                        cid: cid,
                    }
                    this.$store.commit('conversationList/updateMulticenterList',data)
                }
            })
        },
        kickoutAttendeeHandle(data, controller){
            //收到某人退群的消息
            const that = this;
            if(data.uid == that.user.id){
                if(window.vm.$root.currentLiveCid&&window.main_screen.conversation_list[window.vm.$root.currentLiveCid]){
                    let liveRoom = getLiveRoomObj(window.vm.$root.currentLiveCid)
                    if(!liveRoom){
                        return
                    }
                    liveRoom.LeaveChannelAux()
                    window.CWorkstationCommunicationMng.StopConference()
                    setTimeout(()=>{
                        closeChatWindowIfNeed(data.cid);
                    },300)

                }
            }

            let deleteGroupConnent=function(data){
                that.$store.commit('chatList/deleteChatList', {cid: data.cid})
                that.$store.commit('groupList/deleteGroupList', {cid: data.cid})
                that.$store.commit('conversationList/deleteConversationList', {cid: data.cid})
                that.$store.commit('notifications/deleteGroupApplyByCid', {cid: data.cid})
                //删除群相关的图像
                that.$store.commit('consultationImageList/deleteConsultationImageListByGroupID', {cid: data.cid});
                closeChatWindowIfNeed(data.cid);
            }
            if(data.uid == that.user.id){ //自己退群
                let message = "";
                if(data.initiator_uid == that.user.id) {//自己主动退群
                    message = that.lang.user_exit_group_succ;
                    let conversation = that.conversationList[data.cid]
                    if(!conversation){
                        return
                    }
                    if(conversation.attendeeList&&conversation.attendeeList[`attendee_${that.user.id}`]){
                        conversation.attendeeList[`attendee_${that.user.id}`].attendeeState=0
                    }
                    that.$root.eventBus.$emit('createGroupAvatar',{conversation:conversation,callback:()=>{
                        controller.emit("response_delete_attendee", function(is_succ){});

                    }})

                    deleteGroupConnent(data);
                }else{//自己被动退群
                    message = data.initiator_nickname + " " + that.lang.user_passive_exit_group + " " + data.subject;

                    //给服务器发送消息response_delete_attendee，通知断开参与者
                    controller.emit("response_delete_attendee", function(is_succ){});
                    deleteGroupConnent(data);
                }
                that.$message.success(message);
            }else{ //他人退群
                that.$store.commit('conversationList/deleteAttendee',data)
                that.$store.commit('notifications/deleteGroupApplyByUidAndCid',{uid:data.uid, cid:data.cid});
                // this.notify_delete_attendee_timer&&clearTimeout(this.notify_delete_attendee_timer)
                // this.notify_delete_attendee_timer = setTimeout(()=>{
                //     let conversation = that.conversationList[data.cid]
                //     that.$root.eventBus.$emit('createGroupAvatar',conversation)
                // },1000)
            }
        },
        sortChatList(){
            this.$store.commit('chatList/sortChatList')
        },
        getGroupSetList(){
            window.main_screen.getGroupsetList({},(data)=>{
                console.log('getGroupsetList',data)
                if (data.error_code==0) {
                    let list=data.data;
                    list.forEach(item=>{
                        item.type=this.systemConfig.ConversationConfig.type.GroupSet;
                    })
                    this.$store.commit('groupset/initGroupsetList',data.data);
                }
            })
            this.getManagerGroupsetList()
        },
        getManagerGroupsetList(){
            window.main_screen.getManagerGroupsetList({},(data)=>{
                console.log(data,'getManagerGroupsetList')
                if (data.error_code==0) {
                    let list=data.data;
                    list.forEach(item=>{
                        item.type=this.systemConfig.ConversationConfig.type.GroupSet;
                    })
                    this.$store.commit('groupset/initGroupsetManagerList',data.data);
                }
            })
        },
        checkExitConversation(cid) {
            if (
                window.main_screen.conversation_list.hasOwnProperty(cid) &&
                window.main_screen.conversation_list[cid].gateway.check&&
                window.vm.$store.state.conversationList[cid].socket
            ) {
                return true;
            } else {
                return false;
            }
        },
        getApplyCount(cid){
            window.main_screen.conversation_list[cid].getApplyCount({},(res)=>{
                if(res.error_code===0){
                    this.$store.commit('conversationList/updateConversation',{
                        cid:cid,
                        key:'applyCount',
                        value:res.data
                    });
                }
            })
        },
        getUnfinishedHomework(cid){
            service.getIncompleteList({
                gid:cid,
                page:1,
                pageSize:1,
            }).then(res=>{
                if (res.data.error_code === 0) {
                    const homework = res.data.data.data.shift();
                    if (homework) {
                        if (cid === 0) {
                            this.$store.commit("homework/updateHomework", {
                                globalUnfinish:1
                            });
                        }else{
                            let obj = {}
                            obj[cid] = homework
                            this.$store.commit("homework/updateUnfinish",obj);
                        }
                    }else{
                        if (cid === 0) {
                            this.$store.commit("homework/updateHomework", {
                                globalUnfinish:0
                            });
                        }else{
                            let obj = {}
                            obj[cid] = undefined
                            this.$store.commit("homework/updateUnfinish",obj);
                        }
                    }
                }
            })
        },
        getUncorrectHomework(cid){
            service.getUncorrectedList({
                gid:cid,
            }).then(res=>{
                if (res.data.error_code === 0) {
                    const map = res.data.data;
                    if (Object.keys(map).length !== 0) {
                        if (cid === 0) {
                            this.$store.commit("homework/updateHomework", {
                                globalUnCorrect:map
                            });
                        }else{
                            let obj = {}
                            obj[cid] = map
                            this.$store.commit("homework/updateUncorrected",obj);
                        }
                    }else{
                        if (cid === 0) {
                            this.$store.commit("homework/updateHomework", {
                                globalUnCorrect:undefined
                            });
                        }else{
                            let obj = {}
                            obj[cid] = undefined
                            this.$store.commit("homework/updateUncorrected",obj);
                        }
                    }
                }
            })
        },
        getCorrectedHomework(cid){
            service.getFinishedList({
                gid:cid,
                page:1,
                pageSize:30,
                // 不使用status参数，改为在获取数据后过滤
            }).then(res=>{
                if (res.data.error_code === 0) {
                    const homeworkList = res.data.data.data;
                    // 计算status=3（已批改，未阅）的作业数量
                    const correctedCount = homeworkList.filter(item => item.status === 3).length;


                    if (correctedCount > 0) {
                        if (cid === 0) {
                            this.$store.commit("homework/updateHomework", {
                                globalCorrected: correctedCount
                            });
                        } else {
                            let obj = {}
                            obj[cid] = homeworkList.filter(item => item.status === 3)
                            this.$store.commit("homework/updateCorrected", obj);
                        }
                    } else {
                        if (cid === 0) {
                            this.$store.commit("homework/updateHomework", {
                                globalCorrected: 0
                            });
                        } else {
                            let obj = {}
                            obj[cid] = undefined
                            this.$store.commit("homework/updateCorrected", obj);
                        }
                    }
                }
            })
        },
        NotifyUpdateAnnouncement(data){
            if(data.switch){
                this.$store.commit('globalParams/updateGlobalParams',{
                    closedNotifyBar:false,
                    announcementContent:data.content,
                })
            }else{
                this.$store.commit('globalParams/updateGlobalParams',{
                    closedNotifyBar:true,
                    announcementContent:'',
                })
            }

        },
        queryIStationInfo_DR(){
            Tool.createCWorkstationCommunicationMng({
                name:'queryIStationInfo_DR',
                emitName:'IStationInfo_DR',
                params:{},
                timeout:5000,
            }).then((data)=>{
                window.enable_istation=data.Show;
                this.$store.commit('globalParams/updateGlobalIstationInfo',data)
                window.vm.$store.commit('device/updateDeviceInfo', {
                    isIStationInfoDR: true,
                })
            })
            window.CWorkstationCommunicationMng.RequestDrConnectStatus()
        },
        handleDialogClose(){
            this.$set(this,'livingGroupInfo',{})
        },
        observeImageLoad(){
            const fallbackImageUrl = 'static/resource_pc/images/slt_err.png';
            Tool.observeImageLoad(fallbackImageUrl)
        },
        checkAutoEnterTvWall(){
            if(this.user.preferences.auto_enter_tv_wall){
                if(this.functionsStatus.tvwall&&this.user.role>1&&!this.isWorkStation){
                    if(location.href.includes('localhost')){
                        return
                    }
                    this.$root.eventBus.$emit('enterTVmode')
                }

            }
        },
        initDeviceFailureMap(){
            this.$store.commit('device/updateDeviceInfo',{
                deviceFailure:{}
            })
            service.getNewDeviceFailure({series_numbers:[]}).then(res=>{
                if (res.data.error_code===0) {
                    const deviceFailure = this.$store.state.device.deviceFailure;
                    for(let device_id in res.data.data){
                        let num = res.data.data[device_id].length || 0;
                        const obj = {}
                        obj[device_id] = num;
                        this.$store.commit('device/updateDeviceFailure',obj);
                    }
                }
            })
        }
    }
}
</script>
<style lang="scss">
.deep_bg_wrap{
    background-color:#a9bfbe;
    height:100%;
    position:relative;
    z-index:1;
    word-break: break-all;
    .main_viewport{
        position: absolute;
        top: 14px;
        left: 14px;
        right: 14px;
        bottom: 14px;
        overflow: auto;
        background-color:#a9bfbe;
        &.no_gap{
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
        .flex_container{
            display: flex;
            flex-direction: column;
            min-height: 600px;
            min-width: 1200px;
            height: 100%;
            position:relative;
            overflow:hidden;
            .network_unavailable{
                position: absolute;
                left: 50%;
                top: 100px;
                z-index: 9001;
                transform: translate(-50%);
                background: #f92d2d;
                color: #fff;
                padding: 8px 20px;
                border-radius: 10px;
                box-shadow: 4px 4px 4px rgba(0,0,0,.3);
            }
            .header_bar{
                height: 82px;
                background-color: #d7dfe1;
                border-bottom: 1px solid #E7EFF2;
                box-sizing: border-box;
                display: flex;
            }
            .chat_container{
                display: flex;
                border-top: 2px solid #A9BFBE;
                min-height: 0;
                height: calc(100% - 82px);
                user-select:none;
                position: relative;
                z-index: 10;
            }
        }
        .license{
            position:absolute;
            right:10px;
            bottom:0;
            font-size:10px;
            color:#000;
            z-index:10;
        }
        .web_build_time{
            position:absolute;
            right:260px;
            top:0;
            font-size:10px;
            color:#000;
        }
    }
}
.offline{
    filter: grayscale(100%);
    -webkit-filter: grayscale(100%);
//     -webkit-transform: translateZ(0);
}
</style>
