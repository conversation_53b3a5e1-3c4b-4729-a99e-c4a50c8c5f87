<template>
    <div class="uploadTestOverview_container">
        <div class="custom_header">
            <div class="back_btn" @click="back">
                <i class="el-icon-arrow-left"></i>
                <span>{{ lang.back_button }}</span>
            </div>
        </div>
        <div class="custom_body" ref="customBody" v-loading="loading">
            <template v-if="!loading">
                <div class="top_info_section">
                    <div class="icon_area">
                        <div class="placeholder_icon">
                            <i class="el-icon-collection"></i>
                        </div>
                    </div>
                    <div class="details_area">
                        <div class="detail_top">
                            <div class="detail_title">
                                {{ title}}
                            </div>
                        </div>
                        <div class="detail_bottom" v-if="deadline">
                            <p class="deadline">截止时间: {{ deadline }}</p>
                        </div>
                        <div class="action_buttons_container" style="display: flex; align-items: center; margin-top: 22px;">
                            <el-upload
                                ref="uploadComponent"
                                action=""
                                :auto-upload="false"
                                :show-file-list="false"
                                :multiple="false"
                                :on-change="handleFilesSelectedAndImmediateUpload"
                                :file-list="fileListForElUpload"
                            >
                                <el-button
                                    slot="trigger"
                                    type="primary"
                                    class="upload_btn"
                                    :loading="validatingFile"
                                    :disabled="currentlyUploadingCount > 0 || isSubmitting"
                                >
                                    <i class="el-icon-upload"></i>
                                    {{ Object.values(this.uploadTask).some(task => task.status === 'waiting' && !task.isUploaded && !task.ossContent) ? "重新选择文件" : "选择文件" }}
                                </el-button>
                            </el-upload>
                            <el-button
                                type="primary" class="submit_btn upload_btn" @click="handleSubmitAnswer"
                                :loading="isSubmitting"
                                :disabled="Object.values(this.uploadTask).filter(task => task.status === 'waiting' && !task.isUploaded && !task.ossContent).length === 0 || currentlyUploadingCount > 0 || isSubmitting"
                                style="margin-left: 10px;"
                            >
                             提交答案
                            </el-button>
                        </div>
                        <div class="el-upload__tip" style="margin-top: 10px; color: #909399; font-size: 14px; text-align: left;">支持格式：{{ fileTypesText }}</div>
                    </div>
                </div>
                <div class="content_section review_history_section">
                    <h3>批改/审核历史</h3>
                    <el-table :data="reviewHistoryData" style="width: 100%">
                        <el-table-column prop="reviewTime" label="批改时间" width="180"></el-table-column>
                        <el-table-column prop="reviewResult" label="批改结果" width="120"></el-table-column>
                        <el-table-column prop="reviewComment" label="评语"></el-table-column>
                    </el-table>
                </div>
                <div class="content_section uploaded_files_section">
                    <h3>待上传文件列表</h3>
                    <el-table :data="processedUploadedFilesData" style="width: 100%">
                        <el-table-column prop="name" :label="'文件名称'"></el-table-column>
                        <el-table-column prop="size" :label="'文件大小'" width="120"></el-table-column>
                        <el-table-column :label="'状态'" width="180">
                            <template slot-scope="scope">
                                <template v-if="!scope.row.showProgress">
                                    <span
                                        :class="[
                                            {
                                                'status-tag-pending-new': scope.row.rawTask.status === 'waiting',
                                                'status-tag-passed-new': scope.row.rawTask.status === 'success',
                                                'status-tag-failed-new': scope.row.rawTask.status === 'error' || (scope.row.rawTask.isError && scope.row.rawTask.status !== 'cancelled'),
                                                'status-tag-unsubmitted-new': scope.row.rawTask.status === 'cancelled',
                                                'status-tag-default-new': !['waiting', 'success', 'error', 'cancelled'].includes(scope.row.rawTask.status) && !(scope.row.rawTask.isError && scope.row.rawTask.status !== 'cancelled')
                                            }
                                        ]"
                                    >{{ scope.row.statusText }}</span>
                                </template>
                                <el-progress
                                    v-if="scope.row.showProgress"
                                    :percentage="scope.row.percentage"
                                    :stroke-width="16"
                                    text-inside
                                ></el-progress>
                            </template>
                        </el-table-column>
                        <el-table-column :label="'操作'" width="200">
                            <template slot-scope="scope">
                                <div class="operation-buttons-container">
                                    <el-button
                                        @click="handleDownloadFile(scope.row.rawTask)"
                                        type="text"
                                        class="option-btn"
                                        v-if="
                                            scope.row.isUploaded && scope.row.rawTask && scope.row.rawTask.ossImageUrl
                                        "
                                    >
                                        <i class="el-icon-download"></i> {{ "下载" }}
                                    </el-button>
                                    <el-button
                                        @click="handleDeleteFile(scope.row.rawTask)"
                                        type="text"
                                        class="option-btn"
                                        :disabled="
                                            scope.row.rawTask &&
                                            scope.row.rawTask.status === 'uploading' &&
                                            !scope.row.rawTask.ossContent
                                        "
                                    >
                                        <i class="el-icon-delete"></i> {{ "删除" }}
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import base from "../../lib/base";
import { uploadFile, cancelUpload } from "@/common/oss/index";
import Tool from "@/common/tool.js";
import { v4 as uuidv4 } from "uuid";
import service from "../../service/service";
import moment from "moment";
export default {
    mixins: [base],
    name: "UploadTestOverview",
    components: {},
    data() {
        return {
            title: "这是考核要求的名称这是考核要求的名称这是考核要求的名称",
            deadline: 0,
            reviewHistoryData: [
                { reviewTime: "2025-04-23 10:23:30", reviewResult: "不通过", reviewComment: "没有填写完整" },
                { reviewTime: "2025-04-23 10:23:30", reviewResult: "通过", reviewComment: "" },
                { reviewTime: "2025-04-23 10:23:30", reviewResult: "通过", reviewComment: "" },
                { reviewTime: "2025-04-23 10:23:30", reviewResult: "通过", reviewComment: "" },
                { reviewTime: "2025-04-23 10:23:30", reviewResult: "通过", reviewComment: "" },
                { reviewTime: "2025-04-23 10:23:30", reviewResult: "通过", reviewComment: "" },
                { reviewTime: "2025-04-23 10:23:30", reviewResult: "通过", reviewComment: "" },
                { reviewTime: "2025-04-23 10:23:30", reviewResult: "通过", reviewComment: "" },
                { reviewTime: "2025-04-23 10:23:30", reviewResult: "通过", reviewComment: "" },
            ],
            uploadedFilesData: [],
            uploadTask: {},
            fileListForElUpload: [],
            assessmentInfo: {
                pathPrefix: "mockAssessment123",
                allowFileTypes: [
                    "jpg",
                    "jpeg",
                    "png",
                    "mp4",
                    "pdf",
                    "doc",
                    "docx",
                    "xml",
                    "avi",
                    "mov",
                    "wmv",
                    "flv",
                    "mkv",
                ],
                maxFileSizeMB: 200 },
            currentlyUploadingCount: 0,
            uploadQueue: [],
            debouncedAlertUnSupportedFileTypes: null,
            debouncedAlertMaxFileSize: null,
            loading: false,
            userRole: "",
            testId: "",
            trainingId: "",
            testInfo: {},
            validatingFile: false,
            isSubmitting: false,
        };
    },
    computed: {
        processedUploadedFilesData() {
            return Object.values(this.uploadTask).map((task) => {
                let statusText = "";
                let showProgress = false;

                switch (task.status) {
                case "waiting":
                    statusText = (this.lang && this.lang.upload_status_waiting) || "等待上传";
                    break;
                case "uploading":
                    statusText = "";
                    showProgress = true;
                    break;
                case "success":
                    statusText = (this.lang && this.lang.upload_status_success) || "成功";
                    break;
                case "error":
                    statusText = (this.lang && this.lang.upload_status_error) || "失败";
                    break;
                case "cancelled":
                    statusText = (this.lang && this.lang.upload_status_cancelled) || "已取消";
                    break;
                default:
                    statusText = (this.lang && this.lang.upload_status_unknown) || "未知";
                }
                if (task.isError && task.status !== "cancelled") {
                    statusText = (this.lang && this.lang.upload_status_error) || "失败";
                }

                return {
                    uid: task.uid,
                    name: this.truncateFileName(task.name || "", 30),
                    size: this.formatFileSize(task.size || 0),
                    statusText: statusText,
                    percentage: task.percentage || 0,
                    showProgress: showProgress,
                    isUploaded: task.isUploaded,
                    rawTask: task,
                };
            });
        },
        fileTypesText() {
            if (
                !this.assessmentInfo ||
                !this.assessmentInfo.allowFileTypes ||
                this.assessmentInfo.allowFileTypes.length === 0
            ) {
                return "";
            }

            return this.assessmentInfo.allowFileTypes.map((type) => `.${type}`).join("、");
        },
    },
    async created() {
        this.userRole = this.$route.params.role;
        this.testId = this.$route.params.testId;
        this.trainingId = this.$route.params.trainingId;
        this.debouncedAlertUnSupportedFileTypes = Tool.debounce(this.alertUnSupportedFileTypesMsg, 300);
        this.debouncedAlertMaxFileSize = Tool.debounce(this.alertMaxFileSizeMsg, 300);
        this.loading = true;
        await this.getTrainingTestInfoByTestId();
        this.loading = false;
    },
    beforeDestroy() {
        Object.values(this.uploadTask).forEach((task) => {
            if (task.status === "uploading" && task.ossContent && typeof cancelUpload === "function") {
                cancelUpload(task.uploadId)
                    .then(() => console.log(`Upload ${task.uid} cancelled.`))
                    .catch((err) => console.error(`Error cancelling upload ${task.uid}:`, err));
                task.status = "cancelled";
            }
        });
    },
    methods: {
        alertUnSupportedFileTypesMsg() {
            this.$message.error("文件类型不支持，请选择支持的文件类型。");
        },
        alertMaxFileSizeMsg() {
            this.$message.error("文件大小超限，请选择符合要求的文件。");
        },
        back() {
            if (this.$router) {
                this.$router.go(-1);
            } else {
                console.warn("Vue Router not found for back() method.");
            }
        },
        handleUploadFile() {
            console.log("Upload file clicked");
        },
        handleDownloadFile(task) {
            console.log("Download file:", task);
            if (task && task.ossImageUrl) {
                const downloadLink = document.createElement("a");
                downloadLink.href = task.ossImageUrl;
                downloadLink.setAttribute("download", task.name);
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
            } else {
                this.$message.error((this.lang && this.lang.download_url_missing_tip) || "文件链接不存在，无法下载。");
            }
        },
        handleDeleteFile(taskToDelete) {
            if (!taskToDelete || !taskToDelete.uid) {
                return;
            }
            const uid = taskToDelete.uid;
            const task = this.uploadTask[uid];
            console.error(task);
            if (!task) {
                const queueIndex = this.uploadQueue.indexOf(uid);
                if (queueIndex > -1) {
                    this.uploadQueue.splice(queueIndex, 1);
                    this.$message.info((this.lang && this.lang.file_removed_from_queue_tip) || `文件已从上传队列移除`);
                }
                return;
            }

            // 如果文件只是在待提交列表 (status: waiting, 未上传, 也没有ossContent)
            if (task.status === 'waiting' && !task.isUploaded && !task.ossContent) {
                this.$delete(this.uploadTask, uid);
                const queueIndex = this.uploadQueue.indexOf(uid); // 也可能在提交后但在上传前被加入队列
                if (queueIndex > -1) {
                    this.uploadQueue.splice(queueIndex, 1);
                }
                this.$message.success("文件已从待提交列表中移除");
                return;
            }

            this.$confirm(
                (this.lang && this.lang.delete_file_confirm_message) || `确定要删除文件 "${task.name}"吗?`,
                (this.lang && this.lang.confirm_title) || "提示",
                {
                    confirmButtonText: (this.lang && this.lang.button_confirm) || "确定",
                    cancelButtonText: (this.lang && this.lang.button_cancel) || "取消",
                    type: "warning",
                }
            )
                .then(async () => {
                    console.error(task.status, task.uploadId);
                    if (task.status === "uploading" && task.uploadId) {
                        try {
                            await cancelUpload(task.uploadId);
                            task.status = "cancelled";
                            task.progressShow = false;
                        } catch (error) {
                            console.error("Failed to cancel upload:", error);
                            this.$message.error(
                                (this.lang && this.lang.upload_cancel_failed_tip) || `取消上传 ${task.name} 失败`
                            );
                        }
                    }

                    if (task.isUploaded) {
                        console.log(`TODO: Call API to delete server file for ${task.name}, path: ${task.ossImageUrl}`);
                    }
                    console.error(uid, this.uploadQueue, this.uploadTask, this.currentlyUploadingCount);
                    const queueIndex = this.uploadQueue.indexOf(uid);
                    console.error(queueIndex);
                    if (queueIndex > -1) {
                        this.uploadQueue.splice(queueIndex, 1);
                    }

                    this.$delete(this.uploadTask, uid);
                })
                .catch(() => {});
        },
        triggerFileUploadProcess() {
            console.warn("triggerFileUploadProcess is now obsolete in the primary upload flow.");
        },
        async uploadSingleFile(uid) {
            const task = this.uploadTask[uid];
            if (!task || !task.rawFile) {
                console.error("Task or rawFile not found for UID:", uid);
                throw new Error("Task or rawFile not found");
            }

            task.status = "uploading";
            task.progressShow = true;
            task.percentage = 0;
            task.isError = false;

            const fileNameInOss = this.generateUniqueFileName(task.name);
            const filePath = `${this.assessmentInfo.pathPrefix}/${fileNameInOss}`;

            let ossBucket = "";
            try {
                if (
                    !this.$store ||
                    !this.$store.state ||
                    !this.$store.state.systemConfig ||
                    !this.$store.state.systemConfig.serverInfo
                ) {
                    console.error("System config not available in Vuex store.");
                    throw new Error("System config not available");
                }
                ossBucket = this.$store.state.systemConfig.serverInfo.oss_consultation_file_storage_server.bucket;
                if (!ossBucket) {
                    throw new Error("OSS Bucket not configured");
                }
            } catch (e) {
                task.status = "error";
                task.isError = true;
                task.progressShow = false;
                console.error("Error getting OSS bucket:", e);
                this.$message.error((this.lang && this.lang.oss_config_error_tip) || "OSS配置错误，无法上传。");
                throw e;
            }

            return new Promise(async (resolve, reject) => {
                try {
                    const { ossClient } = await uploadFile({
                        bucket: ossBucket,
                        file: task.rawFile,
                        filePath: filePath,
                        callback: (event, data, uploadId) => {
                            if (task.status === "cancelled") {
                                return;
                            }

                            if (event === "progress") {
                                task.percentage = data < 100 ? data : 99;
                                task.uploadId = uploadId;
                            } else if (event === "complete") {
                                task.percentage = 100;
                                task.status = "success";
                                task.isUploaded = true;
                                task.progressShow = false;
                                task.ossImageUrl = data;
                                task.uploadTime = new Date().toLocaleString();
                                console.log(`File ${task.name} uploaded successfully to ${data}`);
                                resolve({ uid: task.uid, success: true, url: data });
                            } else if (event === "error") {
                                console.error("Upload error event for:", task.name, data);
                                task.status = "error";
                                task.isError = true;
                                task.progressShow = false;
                                reject({ uid: task.uid, success: false, error: data });
                            } else if (event === "cancel") {
                                console.error("Upload cancel event for:", task.name, data);
                                task.status = "cancelled";
                                task.isError = true;
                                task.progressShow = false;
                                reject({ uid: task.uid, success: false, error: data });
                            }
                        },
                    });
                    task.ossContent = ossClient;
                } catch (uploadError) {
                    console.error("Failed to start upload for:", task.name, uploadError);
                    task.status = "error";
                    task.isError = true;
                    task.progressShow = false;
                    reject({ uid: task.uid, success: false, error: uploadError });
                }
            });
        },
        generateUniqueFileName(originalName) {
            const ext = originalName.includes(".") ? "." + originalName.split(".").pop() : "";
            const baseName = ext ? originalName.substring(0, originalName.lastIndexOf(".")) : originalName;
            const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9_.-]/g, "_");
            return `${uuidv4()}_${sanitizedBaseName}${ext}`;
        },
        formatFileSize(bytes) {
            if (bytes === 0) {
                return "0 Bytes";
            }
            const k = 1024;
            const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
        },
        truncateFileName(name, maxLength = 30) {
            if (!name) {
                return "";
            }
            if (name.length <= maxLength) {
                return name;
            }
            const extDotIndex = name.lastIndexOf(".");
            if (extDotIndex === -1 || name.length - extDotIndex > 10) {
                return name.substring(0, maxLength - 3) + "...";
            }
            const baseName = name.substring(0, extDotIndex);
            const extension = name.substring(extDotIndex);
            if (baseName.length > maxLength - extension.length - 3) {
                return baseName.substring(0, maxLength - extension.length - 3) + "..." + extension;
            }
            return name;
        },
        validateSingleFile(rawFile) {
            if (!rawFile) {
                return false;
            }
            const { allowFileTypes, maxFileSizeMB } = this.assessmentInfo;
            const fileName = rawFile.name;
            const fileSizeMB = rawFile.size / 1024 / 1024;

            let isValidType = true;
            if (allowFileTypes && allowFileTypes.length > 0) {
                const fileExt = Tool.getFileType(fileName);
                isValidType = allowFileTypes.includes(fileExt);
            }

            if (!isValidType) {
                this.debouncedAlertUnSupportedFileTypes();
                console.error(
                    `${(this.lang && this.lang.file_type_not_allowed_tip) || "文件类型不允许"}: ${fileName}. ${
                        (this.lang && this.lang.allowed_types_are_tip) || "允许的类型"
                    }: ${allowFileTypes.join(", ")}`
                );
                return false;
            }

            if (fileSizeMB > maxFileSizeMB) {
                this.debouncedAlertMaxFileSize();
                console.error(
                    `${
                        (this.lang && this.lang.file_size_exceeds_limit_tip) || "文件大小超限"
                    }: ${fileName} (${this.formatFileSize(rawFile.size)}). ${
                        (this.lang && this.lang.max_size_is_tip) || "最大允许"
                    }: ${maxFileSizeMB}MB`
                );
                return false;
            }

            return isValidType;
        },
        handleFilesSelectedAndImmediateUpload(file, fileList) {
            // 由于 :multiple="false", fileList 参数通常不在此场景下使用，主要处理单个 file 对象
            // 计划：先清除所有"待提交"状态的文件
            Object.keys(this.uploadTask).forEach(uid => {
                const task = this.uploadTask[uid];
                // 将 status === 'waiting' 并且尚未开始上传（没有ossContent）的任务视为待提交任务并清除
                if (task.status === 'waiting' && !task.isUploaded && !task.ossContent) {
                    this.$delete(this.uploadTask, uid);
                }
            });

            if (file.status !== "ready") { // el-upload 触发时，文件状态可能非ready，例如手动调用clearFiles()
                return;
            }

            this.validatingFile = true;
            const rawFile = file.raw;

            if (!this.validateSingleFile(rawFile)) {
                this.validatingFile = false;
                // 清理el-upload内部可能存在的文件引用，以允许用户再次选择相同文件
                if (this.$refs.uploadComponent) {
                    this.$refs.uploadComponent.clearFiles();
                }
                return;
            }

            const uid = file.uid; // 使用el-upload生成的uid

            // 添加新文件到uploadTask，状态为'waiting'（等待提交）
            this.$set(this.uploadTask, uid, {
                uid: uid,
                name: rawFile.name,
                size: rawFile.size,
                rawFile: rawFile,
                percentage: 0,
                status: "waiting", // 'waiting' 现在表示等待用户点击"提交答案"
                isUploaded: false,
                isError: false,
                progressShow: false,
                ossContent: null,
                uploadTime: null,
            });
            this.validatingFile = false;
            // 不再将uid推入this.uploadQueue
            // 不再调用this.tryProcessUploadQueue()
        },
        handleSubmitAnswer() {
            const pendingFiles = Object.values(this.uploadTask).filter(
                (task) => task.status === "waiting" && !task.isUploaded && !task.ossContent
            );

            if (pendingFiles.length === 0) {
                this.$message.info("没有待提交的文件。");
                return;
            }

            // 检查是否已有文件在上传队列或正在上传中
            if (this.currentlyUploadingCount > 0 || this.uploadQueue.length > 0) {
                this.$message.warning("当前有文件正在上传或等待上传，请稍后再提交新文件。");
                return;
            }

            this.isSubmitting = true; // 开始提交过程，设置按钮加载状态

            pendingFiles.forEach((task) => {
                if (!this.uploadQueue.includes(task.uid)) { // 避免重复添加
                    this.uploadQueue.push(task.uid);
                }
            });

            // 如果向队列中添加了文件，则开始处理队列
            if (pendingFiles.length > 0) {
                this.tryProcessUploadQueue();
            } else {
                // 如果没有文件实际被加入队列（理论上pendingFiles检查后不会到这里，除非逻辑错误），则重置状态
                this.isSubmitting = false;
            }
            // isSubmitting 将在 tryProcessUploadQueue 中所有任务完成后被重置为 false
        },
        tryProcessUploadQueue() {
            if (
                this.currentlyUploadingCount < (this.assessmentInfo.maxConcurrentUploads || 5) &&
                this.uploadQueue.length > 0
            ) {
                const uidToProcess = this.uploadQueue.shift();
                const task = this.uploadTask[uidToProcess];

                if (task && task.status === "waiting") {
                    this.currentlyUploadingCount++;

                    this.uploadSingleFile(uidToProcess)
                        .then((result) => {
                            console.log("Upload success for", uidToProcess, result);
                        })
                        .catch((error) => {
                            console.error("Upload failed for", uidToProcess, error);
                        })
                        .finally(() => {
                            this.currentlyUploadingCount--;
                            this.tryProcessUploadQueue();
                        });
                } else if (task) {
                    console.warn(`Task ${uidToProcess} is not in queued state (state: ${task.status}), skipping.`);
                    this.tryProcessUploadQueue();
                } else {
                    console.warn(`Task ${uidToProcess} not found in uploadTask, skipping.`);
                    this.tryProcessUploadQueue();
                }
            } else if (this.currentlyUploadingCount === 0 && this.uploadQueue.length === 0) {
                // 当没有文件正在上传且队列也为空时，意味着所有通过handleSubmitAnswer提交的任务已处理完毕
                this.isSubmitting = false; // 重置提交按钮的加载状态
            }
        },
        getTrainingTestInfoByTestId() {
            return new Promise((resolve, reject) => {
                service
                    .getTrainingTestInfoByTestId({
                        testID: this.testId,
                        trainingID: this.trainingId,
                    })
                    .then((res) => {
                        console.log(res, "res");
                        if (res.data.error_code === 0) {
                            this.testInfo = res.data.data;
                            // 更新页面显示数据
                            if (this.testInfo) {
                                this.title = this.testInfo.title || "未命名考试";
                                this.questionsCount = this.testInfo.questionCount || 0;

                                // 格式化截止时间
                                if (this.testInfo.deadline) {
                                    this.deadline = moment.unix(this.testInfo.deadline).format("YYYY-MM-DD");
                                    console.log(this.deadline, "this.deadline");
                                }
                                this.pagerInfo = this.testInfo.pagerInfo || [];
                            }
                            resolve(res.data.data);
                        } else {
                            this.$message.error(res.data.message || "获取考试详情失败");
                            reject(new Error(res.data.message || "获取考试详情失败"));
                        }
                    })
                    .catch((error) => {
                        console.error("getTrainingTestInfoByTestId error:", error);
                        this.$message.error("网络错误，获取考试详情失败");
                        reject(error);
                    });
            });
        },
        getTrainingTestAnswerHistory() {
            return service
                .getTrainingTestAnswerHistory({
                    testID: this.testId,
                })
                .then((res) => {
                    console.log(res, "res");
                    if (res.data.error_code === 0 && res.data.data && Array.isArray(res.data.data)) {
                        // 处理考试历史数据
                        this.examHistoryData = res.data.data.map((item) => {
                            return {
                                examTime: item.createdAt
                                    ? moment(item.createdAt).format("YYYY-MM-DD HH:mm:ss")
                                    : "未知时间",
                                answeredCount: item.answeredQuestionCount || 0,
                                examResult: item.isPassed ? "通过" : "不通过",
                            };
                        });

                        // 更新考试次数
                        this.updateExamDuration();
                    }
                    return res;
                })
                .catch((err) => {
                    console.log(err, "err");
                    return err;
                });
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";

.uploadTestOverview_container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f7f9fc;

    .custom_header {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        background-color: #fff;
        border-bottom: 1px solid #ebeef5;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
        position: relative;
        z-index: 1;

        .back_btn {
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #303133;
            font-size: 14px;
            transition: color 0.2s ease;

            i {
                margin-right: 5px;
                font-size: 16px;
            }

            &:hover {
                color: #409eff;
            }
        }
    }

    .custom_body {
        height: 100%;
        padding: 25px;
        overflow-y: auto;
        background-color: #f7f9fc;
    }
}

.top_info_section {
    display: flex;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    justify-content: center;
    padding-bottom: 60px;
    border-bottom: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .icon_area {
        margin-right: 25px;

        .placeholder_icon {
            width: 160px;
            height: 160px;
            border-radius: 50%;
            background-color: #ffd700;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 30px;
            color: #fff;
            i {
                font-size: 80px;
            }
        }
    }

    .details_area {
        display: flex;
        flex-direction: column;
        .detail_top {
            max-width: 700px;
            .detail_title {
                font-size: 22px;
                color: #303133;
                margin-bottom: 15px;
                font-weight: bold;
                margin-top: 15px;
            }
        }
        .detail_bottom {
            .deadline {
                font-size: 15px;
                color: #606266;
                background-color: #f8f9fb;
                padding: 5px 12px;
                border-radius: 4px;
                display: inline-block;
                width: 200px;
                text-align: center;
            }
        }
    }

    .upload_btn {
        margin-top: 22px;
        padding: 10px 22px;
        font-size: 15px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    ::v-deep .el-upload {
        width: 100%;
        text-align: center;

        .el-upload__tip {
            color: #909399;
            font-size: 14px;
            margin-top: 10px;
        }
    }
}

.content_section {
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border-bottom: none;

    h3 {
        margin-bottom: 18px;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
    }
}
</style>
