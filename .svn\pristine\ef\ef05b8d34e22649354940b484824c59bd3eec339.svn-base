<template>
  <div class="multicenter_status_count">
      <span>{{lang.multicenter_case_count}}：{{currentMulticenter.caseCount}}</span>
      <span>{{lang.multicenter_collected}}：{{multicenterStatusObj['2']+multicenterStatusObj['6']}}</span>
      <span>{{lang.exam_status['3']}}：{{multicenterStatusObj['3']}}</span>
      <span>{{lang.multicenter_collection_progress}}：{{getCaseProgress()}}%</span>
  </div>
</template>

<script>
import base from '../../../lib/base'
import multiCenterService from '../../../service/multiCenterService.js'
export default {
    name: 'statusCount',
    mixins:[base],
    components: {},
    props:{
    },
    data() {
        return {
            multicenterStatusObj:{
                1:0,
                2:0,
                3:0,
                6:0,
            }
        }
    },
    computed: {
        currentMulticenter(){
            return this.$store.state.multicenter.currentMulticenter||{}
        },
        currentConfig(){
            return this.$store.state.multicenter.currentConfig;
        },
    },
    mounted(){
        this.getMultiCenterStatus();
    },
    created() {
    },
    methods: {
        getMultiCenterStatus(){
            this.multicenterStatusObj={
                1:0,
                2:0,
                3:0,
                6:0,
            }
            multiCenterService.getMultiCenterStatus({
                mcID:this.currentMulticenter.id,
            }).then(res=>{
                if (res.data.error_code === 0) {
                    this.multicenterStatusObj = Object.assign(this.multicenterStatusObj,res.data.data);
                }
                console.log("[multiCenter status]", res.data)
            }).catch(err=> {
                console.log('[multiCenter status error]',err)
            })
        },
        getCaseProgress(){
            return ((this.multicenterStatusObj['2']+this.multicenterStatusObj['6'])/(this.currentMulticenter.caseCount||1)*100).toFixed(2);
        }
    },
}
</script>
<style lang="scss">
.multicenter_status_count{
    font-size: 16px;
    display: flex;
    margin: 20px 0;
    &>span{
        margin-right: 30px;
    }
}
</style>
