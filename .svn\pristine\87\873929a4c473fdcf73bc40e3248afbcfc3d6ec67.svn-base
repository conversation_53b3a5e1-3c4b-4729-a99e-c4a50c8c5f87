let EN = {
    "hospital_name_repeated": "Duplicate hospital name",
    "please_enter_threshold": "Please enter threshold",
    "auto_recognition_threshold_tips": "Please set corresponding thresholds according to the type of examination. After setting, the automatic recognition function will only display results above the threshold based on the corresponding examination type settings.",
    "auto_recognition_threshold": "Automatic recognition threshold",
    "topic_summary_no_score": "There are {a} sub questions in this category",
    "ob_right_ventricular_outflow_tract_view": "Right ventricular outflow tract section",
    "ob_right_ventricular_outflow_tract_view_group": "Right ventricular outflow tract section",
    "renal_long_axis_section_group": "Renal long axis section group",
    "right_kidney": "Right kidney",
    "left_kidney": "Left kidney",
    "spinal_sagittal_section_group": "Spinal sagittal section group",
    "ob_sacral_caudal_segment": "Sacral caudal segment",
    "ob_cervicothoracic_junction": "cervicothoracic junction",
    "angle_between_sound_beam_long_axis_femur": "The angle between the sound beam and the long axis of the femur is 60 ° -90 °",
    "short_axis_section_of_biventricular_myocardium": "Short axis section of biventricular myocardium",
    "ob_weekclear_IVS_display": "The display of the room interval is not clear enough",
    "ob_unclear_IVS_display": "The interval display is not clear",
    "ob_unclear_LV_display": "Unclear display of left ventricle",
    "ob_weekclear_LV_display": "Unclear display of left ventricle",
    "ob_weekclear_pulmonary_artery_and_ductus_arteriosus_display": "The pulmonary artery and ductus arteriosus are not clearly displayed",
    "ob_unkclear_pulmonary_artery_and_ductus_arteriosus_display": "Unclear display of pulmonary artery and ductus arteriosus",
    "ob_weekclear_TV_display": "The tricuspid valve display is not clear enough",
    "ob_unkclear_TV_display": "Unclear display of tricuspid valve",
    "ob_weekclear_main_pulmonary_artery_and_ductus_arteriosus_display": "Unclear display of main pulmonary artery and ductus arteriosus",
    "ob_unkclear_main_pulmonary_artery_and_ductus_arteriosus_display": "Unclear display of main pulmonary artery and ductus arteriosus",
    "ob_unkclear_RV_display": "Unclear display of right ventricle",
    "ob_weekclear_RV_display": "The display of the right ventricle is not clear enough",
    "ob_weekclear_IVC_display": "The inferior vena cava display is not clear enough",
    "ob_unclear_IVC_display": "Unclear display of inferior vena cava",
    "ob_weekclear_LA_display": "Unclear display of left atrium",
    "ob_unclear_LA_display": "Unclear display of left atrium",
    "ob_weekclear_RA_display": "The display of the right atrium is not clear enough",
    "ob_unclear_RA_display": "Unclear display of right atrium",
    "ob_weekclear_head_and_neck_arterial_branch_display": "Unclear display of arterial branches in the head and neck region",
    "ob_unclear_head_and_neck_arterial_branch_display": "Unclear display of arterial branches in the head and neck region",
    "ob_weekclear_ARCH_display": "Unclear display of aortic arch",
    "ob_unclear_ARCH_display": "Unclear display of aortic arch",
    "ob_unclearx_image_display": "The image display is not clear",
    "ob_weekclear_superior_vena_cava_display": "Unclear display of superior vena cava",
    "ob_unclear_superior_vena_cava_display": "Unclear display of superior vena cava",
    "ob_weekclear_DA_display": "Unclear display of descending aorta",
    "ob_unclear_DA_display": "Unclear display of descending aorta",
    "ob_weekclear_ASC_display": "Unclear display of ascending aorta",
    "ob_unclear_ASC_display": "Unclear display of ascending aorta",
    "ob_weekclear_brach_of_right_pulmonary_artery_display": "Unclear display of right pulmonary artery branch",
    "右肺动脉分支显示不清晰": "Unclear display of right pulmonary artery branch",
    "ob_weekclear_rama_arteria_pulmonar_izquierda_display": "Unclear display of left pulmonary artery branch",
    "ob_unclear_rama_arteria_pulmonar_izquierda_display": "Unclear display of left pulmonary artery branch",
    "ob_weekclear_image_display": "The image display is not clear enough",
    "ob_weekclear_gallbladder_display": "The gallbladder display is not clear enough",
    "ob_unclear_spine_display": "Unclear display of spine",
    "ob_unclear_liver_display": "Liver display unclear",
    "ob_unclear_image_display": "The image display is unclear",
    "ob_unclear_diaphragm_display": "Diaphragm unclear display",
    "ob_unclear_magenblase_display": "Unclear display of gastric vesicles",
    "ob_unclear_heart_display": "Heart display unclear",
    "ob_unclear_lung_display": "Unclear lung display",
    "head_and_neck_arterial_branch": "Branches of the head and neck arteries",
    "long_axis_section_aortic_arch_view": "Long axis section of aortic arch",
    "longitudinal_spine_conus_medullaris": "Longitudinal section of spine - conus medullaris",
    "brach_of_right_pulmonary_artery": "Branch of right pulmonary artery",
    "pulmonary_artery_bifurcation_view": "Pulmonary artery bifurcation section",
    "placental_umbilical_cord_entrance_umbilical_cord_entrance": "Placental Umbilical Cord Entrance Section - Umbilical Cord Entrance",
    "blood_flow_map_attachment_site_umbilical_blood_vessels": "Blood flow map at the attachment site of umbilical blood vessels",
    "umbilical_cord_placenta_attachment_site": "Sectional view of umbilical cord placenta attachment site",
    "cervical_internal_opening_cervix_placenta_attachment_site": "Cervical internal opening section - Pregnant woman's bladder",
    "cervical_internal_opening_cervix_fetal_head": "Cervical internal opening section - fetal head",
    "cervical_internal_opening_cervix_cervical_internal_mouth": "Cervical Internal Mouth Section - Cervical Internal Mouth",
    "cervical_internal_opening_cervix": "Cervical internal opening section - cervix",
    "cervical_internal_opening": "Cervical internal opening",
    "neck_of_uterus": "neck of uterus",
    "cervical_internal_opening_view": "Cervical internal opening section",
    "ob_plantar": "Plantar",
    "plantar_bilateral_view": "Plantar section (bilateral)",
    "character_7foot": "7-character foot",
    "longitudinally_cut_foot": "Longitudinally cut foot",
    "transverse_calf_foot_7foot_bilateral_view": "Transverse section of calf and foot (7-foot) (bilateral)",
    "visible_anthor_tibia_and_fibula": "Visible at the other end of the tibia and fibula",
    "visible_one_tibia_and_fibula": "Visible at one end of tibia and fibula",
    "long_axisvsection_tibia_fibula_bilateral_view": "Long axis section of tibia and fibula (bilateral)",
    "ob_femur": "femur",
    "duce_points_angle_between_sound_beam_long": "If the angle between the sound beam and the long axis of the femur is less than 45 °, additional points will be deducted",
    "visible_metaphysis_anthor_end_femur": "Visible at the metaphysis of the other end of the femur",
    "visible_metaphysis_one_end_femur": "Visible metaphysis at one end of femur",
    "femoral_long_axis_bilateral_view": "Femoral long axis section (bilateral)",
    "vertical_cutting_hand": "Vertical cutting hand",
    "forearm_hand_longitudinal_bilateral_view": "Forearm and Hand Longitudinal Section (Bilateral)",
    "crown_hand": "Crown Hand",
    "ob_palm": "palm",
    "coronary_hand_ulna_and_radius_bilateral_view": "Coronal Hand Section (Bilateral)",
    "ulna_and_radius": "ulna and radius",
    "visible_anthor_radius_and_ulna": "Visible at the other end of the radius and ulna",
    "visible_one_radius_and_ulna": "Visible at one end of radius and ulna",
    "long_axis_radius_ulna_bilateral": "Long axis section of radius and ulna (bilateral)",
    "long_bone_endpoints": "Long bone endpoints",
    "ob_humerus": "humerus",
    "duce_points_angle_between_sound_beam_long_axis_humerus": "If the angle between the sound beam and the long axis of the humerus is less than 45 °, additional points will be deducted",
    "angle_sound_beam_and_long_axis_humerus": "The angle between the sound beam and the long axis of the humerus is 60 ° -90 °",
    "tibia_and_fibula": "Tibia and fibula",
    "ob_ductus_arteriosus_arch_view": "Sectional view of ductus arteriosus arch",
    "pulmonary_artery_and_ductus_arteriosus": "Pulmonary artery and ductus arteriosus",
    "ob_upper_inferior_vena_cava_section": "Upper and inferior vena cava section",
    "short_axis_great_arteries_heart": "Short axis section of the great arteries in the heart",
    "visible_metaphysis_other_end_humerus": "Visible at the metaphysis of the other end of the humerus",
    "visible_metaphysis_one_end_humerus": "Visible metaphysis of one end of humerus",
    "long_axis_section_humerus_bilateral": "Long axis section of humerus (bilateral)",
    "longitudinal_pine_full_view": "Longitudinal section of spine - full length",
    "longitudinal_pine_mid_view": "Longitudinal section of spine - mid section",
    "conus_medullaris": "conus medullaris",
    "sacral_vertebra_point": "Sacral vertebra point",
    "caudal_vertebrae": "caudal vertebrae",
    "sacral_tail": "Sacral tail",
    "longitudinal_spine_coccygeal_segment": "Longitudinal section of spine - coccygeal segment",
    "ob_skin": "skin",
    "cervical_vertebra": "cervical vertebra",
    "skin_contour": "Skin contour",
    "vertebral_body": "Vertebral body",
    "vertebral_arch": "vertebral arch",
    "longitudinal_spine_cervical_thoracic_segment": "Longitudinal section of spine - cervical thoracic segment",
    "ob_coronary_kidney_view": "Coronary section of kidney",
    "ob_sagittal_section_right_kidney": "Sagittal section of right kidney",
    "ob_sagittal_section_left_kidney": "Sagittal section of left kidney",
    "bilateral_renal_pelvis": "Bilateral renal pelvis",
    "bilateral_kidneys": "Bilateral kidneys",
    "horizontal_cross_section_both_kidneys": "Horizontal cross-section of both kidneys",
    "bladder_double_umbilical_artery_section_umbilical_artery": "Bladder double umbilical artery section - umbilical artery",
    "dual_umbilical_artery_section_bladder_bladder": "Dual umbilical artery section of bladder - bladder",
    "intersection_of_umbilical_wheel": "Intersection of umbilical wheel",
    "ob_ilateral_umbilical_artery_blood_flow": "Bilateral umbilical artery blood flow",
    "ob_bladder": "bladder",
    "horizontal_bladder_and_bilateral_umbilical_artery_blood_flow_map": "Horizontal section of bladder and bilateral umbilical artery blood flow map",
    "ob_pinal_cone": "Spinal cone",
    "ob_sectional_view_of_umbilical_cord_abdominal_wall_entrance": "Sectional view of umbilical cord abdominal wall entrance",
    "ob_gallbladder_view": "Gallbladder section",
    "extra_deduction_for_kidney_damage": "Extra deduction for kidney damage",
    "ob_one_rib_on_each_side": "One rib on each side",
    "ob_complete_abdominal_wall": "Complete abdominal wall",
    "descending_aorta_and_inferior_vena_cava": "Descending aorta, inferior vena cava",
    "coronary_section_diaphragm_view": "Coronary section of diaphragm",
    "sagittal_section_right_diaphragm_view": "Sagittal section of right diaphragm",
    "ob_diaphragm": "diaphragm",
    "sagittal_section_left_diaphragm": "Sagittal section of left diaphragm",
    "ob_trachea": "trachea",
    "confluence_of_aortic_arch_ductus_arteriosus": "Confluence of aortic arch and ductus arteriosus (V-shaped structure)",
    "ob_three_vessel_tracheal_view": "Three vessel tracheal section",
    "rama_de_la_arteria_pulmonar_izquierda": "Branch of left pulmonary artery",
    "main_pulmonary_artery_and_ductus_arteriosus": "Main pulmonary artery and ductus arteriosus",
    "ob_three_vessel_view": "Three vessel section",
    "main_pulmonary_artery": "Main pulmonary artery",
    "obcleft_ventricular_outflow_tract_section": "Left ventricular outflow tract section",
    "ob_ight_ventricular_outflow_tract_view_2": "Right ventricular outflow tract section 2",
    "ob_ight_ventricular_outflow_tract_view_1": "Right ventricular outflow tract section 1",
    "superior_vena_cava": "superior vena cava",
    "ductus_arteriosus": "ductus arteriosus",
    "main_pulmonary_artery_continuous_with_right_ventricle": "The main pulmonary artery is continuous with the right ventricle",
    "inferior_vena_cava_aorta": "Inferior vena cava aorta",
    "multiple_ribs": "Multiple ribs",
    "single_rib": "Single rib",
    "renal_pelvis": "renal pelvis",
    "ob_kidney": "kidney",
    "umbilical_cord_abdominal_wall_entrance": "Umbilical cord abdominal wall entrance",
    "umbilical_vein": "Umbilical vein",
    "ob_magenblase": "magenblase",
    "ob_bdominal_circumference_view": "Abdominal circumference section",
    "ob_continuous_ascending_aorta_and_interventricular_septum": "Continuous ascending aorta and interventricular septum",
    "ob_left_ventricular_outflow_tract": "left ventricular outflow tract",
    "ob_spine": "spine",
    "ob_lungs": "lungs",
    "ob_four_chamber_view": "Four chamber view",
    "ob_extra_deduction_points_left_ventricular_outflow_tract": "Extra deduction of points for left ventricular outflow tract",
    "ob_bilateral_lungs": "Bilateral lungs",
    "ob_pinal_vertebrae": "Spinal vertebrae",
    "ob_at_least_one_pulmonary_vein_enters_the_left_atrium": "At least one pulmonary vein enters the left atrium",
    "ob_cross_shaped_endocardial_cushion": "Cross shaped endocardial cushion",
    "ob_foramen_ovale_valve": "Foramen ovale valve",
    "ob_four_chambers_of_the_heart": "The four chambers of the heart",
    "ob_mandibular_bone_sagittal_plane": "Mandibular bone (sagittal plane)",
    "ob_maxillary_bone_sagittal_plane": "Maxillary bone (sagittal plane)",
    "ob_lower_jaw_sagittal_plane": "Lower jaw (sagittal plane)",
    "ob_lower_lip_sagittal_plane": "Lower lip (sagittal plane)",
    "ob_upper_lip_sagittal_plane": "Upper lip (sagittal plane)",
    "ob_frontal_bone_sagittal_plane": "Frontal bone (sagittal plane)",
    "ob_nasal_bone_sagittal_plane": "Nasal bone (sagittal plane)",
    "ob_nose_tip_sagittal_plane": "Nose tip (sagittal plane)",
    "median_sagittal_plane_of_face_view": "Median sagittal plane of the face",
    "ob_mandible": "mandible",
    "ob_chin": "chin",
    "ob_maxilla": "maxilla",
    "tip_of_the_nose": "Ttip of the nose",
    "frontal_bone": "frontal bone",
    "ob_lens": "lens",
    "ob_nasal_bone_and_maxillary_frontal_process": "Nasal bone and maxillary frontal process",
    "eye_frame": "Eye frame",
    "ob_cross_section_of_binocular_sphere_view": "Cross section of binocular sphere",
    "nasal_bone": "nasal bone",
    "bilateral_crystalline_lenses": "Bilateral crystalline lenses",
    "ob_bilateral_eye_sockets": "Bilateral eye sockets",
    "ob_jaw": "jaw",
    "lower_lip": "Lower lip",
    "ob_coronal_section_nose_and_lip_view": "Coronal section of nose and lip",
    "upper_lip": "upper lip",
    "ob_bilateral_nostrils": "Bilateral nostrils",
    "ob_posterior_cranial_fossa_pool": "Posterior cranial fossa pool",
    "cerebellum": "cerebellum",
    "ob_transverse_section_of_cerebellum_view": "Transverse section of cerebellum",
    "ob_cisterna_magna": "cisterna magna ",
    "ob_verebellar_hemisphere": "Cerebellar hemisphere",
    "ob_vermis_of_cerebellum": "Vermis of cerebellum",
    "ob_ransverse_section_through_lateral_ventricle_view": "Transverse section through the lateral ventricle",
    "ob_educed_points_with_for_brain_foot": "Extra deduction for brain foot appearance",
    "ob_anterior_horn_of_lateral_ventricle": "Anterior horn of lateral ventricle",
    "ob_posterior_horn_of_lateral_ventricle": "Posterior horn of lateral ventricle",
    "ob_choroid_plexus": "Choroid plexus",
    "ob_transverse_section_through_thalamus_view": "Transverse section through thalamus",
    "ob_educed_points_with_cerebellum": "Extra deduction of points for cerebellum occurrence",
    "ob_image_quality_and_detail_desc": "Image quality (clear, appropriate depth, centered structure)",
    "ob_brain_middle": "brain middle",
    "ob_skull_ring": "Skull ring",
    "ob_cerebral_peduncle": "cerebral peduncle",
    "ob_thalamus": "thalamus",
    "ob_sylvian_fissure": "sylvian fissure",
    "ob_transparent_compartment": "Transparent compartment",
    "statistic": {
        "user_online_avg": "Average online duration of users",
        "answer_sheet_report": "Homework and Assessment",
        "answer_sheet_avg": "Average value of homework and assessment",
        "device_report": "Equipment Statistics Report",
        "content_report": "Content Statistics Report",
        "device_overview": "Overview of Basic Information",
        "large_billboard": "Large screen billboard",
        "statistic_report": "Statistical report",
        "statistic_data_title": "Content statistics data",
        "maintenance_range": {
            "0": "Not maintained",
            "1": "1 month",
            "2": "2 months",
            "3": "3 months",
            "6": "6 months",
            "15": "0.5 months"
        },
        "deviceDetection": "Equipment testing/maintenance time",
        "QCStatistics": "Quality Control Statistics",
        "dr_distribution": "Distribution of DR examination sites",
        "adult": "Adult",
        "child": "Children",
        "dr_exam_types": {
            "P07": "Legs",
            "P06": "Pelvis",
            "P05": "Abdomen",
            "P04": "Chest",
            "P03": "Upper limb",
            "P02": "Spine",
            "P01": "Head"
        },
        "technician": "Technician statistics",
        "quality_images": "Total number of quality control images",
        "BI_DR_title": "Remote DR Data Center",
        "exam_create_image": "Number of images created",
        "exam_create_video": "Number of videos created",
        "device_search": "Equipment inquiry",
        "active_level": "Active level",
        "deviceFailureTip": "Equipment prompt",
        "examCountOfDevice": "Create Inspection Count (device)",
        "byDevice": "By device",
        "video_count": "Total number of videos",
        "statistic_device_new_exam": "Create inspection report",
        "byUploader": "By uploader",
        "byExam": "By exams",
        "unbind": "Unbind",
        "doppler_user_report": "U-Link New User Count Statistics Report",
        "table_attendee_distribution": "Distribution of Participants",
        "statistic_paper": "Homework and Assessment Statistics Report",
        "library_accumulated_views": "Accumulated views",
        "region_txt": "Region",
        "branch_txt": "branch office",
        "correction_teacher": "Correction teacher",
        "assignment_name": "Assignment Name",
        "answer_sheet_report_title": "Homework and Assessment Statistics Report",
        "answerSheetChartTitle": "Distribution of the number of people submitting assignments",
        "probeUsageNumber": "Number of probes used",
        "owner_departments": "Department",
        "data_detail_export": "Export Details",
        "time_range_tip_month": "Please select less than one month",
        "probe": {
            "others": "others",
            "S": "Single Crystal",
            "P": "Phased",
            "L": "Linear",
            "C": "Convex"
        },
        "probeUsageRate": "Probe usage percentage (%)",
        "min_tip": "minimum value",
        "max_tip": "Maximum value",
        "not_support_map": "The map is currently not supported",
        "total": "Total",
        "summary_user_count": "Number of users",
        "doppler_offline_number": "off-line",
        "doppler_standby_number": "Standby",
        "doppler_working_number": "At work",
        "device_install_time_chart": "Factory time statistics (unit)",
        "all_exam_types": {
            "0": "OB",
            "1": "GYN",
            "2": "CARD",
            "3": "VAS",
            "4": "ABD",
            "5": "URO",
            "6": "SMP",
            "7": "PED",
            "8": "Breast",
            "9": "unknown",
            "10": "Undefined",
            "11": "EM",
            "12": "Nerve"
        },
        "summary_exam_count_ingroup": "Number of checks (group)",
        "table_exam_type_count": "Number of exam types",
        "table_total_working_time": "working hours",
        "table_total_startup_time": "Power on time",
        "group_joined_count": "Number of group joined",
        "equipment_ownership": "Equipment ownership",
        "using_departments": "Using departments",
        "exam_type_count": "Number of inspection types",
        "year_range": {
            "0": "unknown",
            "1": "<1 year",
            "2": "1-5 years",
            "3": "6-10 years",
            "4": ">10 years"
        },
        "exam_create": "Create Exam Count",
        "device_exam_report": "Device Inspection Report",
        "BI_device_subtitle": "Device Kanban",
        "chart_legend_other": "other",
        "chart_legend_group": "group ",
        "summary_org_count": "Number of institutions",
        "summary_image_count": "Number of images",
        "summary_exam_count": "Inspection quantity",
        "statistic_device": "Device statistics",
        "statistic_group": "data statistics",
        "examCountOfOrganization": "Create Inspection Count (Institution)",
        "deviceStatus": "Device status",
        "deviceUtilizationRate": "Device utilization rate(%)",
        "deviceInstallTime": "Device installation time",
        "BI_title_dr_demo": "Demonstration - Regional DR Imaging Center",
        "image_count": "Total number of images",
        "minute_text": "minute",
        "sex_map": {
            "0": "male",
            "1": "female",
            "2": "unknown"
        },
        "table_library_like": "Like count",
        "table_library_view": "View volume",
        "table_library_title": "Title",
        "table_iworks_finish_count": "Number of completed sections",
        "table_iworks_standard_count": "Number of iWorks sections",
        "table_patient_sex": "Gender",
        "table_patient_name": "Patient Name",
        "table_iworks_name": "iWorks Protocol Name",
        "table_send_ts": "Sending time",
        "table_exam_id": "exam ID",
        "group_types_map": {
            "0": "private",
            "1": "public"
        },
        "increased_group_total": "Accumulated groups",
        "increased_group_avg": "Average value of newly added groups",
        "table_group_create_ts": "Creation time",
        "table_group_admin": "Group owner",
        "table_group_creator": "Creator",
        "table_group_type": "Group Type ",
        "user_online_duration_report": "User online duration report",
        "user_online_duration": "User online duration",
        "doppler_user_total": "u-Link cumulative number of users",
        "doppler_user_avg": "Average number of new users added to u-Link",
        "table_first_login_ulink": "First login time using ulink",
        "device_active_number": "Number of active ultrasound devices",
        "device_utilization_rate_report": "Ultrasound device utilization report",
        "device_utilization_rate": "Utilization rate of ultrasound device",
        "device_status_report": "Ultrasound device status report",
        "device_status": "Ultrasound device status",
        "device_status_map": {
            "0": "in use",
            "1": "off-line",
            "2": "Standby",
            "3": "in use"
        },
        "table_comment_count": "Number of comments",
        "table_tag_count": "Number of tags",
        "table_exam_type": "exam type",
        "table_exam_date": "exam date",
        "table_birthday": "Date of Birth",
        "table_offline_time": "Offline time",
        "table_standby_duration": "Continuous standby time (minutes)",
        "table_last_standby_time": "Recent standby time",
        "table_online_duration": "Continuous online duration (minutes)",
        "table_last_startup_time": "Recent startup time",
        "table_current_status": "current state",
        "device_install_time_report": "Ultrasound device installation time report",
        "doppler_number": "Number of ultrasound devices",
        "iworks_statistic_report": "iWorks Protocol Statistics Report",
        "doppler_status": "Device status",
        "doppler_utilization_rate": "Equipment utilization rate",
        "doppler_install_time": "Equipment installation time statistics",
        "doppler_user": "Number of new users added to u-Link",
        "library_statistic_report": "Library Statistical Reports",
        "iworksCompliance": "iWorks Compliance Rate",
        "search_user_name": "Please enter the complete account",
        "group_active_avg": "Average of active groups",
        "table_video_count": "Number of videos sent",
        "other_exam_type": "other",
        "device_install_time": "Installation time of ultrasound equipment",
        "table_install_ts": "Installation time",
        "table_product_type": "product type",
        "distance_training_number": "Number of online training",
        "remote_consultation_number": "Number of remote consultations",
        "device_increased_report": "Add device report",
        "total_device": "Accumulated devices",
        "device_increased_avg": "Average value of newly added devices",
        "device_increased": "New device added",
        "devices_report": "Device Statistics Report",
        "device_types": {
            "doppler": "ultrasound equipment",
            "ulinker": "u-Linker",
            "isync": "ISync workstation"
        },
        "device_type_title": "Device Type",
        "breastAI_usage_report": "Dr.M usage report",
        "breastAI_usage": "Dr.M usage",
        "user_types": {
            "0": "Trial users",
            "1": "Official users",
            "2": "Account application rejected",
            "3": "Account disabled",
            "4": "Logged out"
        },
        "time_txt": "Time",
        "total_user": "Accumulated users",
        "user_active_avg": "Average active users",
        "user_increased_avg": "Average value of new users",
        "time_range_tip": "Please select a time range less than one year",
        "table_product_name": "product name",
        "table_series_number": "Series number",
        "table_device_id": "Device ID",
        "table_last_login_account": "Recently logged in account",
        "table_hospital": "Hospital",
        "table_device_name": "Name",
        "table_mac_addr": "MAC Address",
        "table_exam_count": "Number of exams sent",
        "table_last_login_time": "Recent login time",
        "table_login_times": "Number of logins",
        "table_online_time": "Online duration (minutes)",
        "table_last_send_time": "Recent image release time",
        "table_fail_count": "Number of failures",
        "table_success_count": "Success count\n",
        "table_image_count": "Number of images sent",
        "table_upload_account": "Uploader account",
        "table_city": "city",
        "table_province": "Province",
        "table_organization": "Organization",
        "table_email": "Email",
        "table_user_type": "customer type",
        "table_register_time": "Registration time",
        "table_nickname": "User name",
        "table_account": "Account",
        "table_review_duration": "Review duration (minutes)",
        "table_review_times": "Review frequency",
        "table_live_duration": "Live broadcast duration (minutes)",
        "table_attendee_count": "Number of participants",
        "table_live_end": "Live broadcast end time",
        "table_live_start": "Live start time",
        "table_live_type": "Live streaming type",
        "table_host": "Host",
        "table_group_subject": "Group name",
        "table_group_id": "Group ID",
        "table_subject": "Conference subject",
        "table_index": "Index",
        "data_export": "Data export",
        "data_list": "Data list",
        "distance_training": "Online training",
        "remote_consultation": "remote consultation",
        "live_tip2": "Live broadcast type=online training, or there is no mainstream device in the live broadcast (and the duration of mainstream device is less than 5 minutes)",
        "live_tip1": "Live streaming type=remote consultation, or mainstream equipment during live streaming (duration exceeding 5 minutes)",
        "live_attendee_count": "Live streaming participants",
        "live_duration": "Live Stream Duration (mins)",
        "live_times": "Number of Live Streaming",
        "search_btn": "Search",
        "time_map": {
            "1M": "Last 1 months\n",
            "TY": "This year",
            "1Y": "Past one year",
            "6M": "Last 6 months",
            "3M": "Last 3 months\n"
        },
        "weeks": {
            "0": "Sunday",
            "1": "Monday",
            "2": "Tuesday",
            "3": "Wednesday",
            "4": "Thursday",
            "5": "Friday",
            "6": "Saturday"
        },
        "ulinkerIncreasedUsers": "The added number of u-Link users",
        "end_date": "end date",
        "start_date": "start date",
        "theme": "Theme",
        "online_statistic": "Online duration statistics",
        "library_statistic": "library statistics",
        "iworks_statistic": "iWorks Protocol statistics",
        "exam_increased_report": "Report of New Exams",
        "device_manage": "Device Management",
        "increased_group_report": "Report Of New Groups\n",
        "group_active_report": "Active Group Report",
        "user_active_report": "Active User Report",
        "user_increased_report": "Report of New Users",
        "live_report": "Reports of Real-Time Consultation\n",
        "exam_count": "exam total",
        "exam_distribution": "Overall distribution of ultrasound examination",
        "back_btn": "Back",
        "device_distribution": "devices distribution",
        "cancel_btn": "Cancel",
        "confirm_btn": "Confirm",
        "device_related": "devices related",
        "iworks_protocol": "iWorks Protocol",
        "group_related": "Group/Groupset related",
        "content_related": "Content related",
        "user_related": "User related",
        "selected": "Selected",
        "months": {
            "1": "January",
            "2": "February",
            "3": "March",
            "4": "April",
            "5": "May",
            "6": "June",
            "7": "July",
            "8": "August",
            "9": "September",
            "10": "October",
            "11": "November",
            "12": "December"
        },
        "show_type_map": {
            "0": "Curve",
            "1": "Histogram"
        },
        "show_type": "Display type",
        "search_group_name": "Enter the group name.",
        "statistic_time": "statistical time",
        "BI_setting_title": "BI Statistics Dashboard Configuration",
        "iworksUseTimes": "No. of iWorks exams",
        "iworksUserCount": "No. of iWorks users",
        "library": "Library views",
        "breastAI": "Dr.M",
        "videoIncreased": "Number of New Exam Cines",
        "imageIncreased": "Number of New Exam Images",
        "examIncreased": "Number of New Exams",
        "dopplerTotal": "Total number of Ultrasound Devices (u-Link)",
        "dopplerIncreased": "Number of new ultrasound devices added",
        "ulinkerTotal": "Total number of u-Linker Devices\n",
        "ulinkerIncreased": "The added number of u-Link devices",
        "iSyncTotal": "Total number of iSync Devices\n",
        "iSyncIncreased": "The added number of iSync users",
        "liveDuration": "Live Stream Duration (mins)",
        "liveUserTimes": "Number of participants in remote consultation and training",
        "liveCount": "No. of live streams",
        "groupTotal": "Total groups",
        "groupIncreased": "Number Of New Groups",
        "groupActive": "Number of Active Groups",
        "userActive": "Active users",
        "userTotal": "Total number of users",
        "userIncreased": "Number of New Users",
        "BI_subtitle": "data board",
        "BI_title_demo": "Demo - Remote Ultrasound Medical Center",
        "BI_title": "Remote ultrasound data center"
    },
    "data_too_old_to_locate": "The data is too old to locate the original message",
    "unable_locate_original_message": "Unable to locate the original message",
    "quoted_content_has_been_withdrawn": "The quoted content has been withdrawn",
    "quoted_content_has_been_deleted": "The quoted content has been deleted",
    "group_has_joined": "You have already joined this group",
    "scan_qr_code": "Identify the QR code in the image",
    "locate_original_message": "Locate to the original position",
    "share_to_conversation": "Share to conversation",
    "download_to_local": "Download to local",
    "share_qrcode_to": "Share group QR code to",
    "quote_title": "quote",
    "temporarily_store": "temporarily store",
    "total_score": "Total score",
    "improvement_suggestions": "Improvement suggestions",
    "analysis_result": "analysis result",
    "comprehensive_evaluation": "Comprehensive evaluation",
    "ulinker_settings": "Video source settings",
    "AI_analysis_results": "AI analysis results",
    "answer_not_submitted_next_tips": "The answer to the current question has not been submitted. Do you want to switch to the next question?",
    "re_answer_cleared_tips": "Are you sure you want to answer again? The current answer will be cleared.",
    "answer_not_submitted_leave_tips": "The answer to the current question has not been submitted. Are you sure you want to leave?",
    "evaluating": "Evaluating",
    "please_wait_ai_analysis": "In AI analysis, please wait",
    "ai_analysis_encountered_retry_tips": "AI analysis encountered an exception, please try again later",
    "answer_automatically_review_tips": "Your answer has been successfully submitted. Please wait for the system to automatically review it, or click on the next question to continue practicing (continuing practice does not affect the review result of this question, and you can check the analysis result from the answer history later)",
    "please_answer_here": "Please answer here",
    "used_time": "TIME",
    "other_answers": "Other answers",
    "best_answer": "Best answer",
    "my_answer": "My answer",
    "statistical_analysis": "statistical analysis",
    "answering_requirements": "Answering requirements",
    "video_materials": "Video materials",
    "completed_total": "completed",
    "answer_record": "Answer record",
    "professional_imaging_knowledge_tips": "Professional imaging knowledge, fast and accurate response",
    "you_could_say_tips": "You could say it as this",
    "delete_apply_multicenter_tips": "Do you want to delete this multi center application",
    "overdue_assignment_transmit_tip": "The current assignment has expired and cannot be forwarded",
    "password_rule_incorrect": "The password rule is incorrect",
    "PacsServerError": "The PACS service is unavailable",
    "uploading": "Uploading",
    "upload_success": "Upload successful",
    "exam_overdue_creator_tip": "The current assignment has expired. Do you want to modify the assignment configuration?",
    "student_org": "Student organizations",
    "single_chat": "Single Chat",
    "not_group_chat": "None",
    "table_drag_to_reorder": "The table supports dragging and dropping to change the order",
    "edit_collection_group_options_tips": "When a group is set, it is necessary to configure the collection requirements for the set group at the same time, otherwise the group is invalid.",
    "unsaved_item": "Unsaved item",
    "image_label": "Image label",
    "homework": {
        "upload": {
            "error": {
                "noOptions": "The {type} must contain at least one option",
                "invalidScore": "The score for {type} must be greater than 0",
                "fieldRequired": "The {field} cannot be empty",
                "maxColumns": "{sheetName} exceeds maximum columns limit",
                "maxRows": "{sheetName} exceeds maximum rows limit",
                "general": "Upload failed",
                "fileFormat": "Please upload an Excel file (.xlsx or .xls)",
                "fileSize": "File size cannot exceed 2MB"
            },
            "download_excel_template": "Download Template",
            "upload_excel": "Upload Excel File"
        },
        "detail_requirements": "Details",
        "exam_not_exist": "Exam does not exist",
        "revoke": "Revoke",
        "revoke_exam_confirm": "After invalidation, all personnel will be unable to open or submit this assignment. Please confirm?",
        "operation_min_subtopic": "Operation question must keep at least one sub-question",
        "progress": {
            "legend": {
                "graded": "Graded",
                "ungraded": "Ungraded",
                "current": "Current",
                "alreadyDone": "Already done",
                "notDone": "Not done"
            },
            "completionStatus": "Completed {done} of {total} questions",
            "correctionHeader": "Correction progress",
            "answeringHeader": "Answering progress"
        },
        "confirm_delete_topic": "Are you sure to delete this question?",
        "delete_paper": "Delete homework",
        "delete_failed ": "Delete failed",
        "delete_success": "Delete successfully",
        "confirm_delete_paper": "Are you sure to delete this exam paper?",
        "save_failed": "Save failed",
        "save_success": "Saved successfully",
        "topic_answer_required": "Please provide reference answers for each question",
        "topic_score_required": "Please set a score for each question",
        "exam_title_required": "Please enter the title of the exam",
        "reference_answer": "Reference answer",
        "exam_title_tip": "Test name",
        "upload_requirements": {
            "item5": "Please do not exceed 20 columns in the file",
            "item4": "Please do not upload more than 10000 rows of data at a time",
            "item3": "The file size should not exceed 2MB",
            "item2": "Do not place data in merged cells (note: multiple options for multiple-choice questions can be in the same cell)",
            "item1": "The extension name is xls or xlsx"
        },
        "preview_exam_content": "Preview the content of the test questions"
    },
    "welcome_to_realm_imaging_intelligence": "Welcome to the realm of medical imaging intelligence!",
    "leave_ai": "Leave AI",
    "expand_menu": "Expand Menu",
    "empty_title": "Empty title",
    "empty_nodes": "Empty nodes",
    "empty_group_options": "Empty group options",
    "collection_requirements": "Collection requirements",
    "integrity_verified_submit_cases": "Is integrity verified when submitting cases",
    "basic_configuration": "Basic configuration",
    "delete_apply_create_multicenter": "Do you want to delete this application record for creating multiple centers",
    "error": {
        "paperAssignmentNoAuth": "The task already exists in the target group. Please contact the group administrator to change the job configuration,",
        "assignmentNotBelongToUser": "You do not have permission to access this assignment as it does not belong to your account.",
        "paperAssignmentHasBeenRevoked": "This assignment has been cancelled",
        "paperAnswerSheetNotExist": "The homework has expired",
        "paperAnswerSheetLocked": "The homework is being corrected by others",
        "paperAssignmentDuplication": "This group has already assigned this assignment, please do not assign it again",
        "paperSubmitDueTimeError": "The current assignment has exceeded the deadline and cannot be submitted",
        "codeAfsError": "There is a security risk on the current computer, please try again later",
        "patientExternalSame": "Multiple center patient ID duplication",
        "timestampError": "Your current local time is not calibrated",
        "userIsLiving": "Added live stream on other devices",
        "liveStatusError": "The live stream has started or ended and cannot be operated",
        "livingCreating": "Live stream preparing, please try again later",
        "userNeedSafeAuthToken": "This operation requires authentication",
        "userCanNotBindAccount": "There is no need to bind mobile phone or email. Please login and bind again",
        "codeNotSupportSelfNet": "The intranet does not support the verification code function, please contact the administrator",
        "paramsError": "Parameter error",
        "userEmailInvalid": "Invalid email",
        "codeMustRequired": "Login expired, please log in again",
        "userLoginNameInvalidate": "The account name can only contain letters, numbers, and underscores, and cannot be all numbers.",
        "userPasswordLockError": "Enter the wrong password more than 5 times in a row, and the account has been locked. Please try again in five minutes",
        "userReferralCodeError": "Referral code error",
        "userEmailError": "Illegal email address",
        "userMobileError": "Illegal phone number",
        "userPwdEnhanceError": "The password security level is not enough. It must contain both uppercase and lowercase letters, numbers and special characters",
        "userLoginNameRegisted": "The account has been registered.",
        "userMobileRegisted": "The mobile phone has been registered.",
        "userEmailHasRegisted": "The email has been registered.",
        "codeError": "Verification code error",
        "codeSendError": "Failed to send verification code",
        "codeCountLimitError": "Verification code sent today has exceeded the limit",
        "codeTimerLimitError": "The verification code has been sent within 1 minute. Please do not send it again",
        "imageCodeError": "Graphic verification code error",
        "LimitMobileLogin": "Limited by company security mechanism, this function is not available in company intranet.",
        "userWaitVerification": "The account is waiting for the administrator to review",
        "userStatusFail": "The account status is locked, please enter contact the administrator",
        "userPasswordError": "The account or password is incorrect",
        "userLoginNameError": "Account does not exist",
        "operateFrequenceError": "The operation is too frequent, please try again later",
        "userLoginBusy": "Login busy",
        "deviceInfoNotFound": "Device info not found",
        "deviceNotFound": "Device not found",
        "multiCenterExamStatusError": "The current status does not support this operation",
        "multiCenterHadCollector": "There can only be one purchaser of this multi-center project",
        "multiCenterLackReviewer": "There are less than 2 reviewers and cannot be assigned automatically"
    },
    "standardization_rate": "Standardization rate",
    "integrity_rate": "integrity rate",
    "jump_external_links_tips": "We are about to redirect to the browser to access external links. Please pay attention to risk identification",
    "switch_app_client_to_workstation_tip": "To select a workstation, you need to apply for an authorization code and install the acquisition card driver program. Please choose carefully",
    "share_paper_to": "Share the test questions with:",
    "average_duration": "Average duration",
    "cardiovascular": "cardiovascular",
    "human_body_diagram": "Human body diagram",
    "welcome_clinical_thinking_exercise_tips": "Welcome to the ultrasound clinical thinking exercise. You can click on the random exercise or select any part below to start your practice journey.",
    "random_exercise": "Random exercise",
    "total_completion_count": "Total count",
    "re_answer": "Re answer",
    "recent_submission_time": "Recent submission time",
    "completion_times": "completion times",
    "topic_types": "Topic type",
    "exercises_my_completed_title": "The exercises I have completed",
    "clinical_thinking_practice_title": "AI Practice",
    "ai_welcome_tips": "I am DR AI. Nice to meet you!",
    "answering": "In response",
    "thinking": "Thinking",
    "historical_records": "historical records",
    "screen_shot": "screenshot",
    "sorry_an_error_occurred_please_try_again": "Sorry, an error occurred, please try again.",
    "server_request_exception_tips": "Server request exception, please try again later",
    "user_has_terminated_answering": "The user has terminated answering",
    "input_talk_about_tips": "Let's talk about something",
    "input_talk_about_tips_Shift_Enter": "Professional imaging knowledge, fast and accurate response (Shift+Enter=line break)",
    "new_chat": "New Chat",
    "deep_thinking": "DeepThink",
    "ai_chat": "AI chat",
    "select_video_window_to_be_recorded": "Please select the video window that needs to be recorded",
    "detailed_mode": "Detailed Mode",
    "simplified_mode": "Simplified mode",
    "mode_select": "Mode selection",
    "edit_collection_group": "Edit Group",
    "collection_group": "Group",
    "collection_category": "Collection category",
    "please_select_group_title": "Please select a group",
    "add_collection_group": "Add custom group",
    "formatDateTime": {
        "yyyy-MM-dd HH:mm:ss": "Year-Month-Day-Hour-Minute-Second",
        "yyyy-MM-dd HH:mm": "Year-Month-Day-Hour-Minute",
        "yyyy-MM-dd HH": "Year-Month-Day-Hour",
        "yyyy-MM-dd": "Year-Month-Day",
        "yyyy-MM": "Year-Month",
        "yyyy": "Year"
    },
    "please_fill_field_name_tips": "Please fill in the field name",
    "number_options_greater_2": "The number of options must be greater than 2",
    "empty_element_title_tips": "There is currently an empty field name. Please improve the field or delete it",
    "new_options_default_selection": "The selected option will be the default selection, and if not selected, there will be no default selection.",
    "not_required_filed": "Not required",
    "required_filed": "Required",
    "option_name": "Option Name",
    "edit_options": "Edit Options",
    "date_time": "Date time",
    "check_box": "Checkbox",
    "radio": "Radio",
    "supply_exam_image": {
        "err_tip": {
            "select_up_to_number_files": "Select up to {number} files.",
            "unsupported_image_format": "Format of the image is unsupported, only support JPG!",
            "many_tasks_have_failed_tips": " tasks have failed. Please try again later",
            "select_up_to_500_files": "Select up to 500 files.",
            "not_exam_sender": "You are not the sender of this exam , cannot import image to this exam!",
            "import_exam_image_succ": "Import image successfully!",
            "import_exam_image_failed": "Failed to import file!",
            "upload_file_failed": "Failed to upload file!",
            "invalid_exam_id": "Invalid ID of the exam!",
            "invalid_exam_path": "Invalid path of the exam!",
            "unsupported_file_format": "Format of the file is unsupported",
            "more_than_one_file": "Only one file can be imported at a time!",
            "no_files": "Please select at least one file!"
        }
    },
    "only_training_tips": "(only for training and teaching purposes, do not use for diagnosis)",
    "dopplerControl": {
        "Disconnect": "Disconnect",
        "UPDATE": "Update",
        "SAVEIMG": "Save",
        "CALIPER": "Caliper",
        "MEASURE": "Measure",
        "PWMode": "PW Mode",
        "MMode": "M Mode",
        "BMode": "B Mode",
        "CMode": "C Mode",
        "ImageQuality": "Image Quality",
        "Freeze": "Freeze",
        "DuplexOrTriplex": "Duplex/Triplex",
        "Tint Map": "Tint Map",
        "Quick Angle": "Quick Angle",
        "SV": "SV",
        "Angle": "Angle",
        "Unfreeze": "Unfreeze",
        "SwitchMode": "SwitchMode",
        "FlowOnly": "FlowOnly",
        "StereoFlowSwitch": "Glazing Flow",
        "WF": "WF",
        "GlazingFlow": "GlazingFlow",
        "Packer Size": "Packet Size",
        "FineSteer": "FineSteer",
        "Steer": "Steer",
        "Scale": "Scale",
        "Baseline": "Baseline",
        "iClear+": "iClear+",
        "iClear": "iClear",
        "HDscope": "HDscope",
        "iBeam": "iBeam",
        "iOneTouch": "iOneTouch",
        "iTouch": "iTouch",
        "Depth": "Depth",
        "Zoom": "Zoom",
        "Dyn Ra": "Dyn Ra.",
        "Gray Map": "Gray Map",
        "UMA": "UMA",
        "Persistence": "Persistence",
        "Gain": "Gain",
        "Smooth": "Smooth"
    },
    "doppler_controlling_device_tips": "(only for training and teaching purposes, do not use for diagnosis)",
    "doppler_controlling_device_name": "Controlling the device of ${name} ",
    "effect_after_restart": "Effective after restart",
    "white_board": "Whiteboard",
    "whiteboard": {
        "more": "More",
        "large": "Large",
        "normal": "Normal",
        "small": "Small",
        "mini": "Mini",
        "thin": "Thin",
        "thick": "Thick",
        "close": "Close",
        "clear": "Clear",
        "laserPointer": "Laser Pointer",
        "shape": "Shape",
        "eraser": "Eraser",
        "text": "Text",
        "pencil": "Pencil",
        "selector": "Selector",
        "clicker": "Clicker"
    },
    "userBindPacsAccountError": "PACS account verification failed",
    "video_type_proportion": "Proportion of video types",
    "image_type_proportion": "Proportion of image types",
    "relieve": "relieve",
    "unbind_description": "Are you sure you want to unlink the associated account of Cloud PACS?",
    "relation": "relation",
    "bind_description": "The current account is not bound to a cloud PACS account and cannot be started. Please first associate the bound account and try again",
    "unbind_tip": "Account not associated cannot be enabled",
    "account_association": "Related accounts",
    "remove_association": "Remove association",
    "information_system": "Information system",
    "associated": "Already associated",
    "visit": "visit",
    "cloud_platform": "Cloud platform",
    "my_application": "My application",
    "scan_to_download": "Scan the code to download for free",
    "email_login": "Email login",
    "mobile_login": "Mobile login",
    "ecology_welcome": "Welcome to Ruiying Ecology",
    "scan_login_title": "MiCo+App Scan Code Login",
    "qrcode_login": "QR code login",
    "password_login": "Password login",
    "verification_login": "Verification code login",
    "choose_language": "Select region and language",
    "ecology_title": "Medical Imaging Digital Service Expert",
    "cardiac_parasternal_short_axis_psax": "Parasternal short-axis View",
    "Cardiac_Smart": "Cardiac Smart",
    "unsave_tip": "Exiting will lose the current filled content. Do you want to save it?",
    "web_live_name_support_tips": "Only supports letters, numbers, Chinese characters, spaces, underscores, and horizontal bars",
    "LOCAL_TRACK_ERROR": {
        "NOT_SUPPORTED": "The current browser does not support this operation",
        "MEDIA_OPTION_INVALID": "The current device cannot support this resolution or frame rate",
        "DEVICE_NOT_FOUND": "The collection device cannot be found. Please check if the device is connected correctly or if it is occupied by other system applications",
        "PERMISSION_DENIED": "The device is currently not granted permission. Please grant permission and try again",
        "MICROPHONE_PERMISSION_DENIED": "Currently, microphone permission has not been granted. Please grant permission and try again",
        "CAMERA_PERMISSION_DENIED": "Currently, no camera permission has been granted. Please grant permission and try again",
        "CONSTRAINT_NOT_SATISFIED": "The browser does not support the specified collection option",
        "SHARE_AUDIO_NOT_ALLOWED": "When sharing audio on the screen, the option to share audio was not selected",
        "OTHERS": "Device malfunction, please check and try again",
        "NOT_READABLE": "The current device cannot be opened, it may be occupied"
    },
    "cloud_record": "CloudRecord",
    "account_sync_completed_tips": "Account synchronization completed",
    "notify_linker_sync_account_tips": "Due to the current device being bound to a u-Linker device, the u-Linker device will be notified to actively change the account",
    "notify_linker_start_live_tips": "Due to the current device being bound to a u-Linker device, the u-Linker device will be notified to initiate a live stream proactively",
    "cardiac_left_cardiac_ventricle": "LV",
    "cardiac_right_cardiac_ventricle": "RV",
    "cardiac_right_cardiac_atrium": "RA",
    "U-Linker_agree_binding": "u-Linker has agreed and the binding has been successful",
    "cardiac_left_cardiac_atrium": "LA",
    "microphone_detection_tips": "Attention: When detecting the microphone, microphone sharing will be paused",
    "camera_preview_tips": "Attention: When previewing the camera, camera sharing will be paused",
    "cardiac_parasternal_left_ventricular_long_axis": "Parasternal long-axis View",
    "cardiac_parasternal_short_axis_psax_apex": "PSAX-Apex",
    "cardiac_parasternal_short_axis_psax_pm": "PSAX-PM",
    "cardiac_parasternal_short_axis_psax_mv": "PSAX-MV",
    "cardiac_parasternal_short_axis": "Parasternal short axis",
    "cardiac_aortic_arch_view": "Aortic Arch Long- Axis View",
    "cardiac_long_axis_inferior_vena_cava_below_xiphoid_process": "Subcostal IVC View",
    "cardiac_cross_four_chambers_below_xiphoid_process": "Subcostal 4 chamber View",
    "cardiac_five_chambers_apex_heart": "Apical 5 chamber View",
    "cardiac_four_chambers_apex_heart": "Apical four chamber view",
    "cardiac_three_chambers_apex_heart": "Apical 3 chamber View",
    "cardiac_two_chambers_apex_heart": "Apical 2 chamber View",
    "cardiac_short_axis_major_artery": "Parasternal short-axis aortic valve View",
    "parasternal_left_ventricular_long_axis": "Parasternal long-axis View",
    "cardiac_papillary_muscle": "PM",
    "cardiac_aortic_arch": "ARCH",
    "cardiac_ascending_aorta": "ASC",
    "cardiac_hepatic_vein": "HV",
    "cardiac_inferior_vena_cava": "IVC",
    "cardiac_atrial_septum": "IAS",
    "cardiac_interventricular_septum": "IVS",
    "cardiac_pulmonary_aortic_valve": "PV",
    "cardiac_pulmonary_aorta": "PA",
    "cardiac_tricuspid_valve": "TV",
    "cardiac_right_atrium": "RA",
    "cardiac_descending_aorta": "DA",
    "cardiac_mitral_valve": "MV",
    "cardiac_aorta": "AO",
    "cardiac_left_atrium": "RA",
    "cardiac_left_ventricle": "LV",
    "right_ventricular_outflow_tract": "RVOT",
    "unbind": "Unbind",
    "fault_description": "Fault description",
    "fault_code": "Fault code",
    "fault_time": "Fault time",
    "historical_faults": "Historical faults",
    "received_binding_from_pc_tips": "Received binding request from PC device, do you agree",
    "bind_ultrasound_device_title": "Associate and bind ultrasound streaming devices",
    "binding": "Binding",
    "instruction_sent": "Instruction sent",
    "processed": "Processed",
    "unprocessed": "Unprocessed",
    "exam_duration": "Exam duration (seconds)",
    "video_number": "Number of videos",
    "image_mode": "Image mode",
    "network_camera_setting": "Video source settings",
    "exam_type_proportion": "Proportion of exam types",
    "latest_reporting_time": "Latest reporting time",
    "device_model": "Equipment model",
    "groupQrcodeExpire": "QR Code has expired.",
    "latest_version": "Latest version",
    "desktop_text": "desktop",
    "upgrade_input_method": "Upgrade input method",
    "reapply_btn": "Reapply",
    "withdrawn": "Withdrawn",
    "existing_items": "Existing items",
    "new_options": "New options",
    "preview": "preview",
    "delete_item_tips": "Do you want to delete this item",
    "delete_category_contents_tips": "Do you want to delete this category and all its contents",
    "adding_child_nodes_tips": "The current level has reached or exceeded level 5, and adding child nodes is not allowed.",
    "empty_categories_submitted_tips": "Please check that empty categories are not allowed in the submitted data",
    "add_standard_library_fields": "add standard library collection fields",
    "add_custom_collection_fields": "add custom collection fields",
    "add_subcategories": "add subcategories",
    "add_same_level_category": "add same level category",
    "downgrade_action": "downgrade",
    "upgrade_action": "upgrade",
    "move_action": "move",
    "homework_search_key": "Test question name or author",
    "step_tip": "step",
    "score_integer_tip": "The score must be an integer",
    "deadline_exceeded_tip": "The current time has exceeded the deadline",
    "homework_overdue_tip": "The homework has exceeded the deadline and cannot be viewed",
    "homework_correcting_tip": "The homework is currently being graded and cannot be viewed",
    "homework_cancheck_tip": "The homework setting does not allow viewing details, so it cannot be viewed.",
    "publish_at": "Published in {location}",
    "homework_type3": "CARD",
    "homework_type1": "ABD",
    "homework_type2": "Superf.",
    "homework_type4": "OB",
    "homework_type5": "Comp.",
    "device_binding_success_and_more_info": "The device has been successfully added to the group. To view device operation information, please go to Doppler Device Terminal - Presets - Network - Device Management and turn on the permission switch",
    "whether_pull_xxx_equipment_into_group": "Do you want to add device '${a}' to the group chat",
    "copy_link": "Copy link",
    "submit_paper_tip": "You have completed {a} questions, out of a total of {b} questions. Would you like to submit your answer sheet?",
    "correct_paper_tip": "The current total score is {a} points. Do you want to submit the correction results?",
    "question_score": "Score for this question",
    "correction_progress": "Correction progress",
    "zero_point": "Zero points",
    "next_question": "Next question",
    "prev_question": "Previous question",
    "score_range": "${a}- ${b}points",
    "score_less": "Below ${a} points",
    "avg_point": "average",
    "min_point": "Minimum score",
    "max_point": "Highest score",
    "point_tip": "point",
    "grades_distribution": "Distribution of Student Grades",
    "unfinished_number": "Number of unfinished tasks",
    "complete_number": "Completed number of people",
    "to_be_complete_number": "Number of people to be completed",
    "correction_teacher_empty_tip": "Correction teacher cannot be left blank",
    "deadline_empty_tip": "The deadline cannot be empty",
    "allow_view_correction": "Allow viewing of correction details",
    "set_homework": "Set homework",
    "select_homework": "Select test questions",
    "assign_homework_to": "Assign homework to",
    "error_tip": "error",
    "correct_tip": "correct",
    "submitted_answers": "Submitted answers",
    "homework_operation_step": "Step tip:<br/>1. Please use ultrasound equipment, open u-Link and scan the code to log in<br/>2、 Click on 'Start Collection', save the image as required, and send the saved image to the 'File Transfer Assistant'.",
    "stop_collecting": "Stop collecting",
    "start_collecting": "Start collecting",
    "correction_time": "Correction time",
    "correction": "Correction",
    "student_name": "Student Name",
    "not_submitted": "Not submitted",
    "corrected": "Corrected",
    "pending_correction": "Pending correction",
    "feedback_time": "Average duration of student feedback",
    "correcting": "Correction in progress",
    "assign_homework": "Assign homework",
    "share_paper": "Share test questions",
    "submission_time": "Submission time",
    "arrange_time": "Arrange time",
    "assign_people": "Assign people",
    "enter_correction": "Enter the correction process",
    "creation_time": "Creation time",
    "author": "author",
    "paper_results": "achievement",
    "paper_duration": "Homework time",
    "paper_statistics": "Statistics",
    "complete_again": "Complete again",
    "release_time": "Release time",
    "deadline": "deadline",
    "paper_question_count": "Number of test questions",
    "paper_total_score": "Total score of the exam paper",
    "view_homework": "Enter homework",
    "topic_type": {
        "operation": "Practical exercises",
        "shortAnswer": "Short answer questions",
        "multiSelect": "Multiple Choice Questions",
        "singleSelect": "Single Choice Question"
    },
    "exam_incomplete": "Pending completion",
    "sorry_no_post_dat_with_tag": "Sorry, there is no relevant content available",
    "my_cloud_exam": "My cloud homework",
    "famous_teacher_post": "Expert Lectures",
    "vetus_club_post": "Vetus Club",
    "functional_technology_post": "Technologies",
    "medical_workers_home_post": "Customer Service",
    "explosive_product_post": "Products",
    "operation_guidance": "Operation Guide",
    "clinical_beauty_map_post": "Image Gallery",
    "new_hot_post": "What's New",
    "certificate_agree_tips": "The current service may be restricted in access due to certificate reasons. Do you agree to trust the certificate",
    "live_expired_tips": "The current live stream has expired, please rejoin",
    "re_analyze_tips": "Re analysis will overwrite the original analysis results. Please confirm if you want to continue?",
    "re_analyze_title": "Reanalysis",
    "reason_for_deficiency_text": "Reason for deficiency:",
    "exam_bank": "My question bank",
    "arranged_exam": "I arranged it",
    "topic_count": "This question is worth {a} points",
    "topic_summary": "There are {a} sub questions in this category, with a total of {b} points",
    "correcting_exam": "homework correcting",
    "exam_completed": "Completed",
    "cloud_exam": "Cloud homework",
    "input_your_name": "Please enter your name",
    "image_tag_unique_tip": "Image labels can only be configured with one set",
    "verify_expired": "Identity verification has expired, please restart verification",
    "multicenter_release_tip": "Are you sure you want to publish the current configuration?",
    "retry": "retry ",
    "ai_doppler_layout_list_error": "Data loading failed",
    "not_filled": "Not filled in",
    "apply_time": "Application time",
    "apply_name": "Applicant Name",
    "apply_account": "Applicant's account",
    "enter_age_wrong": "Please enter the correct age",
    "standard_library": "Standard Library",
    "add_category": "Add Category",
    "unit": "unit",
    "filed_name": "Field Name",
    "add_field": "Add Field",
    "case_submitting_tip": "After submitting the case, it will enter the multi center list, and the reviewer can review the case. You will no longer be able to delete this case. Please confirm if you want to submit it?",
    "not_filled_tip": "The following fields are not filled in",
    "multicenter_collection_progress": "Case collection progress",
    "multicenter_collected": "Collected",
    "multicenter_reject_tip": "Supplementary case rejected",
    "enter_correct_number": "Please enter the correct numerical value",
    "please_select_end_time": "Please select the end time",
    "domain_verify": "The current domain name verification failed, unable to connect to the server",
    "setting_management": "configuration management",
    "uploader_nickname": "Uploader's nickname",
    "uploader_login_name": "Uploader account",
    "please_input_login_name": "User account",
    "data_detail_export": "export detail data",
    "Abdomen_smart_abd": "Smart ABD",
    "multicenter_approve": "Multi center approval",
    "start_ai_live_analyze": "Start real-time analysis",
    "apply_btn": "application",
    "required_tip": "Required fields incomplete",
    "multicenter_remarks": "Remarks Description",
    "multicenter_materials": "Project proposal materials",
    "multicenter_unit_count": "Estimated number of participating research units",
    "multicenter_case_count": "Estimated number of collected cases",
    "multicenter_project_cycle": "project cycle",
    "PI_unit_name": "PI unit name",
    "multicenter_name": "Multi center name",
    "withdrawal_of_application": "Withdrawal of application",
    "multicenter_release": "release",
    "multicenter_config_tip": "Configuration collection requirements",
    "multicenter_approval": "System Approval",
    "applying_tip": "Create application",
    "pending_approval_tip": "Your multi center application is awaiting approval from the system administrator.",
    "to_be_configured": "To be configured",
    "pending_approval": "Pending approval",
    "myocardial_strain_multicenter": "Multi center myocardial strain",
    "create_multicenter": "Create multicenter",
    "view_groupset_statistics_report": "View community statistics report",
    "view_groupset_bi_data_display": "View community BI data display",
    "groupset_admin_abilities_tips": "Community administrators can have the following abilities",
    "my_manage_groupset": "The community I manage",
    "my_created_groupset": "The community I created",
    "auth_mng": "authorization",
    "library": {
        "tag": "Tags",
        "category": "Categories"
    },
    "check_filter_result_of_post": "View ${count} content",
    "total_count_of_post": "A total of ${name} items",
    "is_optional": "Optional",
    "ExamBelongMultiCenterError": "The case data has been submitted to multiple centers and cannot be deleted",
    "batch_cancel_download": "Batch Cancel Download",
    "batch_continue_downloading": "Batch continue downloading",
    "pause_download": "Pause download",
    "paused": "Paused",
    "delete_message_by_other": "Deleted a file resource",
    "delete_message_by_self": "You deleted a file resource",
    "device_pixel_Ratio_change_tips": "A change in screen resolution has been detected, and there may be issues with page adaptation. Do you want to refresh the app now?",
    "delete_case_fail": "Delete case failed",
    "delete_case_success": "Delete case successful",
    "reselect_view_type_anaylze_tips": "Tip: The system will reanalyze according to the type of section you specified and refresh the section evaluation information.",
    "go_download": "Go download",
    "reselect_view_type_tips": "Are you sure to reselect section: ${name} for reanalysis?",
    "reselect_view_type_anaylze_success": "Re selected section: ${name} analysis successful, overall evaluation refreshed",
    "reselect_view_type_anaylze_fail": "Reselect section: ${name} analysis failed",
    "send_by_ctrl_enter": "Press the Ctrl+Enter keys to send",
    "delete_exam_tips": "Do you want to delete this exam and all the data under it?",
    "multi_center_patient_id_empty": "Multi center patient ID cannot be empty",
    "feedback_success_tips": "Your question has been received, thank you very much for your feedback.\n\n",
    "save_txt": "Save",
    "AiAnalyzeFailError": "AI analysis failed\n\n",
    "AiAnalyzeError": "AI analysis error",
    "exam_mode": "Exam Mode",
    "View_database_tables": "View database tables",
    "jump_external_browser_tips": "About to jump to an external browser to download the file",
    "cancel_tasks_ask": "Do you want to cancel tasks that have not been downloaded yet?",
    "open_directory_failed_tips": "Failed to open directory",
    "continue_downloading_ask": "Do you want to continue downloading unfinished tasks?",
    "cancel_download": "Cancel download",
    "feedback_date_range": "Feedback Time",
    "feedbacker": "Feedback person",
    "continue_downloading": "Continue downloading",
    "save_directory": "Save Directory",
    "download_task_manager": "Download Task Manager",
    "reselect_view_type": "Reselect_section_type",
    "please_describe_your_issue": "Please describe your issue:\n\n",
    "question_feedback": "Question Feedback",
    "failed": "Failed",
    "success": "Successful",
    "expiration_date": "Expiration date",
    "delete_case_confirm": "Are you sure to delete the case?",
    "delete_case": "Delete case",
    "patientExternalEditNotAllowed": "Non influencers, group leaders, or group administrators do not have the authority to edit multi center patient ID",
    "big_files_tips": "Please note that the current file is larger than {limitFileSize}MB and will expire in {expireDays} days after uploading",
    "display_exam_picture_with_struct": "Display Structural Box",
    "AiServerConnectError": "AI server connection failure",
    "multi_center_patient_id_too_long": "Multi center patient ID cannot exceed 64",
    "multi_center_patient_id": "Multi center patient ID",
    "system_administrator": "System Administrator\n\n",
    "group_view_summary": "Overall evaluation of the section group",
    "not_friend_tips": "You are currently not friends with this user. Please add a friend before proceeding",
    "ABD_Struct_Convergence_Lower_Cavity": "Inferior vena cava",
    "ABD_Struct_Right_Hepatic_Vein": "Right hepatic vein",
    "ABD_Struct_Left_Hepatic_Vein": "Left hepatic vein",
    "ABD_Struct_Middle_Hepatic_Vein": "Middle hepatic vein",
    "ABD_Struct_Pancreas": "Pancreas",
    "ABD_Struct_left_External_Superior_Branch": "Left external superior branch of portal vein",
    "ABD_Struct_Left_Inferior_External_Branch": "Left external inferior branch of portal vein",
    "ABD_Struct_Left_Lateral_Branch": "Horizontal part of left branch of portal vein",
    "ABD_Struct_Left_Medial_Branch": "Left internal branch of portal vein",
    "ABD_Struct_Sagittal_Portal_Vein": "Sagittal part of left portal vein branch",
    "ABD_Struct_Corner": "Right cardiac entrance",
    "ABD_Struct_Long_Aaxis_Inferior_Vena_Cava": "inferior vena cava (IVC)",
    "ABD_Struct_Long_Axis_Abdominal_Aorta": "Abdominal aorta",
    "ABD_Struct_Left_Lobe": "liver",
    "ABD_Struct_Caudal_Lobe_Liver": "Caudal lobe of liver",
    "ABD_Struct_Gallbladder": "Gallbladder",
    "ABD_Struct_Right_Lobe": "liver",
    "ABD_Struct_Kidney": "kidney",
    "ABD_Struct_Portal_Vein": "Portal vein",
    "ABD_UNDEFINED": "UNDEFINED",
    "ABD_Liver_2nd_PH": "Trans 2nd Portal Hepatis",
    "ABD_Panc_LongAxis": "Trans Pancreas Long",
    "ABD_Liver_Lt_Portal_Vein": "Trans 1st Portal Hepatis",
    "ABD_GB_LongAxis": "Long GB",
    "ABD_Liver_LHV_IVC": "Sag LL-IVC",
    "ABD_Liver_Lt_Lobe_AAo": "Sag LLobe-AAo",
    "ABD_Liver_Lt_Lobe_Caudate": "Sag LL-Caudate",
    "ABD_Liver_1st_PH": "Obliq 1st Porta Hepatis",
    "ABD_Liver_Rt_Kidney": "Sag RL-RK",
    "ABD_Portal_Vein": "Portal Vein",
    "alternative_images": "Alternative images",
    "selected_image": "Selected images",
    "edit_report_unenable_tips": "This report currently does not support editing",
    "move_down": "move down",
    "move_up": "move up",
    "dr_ai_analyze_statistics_error": "Exception in obtaining Dr quality control statistics data!",
    "select_transfer_image_max": "Up to ${1} images can be selected",
    "select_image": "Select Image",
    "inspection_overview": "Inspection Overview",
    "not_started": "Not started",
    "refresh_status": "refresh status",
    "initiated_live": " initiated a live broadcast",
    "join_live_streaming": "Join live streaming",
    "ai_search_suggest": "Discovered similar cases, click to view",
    "level_d_count_and_rate": "Number/Proportion of Class D",
    "level_c_count_and_rate": "Number/Proportion of Class C",
    "level_b_count_and_rate": "Number/Proportion of Class B",
    "level_a_count_and_rate": "Number/Proportion of Class A",
    "operation_doctor": "Operation Doctor",
    "body_position": "Body Position",
    "sub_part": "Sub Part",
    "part": "Part",
    "view_score_display_rule": "If there are multiple images in the same view, the system automatically takes their highest score",
    "everyone": "Everyone",
    "test_time_tips": "Exam the difference between the start and end times",
    "is_visible_text": "Visible:",
    "after_nickname_modified_in_group": "After the nickname is modified, it will only be displayed within this group, and all members of the group can see it",
    "my_nickname_in_group": "My nickname in the group",
    "group_modify_nickname": "Modify Group Nickname",
    "group_nick_name": "Group nickname",
    "level_d": "D",
    "level_c": "C",
    "level_b": "B",
    "level_a": "A",
    "not_group_member_live_tips": "You are currently not a member of this group. You can contact the administrator to join the group chat and experience the complete live streaming function",
    "dr_ai_analyze_statistics": "DR Image Quality Control Analysis Statistics",
    "dr_ai_analyze": "DR AI",
    "display_exam_original_picture": "Display Original Image",
    "box_color": "Box Color",
    "deletion_text": "Deletion:",
    "view_deletion": "View Deletion",
    "structure_evaluation_text": "Structure Evaluation:",
    "exam_type_text": "Exam Type:",
    "pravicy_policy_content": {
        "j": {
            "lead": {
                "d": "We will review the questions as soon as possible and give you response within 15 days after verifying your user identity.",
                "c": " or mail it to the following address: ",
                "b": " to contact us. Also, you can send your questions to the e-mail ",
                "a": "If you have other complaints, suggestions, or questions related to personal information of minors, please go to the website "
            },
            "zipcode": "Postal code: 518057",
            "address": "Data and Privacy Protection Center of Legal Department, Mindray Building, Hi-tech Industrial Park, Nanshan, Shenzhen, Guangdong Province, People's Republic of China.",
            "email": "<EMAIL>",
            "website": "https://consult.mindray.com/kf/",
            "title": "10. Contact Us"
        },
        "i": {
            "lead": "This privacy policy is specified for MiCo+. If there is inconsistency or conflicts between this policy and the general user rights and information security protection measures of Mindray, please refer to this policy.",
            "title": "9. Others"
        },
        "h": {
            "lead": "We may revise this guidance as appropriate. If the guidance content is revised, we will present the revised guidance to you by sending notification, pop-up window or other appropriate forms when you log in MiCo+ or update the version.",
            "title": "8. Policy Update"
        },
        "g": {
            "lead": "With the continuous development of our business, we may carry out business combination, acquisition and asset transfer. We will inform you of such changes and continue to protect your personal information according to the standards required by laws and regulations or required the new data controller to protect your personal information.",
            "title": "7. Global Transfer of Users' Personal Information"
        },
        "f": {
            "lead": "We attach great importance to protect personal information of minors.Since MiCo+ is a social platform specified for doctors, it is only applicable to adults above 18 years old for registration and use.If the case information summited in the platform involves patients who are under 14 years old, we will adopt a more strict privacy policy. For example, the name, age, height, and gender of a child case are hidden by default. If the user (doctor) needs to check the case, an additional request is required and the system records this check operation.",
            "title": "6. Handling of Children's Personal Information"
        },
        "e": {
            "content": {
                "f": "5.6 Complaints and Reports",
                "e": "5.5 Close Account",
                "d": "5.4 Withdraw Authorization",
                "c": "5.3 Delete Personal Information",
                "b": "5.2 Edit Personal Information",
                "a": "5.1 Access Personal Information"
            },
            "title": "5. Users' Rights"
        },
        "d": {
            "content": {
                "g": "g) Situations related to collect personal information from legally disclosed information, such as legal news reports and government information.",
                "f": "f) Personal data subject self-discloses personal information;",
                "e": "e) Situations related to protect personal data subject or significant legal rights and interests such as other’s lives and property in which it is difficult to obtain users' consent;",
                "d": "d) Situations directly related to criminal investigation, prosecution, trial, and execution of judgment;",
                "c": "c) Situations directly related to public security, public health, and major public interests;",
                "b": "b) Situations directly related to national security and national defense security;",
                "a": "a) Situations related to our performance of obligations stipulated by laws and regulations;"
            },
            "lead": "Your personal information will only be used for the normal operation of product service and it will not be used for other purpose.To ensure service security and help us better improve the stability of MiCo+, we may record the usage frequency, crash data, performance data, and other information. We will not associate these information with your personal information. If we want to use your personal information beyond the purpose stated in the previous section, we will inform you again and obtain your explicit consent before using it.In addition, according to relevant laws and regulations, as well as national standards, we may share, transfer, and publicly disclose personal information without obtaining prior authorization from personal data subject in the following situation:",
            "title": "4. Personal Information Usage Rules"
        },
        "c": {
            "content": {
                "g": "3.7 We will try our best to protect your personal information. We also ask for your understanding that no security measures are impeccable.",
                "f": "3.6 Currently, MiCo+ has completed national network security level protection (Level 3) registration. It is expected that MiCo+ can pass the evaluation and obtain security level certification by the end of 2021. In addition, Mindray has passed authoritative certification of ISO/IEC 27001 and ISO/IEC 27701 in information security.",
                "e": "3.5 If security events such as personal information disclosure occur, we will initiate emergency plans to prevent further development of security events and notify you by sending notice or announcement.",
                "d": "3.4 We establish special management systems, management process, and management organizations to ensure information security. For example, we strictly control the personnel scope for information access., and we require these personnel to comply with confidentiality obligations, and audit them.",
                "c": "3.3 We will strengthen software security of your device by continuously improving technology so as to prevent personal information disclosure. For example, we will encrypt some information locally on your device for secure transmission.",
                "b": "3.2 We will use various security protection measures to ensure the information security within a reasonable level. For example, we will use encryption technology (such as SSL) and anonymization to protect your personal information.",
                "a": "3.1 We strive to safeguard user’s information security so as to prevent information loss, improper use, unauthorized access or disclosure."
            },
            "title": "3. Information Security"
        },
        "b": {
            "content": {
                "b": {
                    "content": "we reserve your personal information only on the premise of realizing service functions. For example, if you cancel your account in MiCo+, we will delete your personal information, including the mobile phone number.",
                    "lead": "2.2 Storage Period: "
                },
                "a": {
                    "content": "according to the laws and regulations, users' personal information collected domestically will be stored in China.",
                    "lead": "2.1 Storage Place: "
                }
            },
            "title": "2. Information Storage"
        },
        "a": {
            "content": {
                "f": "1.6 When you write comments on case images, your comments will be stored in our server, since this is indispensable to this function. We will ensure the security of the stored information. If we use a third-party service to store your information, we will also require the third party to issue relevant certifications and qualifications to ensure data security. The third-party storage service provider cannot access or read your information.",
                "e": "1.5 When you use the real-time consultation function, we may use your camera to complete this function. If you enable the storage function when using real-time consultation, the voice and videos of the entire consultation process will be stored in our server. We will ensure the security of the stored information. If we use a third-party service to store your information, we will also require the third party to issue relevant certifications and qualifications to ensure data security. The third-party storage service provider cannot access or read your information.",
                "d": "1.4 When you use the case consultation function, the case information that you have sent will be stored in our server, since this is indispensable to this function. We will ensure the security of the stored information. If we use a third-party service to store your information, we will also require the third party to issue relevant certifications and qualifications to ensure data security. The third-party storage service provider cannot access or read your information.",
                "c": "1.3 When you use the chat function, the text, photos, videos, files, voice and other information that you have sent will be stored in our server, since this is indispensable to this function. We will ensure the security of the stored information. If we use a third-party service to store your information, we will also require the third party to issue relevant certifications and qualifications to ensure data security. The third-party storage service provider cannot access or read your information.",
                "b": "1.2 When you use MiCo+, we will collect information such as device model, operating system, and device identification code to ensure normal use of service, maintain normal running of services, optimize service experience, and ensure account security. These are basic information for ensuring the service quality of MiCo+.",
                "a": "1.1 When you register an account in MiCo+, we will collect your nickname, photo profile, and mobile phone number to complete user registration and protect your account security. The mobile phone number belongs to sensitive information, which is collected to meet the requirements of relevant laws and regulations for the real name system on the Internet. If you refuse to provide such information, you will fail to register an account. As a result, we cannot provide services to you. You can decide whether to fill in information such as gender and hospital."
            },
            "lead": "When you use MiCo+, we will collect the information which is provided by you or which is generated by using services in following ways so as to provide services to you, optimize our services, and safeguard your account security.",
            "title": "1. Personal Information Collection Rules"
        }
    },
    "pravicy_policy_catalogue": {
        "content": {
            "j": "10. Contact Us",
            "i": "9. Others",
            "h": "8. Policy Update",
            "g": "7. Global Transfer of Users' Personal Information",
            "f": "6. Handling of Children's Personal Information",
            "e": "5. Users' Rights",
            "d": "4. Personal Information Usage Rules",
            "c": "3. Information Security",
            "b": "2. Information Storage",
            "a": "1. Personal Information Collection Rules"
        },
        "title": "Catalogue",
        "lead": "In particular, obtaining sensitive permission is a necessary rather than sufficient condition for collecting specific information. If we have obtained a sensitive permission for a specific item, it does not mean that we will collect relevant information about you. Even if we have obtained sensitive permissions, we collect your relevant information according to this guideline when necessary. For more detailed information, please read relevant chapters according to the following index:"
    },
    "pravicy_policy_foreword": {
        "content": {
            "g": "g) We need to ask for your permissions in relevant activities in order to collect your information under this guide, or to provide you with services, optimize our services and ensure the security of your account. Sensitive permissions such as storage, camera, microphone, and photo album will not be enabled by default. We are authorized with permissions only if you explicitly agree to enable these permissions.",
            "f": "f) You can access, correct and delete your personal information according to the ways listed in this guide. Also, you can withdraw your consent, close your account, and make complaints and report.",
            "e": "e) Currently, MiCo+ does not obtain your personal information from any third party beyond Shenzhen Mindray Bio-Medical Electronics Co., Ltd. In the future, if we need to obtain your personal information indirectly from a third party for business development, we will indicate to you the source, type, and application scope of your personal information before obtaining the information. If the personal information processing activities required by MiCo+ for conducting business are beyond the scope of authorization provided to a third party, we will ask for your explicit consent before processing such personal information. Furthermore, we will strictly comply with relevant laws and regulations and ask third parties to guarantee the legality of information provided by them.",
            "d": "d) Currently, MiCo+ does not share or transfer your personal information to a third party beyond Shenzhen Mindray Bio-Medical Electronics Co., Ltd. If there is a situation in which we need to share or transfer your personal information or you ask us to share or transfer your personal information to a third party beyond Shenzhen Mindray Bio-Medical Electronics Co., Ltd., we will directly ask for your explicit consent or we will confirm that the third party obtains your explicit consent for the above behaviors, except for protecting user rights or protecting the ecological security of MiCo+. Moreover, we will perform risk assessments when we provide information to a third party.",
            "c": "c) When you use some functions, we will collect some sensitive information about you or your patient after obtaining your consent. For example, when you use the recommended contacts function, we will collect information about your mobile phone contacts. When you use the case consultation function, we will collect the case information from your uploaded cases. Unless some information are not allowed to collect according to relevant laws and regulations, otherwise, refusing to provide the information will only make you unable to use relevant specific functions. But you can use other functions in MiCo+.",
            "b": "b) We will illustrate the types of personal information collected by us and their corresponding uses, so that you can understand the types, reasons and collection methods for using specific personal information.",
            "a": "a) We understand that personal information is important to you and we will do our best to protect the security and reliability of your personal information. We are committed to maintaining your trust in us. We adhere to the following principles to protect your personal information: balancing rights with responsibilities, clear purpose, consent, minimum necessary, security, participation of personal information subject, and transparency. In addition, we will take appropriate security protection measures to protect your personal information according to the mature security standards of the industry. Please read and understand this Personal Information Protection Policy thoroughly before using our products (or services).",
            "brief": " MiCo+ is a social platform specified for doctors, which is developed by Shenzhen Mindray Bio-Medical Electronics Co., Ltd. This policy will illustrate relevant matters related to the collection, usage, and storage of your personal information by MiCo+ and your rights. The illustration is as follows:"
        },
        "title": "Foreword"
    },
    "pravicy_policy_date": {
        "public": "November, 1, 2021",
        "effective": "Effective date: November 1, 2021",
        "update": "Update date: November 1, 2021"
    },
    "pravicy_policy_title": "MiCo+ Personal Information Protection Policy Statement",
    "pravicy_policy_company_name": "Shenzhen Mindray Bio-Medical Electronics Co., Ltd.",
    "filename_illegal_characters": "Illegal characters exist, only supported: uppercase and lowercase letters, numbers, underscores, dots, commas, spaces, plus signs, dashes (-), equal signs (=), parentheses (()), and symbols (&), percent signs (%), pound signs (#), square brackets ([]), tildes (~), single quotes ('), reverse quotes (`), Chinese characters, Chinese colons, Chinese brackets, Chinese square brackets, Chinese book titles, Chinese exclamation marks, Chinese commas, Chinese periods, Chinese question marks",
    "quick_start_guide": "Quick start guide",
    "iworks_clip_summary_text": "${total} sections need to be completed for drawing. The number of effective uploaded sections is ${upload_num}, and the completion level is ${complete_rate}. In the effective upload section, the section standard rate is ${standard_rate}.",
    "data_loading_failed_tips": "Data loading failed. Please try again later",
    "avatar_preview_tips": "Distortion or scaling may occur due to non 200*200 size",
    "files_export_failed": " files failed",
    "exam_original_picture": "Original Image",
    "exam_picture": "Exam Image",
    "downloaded_task_failed": "Failed",
    "downloaded_task_downloaded": "Downloaded",
    "download_task_total": "Total",
    "download_task_completed": "Download task completed",
    "avatar_preview": {
        "submit": "Submit",
        "wait": "Loading...",
        "general": "Preview & Save Temporarily"
    },
    "avatar_preview_title": "Avatar Preview",
    "data_preparing": "Data preparation in progress",
    "no_ai_result": "No AI Result",
    "missing_members": "Missing members",
    "missing_keys": "Missing keys",
    "invalid_access": "Invalid Access!",
    "live_disabled": "Live function disabled",
    "qr_install_app": {
        "unknown_server_type": "Unknown Version",
        "add_friend": "Add friends: ",
        "join_group": "Join group chat",
        "allow_clipboard": "(Please allow the app to read the clipboard contents)",
        "first_start_with_var": "Starting the app for the first time will automatically do the following steps for you:",
        "start_directly": "Open directly",
        "has_downloaded": "Already downloaded?",
        "download": "Download"
    },
    "personal_profile_tips": "You can enter your company information, personal strengths, or interests",
    "personal_profile": "Personal Profile",
    "exam_socre_text": "Exam Score:",
    "discover": "Discover",
    "chat_text": "Chat",
    "view_count": "View Count",
    "completion_rate": "Completion Rate",
    "quality_score": "Quality Score",
    "jiajia_summary": "Dr.M Summary",
    "test_time": "Time Consumption",
    "test_time_text": "Time Consumption:",
    "iWorks_test": "iWorks Test",
    "video_path_full_tips": "The current video path is full, unable to continue subscription",
    "auto_cancel_video_tips": "The current video path is full and will automatically cancel one video for you",
    "cookies_privacy_agreement_acknowledge": "Acknowledge",
    "cookies_privacy_agreement_content": "User privacy is highly valued. By using Cookies, we are able to improve the browsing experience, understand the habits when users are using this app to improve our service quality. Continuing to use this app will indicate that you are clearly aware of and agree to store and process Cookies on your device.",
    "cookies_privacy_agreement": "Cookies Privacy Agreement",
    "instruction_manual_version": "Instruction manual version",
    "wechat_unsupported_tip": "Please tap ···, and to open it with system browsers.",
    "userNoAuth": "No permission to operate",
    "other_side": "Other Side",
    "one_side": "One Side",
    "exam_mode_fetal_heart": "Fetal Heart",
    "exam_mode_obe": "OB2/3",
    "export_live_data": "Export live streaming data",
    "upload_image_max_text": "Upload failed. The image size exceeds",
    "upload_ai_image": "AI Image",
    "upload_normal_image": "Normal Image",
    "supplementary_image_types": "Supplementary image types",
    "new_exam_switch_image_type_tip": "After switching the upload image type, the currently selected data will be lost!",
    "submit_ai_image_upload_tip": "${name} is missing, please upload!",
    "leave_ai_image_upload_tip": "You have not submitted the selected data, the data will not be saved!",
    "power_image": "Power Image",
    "color_image": "Color Image",
    "no_iclear_image": "No Iclear Image",
    "b_image": "B Image",
    "cover_image": "Cover Image",
    "power_mode": "Power Image Mode",
    "color_mode": "Color Image Mode",
    "no_color_mode": "Regular B Image Mode",
    "color_type": "Color Type",
    "group_apply_expire": "Expired",
    "group_apply_pass": "Passed",
    "group_apply_success": "Group entry request has been sent",
    "send_files": "Send File",
    "unit_file": " file",
    "file_to_be_sent": " file to be sent",
    "like_action": "Like",
    "group_join_input_tip": "The group owner has enabled group entry verification. Please enter a verification message",
    "group_join_apply_tip": "${1} Apply to join the group chat",
    "group_join_verify_btn": "Group application",
    "group_join_verify_tip": "After activation, group members need confirmation from the group owner or group administrator before entering the group",
    "group_join_verify": "Group entry application verification",
    "send_success": "Sending successfully.",
    "sender": "Sender",
    "total_duration": "Total Duration",
    "latest_half_year_label": "Last 6 Months",
    "latest_months_label": " Last 3 Months ",
    "latest_month_label": " Last Month ",
    "latest_week_label": " Last Week ",
    "message_type": "Message Type",
    "search_by_specified_memcontent": "Search for specified content",
    "search_by_date": "Search by date",
    "search_by_group_member": "Search by members",
    "upload_log_file_success": "Upload log file successfully",
    "upload_log_file": "Upload Log File",
    "choose_a_chat": "Choose a chat",
    "chunk_load_error": "The server has been updated and the application needs to be restarted to use this feature",
    "remote_control_response_timeout": "Remote control response timeout",
    "push_stream_not_allow_remote_control": "The streaming client does not allow remote parameter adjustment",
    "search_uploader_name": "Uploader's name or patient's name",
    "search_patient_name": "Search patient name",
    "confirm_patient_information": "Please confirm whether the entered patient information and image information are consistent",
    "exam_created_success_tips": "The case has been created successfully. Please wait patiently for the image information to finish uploading",
    "never_notify": "Never notify",
    "delete_group_manager": "Delete Manager",
    "add_group_manager": "Add Manager",
    "group_manager_permissions6": "- Set the method of joining group chat in group management and confirm the application to join group chat",
    "group_manager_permissions5": "- View live details",
    "group_manager_permissions4": "- Remove group members",
    "group_manager_permissions3": "- Force end of live seeding",
    "group_manager_permissions2": "- Publish group announcement",
    "group_manager_permissions1": "- Editing review and removing pictures and video files within the group",
    "group_manager_permissions": "Group manager can have the following abilities",
    "manager": "Manager",
    "group_managers": "Group Managers",
    "group_manage": "Group Manage",
    "not_allowed_deleted_invited": "Users who have already been invited are not allowed to be deleted",
    "exam_number": "Exam Number",
    "exam_info_text": "Exam Information",
    "basic_info_text": "General Information",
    "input_select_tips": "Select",
    "input_enter_tips": "Please input",
    "leave_new_exam_tip": "Leaving this page soon,please ensure that your information has been uploaded",
    "create_new_exam": "Create a new examination case",
    "end_time_greater_tips": "The end time should be greater than the start time",
    "transfer_group_error": "Transfer group failed. Please try again!",
    "transfer_group_confirm": "Set {1} as the group leader. You will no longer be the group leader for this session. Are you sure to continue?",
    "transfer_group": "Transfer Group",
    "clear_text": "Clean",
    "contacts_select": "Contacts Select",
    "reselect_chat_send": "Reselect Chat Send",
    "send_original_chat": "Send in original chat",
    "export_task_submitted": "The export task has been submitted, and there may be a delay in sending messages. Please be patient and wait",
    "exported_video_use_cover": "Successfully uploaded, the exported video will use this cover page",
    "area_cannot_cropped": "This area cannot be cropped",
    "delete_video_clip_not_allowed": "Deleting the last video clip is not allowed",
    "delete_video_track_not_allowed": "Deleting this video track is not allowed",
    "cloud_resources_processed": "Cloud resources are currently being processed, please try again later",
    "server_resource_retried": "Server resources are being generated, please wait",
    "instruction_manual": "Instruction manual",
    "cannot_edit_multiple_modules": "Multiple modules cannot be edited simultaneously",
    "update_ready_tip": "The new version is ready, restart to experience the latest features",
    "no_permission_operate": "You do not have permission to operate this function",
    "case_database_fliter": {
        "papillary_carcinoma": "Papillary carcinoma",
        "mucinous_carcinoma": "Mucinous carcinoma",
        "cephaloma": "Cephaloma",
        "infiltrating_lobular_carcinoma": "Infiltrating lobular carcinoma",
        "infiltrating_ductal_carcinoma": "Infiltrating ductal carcinoma",
        "invasive_breast_cancer": "Invasive breast cancer",
        "lobular_carcinoma_in_situ": "Lobular carcinoma in situ",
        "ductal_carcinoma_in_situ": "Ductal carcinoma in situ",
        "noninvasive_breast_cancer": "Noninvasive breast cancer",
        "pathological_classification_breast_cancer": "Pathological classification of breast cancer",
        "bi_rads_6": "Category 6",
        "bi_rads_5": "Category 5",
        "bi_rads_4c": "Category 4c",
        "bi_rads_4b": "Category 4b",
        "bi_rads_4a": "Category 4a",
        "bi_rads_3": "Category 3",
        "bi_rads_2": "Category 2",
        "bi_rads_1": "Category 1",
        "bi_rads_type_text": "BI-RADS classification:",
        "bi_rads_type": "BI-RADS classification",
        "coarse_calcification": "Coarse calcification",
        "microcalcification": "Microcalcification",
        "intraductal_calcification": "Intraductal calcification",
        "calcification_out_mass": "Out mass calcification",
        "calcification_in_mass": "In mass calcification",
        "no_calcification": "No calcification",
        "calcification": "Calcification",
        "mixed_change": "Mixed change",
        "acoustic_shadow": "Acoustic shadow",
        "echo_enhancement": "Echo enhancement",
        "no_change": "No change",
        "posterior_echo": "Posterior echo",
        "uneven_echo": "Uneven echo",
        "mixed_echo": "Mixed echoes",
        "isoechoic": "Iso echoic",
        "hyperechoic": "High echo",
        "hypoechoic": "Low echo",
        "anechoic": "Anechoic",
        "echo_type": "Echo type",
        "hairpin_like": "Hairpin like",
        "microphylation": "Differential leaf",
        "angled": "Angled",
        "finishing": "Smooth",
        "vague": "Vague",
        "edge": "Edge",
        "unparallel": "Non-parallel",
        "parallel": "Parallel to the skin",
        "direction": "Orientation",
        "irregular": "Irregular",
        "circular": "Round",
        "oval": "Oval",
        "shape": "Shape",
        "bi_rads_feature": "BI-RADS features",
        "malignant": "Malignant",
        "benign": "Benign",
        "benign_or_malignant": "Benign & Malignant"
    },
    "obstetric_qc": {
        "obstetric_mid_pregnancy_view_description": "Combining the existing quality control solutions and product planning of MiCo+, we focus on the intelligent quality control direction of production screening images as the key to promoting MiCo+ in the maternal and child systems.",
        "obstetric_mid_pregnancy_view_name": "Quality control configuration of obstetric mid pregnancy view",
        "title": "Obstetrics AI quality control (AI identification results, for reference only)",
        "nalysis_rconsider_uncompleted": "No Rconsider results obtained",
        "nalysis_uncompleted": "No quality control analysis results obtained",
        "quality_user": "Quality Controller",
        "quality_user_leader": "Quality Control Leader",
        "quality_control_leader": "Quality Control Leader ",
        "preset_data_set": "Views quality control configuration",
        "nalysis_completed": "Quality control analysis completed",
        "result": "Quality Control Results",
        "non_view": "Non Quality Control View",
        "base_view": "Base View",
        "optional_view": "Optional View"
    },
    "verall_evaluation_qc": "Overall evaluation of quality control",
    "obstetric_qc_ai": "Obstetric AI Quality Controller",
    "no_relative_data_find": "No relevant data found",
    "ai_non_standard_view_text": "Number of non-standard viewss:",
    "ai_recognition_view": "Number of AI recognition views",
    "last_update_time": "Last update time",
    "uploader": "Uploader",
    "organization_name_too_long": "The length of the institution name cannot exceed 64",
    "orgn_name_too_long": "The length of the institution name cannot exceed 64",
    "patient_name_too_long": "Patient name length cannot exceed 64",
    "patient_id_too_long": "Patient ID length cannot exceed 64",
    "exam_time_interval_too_long": "The maximum inspection interval cannot exceed one year",
    "data_list": "Data List",
    "ai_nalysis_no_result_tips": "There are currently no AI recognition results available",
    "ai_nalysis_result_tips": "AI analysis results, for reference only",
    "reconsidered": "Reconsidered",
    "structure_name": "Structure Name ",
    "structure_evaluation": "Structure Evaluation",
    "cancel_rconsider_success": "Cancel reconsideration successful",
    "cancel_rconsider_fail": "Cancel reconsideration failed",
    "cancel_rconsider_tips": "Are you sure to cancel reconsideration?",
    "exam_date": "Exam Date",
    "view_type_select_tips": "Please select the view type!",
    "view_type": "View Type",
    "view_type_ai": "View Type(AI)",
    "cancel_reconsider": "Cancel reconsideration",
    "apply_reconsider": "Apply for reconsideration",
    "institution_id": "Institution ID",
    "institution_name": "Institution Name",
    "compliance_rate": "Compliance Rate",
    "view_compliance_rate": "Image Compliance Rate",
    "exam_compliance_rate": "Exam Compliance Rate",
    "reason_for_deficiency": "Reason for deficiency",
    "go_location_of_file": "Jump to the location of the file",
    "check_view_info": "View Details",
    "view_quality_text": "View Quality:",
    "view_quality": "View Quality",
    "view_detail": "View Details",
    "group_view_score": "View Group Score",
    "view_score_text": "View Score:",
    "view_score": "View Score",
    "view_name_uploader": "View Name(Uploader)",
    "view_name_text": "View Name:",
    "view_name": "View Name",
    "latest_exam_time": "Last Exam Time",
    "image_number": "Number of Images",
    "non_standard": "Non-standard",
    "basic_standard": "Basic Standard",
    "standard": "Standard",
    "reset_data_tips": "Do you want to reset all configuration data",
    "last_uploader": "Last Updated By",
    "search_criteria": "Search Criteria",
    "ai_score_no_standard": "AI score (non-standard)",
    "ai_score_basic_standard": "AI Score (basic standard)",
    "ai_score_standard": "AI Score (standard)",
    "ai_score": "AI Score",
    "score_value": "Score",
    "total_score_value": "Total Score",
    "reconsider_result_new_tips": "The image analysis results have been refreshed. Please confirm if you agree with the analysis results?",
    "overall_evaluation_desc": "A total of {total} images were uploaded during this inspection, a total of {finshed} view were identified. View quality: standard: {standard}, basic standard: {basic_standard}, non-standard: {non_standard}, missing: {deletion}, and compliance rate: {compliance_rate}%，integrity rate: {integrity_rate}%， standardization rate: {standardization_rate}%.",
    "overall_evaluation": "Overall Evaluation",
    "view_group_name": "View Group Name",
    "view_group": "View Group",
    "view": "View",
    "score_items": "Score Items",
    "full_mark": "Full Mark",
    "proportion_weight_value": "Proportion Weight Value",
    "proportion_weight": "Proportion Weight",
    "exam_view_page": "Exam View",
    "view_view_page": "Section View",
    "detect_image": "Detect Image",
    "image_effects": "Image Effects",
    "deletion": "Deletion",
    "display": "Display",
    "status_text": "Status",
    "status": "Status",
    "obstetric_qc_multicenter": "Obstetrics Quality Control Multi Center",
    "ai_error": {
        "invalid": "invalid parameter",
        "anaylse_timeout": "Analysis timeout",
        "report_anaylse_fail": "Parsing report failed",
        "anaylse_fail": "Analysis failed",
        "connect_fail": "Connection failed",
        "disconnect": "Disconnect",
        "prohibit_call": "Prohibit calling interfaces"
    },
    "load_more": "load more",
    "video_clip_msg": "[video clip]",
    "clip_time": "Clip time",
    "generated_video_clips": "'s generated video clips",
    "failed_to_enter_editing_mode": "Failed to retrieve data, unable to enter editing mode. Please try again later.",
    "export_video": "Export Video",
    "exit_clip_tips": "You are about to exit the clip. Please confirm whether the work has been completed and export",
    "pthological_conclusion": "Pathological conclusion",
    "ultrasonic_diagnosis": "Ultrasonic diagnosis",
    "update_confirm_tip": "The new version is ready. Would you like to restart the update now?",
    "video_clips": "Video clips",
    "local_library": "Album",
    "take_picture": "Take a picture",
    "searc_in_case_database_one_picture": "Please select a picture",
    "searc_in_case_database_many_picture": "Search by image only supports one image search",
    "ai_searc_in_case_database": "AI image recognition",
    "searc_in_case_database_result": "After Mindray's AI calculation, {1} suspected lesions were identified in the image (AI identification results, for reference only)",
    "is_no_case_text": "No similar case information found",
    "picture_is_no_roi": "The picture you selected can not be recognized",
    "get_picture_info_fail": "Image information reading failed, please check the network",
    "picture_is_only_jpg_jpeg_bmp_png": "Only images in png, jpg, jpeg, and bmp formats are allowed",
    "picture_is_too_blurred": "The picture is too blurry, please take a picture again or select a picture",
    "custom_server": "Custom server",
    "default_server": "Default server",
    "current_server": "Current server",
    "favorite_confirm_private": "Private (only visible to yourself)",
    "favorite_confirm_public": "Public (only visible to friends)",
    "favorite_confirm_tip": "Will be added to personal cloud collection, who can see",
    "favorites_private_tip": "Make the collection private (only visible to you)?",
    "favorites_public_tip": "Make the collection public(only visible to friends)?",
    "live_has_no_playback": "The live stream has no playback data",
    "rotate_right": "Rotate right",
    "rotate_left": "Rotate left",
    "zoom_out": "Zoom out",
    "zoom_in": "Zoom in",
    "local_camera": "Local camera",
    "camera_error": "Camera is Not Working. Please try again",
    "mic_error": "Microphone is Not Working. Please try again",
    "thyroid_multicenter": "Thyroid MultiCenter",
    "hfr_multicenter": "HFR MultiCenter",
    "repeat_supply": "This exam has been added information in other groups and cannot be submitted again",
    "switch_to_aux_screen": "camera",
    "switch_to_main_screen": "main screen",
    "server_ended_live": "The server has ended your live stream",
    "image_type_not_compliant": "The image type does not meet the requirements. Do you still want to submit?",
    "set_to_ordinary_ultrasound": "Set as normal ultrasound",
    "set_to_RCEH": "Set as conventional contrast ultrasound",
    "set_to_HFRR": "Set as HFR contrast ultrasound",
    "RCEH": "Conventional contrast ultrasound",
    "HFRR": "HFR contrast ultrasound",
    "ordinary_ultrasound": "Normal ultrasound",
    "picture_type": "Picture type",
    "end_cloud_record": "End cloud record",
    "start_cloud_record": "Start cloud record",
    "only_host_end_cloud_recording": "You do not have permission to end cloud recording",
    "only_host_initiate_cloud_recording": "You do not have permission to start cloud recording",
    "review_detail_title": "Review",
    "live_detail_title": "Live",
    "more_settinngs": "More settings",
    "share_QR_code": "Share QR code",
    "recording_ended": "Recording ended",
    "recording_turned_on": "Recording turned on",
    "whose_recording_file": "'s recording file",
    "cancel_btn": "Cancel",
    "envTitleMap": {
        "CE": "MiCo+",
        "CN": "MiCo+"
    },
    "recommend_download_app": "It is recommended that you download MiCo+ app to get a better viewing experience",
    "retract_more_details": "Hide more details",
    "expand_more_details": "Unfold for more details",
    "is_in_conference": " is in conference",
    "file_in_progress": "File uploading, please wait",
    "live_setting": "Live Setting",
    "someone": "Someone",
    "mainstream_is_sharing": " sharing screen/ultrasound",
    "data_traffic_waring": "Data usage warning and limit",
    "waiting_for_reconnection": "Waiting for reconnection",
    "public_group_tip": "All users can join",
    "private_group_tip": "Requires an invitation from the group member to join the group",
    "not_allow_modify_others_data": "Do not allow to modify others' data",
    "cancel_favorite": "Cancel favorites",
    "live_broadcast_viewers": "viewers",
    "live_broadcast_duration": "duration",
    "live_broadcast_information": "information",
    "live_broadcast_initiation_time": "Start Time",
    "live_broadcast_initiator": "Creator",
    "view_details": "View details",
    "exit_edit": "Exit edit ?",
    "organization_name": "Organization",
    "not_set_tip": "Not set",
    "avatar_title": "Profile phote",
    "initiated_live_broadcast": "'s live ",
    "teaching_live": "Online training",
    "consultation_live": "Remote consultation",
    "universal_live": "General Live",
    "got_it": "got it",
    "select_organization": "Organization",
    "enter_organization": "Enter organization",
    "improve_infomation": "more personal information>>",
    "modify_nickname": "Please modify your nickname",
    "body_parts": "Exam parts",
    "patient_sex": "Gender",
    "patient_age": "Age",
    "patient_name": "Patient Name",
    "apply_description": "I am ${nickname} from the group ${group}",
    "apply_friend_remark": "Remarks",
    "apply_friend_info": "Verification information",
    "edit_review_info": "Edit playback info",
    "live_token_has_expired": "The token has expired, please try to re-enter the live broadcast",
    "live_connection_continue_waiting_tips": "Network disconnected. Please check it or reconnect.",
    "keep_waiting": "Reconnect",
    "send_apply_add_friend": "Send application for adding friends",
    "apply_add_friend": "Apply to add friends",
    "live_conference_reconnecting": "Reconnecting",
    "reload_page_tip": "Network exception, the page will be restarted for you",
    "resource_being_generated": "The resource is being generated, please try again later",
    "start_live_broadcast": "Start live stream",
    "whether_last_stream_pushing_action": "Whether to initiate the last stream automatically",
    "whether_enable_automatic_recording": "Whether to enable recording automatically",
    "linking": "Connecting",
    "input_device_remark": "Remark",
    "edit_device_remark": "Remark",
    "no_device_remark": "No remark",
    "current_device": "Current device",
    "hospital_name_length_limit_32": "The hospital name length limit of 32",
    "hospital_name_length_limit_0": "The hospital name cannot be empty.",
    "hospital_name_exist": "The hospital already exists in the list",
    "input_hospital_name": "Please enter hospital name",
    "add_hospital_name": "Add Hospitals",
    "not_upload_text": "Not uploaded",
    "in_silent_streaming": "Connected to TV wall",
    "live_session_in_progress": "The live stream is in progress",
    "share_link_success": "Share succeeded",
    "searc_in_case_database": "Search by image",
    "share_email_title": "Share to email",
    "whether_remove_equipment_from_group": "Whether to remove the device from the group",
    "share_sms": "SMS",
    "share_wechat": "Wechat",
    "exam_images": "Exam Images",
    "not_connected": "Disconnected",
    "piece_tip": "  ",
    "exam_images_title": "[Exam]",
    "whether_pull_equipment_into_group": "Whether to let the device join into the group",
    "traceless_failed": "Verification failed, please try again!",
    "traceless_error": "Error, please try again!",
    "traceless_success": "Verification succeeds",
    "traceless_slide": "Please swipe right to verify",
    "undefined_name": "Unnamed",
    "input_device_name": "Please enter device name",
    "edit_device_name": "Change device name",
    "device_detail": "Device detail",
    "default_push_way": "The default method of launching streaming",
    "back_to_login": "Back to login",
    "tv_wall_text": "TV WALL",
    "remote_camera": "Remote Camera",
    "main_stream_screen": "Main Screen",
    "quick_lauch_live_title": "Quick live stream",
    "start_record": "Start record",
    "weblive_live_not_yet": "No videos are shared currently",
    "switch_video_to_main": "Currently the camera. Click to switch to the main video",
    "switch_video_to_aux": "Currently the main video, click to switch to the camera",
    "weblive_download_client_tips": "For better experience, please click to download MiCo+ Application",
    "weblive_download_app_tips": "For better experience, please click to download MiCo+ app",
    "no_support_browser_live": "The current browser does not support watching the live stream. Please open it with another browser.",
    "live_des": "Live description",
    "conneting": "Connecting",
    "error_live_address_tip": "Wrong address, no live stream information",
    "remote_current_mode": "Current mode:",
    "storeState": "Store state",
    "please_enter_code": "Please enter verification code ",
    "open_system_browser": "Open Browser",
    "device_binding_success": "Device binding succeeded",
    "more_device_title": "More",
    "device_list": "Devices",
    "device_binding_tip": "Device binding to group chat：",
    "authorized_devices": "Authorized devices",
    "device_binding": "Binding device",
    "end_live_tips": "End the live stream and all users exit",
    "quit_live_tips": "Just end your own stream",
    "end_live": "End Live",
    "quit_live": "Leave Live",
    "whether_dissolve_live": "Whether to end the live stream and notify other members to quit",
    "same_label_can_selected": "Only file operations under the same label can be selected",
    "conference_seeding": "Live stream",
    "live_not_allow_operation": "Live streaming now, operation is not allowed",
    "applying_join_room_error": "Error in applying to join the room",
    "open_mic_to_many_tips": "Currently, there are too many people opening the mic. Try again after other users close the mic",
    "processing_wait": "Processing, please wait",
    "lost_connect_with_server": "Lost connection with the server",
    "live_ended": "The live broadcast has ended",
    "reset_email_success": "Email address set successfully",
    "reset_email_title": "Reset email address",
    "reset_email_tip": "Set new email address",
    "auth_by_mobile": "Verify with mobile phone",
    "auth_by_password": "Verify with password",
    "download_tip": "Download MiCo+",
    "userLoginQrCodeError": "The QR code is expired, please scan to login again",
    "qrcode_time_out": "The QR code is expired, please click to refresh!",
    "qrcode_time": "The QR code is valid for 120seconds",
    "time_out_msg": "The QR code will be expired in {1} seconds",
    "cancel_login": "Cancel the login",
    "auto_login_device": "Automatically log in to the device",
    "login_to_pc": "Login MiCo+",
    "is_force_conference_by_group_owner": "Whether to forcibly end the live stream with the rights of the group owner",
    "push_stream_setting": "Streaming Setting",
    "merge_choose_other": "Use account data of ${1}",
    "merge_choose_current": "Use the data of the current account",
    "merge_account_tip": "Account data already exists in ${1}. If you continue to bind, you can only choose to keep one account data, and the other account data may be cleared:",
    "verify_bind_description": "This operation requires binding mobile phone / email",
    "tap_to_join_conference": "live streaming now, tap to join",
    "service_setting": "Service_Setting",
    "network_setting": "Network Setting",
    "thyroid": {
        "export_excel_confirm": "Only data with a case status of approved can be exported. Please confirm if you want to export it?",
        "thyroid_multicenter_form": "thyroid multicenter project form",
        "export_excel": "Export excel",
        "shell_two_hardness_SD": "Shell 2.0mm hardness (SD)",
        "shell_two_hardness_min": "Shell 2.0mm hardness (min)",
        "shell_two_hardness_max": "Shell 2.0mm hardness (max)",
        "shell_two_hardness_mean": "Shell 2.0mm hardness (mean)",
        "shell_one_hardness_SD": "Shell 1.0mm hardness (SD)",
        "shell_one_hardness_min": "Shell 1.0mm hardness (min)",
        "shell_one_hardness_max": "Shell 1.0mm hardness (max)",
        "shell_one_hardness_mean": "Shell 1.0mm hardness (mean)",
        "shell_zero_point_five_hardness_SD": "Shell 0.5mm hardness (SD)",
        "shell_zero_point_five_hardness_min": "Shell 0.5mm hardness (min)",
        "shell_zero_point_five_hardness_max": "Shell 0.5mm hardness (max)",
        "shell_zero_point_five_hardness_mean": "Shell 0.5mm hardness (mean)",
        "lesion_hardness_SD": "Lesion hardness (SD)",
        "lesion_hardness_min": "Lesion hardness (min)",
        "lesion_hardness_max": "Lesion hardness (max)",
        "lesion_hardness_mean": "Lesion hardness (mean)",
        "high_resolution_shear_wave_elastic_ultrasound": "High-resolution shear wave elastography",
        "shear_wave_elastic_ultrasound": "Shear wave elastography",
        "quantitative_longitudinal": "quantitative (Longitudinal cutting)",
        "qualitative_Rago_criteria": "qualitative (Rago criteria)",
        "strain_type_elastic_ultrasound": "Strain Elastography",
        "focus_enhancement_direction": "Lesion enhancement direction",
        "focus_enhancement_mode": "Lesion enhancement mode",
        "enhancement_degree_of_focus_refer_to_thyroid": "Enhancement level of lesion (refer to surrounding thyroid parenchyma)",
        "enhancement_time_of_focus_refer_to_thyroid": "Enhancement time of lesion (refer to surrounding thyroid parenchyma)",
        "high_frame_rate_contrast_ultrasound": "High frame rate contrast ultrasound",
        "conventional_contrast_enhanced_ultrasound": "Conventional contrast-enhanced ultrasound",
        "focal_blood_flow_CPP": "Focal blood flow CPP",
        "ultrafine_blood_flow_imaging_UMA": "Ultrafine blood flow imaging UMA",
        "focal_blood_flow_AdlerGrade": "Focal blood flow (Adler grade)",
        "color_Doppler_CDFI": "Color Doppler CDFI",
        "TI_RADS_category": "TI-RADS classification",
        "microcalcification_in_lesion": "Microcalcification in lesion",
        "lesion_margin": "Lesion margin",
        "aspect_ratio_of_focus": "Aspect ratio of lesion",
        "focal_echo": "Lesion echo",
        "focus_size_diameter_line_2": "Lesion size: diameter line 2 (cm)",
        "focus_size_diameter_line_1": "Lesion size: diameter line 1 (cm)",
        "focus_location": "Lesion location",
        "focus_No": "Focus No",
        "focus_number": "Number of lesions",
        "focus": "Lesion",
        "cervical_lymph_nodes": "Cervical lymph nodes",
        "thyroid_parenchyma_echo": "Thyroid parenchyma echo",
        "overview_of_neck_ultrasound": "Overview of neck ultrasound",
        "followUpReuslt": "Follow-up results",
        "pathologyResult": "pathological results of surgery",
        "FNA_result": "FNA result",
        "pathology_or_follow_up": "Pathology/follow-up",
        "thyrotropin_receptor_antibody": "Thyrotropin receptor antibody (TRAb)",
        "anti_thyroid_peroxidase_antibody": "Antithyroid peroxidase antibody (TPOAb)",
        "thyroglobulin_antibody": "Thyroid globulin antibody (TGAb)",
        "thyrotropin": "Thyroid stimulating hormone (TSH)",
        "free_triiodothyronine": "Free triiodothyronine (FT3)",
        "free_thyroxine": "Free thyroxine (FT4)",
        "tetraiodothyronine": "Tetraiodothyronine (TT4)",
        "triiodothyronine": "Triiodothyronine (TT3)",
        "supply_thyroid_function_text": "Thyroid function",
        "supply_choice": {
            "points_5": "5 points",
            "points_4": "4 points",
            "points_3": "3 points",
            "points_2": "2 points",
            "mixability": "mixability",
            "centrifugality": "centrifugality",
            "centripetal": "centripetal",
            "annular_enhancement": "annular enhancement",
            "uneven_enhancement": "uneven enhancement",
            "uniform_enhancement": "uniform enhancement",
            "punctate_enhancement": "punctate enhancement",
            "high_enhancement": "high enhancement",
            "Isoenhancement": "iso enhancement",
            "low_enhancement": "low enhancement",
            "no_enhancement": "no enhancement",
            "fast_rewind": "fast rewind",
            "sync_enhancements": "sync enhancements",
            "fast_forward": "fast forward",
            "level_III": "level III",
            "level_II": "level II",
            "level_I": "level I",
            "level_0": "level 0",
            "category_5": "category 5",
            "category_4C": "category 4C",
            "category_4B": "category 4B",
            "category_4A": "category 4A",
            "category_3": "category 3",
            "unsmooth": "unsmooth",
            "smooth": "smooth",
            "no_erect": "no erect",
            "erect": "erect",
            "high_echo": "high echo",
            "isoechoic": "Iso echoic",
            "low_echo": "low echo",
            "very_low_echo": "very low echo",
            "right": "right",
            "left": "left",
            "isthmus": "isthmus",
            "highEchoGroup": "high echo group",
            "microcalcification": "microcalcification",
            "cysticDegeneration": "cystic degeneration",
            "no_suspicious_MT_lymph_nodes": "no suspicious MT lymph nodes",
            "suspicious_MT_lymph_nodes": "suspicious MT lymph nodes",
            "lesion_is_significantly_enlarged": "Significant increase in lesion follow-up",
            "no_change_of_benign_lesions": "no change of benign lesions in follow-up",
            "no_lymph_node_metastasis": "no lymph node metastasis after malignant operation",
            "lymph_node_metastasis": "lymph node metastasis after malignant operation",
            "malignant": "malignant",
            "benign": "benign",
            "ambiguity": "ambiguity",
            "not_have": "No",
            "have": "Yes"
        },
        "confirm_passing_the_case": "Are you sure to pass the case information?",
        "caseid_not_null": "Please fill in the case number"
    },
    "hfr": {
        "HFRR_diagnosis": "High frame rate imaging diagnosis",
        "no_same_no_annotate": "The Review conclusion is not consistent.",
        "case_info_no_complete": "Incomplete patitent info",
        "pass_review": "Are you sure to approve the review?The conclusion cannot be modified after that.",
        "search_other_options": {
            "0": "Complete Patient Info",
            "1": "The review conclusion is unanimous",
            "2": "The review conclusion is unanimous and the patient Info is complete ",
            "-1": "All"
        },
        "same_annotation": "The review conclusion is unanimous",
        "case_info_complete": "Complete Patient Info",
        "no_assignment": "No assignment",
        "assignment_B": "Assignment B",
        "assignment_A": "Assignment A",
        "approval_btn": "Approval",
        "topic_text": "Subject",
        "belonging_topic_text": "Subject",
        "end_comment_text": "Review conclusion",
        "new_create_comment": "New conclusion",
        "confirm_to_approve_B": "Are you sure to accept the review conclusion of high frame rate angiography?The conclusion cannot be modified after approval.",
        "confirm_to_approve_A": "Are you sure to accept the review conclusion of routine angiography?The conclusion cannot be modified after approval.",
        "project_name_title": "Project Name",
        "only_view_mode_tip": "In read-only mode and cannot modify",
        "review_info_submit_success": "Review info submit successfully",
        "review_info_save_success": "Review info save successfully",
        "judge_btn": "Decide",
        "confirm_notice_text": "This is an irreversible operation.do you confirm to submit?",
        "annotation_view_label": "review",
        "annotateCase_option": {
            "HCC": "HCC",
            "equal": "Equal",
            "vague": "Unclear or other",
            "spoke_like": "Spoke",
            "disordered_irregular_shape": "Disordered irregular shape",
            "coarse_twisted_shape": "Coarse twisted shape",
            "branch_like": "Branch",
            "centrifugal": "Centrifugal",
            "centripetal": "Centripetal",
            "whole_part": "Whole",
            "inset_part": "Internal/Central Department",
            "peripheral_part": "Peripheral Part",
            "random_distribution": "Random Distribution",
            "inset_high_strength": "Internal high enhancement,peripheral equal/low enhancement",
            "inset_no_strength": "Peripheral zonal high enhancement,internal low/no enhancement",
            "no_high": "No-High",
            "no_early": "No-Early",
            "early": "Early"
        },
        "distance_to_body": "Distance from lesion front to body surface(cm):",
        "is_CA_subside": "Does the lesion show contrast medium regression?",
        "diagnosis": "Angiographic diagnosis",
        "delay_phase": "Contrast delay period",
        "venous_phase": "Portal vein phase of angiography",
        "arterial_phase": "Angiographic arterial phase",
        "HFRR_delay_phase": "High frame rate contrast delay period",
        "HFRR_venous_phase": "Portal vein phase of high frame rate angiography",
        "HFRR_arterial_phase": "High frame rate angiography arterial phase",
        "delay_lesions_enchance_degree": "Degree of lesion enhancement at the end of the delay period:",
        "RCEH_delay_phase": "Delayed phase of conventional contrast-enhanced ultrasound",
        "portal_lesions_enchance_degree": "Degree of enhancement at the end of portal vein phase:",
        "RCEH_portal_phase": "Conventional contrast-enhanced ultrasonography in portal vein stage",
        "vascular_morphology": "Vascular morphology during enhancement of early rising lesions of arteries:",
        "CA_direction": "Contrast medium perfusion direction during enhancement:",
        "first_strength_area": "The site of enhancement for the first time:",
        "strength_distribution_features": "Strength distribution characteristics:",
        "strength_uniform_distribution": "Uniformity of strength distribution:",
        "lesions_enchance_degree": "Peak enhancement degree of lesion(compared with surrounding liver tissue):",
        "lesions_enchance_time": "Time of lesion enhancement(compared with surrounding liver tissue):",
        "RCEH_arterial_phase": "Conventional contrast-enhanced ultrasound arterial phase:",
        "case_date_text": "Date",
        "assign_succuss_text": "Auto-assign successfully",
        "confirm_to_assign_exam": "This operation will automatically assign the exam to two reviewers.Do you want to continue?",
        "data_examine_btn": "Data Review"
    },
    "numberOfImages": "Image counts",
    "HFRR_diagnosis": "High frame rate imaging diagnosis",
    "RCEH_diagnosis": "Normal contrast diagnosis",
    "character_type": {
        "0": "No",
        "1": "Ordinary User",
        "2": "Reviewer",
        "3": "Approver",
        "4": "Arbiter",
        "5": "Administrator",
        "6": "Purchaser"
    },
    "exam_status": {
        "1": "To be submitted",
        "2": "Pending review",
        "3": "Rejected",
        "4": "To be reviewed",
        "5": "To be decided",
        "6": "Approved",
        "-1": "All"
    },
    "sex": {
        "0": "Male",
        "1": "Female",
        "2": "Unknown"
    },
    "no_uniformity": "No-Uniformity",
    "uniformity": "uniformity",
    "export_empty_tip": "Please select at least one case",
    "export_fail_tip": "Case number {1} failed to load the picture. Do you want to continue exporting other cases?",
    "export_case": "Export",
    "number_text": "Serial Number",
    "please_input_nickname": "User nickname",
    "group_by": "Group",
    "reject_success_tip": "Case rejected successfully!",
    "reject_reason_no_white": "Reason for rejection cannot be blank!",
    "recent_two_month": "Last two months",
    "recent_one_month": "Last one month",
    "recent_two_week": "Last two weeks",
    "reject_reason_title": "Please enter the reject reason",
    "case_view_label": "view case",
    "view_btn": "View",
    "reject_btn": "Reject",
    "query_btn": "Query",
    "is_modify_authorition": "Do you want to modify the user's authorition",
    "authorition": "Authorition",
    "nickname": "Nickname",
    "assign_btn": "Review",
    "exam_type": "Exam Type",
    "case_num": "Case Number",
    "index_num": "Index",
    "case_status": "Case Status",
    "end_date": "end date",
    "date_to": "to",
    "start_date": "start date",
    "upload_datetime": "upload date",
    "permission_assignment": "Permission Assignment",
    "exam_view_title": "Exam View",
    "multicenter_title": "Multi Center",
    "select_from_friend_list": "Select from friends",
    "select_from_group_list": "Select from groups",
    "length_limit_of": "length limit of ",
    "server_disconnect": "server disconnected",
    "AGORA_MEDIA_DEVICE_STATE_TYPE": {
        "0": "READY",
        "1": "_ACTIVE",
        "2": "_DISABLED",
        "4": "_NOT_PRESENT",
        "8": "_UNPLUGGED",
        "16": "_UNRECOMMENDED"
    },
    "AGORA_MEDIA_DEVICE_TYPE": {
        "0": "AUDIO_PLAYOUT_DEVICE",
        "1": "AUDIO_RECORDING_DEVICE",
        "2": "AUDIO_RECORDING_DEVICE",
        "3": "VIDEO_CAPTURE_DEVICE",
        "4": "AUDIO_APPLICATION_PLAYOUT_DEVICE",
        "-1": "UNKNOWN_AUDIO_DEVICE"
    },
    "groupSetNameExists": "Group community already exists",
    "ota_update": "OTA update",
    "reverse_control_fail_another": "The request for remote control failed. The device may be under the control of another user",
    "disconnect_control": "Disconnect",
    "reverse_control_gain": "Gain：",
    "reverse_control_depth": "Depth：",
    "reverse_control_name": "Controlling ${1}'s ultrasond machine",
    "input_collection_name": "Please enter tag name",
    "group_favorite_total_text": "Total",
    "personal_favorite_text": "My favourites",
    "update_tag_success": "Update tag succeeded.",
    "select_at_least_item": "Please select at least one item for operation",
    "change_a_tag": "Change tag",
    "new_a_tag": "New tag",
    "exit_same_tag_name": "Same tag exists",
    "reverse_control_disconnect": "Remote control disconnected",
    "reverse_control_loading": "Waiting for the other side to respond the remote control",
    "reverse_control_close": "Are you sure to disconnect the remote control?",
    "reverse_control_reject": "The ultrasound side rejected your reomote control request.",
    "reverse_control_confirm_tip": "Whether to remotely control the ultrasound used by the user",
    "reverse_control_title": "Remote Control",
    "delete_group_favorite_tag_confirm": "Are you sure to delete this tag?",
    "group_favorite_text": "Group collection",
    "live_replay_second": "S",
    "live_replay_minute": "M",
    "live_replay_hour": "H",
    "private_comment": "Private comment",
    "current_live_status": "Status",
    "referral_introduce_A4": "After registration, fill in the referral code or be approved by the administrator to become an official user",
    "referral_introduce_Q4": "Q4:How to become an official user？",
    "referral_introduce_A3": "The App is at Me - Invite Registration; Windows Application at + Invite Registration",
    "referral_introduce_Q3": "Q3:Where is the referral code？",
    "referral_introduce_A2": "Ask other official users of MiCo+ for referral code",
    "referral_introduce_Q2": "Q2:How can I get a referral code？",
    "referral_introduce_A1": "The referral code is the certificate for other official users of MiCo+ to invite you to become an official user",
    "referral_introduce_Q1": "Q1:What is a referral code",
    "how_to_get_referral": "How to get",
    "no_referral_code": "No referral code？",
    "client_only_tip": "Download the APP to use this feature",
    "export_start_tip": "Download task started",
    "login_or_register_email": "Log in with Email verification, new email will be registered automatically",
    "login_or_register_mobile": "Log in with phone number verification, new number will be registered automatically",
    "live_detail": "Live Detail",
    "iworks_fail_label": "Failed to get score",
    "iworks_score_label": "Score:",
    "only_jpg_png": "Only allow uploading images in PNG and JPG formats",
    "not_support_del_invite_members": "Invited members can not be deleted.",
    "no_permission_to_open_room": "You do not have permission to open the Live Room",
    "cover_upload": "Upload the cover",
    "live_broadcast_cancel": "Cancelled",
    "live_broadcast_end": "Ended",
    "live_broadcasting": "Live Streaming",
    "begin_in_minute": "Coming soon...",
    "waiting": "Waiting",
    "live_room": "Live room",
    "remark_text": "Remark",
    "set_remark": "Edit Contacts",
    "quick_entry": "Quick entry",
    "login_need_password": "Administrator account, please login with account and password",
    "login_with_account": "Login via Account",
    "login_with_mobile": " Login/Register with SMS",
    "picture_unit": "P",
    "search_result_tip": "Search results for “{1}”",
    "hot_search_tip": "Hot",
    "delete_all_history_tip": "Are you sure to delete all search history?",
    "delete_history_tip": "Are you sure to delete this search history?",
    "live_invite_status3": "The live stream scheduled by {1} has been canceled.",
    "live_invite_status2": "The live stream conducted by {1} has ended.",
    "live_invite_status0": "The live stream scheduled by {1} has started.",
    "live_invite_status4": "The live stream scheduled by {1} is about to start.",
    "live_invite_status1": "{1} has scheduled a live stream.",
    "live_theme": "Live theme",
    "send_invitation": "Send invitation",
    "search_invite_member": "search friend/group/groupSet",
    "describe": "Describe",
    "available_live": "Live stream available",
    "my_booking_live": "The live I reserved",
    "edit_live": "Edit Live",
    "booking_live": "Live Reservation",
    "my_live": "My live broadcast",
    "upload_live_cover": "Please upload the live stream cover",
    "enter_live_des": "Please describe the live",
    "select_live_time": "Please select the live broadcast time",
    "enter_live_name": "Please enter the name of the live",
    "group": "Group",
    "friend": "Friends",
    "dissolve_live": "Do you want to dissolve the live?",
    "live": "Live",
    "moderator": "host",
    "mine": "mine",
    "reset_mobile_success": "Mobile phone number set successfully",
    "welcome_tip": "Welcome to MiCo+",
    "referral_success_tip": "The referral code you filled in is correct. You have become an official user",
    "fill_in_referral_code": "Fill in the referral code",
    "login_directly": "Seven days trial",
    "register_by_mobile": "Register with mobile number",
    "reset_password_way": "Please choose how to reset your password",
    "not_surpport_forget": "The intranet does not support password retrieval. Please contact the administrator.",
    "read_and_agree": "Please read and agree to the privacy policy first",
    "other_register_way": "Other register options",
    "other_login_way": "Other login options",
    "desensitization_reception": "Hide Patient Information",
    "ask_want_to_cancel_apply_speak_permission": "You have sent a raise of hand request, would you like to cancel the request?",
    "ask_want_to_apply_speak_permission": "You do not have the speaking permission. Do you want to apply?",
    "allows_members_self_mute": "Allows members to self-mute",
    "destroy_replace_text": "Logged Out User",
    "account_destroy_tip": "The account has been canceled",
    "unmute_all": "Unmute all",
    "members_manage_title": "Members Management",
    "is_agree_all_user_speak": "Whether to unmute all?",
    "is_agree_user_speak": "Whether to allow this user to speak?",
    "all_members_silenced": "Mute all and new members",
    "more_than_six_mute": "Mute for more than 6 people",
    "no_mute": "Turn on the mic after joining the live",
    "all_mute": "Mute after joining the live",
    "mute_setting_voice_ctrl_mode": "Mute control mode",
    "log_off_waring": "If you cancel the Ultracync account, you cannot use this account any longer or retrieve any content or information you added or contacted (even if you use the same mobile phone number/email to register again). Are you sure t o continue?",
    "admin_cannot_autologin": "The administrator account cannot log in automatically due to security restrictions",
    "cancel_account": "Cancel account",
    "unrecognized_device_type": "Unrecognized device type",
    "device_not_logged_server": "The device is not logged in to the server and is not connected with the same network",
    "incorrect_network": "Incorrect network type. Network disconnected.",
    "box_not_found": "Box not found",
    "switch_account": "Switch account",
    "account_logged_in": "This account has been logged in",
    "phone_no_login": "Not logged in on your phone",
    "share_risk_content": "When sharing information with other groups, please ensure that the members in that group are qualified and it is necessary to see the information, otherwise, it may lead to patient data leakage.",
    "codeMustRequired": "Login expired, please log in again",
    "userChangePwdNeedLogin": "Password has changed. Please log in again.",
    "email_verification_code_empty": "Email Verification Code cannot be blank.",
    "userTokenError": "Login expired, please log in again",
    "userOutOfTrail": "The trial time of the account has expired, please contact the administrator",
    "image_code_tip": "Image Verification Code",
    "image_code_empty": "Image Verification Code cannot be blank.",
    "verify_send_email": "Send Email : ",
    "verify_send_sms": "Send SMS : ",
    "verify_description": "This operation requires authentication:",
    "verify_title": "Authentication",
    "please_agree_privacy_policy": "Please agree to the MiCo+ Privacy Policy",
    "please_agree": "Please agree to the",
    "email_is_invalid_input_again": "Email is invalid. Please enter again!",
    "verify_with_email": "Verify with email",
    "verify_with_mobile": "Verify with mobile phone number",
    "forget_password_get_email_code": "Send",
    "email_verification_code": "Verification Code",
    "register_email": "Email",
    "equipment_testing": "Audio and Video settings",
    "slide_tip": "You can drag the mouse left and right to switch pictures. ",
    "case_database_error_code": "Search failed. Error:",
    "case_database_tip": "Currently, only breast cases are included and this will be expanded in the future.",
    "private_group_not_share": "Private groups cannot be shared to WeChat.",
    "case_database_id": "ID",
    "image_count": "Images",
    "live_playback": "Live Playback",
    "product_name_value_ce": "MiCo+",
    "product_name_value_cn": "MiCo+ Remote Imaging System",
    "software_description_ce": "Mindray medical imaging IT solution MiCo+ aims to build a professional social community for doctors. With the MiCo+, you can meet the demands of remote quality control and consultation, teaching and training, as well as workshop community and other smart medical applications.@2021 Shenzhen Mindray Bio-Medical Electronics Co., Ltd. All rights reserved",
    "software_description_cn": "Mindray remote imaging platform MiCo+ aims to build a professional social community for doctors. With the UltraSync, you can perform remote quality control and realtime/offline consultation, teaching and training tasks, online case management as well as other smart medical applications.@2021 Shenzhen Mindray Bio-Medical Electronics Co., Ltd. All rights reserved.",
    "device_notice_camera_insert": "connect camera device ",
    "device_notice_camera_pull_out": "Unplug the camera device",
    "case_require_no_filled": "Patient information cannot be blank (items with * is optional, while others are required)",
    "case_database_title": "Breast Case",
    "confirm_update_report": "The report needs to be updated.Do you want to continue?",
    "refute_reason_text": "Rejection reason：",
    "live_management": "Live Stream Management",
    "privacy_welcome_p3": "Please review the user manual after logging in",
    "privacy_welcome_p2": "We will remind you in a pop-up window when you download the application for the first time or use the functions listed above. You can make choice based on your needs. See MiCo+ Privacy Policy for more details. MiCo+ will strictly protect your personal information and ensure information security. We have formulated the MiCo+ Privacy Policy in accordance with relevant laws. Please carefully read and fully understand the terms in the agreement before clicking to agree to the following policy.",
    "privacy_welcome_p1": "Dear users, thanks for using MiCo+. MiCo+ is a social networking platform specified for doctor community, developed by Shenzhen Mindray Bio-Medical Electronics Co., Ltd. This privacy policy is designed to inform you of how MiCo+ will collect, use, and store your personal infromation and what rights you have in relation to your personal infromation.",
    "privacy_welcome_title": "Welcome to MiCo+",
    "ultrasync_privacy_protocol": "MiCo+ privacy protocol",
    "exit_group_tip": "quit the group chat",
    "join_group_tip": "joined group chat",
    "add_btn": "Add",
    "replace_btn": "Replace",
    "autograph": "Signature:",
    "examining_doctor": "Examining Doctor：",
    "report_time": "Report Time：",
    "ultrasonic_prompt": "Ultrasonic Prompt：",
    "ultrasonic_discovery": "Ultrasonic Discovery：",
    "exam_position": "Exam Position",
    "sending_physician": "Reception doctor：",
    "bed_number": "Bed Number：",
    "inpatient_number": "Hospitalized Number：",
    "outpatient_number": "Clinic Number：",
    "department_title": "Department：",
    "ultrasound_number": "Ultrasound Examination No.",
    "report_subtitle": "Ultrasonic Examination Report",
    "report_title": "Hospital Ultrasound Department",
    "registration_certificate_no": "Registration Certificate No.",
    "product_model": "Model",
    "product_name": "Product Name",
    "conversation_not_init": "The conversation has not been initialized. Please try again later.",
    "no_realtime_tip": "There is no real-time video. Please wait. ",
    "will_open_conversation": "（Open the ${subject} conversation interface）",
    "enter_live": "Enter Live stream",
    "enter_gallery": "Enter Gallery",
    "browser_no_support_video": "Your browser does not support following tags",
    "browser_no_support_audio": "Your browser does not support following tags",
    "file_has_downloaded": "The file has been downloaded.",
    "save_path": "Save path:",
    "select_groupset": "Please select community first.",
    "groupset_not_activity": "There is no group activity in this community.",
    "select_statistics": "Please select statistical content",
    "details": "Details",
    "ultrasync_live": "MiCo+ live broadcast",
    "conference_invitation": "Conference invitation:",
    "unsurpport_pdf": "Preview PDF files are not supported in your mobile version.",
    "file_storage_manage": "Manage Current Account File Cache",
    "privacy_statement_title": "MiCo+ personal information protection policy statement",
    "domain_name_tip": "No need to input HTTPS://",
    "other_data": "Other data",
    "file_upload_exception": "File uploading error",
    "ftp_path_tip": "Tips: Storage address format is an IP address or domain name.",
    "reselect_upload_file": "Please reselect the upload file",
    "copyright": "SHENZHEN MINDRAY BIO-MEDICAL ELECTRONICS CO., LTD. All Rights Reserved.",
    "no_videocamera_be_detected": "No camera was detected. Please insert the camera device first.",
    "videocamera_muti_devices": "Multiple Cameras detected.Please select one.",
    "videocamera_title": "Camera",
    "select_micro_device_text": "Multiple microphones are detected. Please select an appropriate device.",
    "watch_supply_case_title": "View Information",
    "case_exam_status": {
        "judgeSubmit": "Approved",
        "reviewed": "To be decided",
        "assigned": "To be reviewed",
        "reject": "Rejected",
        "submited": "Pending review",
        "saved": "To be submitted",
        "unsubmit": "To be submitted"
    },
    "patient_case_status": "Exam Status：",
    "supply_case_close_btn": "Close",
    "privacy_policy": "Privacy Policy",
    "file_downloading": "Downloading...",
    "open_file": "Open the file",
    "file_download_progress": "Download Progress:",
    "less_than_1kb": "Less than 1KB",
    "file_size": "File Size:",
    "file_name": "File Name:",
    "wait_download_file": "Downloading other files. Please wait…",
    "tip_file_open_fail": "The file cannot be opened.",
    "tip_file_download_success": "File download completed!",
    "tip_file_download_fail": "File download failed!",
    "confirm_delete_file": "Do you want to delete the selected file?",
    "image_corruption_text": "Unsupported image format. Please try another image.",
    "ban_to_negative_number": "This item cannot be negative. ",
    "mobile_number_is_invalid_input_again_cn": "Invalid phone number. Please enter the correct number!",
    "invite_you_join_group": "Invite you to the group:",
    "import_licence": "Import license",
    "no_more_text": "No more message",
    "playing_video_tip": "You are watching the live stream. This function is not available.",
    "login_fail_with_lock_tip": "The test failed {2} within {1} minute. If verification fails {3} times, the account will be locked for {4} hours.",
    "enhance_password_tip": "The strength of password is weak. Please change the password!",
    "please_choose_topic_text": "Please Select a Topic",
    "now_topic_text": "Current topic:",
    "liver_topic_text": "Liver Topic",
    "gall_bladder_text": "Gallbladder Topic",
    "gall_bladder_no_file_preview": "The item cannot be uploaded or previewed under the gallbladder topic.",
    "no_upload_report_text": "No attachments are uploaded. Please upload the attachment before preview.",
    "upload_file_error_text": "Uploading attachment failed.",
    "delete_groupset_tip": "The group community has been deleted",
    "back_button": "Back",
    "ref_res_expired": "Invalid",
    "init_supply_case_fail": "Initializing case information failed.",
    "supply_case_fail": "Updating case information failed. Please contact the administrator.",
    "supply_case_success": "The case information is updated successfully.",
    "choose_date_time": "Select Date",
    "ultrasound_diagonse_time": "Ultrasound Exam Time",
    "cdfi_blood_flow": "CDFI Blood Flow",
    "echo_text": "Echo",
    "sizeof_lesions_two": "Lesions size (2cm diameter line)",
    "sizeof_lesions_one": "Lesions size (1cm diameter line)",
    "target_lesions_site": "Target Lesions Site",
    "number_of_gallbladder_lesions": "Number of Gallbladder Lesions",
    "number_of_intrahepatic_lesions": "Number of Intrahepatic Lesions",
    "grey_scale_ultrasound_text": "Grey Scale Ultrasound",
    "pathologic_diagonsis_text": "Pathological Diagnosis",
    "more_mri_diagonse_text": "MRI Diagnosis Follow-up  (≥6 months)",
    "first_time_mri_diagonse_text": "First MRI Diagnosis",
    "mri_diagonse_text": "MRI Diagnosis",
    "cirrhosis_text": "Cirrhosis",
    "hepatitis_text": "Hepatitis",
    "patient_history_text": "Patient History",
    "tumor_markers_text": "Tumor Markers",
    "indirect_bilirubin_text": "Indirect Bilirubin (IB, µmol/l)",
    "direct_bilirubin_text": "Direct Bilirubin (DB, µmol/l)",
    "total_bilirubin_text": "Total Bilirubin (TB, µmol/l)",
    "alanine_aminotransferase_text": "Alanine Aminotransferase (ALT, IU/l)",
    "aspartate_aminotransferase_text": "Aspartate Aminotransferase (AST, IU/l)",
    "biochemical_examination_text": "Biochemical Examination",
    "anti_hiv_text": "Anti-HIV",
    "treponema_antibody_text": "Treponema Pallidum Antibody",
    "anti_hcv_text": "Anti-HCV",
    "four_before_operation_text": "4 exam items before operation",
    "select_placeholder_text": "Select",
    "no_type": "<span class=\"red\">Not filled</span>",
    "no_need_follow_diagonse": "No follow-up is required",
    "mixed_echo": "Mixed echoes",
    "high_level": "High",
    "middle_level": "Middle",
    "low_level": "Low",
    "mutiple": "Multiple",
    "sigle": "Single",
    "adenomatous_polyp": "Adenomatous polyp",
    "cholesterol_polyp": "Cholesterol polyp",
    "matastatic_carcinoma": "Metastatic carcinoma",
    "poorly_differentiated_hcc": "Poorly differentiated HCC",
    "moderately_poorly_differentiated_hcc": "Moderately poorly differentiated HCC",
    "moderately_differentiated_hcc": "Moderately differentiated HCC",
    "moderately_well_differentiated_hcc": "Moderately well differentiated HCC",
    "well_differentiated_hcc": "Well differentiated HCC",
    "no_FNH": "Non-FNH",
    "negative": "Negative",
    "positive": "Positive",
    "ICC": "ICC",
    "FNH": "FNH",
    "no": "No",
    "yes": "Yes",
    "hepatitis_five_items_text": "Hepatitis B five items",
    "blood_cell_text": "Blood platelet (x10^9/L)",
    "middle_size_cell_rate": "Granulocyte percentage (%)",
    "white_cell_text": "White blood cells (x10^9/L)",
    "red_cell_text": "Red blood cells (x10^12/L)",
    "supply_patient_age": "Age (Year)",
    "supply_patient_name": "Name",
    "supply_blood_routine_text": "Blood routine",
    "supply_patient_info_text": "Patient Info",
    "supply_case_title": "Add Info",
    "cannot_share_qrcode_text": "The private group cannot be shared through QR code.",
    "is_recording_text": "Recording...",
    "skip_to_unread_text": "Jump to Unread",
    "consulation_review_text": "Live review",
    "exam_patient_text": "Patient：",
    "withdraw_chat_message_fail": "Recall failed. ",
    "withdraw_chat_message_fail_sended_by_others": "The messages sent by other users cannot be recalled.",
    "send_recoding_text": "Release to Send",
    "other_cancel_realtime_voice_text": "Call cancelled by the caller",
    "self_cancel_realtime_voice_text": "Duration: ",
    "exam_conversation_creator_tag": "Created by",
    "groupset_manager": "Administrator",
    "groupset_member_title": "Choose Members",
    "next_step_text": "Next",
    "select_audio_device_text": "Multiple voice output devices are detected. Please select an appropriate device.",
    "exceeded_max_withdrawal": "Maximum recall time exceeded.",
    "re_edit": "Edit",
    "revocation_message_by_other": "Recalled a message",
    "revocation_message_by_self": "You recalled a message. ",
    "revocation_message": "Recall",
    "get_country_list_fail": "Loading list failed.",
    "search_groupsets_text": "Search community",
    "search_recent_chat_text": "Search recent chats",
    "choose_country": "Select country or region",
    "in_total_text": "Total",
    "group_has_deleted_text": "This group has been deleted.",
    "select_hospital": "Select Hospital",
    "check_groupset_exist_text": "Community search failed. Please check if the community exists!",
    "search_type_error": "Search type error!",
    "display_all_data_error_txt": "Search errors!",
    "search_none_text": "No related content found. Please try another keyword.",
    "real_time_warning_message": "Workstation data cannot be sent under live stream.",
    "warning_title": "Warning",
    "tv_wall_warning_message": "Workstation data cannot be sent under TV wall mode. ",
    "device_notice_unusual_exit": "Exit",
    "device_notice_unusual_continue": "Continue",
    "device_notice_unusual_message": ". Click [Continue] and the Voice is abnormal. Click [Exit] to exit the live stream. ",
    "device_notice_unusual_title": "Notice of Abnormality",
    "device_notice_now_device_pull_out": "The currently used microphone is unplugged.",
    "analyze_min_save_time": "The minimum saving time is ",
    "analyze_max_save_time": "The maximum saving time is ",
    "analyze_avarage_save_time": "The avarage saving time is ",
    "analyze_min_download_time": "The minimum download time is ",
    "analyze_max_download_time": "The maximum download time is ",
    "analyze_avarage_download_time": "The average download time is ",
    "analyze_min_text": "The minimum is ",
    "analyze_max_text": "The maximum is ",
    "analyze_avarage_text": "The average size is ",
    "analyze_total_download_text": "Total downloaded pictures",
    "charts_sunday_text": "Sunday",
    "charts_saturday_text": "Saturday",
    "charts_friday_text": "Friday",
    "charts_thursday_text": "Thursday",
    "charts_wednesday_text": "Wednesday",
    "charts_tuesday_text": "Tuesday",
    "charts_monday_text": "Monday",
    "charts_online_device_text": "Number of online devices：",
    "charts_device_unit_text": "",
    "charts_device_total_text": "Total number of devices：",
    "parsetime_seconds_text": " Seconds",
    "parsetime_hours_text": " Hours",
    "not_online_text": "Offline",
    "online_text": "Online",
    "online_rate": "The Online Rate",
    "unsupported_location_notice": "Positioning is not supported in this area temporarily. Please contact the administrator to upgrade the Map Data package!",
    "repeat_client_text": "This link has been opened elsewhere.",
    "link_connected_fail_text": "This link is disconnected. Please reopen the window.",
    "link_expired_text": "This link is invalid. Please reopen the window.",
    "groupset_no_conversation_notice": "This group community has not created a conversation! Please close the window, and start a conversation first.",
    "device_number_text": "Number of Devices",
    "ultrasonic_today_text": "Today's Real-Time Quality Control Statistics",
    "device_online_rate_text": "Device online rate",
    "back_text": "Back",
    "remote_QC_image_quality_text": "Quantity of Images under Remote Quality Control",
    "live_broadcast_volume_text": "Live stream Time (duration)",
    "accumulation_text": "Cumulative",
    "the_day_text": "The day",
    "live_control_statistics_text": "Real-Time Quality Control Statistics (Duration)",
    "remote_ultrasonic_center_text": "Remote Ultrasound Quality Control Data Center",
    "no_support_webrtc_text": "The current browser does not support webrtc, and the real-time live broadcast function is limited. Please use the App version.",
    "voice_device_error_text": ". Please insert the voice device and restart the App.",
    "rt_voice_connect_error_prefix": "Voice connection failed.",
    "webrtc_connect_fail_text": "Webrtc signaling server connection failed. Please restart the server.",
    "room_need_close_affix": ". Please restart the live stream.",
    "join_room_err_prefix": "Room enter error",
    "no_speak_permission": "Voice is not allowed when you log in via browser.\n Please install App  if voice is required.",
    "groupset_text": "Group Community",
    "no_found_group_text": "Group not found",
    "no_found_user_text": "User not found",
    "more_text": "More",
    "recent_chat_text": "Recent Chats",
    "group_chat_text": "Group Chats",
    "contact_text": "Contacts",
    "wechat_invite_affix": "'s wechat invitation",
    "wechat_invite_prefix": "Pass",
    "my_groupset": "My Community",
    "group_set_profile": "Profile Photo",
    "delete_groupset_text": "Delete Group Community",
    "already_friend_msg": "We are friends now. Let's chat!",
    "paste": "Paste",
    "copy": "Copy",
    "copy_text_success": "Copy succeeded.",
    "join_group_by_qrcode_tips": "'s QR Code",
    "scaned_tip": "Join the group chat by scanning ",
    "qrcode_expired": "QR Code has expired.",
    "add_group_successful": "Successfully joined the group",
    "auto_add_sharer_text": "Automatically add me as friend when join the group",
    "group_card_apply_btn": "Join Group",
    "group_visiting_card_title": "Group QR Code",
    "no_description_tip": "No Description",
    "groupset_delete_attendee": "Delete Members",
    "groupset_add_attendee": "Add Members",
    "groupset_description": "Group Description",
    "choose_only_one_file_to_handle": "Select only 1 file to process.",
    "group_qrcode_card_notice_text": "The QR code will expire in 7 days.",
    "group_qrcode_card_text": "Group QR Code",
    "unrecognized_qrcode": "Unrecognized QR Code",
    "no_group_id": "Group ID Acquisition Error from the QR Code",
    "no_user_id": "User ID Acquisition Error from the QR Code",
    "qrcode_card_notice_text": "Scan the QR Code to add me",
    "qrcode_card_text": "My QR Code",
    "skip_set_password": "Skip",
    "set_password_first": "Please set the password first!",
    "filter_hospital": "Search hospitals",
    "input_info_check_tip": "Enter correct characters and length.",
    "length_limit_info": "(Length<=20)",
    "input_info_check_err": "Only Chinese and English characters or numbers can be entered.",
    "name_null": "The name is blank.",
    "live_name": "Enter Live Name",
    "input_new_name": "Enter New Name",
    "rename": "Rename",
    "exam_end_time": "End",
    "exam_start_time": "Start",
    "exam_mode_label": "Mode:",
    "exam_patient_sex": "Gender",
    "exam_patient_age": "Age",
    "exam_patient_name": "Name",
    "all_iworks": "All iWorks Protocols",
    "more_iworks_text": "More",
    "exception_to_login_again": "Unknow error.Please login again!",
    "index_nav_files": "File",
    "library_video": "Video",
    "library_image": "Image",
    "protocol_title": "Protocol Detail",
    "view_not_uploaded": "The view has not been uploaded.",
    "click_to_refresh": "Click to refresh",
    "cancel_download_confirm": "Do you want to cancel the download task?",
    "view_txt": "View",
    "checkout_protocol": "Check the protocol",
    "export_running": "Some download tasks have not been completed. Multi-tasks download is not supported.",
    "media_transfer": {
        "error": {
            "add_task_error_waiting_queue_full": "Tasks cannot be created when the queue is full! Please try again later!",
            "add_task_error": "Creating task failed. Please try again later."
        },
        "clip_tip_startup": "Start clipping. Please check the progress in Task Management. After the clipping is completed, you can save, forward, and download the videos.",
        "creating_task": "Creating task. Please wait."
    },
    "search_input_key": "Please enter the key words.",
    "version": "MiCo+ version",
    "protocol_version": "Protocol Version",
    "doppler_version": "Doppler version",
    "ecr_version": "ECR version",
    "product_manufacturer": "Product Manufacturer",
    "product_type": "Product type",
    "hospital_associate": "assistant",
    "hospital_director": "director",
    "hospital_location": "Hospital location",
    "hospital_address": "Hospital address",
    "hospital_name": "Hospital name",
    "mac_addr": "MAC address",
    "loginLoading": "Automatically logging in",
    "task_manager": {
        "media_transfer": {
            "error": {
                "delete_task_error": "Failed to delete tasks!",
                "query_task_error": "Failed to query tasks!"
            },
            "status_list": {
                "1": "Waiting",
                "2": "Processing",
                "3": "Success",
                "4": "Fail",
                "5": "Cancel"
            },
            "operation_import": "Import",
            "progress": "Progress",
            "status": "Status",
            "image_name": "image Name",
            "image_source": "Image Source",
            "task_id": "Task ID"
        },
        "title": "Task manager"
    },
    "add_association": "Add",
    "association_hospitals": "Association hospitals",
    "bi_data_display": "BI data display",
    "location": "Location",
    "device_has_no_network": "Scan-to-login failed:the device is not connected to the network",
    "groupset_msg_empty": "No Image",
    "select_all_group": "Select all groups",
    "select_all_friend": "Select all friends",
    "get_groupset_exam_fail": "Obtaining exam list failed.",
    "get_groupset_detail_fail": "Obtaining exam list failed.",
    "groupset_detail_navbar": "Uploaded Exam List",
    "groupset_exam_count": "Number of Exams sent",
    "groupset_video_count": "Number of video sent",
    "groupset_image_count": "Number of pictures sent",
    "groupset_navbar": "Group members",
    "delete_group_set_success": "Deleting group succeeded!",
    "delete_group_set_fail": "Deleting group failed. Please try again later.",
    "delete_groupset_confirm": "Are you sure to delete this group community?",
    "update_group_set_success": "Updating group succeeded!",
    "update_group_set_fail": "Updating groups failed. ",
    "query_groupset_list_fail": "Obtaining group list failed. ",
    "create_group_set_success": "Creating new group succeeded!",
    "create_group_set_fail": "Creating group failed. Please try again later.",
    "group_set_name_empty": "Group Community name cannot be empty",
    "edit_group_set": "Edit Group",
    "group_list_empty": "The group list is empty. Please join a group first.",
    "group_set_name": "Subject",
    "create_group_set": "Create Group Community",
    "group_statistics": "Group statistics",
    "group_set_statistics": "Group Community statistics",
    "mute_message_number": "message(s)",
    "mute_notifications": "Mute Notifications",
    "female": "Female",
    "male": "Male",
    "exam_manager": {
        "error": {
            "sendto_target_invalid": "Forwarding destination is invalid!",
            "sendto_error": "Sending failed!",
            "mutiple_selected_unmatch": "{1} images were sent by others, and the exam information cannot be set! Continue to set up available images?",
            "single_selected_unmatch": "Exam information cannot be set for images sent by others!",
            "conversation_not_open": "Connection is not established. Please try later!",
            "patient_id_repeated": "Patient ID has been used. Please enter again!",
            "patient_id_empty": "Patient ID cannot be blank!",
            "import_exam_image_error": "Importing image to patient database failed!",
            "new_exam_error": "Creating new patient failed!",
            "search_exam_error": "Searching for cloud exam list failed!"
        },
        "sendto_success_tip": "Sending succeeded. Please check in {1}.",
        "import_exam_image_success_tip": "Importing succeeded. Please check in {1}.",
        "import_exam_image_tip": "Do you want to import to patient {1}?",
        "ai_image_info": "AI Image information",
        "image_info": "Image information",
        "exam_info": "Exam information",
        "import_exam_image_confirm": "Importing confirmation",
        "new_exam": "New patient",
        "title": "Select or create a new patient"
    },
    "operation": "Operation",
    "exam_time": "Exam Time",
    "patient_id": "Patient ID",
    "online_time": "online time(min)",
    "user_id": "User ID",
    "cellphone": "Phone Number",
    "time": "Time",
    "resource_id": "resource ID",
    "exam_id": "exam ID ",
    "series_number": "series number",
    "group_name": "group name",
    "user_name": "user name",
    "active": "active",
    "optional": "(Optional. Registration without approval )",
    "users_without_hospital": "Users that does not belong to any hospital ",
    "all_users_of_this_hospital": "All users in this hospital",
    "hospital_user": "Hospital/User:",
    "unread_message_tip": "new messages",
    "someone_mention": "[Someone mentioned you]",
    "mention_dialog_title": "Mention",
    "reset_mobile_tip_reset_mobile": "Setting new mobile phone number:",
    "verify_password": "Verify password:",
    "reset_login_name_tip_reset_login_name": "Set new login name:",
    "reset_login_name_success": "Login name set successfully",
    "reset_login_name": "Reset login name",
    "group_by_month": "Grouping by Month",
    "group_by_week": "Grouping by Week",
    "group_by_day": "Grouping by Day",
    "sort_by_upload_ts": "Sorting by uploading time",
    "sort_by_exam_ts": "Sorting by exam time",
    "exam_group_separation_char": " to ",
    "in_consultation_text": "In Consulting",
    "msg_review_tip": "Mark message as read",
    "review_all": "Mark all as read",
    "share_link_to_wx": "Share links to WeChat friends",
    "device_bar_type": {
        "6": "Doppler",
        "7": "MiCo+ Box"
    },
    "query_faq_fail": "Inquiring FAQ failed!",
    "faq_title": "FAQ",
    "delete_conference_tip": "Cancel the meeting.",
    "conference_begin_tip": "The meeting will be started in 5 minutes",
    "reserved_conference_tip": "Reserve a meeting.",
    "reserved_success": "Reserve succeeded",
    "delete_reserved_tip": "Are you sure to delete this reservation?",
    "reserved_duration_illegal": "The conference duration should be longer than 5 minutes.",
    "reserved_starttime_illegal": "Start time cannot be earlier than the current time",
    "reserved_overtime": "The reserved meeting duration exceeds 8 hours.",
    "add_reserved_conference": "Add",
    "conference_subject_empty": "Please enter the meeting subject.",
    "end_time": "End Time",
    "start_time": "Start Time",
    "reserved_conference_time": "Conference time",
    "reserved_conference_date_tip": "Select date",
    "reserved_conference_date": "Conference date",
    "reserved_conference_subject": "Conference subject",
    "reserved_conference": "Reserve a meeting",
    "notification_body": {
        "new_message": "You have a new message.",
        "friend_apply": "You have a new friend request.",
        "join_group": "You have a new group application."
    },
    "notification_reqeust_tip": "To receive the message in time, please turn on “Allow Message Notifications”.",
    "scan_room_list_none": "No tv wall videoes available now",
    "tv_wall_setting_edit_error": "TV wall setting failed!",
    "scan_room_sort_by_name": "Name",
    "scan_room_sort_by_creation_ts": "Create time",
    "scan_room_sort": "Order",
    "scan_room_list": "TV wall list (Drag to adjust order)",
    "tv_wall_setting": "TV wall settings",
    "sharing_starting": "Starting sharing. Please wait…",
    "loading_module_text": "Loading module…",
    "loading_fail": "Loading failed. Click reload.",
    "login_sms_verification_code_empty": "SMS Verification Code cannot be empty.",
    "login_mobile_phone_empty": "Mobile phone number cannot be empty.",
    "edit_txt": "Edit",
    "reset_mobile": "Reset mobile phone number",
    "reset_password_fail": "FResetting password failed!",
    "set_admin_error": "Setting {1} as administrator failed. Please try again!",
    "set_admin_confirm": "Set {1} as the administrator. You will no longer be the administrator for this group. Are you sure to continue?",
    "set_admin": "Setting administrators",
    "download_app_tip": "Scan QR code to download Ultracync",
    "file_transfer_assistant": "File Transfer",
    "service_accounts": "Service ID",
    "referral_code_generate_err": {
        "time_out": "Timeout. Please try again!",
        "database_err": "Database error. Please try again!",
        "none_referral_code": "No referral codes. Please try again!",
        "unknown_err": "Unknown error. Please try again!"
    },
    "referral_code_tip_expire": "The referral code expires after {1}!",
    "referral_code_tip": "Tips: You can use the referral code to register as formal users. No need to be approved by the administrator.",
    "referral_code_generate": "Refresh referral code",
    "invite_registration": "Invite Registration",
    "search_friend": "Search friend",
    "referral_code_is_invalid_input_again": "Referral code must be 6 digits or empty. Please try again.",
    "referral_code": "Referral code",
    "mindray_library": "Library",
    "reupload_frame_tip": "The examination file cannot be resent automatically. Please select and send the consultation file again.",
    "not_login_and_fail_to_start_rt_video": "Real-time Ultrasound is unavailable without login.",
    "history_tip": "The above is historical information.",
    "switch_language_to_shoutdown": "Language switching needs to be restarted to take effect. Are you sure to continue?",
    "push_image_mode_set_fail": "MiCo+ Pusher image mode setting failed! Error message: {1}.",
    "push_image_mode_set_succ": "The set of image mode of the MiCo+ Pusher succeeded!",
    "push_image_mode_setting": "Setting image mode of the MiCo+ Pusher now. Please wait!",
    "push_image_mode_confirm_to_set": "Setting image mode of the MiCo+ Pusher. Are you sure to continue?",
    "push_image_mode_fluency": "Fluency",
    "push_image_mode_clarity": "Clear",
    "push_image_mode": "Image mode of MiCo+ Pusher",
    "realtime_video_review_text": "Live playback",
    "realtime_video_text": "Realtime Video",
    "report_text": "Report",
    "change_voice_device_need_to_close_realtime": "The modification of the audio device needs to restart the live stream to take effect.",
    "can_not_change_voice_device_in_browser": "The default audio device of the system cannot be modified by the browser. Please modify the default audio device in Windows APP.",
    "history_version_introduce_content": "[Content] ",
    "history_version_introduce_time": "[Time] ",
    "history_version_introduce_version": "[Version] ",
    "history_version_title": "Main updates",
    "query_history_version_fail": "Inquiring version history failed!",
    "history_version_introduce": "Version Introduction",
    "history_version": "Version History",
    "only_creator_can_edit": "Only group owner and manager can edit.",
    "no_service_tip": "No service description",
    "no_announcement_tip": "No group announcement",
    "is_save_change": "Do you want to save the changes?",
    "semi_public_group_tip": "Speaking in public group is not allowed unless being approved. Are you sure to continue?",
    "security_restrictions": "Limited by company security mechanism, this function is not available in company intranet.",
    "send_realtime_to_conversation": "Real-time Ultrasound",
    "send_uf_to_conversation": "Sending UF",
    "no_BI_RADS_features_results_tip": "No summary conclusion",
    "no_BI_RADS_features": "No analyzed conclusion",
    "results_reference_only": "Note: This analysis result is from the Dr.M analysis and is only for reference.",
    "BI_RADS_features_results_tip": "Summary conclusion: ",
    "BI_RADS_features": {
        "0": {
            "0": "Parallel to skin",
            "1": "not parallel to skin"
        },
        "1": {
            "0": "Oval",
            "1": "Round",
            "2": "Irregular"
        },
        "2": {
            "0": "Circumscribed",
            "1": "Indistinct margin",
            "2": "，Angular margin",
            "3": "，Microlobulated",
            "4": "，Spiculated margin"
        },
        "3": {
            "0": "Anechoic",
            "1": "Isoechoic",
            "2": "Complex cystic and solid",
            "3": "Hypoechoic",
            "4": "Heterogeneous"
        },
        "4": {
            "0": "Enhancement ",
            "1": "No posterior features ",
            "2": "Shadowing",
            "3": "Combined pattern"
        },
        "5": {
            "0": "No calcifications in a mass",
            "1": "Calcifications in a mass"
        },
        "6": {
            "0": "No blood flow",
            "1": "Marginal blood flow",
            "2": "Internal blood flow"
        }
    },
    "not_exam_picture": "Non-exam picture",
    "sort_by_time": "Sorting by adding to favorite time",
    "sort_by_exam": "Sorting by exam",
    "sort_by_group": "Sorting by groups",
    "nls_probationary_expiry_tip": "The trail of the current account will expire on {1}. Please contact the administrator to approve the account before it can be used again.",
    "multi_select": "Multi Select",
    "no_new_version": "No new version",
    "confirm_update_to_new_version": "Are you sure to update to the new version?",
    "version_update": "Version Update",
    "cur_version": "Current version: ",
    "sned_to_analyze_tip": "Picture has been sent to the Image Processing.",
    "action_analyze_text": "Image Processing",
    "mindray_analyze": "Dr.M Analysis",
    "ai_analyze": "Dr.M",
    "click_here": "Click here",
    "share_live_addr_tip4": "Sharing method 3:",
    "share_live_addr_tip3": "Copy the link address and open it in Google Chrome.",
    "share_live_addr_tip2": "Sharing method 2: taking photos of the QR code with mobile phone and share the photos",
    "share_live_addr_tip1": "Sharing method 1: Access the live page through WeChat Scanning and click the upper right corner for sharing.",
    "get_live_addr_error": "Obtaining the live stream address failed.",
    "set_record_name": "Set the recording name",
    "live_address": "Live stream address",
    "auto_analyze": "The images are automatically processed. ",
    "from_ai_analyze": "From Dr.M",
    "share_analyze_result": "The image processing results are shared:",
    "can_not_analyze_video": "Video file analysis is not supported.",
    "analyze_error": "No lesions found",
    "analyzing": "Analyzing...",
    "see_trace_tip": "Click the image to view the trace.",
    "malignant_probability": "Probability of malignancy:",
    "bengin_probability": "Benign probability:",
    "analyze_fail_tip": "Analysis failed",
    "analyze_result_tip": "Analysis result:",
    "close_consultation": "Exit consultation",
    "export_comment_fail": "Exporting comments failed.",
    "export_comment": "Export",
    "multi_audio_device": "Multiple audio devices detected, please select the required device first",
    "record_no_network": "Voice recording cannot be used when the network is disconnected.",
    "admin_hospital_delete": "Delete Hospital ?",
    "admin_hospital_name_empty": "Hospital name cannot be empty",
    "admin_user_id": "User ID",
    "admin_active_user_tip": "Are you sure to activate this user?",
    "admin_forbidden_user_tip": "Are you sure to deactivate this user?",
    "admin_relief_director_tip": "Are you sure to remove the director level?",
    "admin_setup_director_tip": "Are you sure to remove the director's permission?",
    "admin_revoking_permissions_tip": "Are you sure to remove administrator permissions?",
    "admin_granting_permissions_tip": "Are you sure to authorize administrator permissions?",
    "admin_pass_empty": "Please select at least one user.",
    "admin_add": "Add",
    "admin_hospital_name": "Hospital Name",
    "admin_revoking_permissions": "Remove permissions",
    "admin_granting_permissions": "Give Permissions",
    "admin_disabled": "Disabled",
    "admin_activation": "Activate",
    "admin_activation_and_disabled": "Activate/Deactivate",
    "admin_setting_password": "Setting Password",
    "admin_is_director": "Are you the director? ",
    "admin_is_manager": "Are you the administrator? ",
    "admin_batch_pass": "Batch Pass",
    "admin_pass": "Pass",
    "admin_approve": "Approve",
    "admin_referee_nickname": "Recommended by",
    "admin_approver_nickname": "Approved by",
    "admin_register_date": "Registration Time",
    "admin_login_name": "Account Name",
    "admin_tab_hospital_manage": "Hospital management",
    "admin_tab_user_manage": "User Management",
    "admin_tab_approve": "Registration Approval",
    "admin_account_not_admin": "Non-administrator account",
    "admin_login_title": "Background Management Login Platform",
    "register_workstation_fail": "Workstation registration failed:",
    "register_workstation_success": "The workstation is registered successfully.",
    "stop_test_microphone": "Stop testing microphone",
    "stop_test_speaker": "Stop testing speakers",
    "microphone_input_strength": "Microphone input strength",
    "testing_microphone": "After the speaker test is passed, click on me to test the microphone. Please speak to the microphone.",
    "testing_speaker": "Click on me to test the speaker to see if you can hear the music.",
    "no_microphone_be_detected": "No microphone was detected. Please insert the microphone device first.",
    "no_speaker_be_detected": "No speaker was detected. Please insert the speaker device first.",
    "microphone_muti_devices": "Multiple Microphone devices detected",
    "speaker_muti_devices": "Multiple speaker devices detected",
    "microphone_title": "Microphone",
    "speaker_title": "Speaker",
    "ultrasync_box_mac_empty_tip": "If the MiCo+ Pusher MAC is empty, the pusher is not bound.",
    "workstation_MAC": "Workstation mac",
    "workstation_consulting_room": "Workstation Consulting Room",
    "register_workstation_title": "Register Workstation",
    "arrangement_text": "Arrangement",
    "enter_conversation": "Enter Conversation",
    "export_no_select": "Please select the image to be exported.",
    "general_mode": "General Mode",
    "tv_wall_mode": "TV Wall Mode",
    "download_image_success": "Download succeeded!",
    "download_directory_empty": "Please select the export destination address.",
    "download_image_space": "The image name cannot contain spaces.",
    "download_image_name": "Please enter the name of the picture.",
    "download_exam_num_space": "Exam Number cannot contain spaces.",
    "download_exam_num_empty": "Please enter exam number.",
    "download_choose_btn": "Select",
    "download_directory": "Select the exporting address: ",
    "download_name": "Exported File Name: ",
    "download_exam_num": "Exam Number: ",
    "pusher_mac_format_error": "The MAC address format of the pusher is wrong and must be XX-XX-XX-XX-XX-XX.",
    "max_ultrasync_box_length": "The length of the MiCo+ Pusher name must be between 4 and 16 characters.",
    "max_scan_room_name_length": "The length of the scan room name must be between 4 and 16 characters.",
    "delete_ultrasync_box_text": "Delete MiCo+ Pusher:",
    "delete_scan_room_text": "Delete scan room:",
    "other_infomation": "Other information",
    "device_id": "Device ID",
    "ultrasync_box_mac": "MiCo+ Pusher MAC",
    "scan_room_lastest_user": "User recently logged in",
    "scan_room_hospital": "Hospital",
    "scan_room_name": "Name",
    "ultrasound_device_mng": "Ultrasound Device Management",
    "pusher_manage": "MiCo+ Pusher Management",
    "scan_room_mng": "Workstation Management",
    "system_info_text": "System Information",
    "cloud_statistics_iworks_get_token_err": "Failed to get token!",
    "cloud_statistics_iworks2": "iWorks Statistics",
    "cloud_statistics_iworks": "iWorks",
    "cloud_statistics_range_empty": "Please select the time interval.",
    "cloud_statistics_day_text": "Days",
    "cloud_statistics_hour_text": "Hours",
    "minute_text": "Minutes",
    "cloud_statistics_second_text": "Seconds",
    "cloud_statistics_client_type": "Client Type: Box/Workstation Side/Consulting Side /Phone App/Ultrasound Machine/Browser",
    "cloud_statistics_tags_count": "Tag Numbers",
    "cloud_statistics_comment_count": "Annotation Numbers",
    "cloud_statistics_group_message_count": "Number of speeches",
    "cloud_statistics_start_offline_consultation_count": "Number of Offline Consultations Launched",
    "cloud_statistics_active_users_count_tip": "Tip: active user - online users can be considered active",
    "cloud_statistics_active_users_count": "Number Of Active Users",
    "cloud_statistics_by_the_hour": "By hour",
    "cloud_statistics_by_the_month": "By month",
    "cloud_statistics_by_the_week": "By The Week",
    "cloud_statistics_by_the_day": "By day",
    "cloud_statistics_last_year": "Last Year",
    "cloud_statistics_last_thirty_days": "Recent 30 days",
    "cloud_statistics_last_seven_days": "Recent 7 Days",
    "cloud_statistics_bar_chart": "Histogram",
    "cloud_statistics_line_chart": "Curve",
    "cloud_statistics_chart_type": "Chart type:",
    "cloud_statistics_fineness_with_colon": "Granularity: ",
    "cloud_statistics_client_type_short": "Type of client-server",
    "cloud_statistics_new_group_number": "Number Of New Groups",
    "cloud_statistics_global_group_number": "Total Number of Groups",
    "cloud_statistics_active_live_user_count": "Number of active live broadcasting users",
    "cloud_statistics_live_user_total_count": "Total Live Broadcast Users",
    "cloud_statistics_ulink_user_tip": "Tip: U-link user - the user who has sent the check",
    "cloud_statistics_ulink_user": "u-Link users",
    "cloud_statistics_ulink_active_user_count_tip": "Tip: number of active u-link users - users who have used u-Linker within one month can be considered active",
    "cloud_statistics_ulink_active_user_count": "The number of active u-Link users",
    "cloud_statistics_ulink_user_count": "The added number of u-Link users",
    "cloud_statistics_ulink_user_total_count": "Total number of u-Link users",
    "cloud_statistics_total_ultra_device_ulink": "Total number of Ultrasound Devices (u-Link)",
    "cloud_statistics_ulink_install_device_total_count": "Total number of u-Link installed",
    "cloud_statistics_ulink_install_device_count": "The added number of u-Link installed",
    "cloud_statistics_live_duration_enter": "Duration of Live Streaming Reception (Minute)",
    "cloud_statistics_live_duration": "Live Streaming Duration (Minute)",
    "cloud_statistics_group_total_count": "Total groups",
    "cloud_statistics_user_total_count": "Total number of users",
    "cloud_statistics_active_group_count_tip": "Tip: active group - if any user in the group sends a message, the group is considered active",
    "cloud_statistics_active_group_count": "Number of Active Groups",
    "cloud_statistics_new_group_message_count": "Number of New Group Message (All types of chat messages except system messages)",
    "cloud_statistics_new_realtime_count": "Number of New Live Streaming",
    "cloud_statistics_total_exam_video": "Total Number of Exam Cines",
    "cloud_statistics_total_exam_picture": "Total Number of Exam Images",
    "cloud_statistics_total_case_count": "Total Number of Exams",
    "cloud_statistics_new_exam_video": "Number of New Exam Cines",
    "cloud_statistics_new_exam_picture": "Number of New Exam Images",
    "cloud_statistics_new_case_count_for_iworks": "Number of New Exams with iWorks",
    "cloud_statistics_new_case_count": "Number of New Exams",
    "cloud_statistics_new_user_approve_number": "Number of New Users Approved",
    "cloud_statistics_new_user_number": "Number of New Users",
    "cloud_statistics_receive_real_time_consultation": "Peak Value of Received Real-Time Consultations",
    "cloud_statistics_launch_real_time_consultation": "Peak Value of Launched Real-Time Consultations",
    "cloud_statistics_active_group_peak": "Peak Value of Active Groups",
    "cloud_statistics_online_user_peak": "Peak Value of Online Users",
    "cloud_statistics_exam_number": "Number of Exams",
    "cloud_statistics_chat_messages_other": "Other",
    "cloud_statistics_chat_messages_sound": "Sound",
    "cloud_statistics_chat_messages_realtime_video": "Video",
    "cloud_statistics_chat_messages_image": "Images",
    "cloud_statistics_chat_messages_realtime_review": "Live Playback",
    "cloud_statistics_chat_messages_cine": "Medical Video",
    "cloud_statistics_chat_messages_frame": "Medical Images",
    "cloud_statistics_chat_messages_text": "Text: ",
    "cloud_statistics_chat_messages_number": "Number Of Chat Messages",
    "cloud_statistics_access_realtime_person_time": "Number of Access to Real-time Voice",
    "cloud_statistics_access_realtime_total_time": "Real-time Voice Access Duration",
    "cloud_statistics_times_of_realtinme_voice": "Times Of Real Time Voice",
    "cloud_statistics_total_time_of_realtinme_voice": "Real-time Voice Duration",
    "cloud_statistics_viewing_person_time": "Number of Users Watching the Real-Time Consultation",
    "cloud_statistics_viewing_total_time": "Total Viewing Time of Real-Time Consultation",
    "cloud_statistics_real_time_consultation_times": "Times of Real-Time Consultation",
    "cloud_statistics_real_time_consultation_total_time": "Real-time Consultation Duration",
    "cloud_statistics_increased_conversation": "Number of New Conversations",
    "cloud_statistics_total_conversation": "Total Number of Sessions",
    "cloud_statistics_user_total_online_time": "Total Online Duration of Users",
    "cloud_statistics_online_users": "Online Users",
    "cloud_statistics_increased_users": "Number of New Users (Including registration not approved)",
    "cloud_statistics_total_users": "Number of ALL Users (Including registration not approved)",
    "cloud_statistics_increased_approved_users": "Number of Users to be Approved",
    "cloud_statistics_total_approved_users": "Number of ALL Approved Users",
    "cloud_statistics_this_month": " This Month ",
    "cloud_statistics_this_week": " This Week ",
    "cloud_statistics_today": " Today ",
    "cloud_statistics_inquiry": " Inquiry ",
    "cloud_statistics_statistical_item_result": "Statistical Result",
    "cloud_statistics_statistical_item": "Statistical Item",
    "cloud_statistics_diagram": "Chart",
    "cloud_statistics_interval_table": "Interval Table",
    "cloud_statistics_global_table": "Global Table",
    "device_mng": "Device Management",
    "cloud_statistics_title": "Cloud Statistics",
    "background_manage_title": "Background Management",
    "enter_purpose_tip": "Please enter consultation topic.",
    "fill_in_purpose": "Enter consultation topic.",
    "temp_attendee_text": "Temporary",
    "video_cannot_played": "This video cannot be played.",
    "exam_conversation_title": "Consultation Room",
    "add_exam_opinion_success": "The consultation comments are submitted successfully.",
    "add_exam_conclusion_success": "The consultation conclusion is submitted successfully.",
    "exam_conclusion_text": "Consultation Conclusion",
    "exam_opinion_text": "Consultation Comments: ",
    "cancel_conversation_btn": "Cancel Consultation",
    "close_conversation_btn": "End Consultation.",
    "add_exam_conclusion": "Enter Consultation Conclusion",
    "app_no_speak_permission": "No permission to speak. Please apply in group setup and wait for approval.",
    "applying_join_group": "Requesting speaking permission…",
    "apply_join_semi_group": "Apply to speak in public group",
    "toggle_to": "Do you want to switch to",
    "search_empty_tip": "Enter search criteria and press Enter or select the search type from the pull-down menu!",
    "choose_file": "Choose File",
    "submit_btn": "Submit",
    "download_title": "Download",
    "share_video_limited": "WeChat does not support sharing videos in this way. You can try to download videos to albums and use Wechat to send videos.",
    "history_tag": "Search History",
    "stop_multi_frame_first": "Please stop saving multiple-frame image first!",
    "device_save_video_unit": " ",
    "device_save_video_num": "Video",
    "device_save_picture_num": "Image: ",
    "device_create_exam": "New Exam",
    "get_repository_list_fail": "Loading database failed.",
    "no_search_data": "No relevant results were found.",
    "load_pdf_error": "Loading PDF error: ",
    "scan_to_login": "Scan to login",
    "upload_date_text": "Upload time: ",
    "delete_chat_message_warm_sended_by_others": "The {1} records are sent by other users and only the data sent by the current user can be deleted.",
    "delete_chat_message_fail_sended_by_others": "The data sent by other users cannot be deleted.",
    "ultrasync_box_detele_fail_online": "The MiCo+ Pusher is online and cannot be deleted.",
    "ultrasync_box_detele_fail": "Delete pusher failed.",
    "scan_room_detele_fail_online": "You cannot delete an online workstation. ",
    "scan_room_detele_fail": "Delete workstation failed.",
    "delete_chat_message_fail": "Delete failed!",
    "auto_join_group": "The public group can be added after login.",
    "control_panel": "Control panel",
    "auth_fail": "Authorization Failed.",
    "auth_ok": "Authorization succeeded.",
    "scan_mindray_ultrasync": "Scan MiCo+",
    "join_group_without_approve": "Adding groups does not require approval.",
    "join_group_with_approve": "Adding groups requires approval.",
    "semi_public_group_text": "Semi Public Group",
    "private_group_text": "Private Group",
    "public_group_text": "Public Group",
    "group_realtime_review": "Review",
    "group_conversion_file": "Consultation File",
    "group_all_file": "All Files",
    "attendee_member_text": "Members",
    "to_scan_device": "Login-free Connection Device",
    "send_by_enter": "Press the Enter key to send",
    "chat_history": "Chat History",
    "clear_history": "Clear",
    "more_features": "More Features",
    "camera_direct_seeding": "Camera is in live broadcasting. ",
    "desktop_direct_seeding": "Desktop Live Streaming",
    "ultrasound_seeding": "Real-time Ultrasound Streaming",
    "voice_recording": "Voice Recording",
    "export_image": "Export Image",
    "upload_file": "Upload Attachments",
    "emoticon": "emoji",
    "search_history_text": "Search Chat Record",
    "search_file_text": "Search files",
    "search_group_text": "Search group",
    "search_friend_text": "Search friend",
    "version_info": "Version Information",
    "uncalibrated_text": "Uncalibrated",
    "calibrater_text": "Calibrator: ",
    "inputPatientOrFtpInfo": "Please input patient or FTP info.",
    "shutdown_fail": "Shutdown failed.",
    "shutdown_succ": "Shutdown succeeded.",
    "save_multi_frame_fail": "Saving multi-frame images failed.",
    "save_multi_frame_succ": "Saving multi-frame images succeeded.",
    "save_multi_frame_start": "Start saving multi-frame images.",
    "save_single_frame_fail": "Saving single-frame images failed.",
    "save_single_frame_succ": "Saving single-frame images succeeded.",
    "update_device_cur_session_fail": "Setting current session failed.",
    "device_offline": "The device is offline.",
    "realtime_video_error": {
        "ultrasync_box_not_binded_switch": "The MiCo+ Pusher is not bound, and the real-time video cannot be switched.",
        "ultrasync_box_offline_switch": "The MiCo+ Pusher is offline, and the real-time video cannot be switched.",
        "push_error_switch": "Processing data failed. The real-time video cannot be switched.",
        "push_camera_desktop_error_switch": "Requesting camera failed. The real-time video cannot be switched.",
        "push_local_desktop_error_switch": "Sharing local desktop failed. The real-time video cannot be switched.",
        "push_error": "Processing data failed. The real-time video cannot be started.",
        "push_camera_desktop_error": "Requesting camera failed. The real-time video cannot be started.",
        "push_local_desktop_error": "Sharing local desktop failed. The real-time video cannot be started.",
        "rt_video_started": "Real-time video has been started. Real-time video cannot be started.",
        "timeout": "Timeout. Real-time video cannot be started.",
        "rt_video_started_by_others": "The real-time video of this workstation has been started. Other users cannot start the real-time video.",
        "camera_error": "Camera recognition error. Real-time video cannot be started.",
        "video_transmit_server_not_run": "The video forwarding server is not started, and the real-time video cannot be started.",
        "ultraSync_box_image_reset": "The image is reset and the real-time video is turned off.",
        "ultraSync_box_push_error": "Streaming failed. The real-time video is closed.",
        "ultrasync_box_no_signal": "The MiCo+ Pusher has no signal, and the real-time video will be closed.",
        "unbind_scan_room_user": "The workstation is offline and the real-time video is closed.",
        "ultrasync_box_disabled": "The MiCo+ Pusher is used by other workstations and the real-time video will be closed.",
        "ultrasync_box_reset": "The MiCo+ Pusher is rebound, and the real-time video will be closed.",
        "ultrasync_box_offline": "The MiCo+ Pusher is offline, and the real-time video will be closed.",
        "unknown_error": "Unknown error. The real-time video will be closed."
    },
    "set_catch_option_error": {
        "ultrasync_box_pushing": "Streaming now",
        "ultrasync_box_setting": "Settings are not completed.",
        "ultrasync_box_offline": "The MiCo+ Pusher is offline.",
        "ultrasync_box_not_binded": "The MiCo+ Pusher is not bound.",
        "timeout": "Timeout"
    },
    "pusher_enable_error": {
        "ultrasync_box_no_signal": "The MiCo+ Pusher has no signal, and the real-time video will be started.",
        "ultrasync_box_disabled": "The MiCo+ Pusher is used by other workstations and the real-time video cannot be started.",
        "ultrasync_box_offline": "The MiCo+ Pusher is offline, and the real-time video cannot be started.",
        "ultrasync_box_not_binded": "The MiCo+ Pusher is not bound, and the real-time video cannot be started.",
        "unknown_error": "Unknown error. Real-time video cannot be started."
    },
    "ultrasync_box_disabled": "The MiCo+ Pusher is used by other workstations and the real-time ultrasound cannot be started.",
    "starting_rt_video": "Starting real-time video. Please wait!",
    "stop_device_ultrasound_desktop": "Stop Realtime",
    "start_device_ultrasound_desktop": "Start real-time video",
    "shutdown_confirm": "Shut down?",
    "save_multi_frame_stop": "Stop saving multi-frame image",
    "save_multi_frame": "Saving multi-frame images",
    "save_single_frame": "Saving single-frame images",
    "please_enter_ftp_path": "Please_enter_ftp_path.",
    "please_enter_ftp_password": "Please enter FTP password.",
    "please_enter_ftp_account": "Please enter FTP account.",
    "ftp_port": "FTP port: ",
    "ftp_path": "Storage path: ",
    "ftp_password": "FTP password: ",
    "ftp_account": "FTP account: ",
    "ftp_anonymous": "Anonymous",
    "ftp_info": "Storage information ",
    "forbid_input_quotation": "Quotation marks are forbidden.",
    "please_enter_patien_id": "Please enter patient ID.",
    "requestTimeout": "Request timeout.Please try again.",
    "modify_btn_text": "Change",
    "current_conversation": "Current conversation: ",
    "toggle_conversation_to": "Switch to",
    "ultrasound_device_title": "My Device",
    "recent_consultation": "Recent Consultation",
    "recent_images": "Recent File",
    "camera_ex_enter_by_other_client": "Other clients have joined dual camera, and you cannot join it again.",
    "request_done_by_other_clients": "The request has been processed by this account in another log-in device and cannot be repeatedly processed.",
    "user_passive_exit_group": "You were removed from the group",
    "user_exit_group_fail": "Leave group failed.",
    "user_exit_group_succ": "Leave group succeeded. ",
    "user_exit_group_tip": "After exiting, all data will be cleared. The operation is irreversible. Are you sure to continue?",
    "creator_user_exit_group_tip": "As the group creator, your exiting will dissolve the whole group and all data will be cleared. The operation is irreversible. Are you sure to continue?",
    "exit_group": "Exit Group",
    "delete_group": "Delete group",
    "i_o_error": "Read/Write error: ",
    "send_message_error": "Sending message failed.",
    "audio_not_support": "The browser does not support recording.",
    "invalid_qrcode": "Invalid QR code!",
    "init_not_ready": "Initialization is not complete. Please try again later.",
    "file_does_not_exist": "File does not exist.",
    "webrtc_transfer_close": "The voice server is not started. ",
    "speak_panel": "Speaker panel",
    "start_conversation_error": "Open session failed.",
    "banned_this_moment": "This function is not available.",
    "copy_text_not_support": "The current mobile phone does not support copy text.",
    "copy_text_fail": "Copy failed.",
    "text_has_copied": "Text copied to clipboard",
    "tag_emoji_err": "User-defined tags cannot contain emoji",
    "system_tag_top": "System Tags",
    "custom_tag_top": "Common Tags ",
    "tag_repeat_add": "Adding labels repeatedly",
    "update_failed_text": "Modify failed",
    "update_success_text": "Modify succeeded",
    "exam_types": {
        "0": "OB",
        "1": "GYN",
        "2": "CARD",
        "3": "VAS",
        "4": "ABD",
        "5": "URO",
        "6": "SMP",
        "7": "PED",
        "8": "Breast",
        "9": "Unknown",
        "10": "Undefined",
        "-1": "All"
    },
    "no_more_data": "No more data",
    "exam_view_mode": "Exam View",
    "normal_view_mode": "Normal mode",
    "freedom_voice_ctrl_mode": "Free speak",
    "group_setting_voice_ctrl_mode": "Voice control mode",
    "group_setting_view_mode": "View Mode",
    "search_key": "Keyword",
    "searching": "searching",
    "search": "Search",
    "exam_flesh_btn": "Refresh",
    "confirm_min_app": "Press again to exit.",
    "delete_tag_msg_text": "Delete tag",
    "add_tag_msg_text": "New tag",
    "add_comment_msg_text": "New comment",
    "no_net_no_open_img": "The big picture is opened abnormally offline. ",
    "no_net_no_open_video": "You cannot open video offline. ",
    "exam_date_more_tip": "The start time cannot be later than the end time.",
    "exam_date_less_tip": "End time cannot be earlier than start time.",
    "search_text": "Filter",
    "save_not_support_tip": "This type of file is not supported to be saved to the photo album",
    "save_support_fail_tip": "Save failed. The device does not support this file format!",
    "save_null_fail_tip": "Preparing files. Please try later!",
    "has_download_tip": "The file has been saved to the album.",
    "gallery_dowloading_tip": "Downloading. Please wait.",
    "confirm_create_group": "Are you sure to create the group chat : ${1}?",
    "patient_birthday_text": "DOB",
    "sender_nickname": "Sender:",
    "exam_browse_text": "Exam Review",
    "transfer_helper_text": "Transfer helper",
    "use_app_tip": "Please install the application to use this function.",
    "share_to_wechat_failed": "Sharing failed!",
    "share_to_wechat_succeed": "Sharing succeeded!",
    "network_error_tip": "Network connection failed. Please check the network settings and try again.",
    "machine_transmit_tip": "The forward command has been sent.",
    "machine_local_confirm": "Are you sure you want to save the selected image locally?",
    "machine_destroy_confirm": "Are you sure you want to discard the selected image?",
    "machine_local_btn": "Local",
    "machine_destroy_btn": "Discard",
    "cancel_select_all": "Deselect All",
    "select_all": "Select All",
    "machine_info_serial": "Machine serial",
    "machine_info_type": "Machine type",
    "machine_info_name": "Machine name",
    "disconnet_machine_btn": "Disconnect",
    "machine_info": "Device Information",
    "ultrasound_machine_title": "Ultrasound Images Transfer",
    "password_error_text": "Wrong password. Please try again!",
    "modify_photo_success": "Modify profile photo succeeded!",
    "card_request_fail": "Add friend failed.",
    "card_request_sended": "Friend adding request has been sent.",
    "card_applying_tip": "Adding friends…",
    "card_chat_btn": "Send Message ",
    "visiting_card_title": "Personal profile",
    "tag_text_has_newline": "Line break character cannot be input for user-defined tags.",
    "tag_text_null": "User-defined tags cannot be null",
    "comment_text_null": "Comments cannot be empty.",
    "close_realtime_video_tip": "Close real-time video.",
    "request_realtime_video_tip": "Request real-time video",
    "close_voice_system_tip": "Close voice chat",
    "request_voice_system_tip": "Request voice chat",
    "search_join_tip": "Join the group chat by searching",
    "remove_group_tip2": "from the group chat",
    "active_exit_group_tip": "Initiatively quit the group chat",
    "remove_group_tip1": "removed",
    "invited_join_group_tip2": "to the group chat",
    "invited_join_group_tip1": "invited",
    "cannot_delete_tag": "Tags created by others cannot be deleted.",
    "age_unit_month": "Months",
    "age_unit_year": "Years",
    "unknow_text": "Unknown",
    "action_clip_text": "Clip",
    "action_set_exam_info_text": "Import to patient database",
    "action_favorite_text": "Favorites",
    "action_delete_text": "Delete",
    "transmit_fail_tip": "Sending failed!",
    "transmiting_tip": "The forwarded file has been sent.",
    "transmit_less_tip": "Please select at least one non JSON format file",
    "transmit_comfirm_msg": "Forward To:",
    "transmit_title": "Forward",
    "add_favorites_fail": "Add to favorites failed.",
    "add_favorites_success": "Add to favorites succeeded",
    "operating_text": "Operating",
    "share_to_wechat_moment": "Share to WeChat moment",
    "share_to_wechat_friends": "Share to WeChat friends",
    "share_to_wechat": "Share to WeChat",
    "attendee_join_group_already": "Already In Group",
    "invite_wechat_user_to_group": "Invite WeChat friends",
    "invite_wechat_user": "Invite WeChat friends",
    "wechat_no_wechat_installed": "No WeChat installed",
    "wechat_is_not_supported": "Not supported by WeChat",
    "send_failed": "Sending failed.",
    "wechat_user_tap_cancel_and_back": "The user tap Cancel and select Back.",
    "wechat_regular_wrong_type": "Common error type",
    "wechat_get_user_info_failed": "Obtain WeChat user information failed.",
    "wechat_get_access_token_failed": "Obtain WeChat access token failed.",
    "wechat_create_group": "WeChat Contacts",
    "remote_ultrasonic_consultation_system": "MiCo+",
    "authentication_authorization_failed": "Authentication authorization failed",
    "invalid_sharing_service": "Invalid sharing service",
    "mindray_final_mission": "Mission-Advance medical technologies to make healthcare more accessible",
    "account_format_tip": "The login name consists of letters/numbers underscores, starting with letters/numbers and not pure numbers.",
    "enhanced_password_format_tip": "The password must be 8-16 characters and be some combination of letters,number,and/or special characters(at least two of them).",
    "password_format_tip": "The password consists of 8-16 digits of numbers/letters/special symbols.",
    "nickname_cannot_contain_special_character": "Nickname cannot contain special character.",
    "single_picture_sharing": " Sharing",
    "multi_picture_sharing": "Multi-image sharing",
    "choose_transmit_tip": "choose image please",
    "choose_favorites_tip": "Please select the image to be added to favorites. ",
    "realtime_review_msg": "[Live Playback]",
    "unknown_error": "Unknown error",
    "one_mobile_register_multiple_user_forbid": "Do not allow multiple users to use the same mobile phone for registration.",
    "SMS_validation_is_not_supported": "SMS validation is not support, please contact the administrator",
    "userNotExist": "The user does not exist",
    "data_base_error_contact_admin": "Database error. Please contact administrator.",
    "retrieve_sms_verification_code": "Send again",
    "sms_verification_code_failed": "Verification of SMS verification code failed",
    "sms_verification_code": "SMS Code",
    "submit_sms_verification_code": "Submit SMS Verification Code",
    "user_reset_password_button": "Reset Password",
    "mobile_number_is_invalid_input_again": "The mobile phone number is invalid. Please enter again!",
    "nickname_should_not_be_empty": "Nickname cannot be empty.",
    "modify_password_fail_database_err": "Database error, please contact to administrators!",
    "modify_password_fail_password_same": "The old and new passwords are the same, failed to modify the password.",
    "modify_password_fail_password_incorrect": "The original password is incorrect.",
    "modify_password_fail_account_incorrect": "The account is incorrect.",
    "personal_information_fail_mobile_phone_repeated": "The mobile phone has been registered.",
    "personal_information_fail_nickname_repeated": "The nickname has been registered.",
    "personal_information_fail_email_repeated": "The email has been registered.",
    "personal_information_fail_name_pure_numbers": "login name is not allowed to be all numbers.",
    "personal_information_fail_name_repeated": "The Login name has been registered.",
    "modify_basic_info_success": "Personal information is modified successfully.",
    "modify_password_success": "Changed password successfully.",
    "confirm_password_and_new_password_not_match": "The confirmation password  does not match",
    "confirm_password_and_password_not_same": "The confirmation password does not match the password.",
    "confirm_password_length_not_correct": "Confirmation password length should between 8 and 16 characters",
    "password_length_not_correct": "The password length must be between 8 and 16 characters",
    "login_name_length_not_correct": "Login name length must be between 4 and 16 characters.",
    "voice_manager_text": "Initiator",
    "close_realtime_video": "[Close real-time video]",
    "close_voice_chat": "[Close voice chat]",
    "request_realtime_video": "[Request real-time video]",
    "request_voice_chat": "[Request voice chat]",
    "group_modify_subject_title": "Modify Group Name",
    "confirm_txt": "Confirm",
    "group_add_attendee_title": "Add Members",
    "group_all_attendee_title": "All Members",
    "group_all_file_title": "All Consultation Files",
    "toggle_unlive_record_tip": "Do you want to stop recording in real-time mode?",
    "toggle_live_record_tip": "Do you want to start recording in real-time mode?",
    "group_setting_whether_live_record": "Start Recording",
    "group_setting_is_live_record": "Do you want to start real-time recording?",
    "group_setting_is_need_to_approve": "Whether the speeches of users who join the group by searching need to be reviewed",
    "group_setting_is_public": "Is it a public group?",
    "group_setting_announce": "Group announcement",
    "more_attendee_text": "See more group members",
    "more_file_text": "Check more group examination files",
    "creator_tag_text": "Group owner",
    "group_setting_title": "Group Info",
    "yesterday_text": "Yesterday",
    "cancel_button_text": "No",
    "confirm_button_text": "Yes",
    "disable_speak_comfirm": "This action will disable the member from speaking. Are you sure to continue?",
    "apply_speak_disagree_comfirm": "This action will reject this member's speaking request. Are you sure to continue?",
    "apply_speak_agree_comfirm": "This action will allow this member to speak. Are you sure to continue?",
    "all_forbidden_speak": "Mute All",
    "authorize_speak_text": "Authorized to speak",
    "attendee_text": "Participants:",
    "modify_confirm_password_input": "Enter new password again",
    "modify_new_password_input": "Enter new password",
    "modify_old_password_input": "Enter old password",
    "modify_new_password_label": "New password",
    "modify_old_password_label": "Old password",
    "browser_no_support_recording": "The browser does not support recording!",
    "no_audio_input_warn": "Voice input device is not connected!",
    "loaded_all_message_no_net": "Offline. No more messages.",
    "loaded_all_message": "No more history",
    "image_all_loaded": "All images are loaded",
    "has_chosen_text": "Selected",
    "not_choose_text": "Not selected",
    "upload_net_error": "The current network is abnormal and does not support sending large files. Please set up the network and try again!",
    "upload_forbidden_file_type_text": "The file format is not supported and cannot be uploaded!",
    "upload_min_text": "Upload failed. The file size is ",
    "upload_max_text": "Upload failed. The file size exceeds",
    "record_short_text": "Recording time too short",
    "cancel_recording_text": "Release your finger to cancel sending",
    "recording_sound_text": "Swipe up to cancel sending.",
    "chat_upload_text": "Images",
    "login_another": "Your account is logged in elsewhere.",
    "tip_title": "Tips",
    "create_group_text": "Group is created successfully.",
    "unsupport_video_text": "The browser does not support playing video",
    "gallery_navbar_patient": "Patient Info",
    "label_txt": "Tags",
    "gallery_navbar_comment": "Comments",
    "gallery_add_tags_btn": "Add Tags",
    "gallery_add_comment_btn": "Add comments",
    "unsupport_msg_type": "Unsupported Message Type",
    "choose_shared_image": "Please select the image to share.",
    "max_transfer_image": "up to 20 pictures or videos can be selected.",
    "max_share_image": "up to 99 pictures or videos can be selected",
    "msg_type_sound_label": "Voice",
    "msg_type_image_string_label": "Text",
    "msg_type_consultation_file_label": "Examination File",
    "msg_type_iworks_protocol_label": "iWorks Protocol",
    "msg_type_consultation_file": "[Examination File]",
    "msg_type_iworks_protocol": "[iWorks Protocol]",
    "msg_type_file": "[File]",
    "msg_type_video": "[Video]",
    "msg_type_sound": "[Voice]",
    "msg_type_image": "[Image]",
    "loading_complete": "Loading completed.",
    "bottom_loading_text": "Loading...",
    "click_more_text": "Click to load more",
    "bottom_drop_text": "Release to load more",
    "bottom_pull_text": "Pull up to load more.",
    "top_pull_text": "Pull down to load more.",
    "scanner_text": "Scan",
    "hold_to_talk": "Hold to Talk",
    "send_message_txt": "Send",
    "init_conversation_err": "Open conversation failed.",
    "no_data_txt": "No data",
    "applying_txt": "Applying",
    "tmp_joined_txt": "Temporarily joined",
    "joined_txt": "Joined",
    "subject_empty_tip": "Enter the group name.",
    "create_group_empty_list": "The friend list is empty. Please add a friend first.",
    "group_chat_params": "Group params",
    "group_create_steps": {
        "choose_friends": "Choose Members",
        "edit_params": "Settings",
        "finish": "Done",
        "group_create_progress_100": "Done",
        "group_create_progress_50": "Almost Done"
    },
    "group_chat_name": "Group name",
    "search_group_title": "Public Groups",
    "create_group_title": "Create Groups",
    "response_disaccept_friend": "${1} was rejected Your friend's request.",
    "response_accept_friend": "${1} was accepted Your friend's request.",
    "added_txt": "Added",
    "no_user_result": "No data found",
    "search_friend_tip": "Please enter keyword.",
    "search_err": "Search failed.",
    "search_friend_txt": "Enter nickname/account/phone number/email.",
    "disagree_txt": "Disagree",
    "agree_txt": "Agree",
    "apply_group_txt": "Apply to join a group",
    "apply_friend_txt": "Apply to add you",
    "operate_success": "Operation succeeded.",
    "operate_err": "Operation failed.",
    "logout": "Log Out",
    "my_groups": "My Groups",
    "new_apply": "New Friends",
    "friends_title": "My friends",
    "page_loading_tip": "Loading…",
    "international_title": "Language",
    "modify_basic_info_text": "Change Basic Information",
    "system_setting_text": "System Settings",
    "change_language_success": "Changed language successfully",
    "into_tv_wall_after_login": "Enter the TV wall after successful login",
    "auto_push_stream_for": "Automatically initiate live streaming with:",
    "auto_push_stream": "Automatically initiate live streaming",
    "auto_download_attachment": "Automatically download attachment",
    "auto_forwarding_for": "Automatically Send To:",
    "auto_forwarding": "Automatically Send",
    "other_setting_text": "Other Setting",
    "modify_photo_text": "Change Profile Photo",
    "modify_password_text": "Change Password",
    "personal_setting_title": "Account Security",
    "software_version_number": "Software Version",
    "app_and_server_build_version": "UPG Version",
    "about_ultrasync": "About MiCo+",
    "cloud_favorites": "My Cloud Favorites",
    "local_patients": "Local Patient Database",
    "setting_title": "Settings",
    "add_friend_text": "Add Contacts",
    "create_group_btn": "Create",
    "files": "Image List",
    "contacts": "Contacts",
    "index_nav_mine": "Me",
    "index_nav_chat": "Chat",
    "forget_password_getcode": "Send",
    "forget_password_title": "Forget Password",
    "register_reset_btn": "Reset",
    "register_sex": "Gender",
    "register_mobile": "Phone",
    "register_confirm_password": "Confirm",
    "register_password": "Password",
    "register_account": "Account",
    "register_account_or_mobile": "Account/Mobile/Email",
    "register_title": "Register",
    "to_forget_password": "Forget Password",
    "to_register": "Sign Up",
    "download_mobile_app": "Download mobile APP",
    "download_window": "Download MiCo+ for Windows",
    "auto_login": "Auto-Login",
    "register_success": "Registered successfully",
    "login_password_empty": "Password can not be empty.",
    "login_account_empty": "Account cannot be empty.",
    "login_password": "Login Password",
    "login_account": "Enter your account.",
    "login_title": "Log In",
    "network_unavailable": "Network unavailable. Please check your network.",
    "new_version": "New version: ",
    "app_name": "MiCo+",
    "RemoteTeaching": "RemoteTeaching",
    "RemoteTeaching not available": "RemoteTeaching not available",
    "AutoStart": "AutoStart",
    "CARD": "CARD",
    "AppWorkstation": "Workstation",
    "AppClient": "Consultation terminal",
    "unknown": "Others",
    "StopTest": "StopTest",
    "TestMicrophone": "TestMicrophone"
}

export default EN