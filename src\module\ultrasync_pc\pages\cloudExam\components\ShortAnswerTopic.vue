<template>
    <div class="ShortAnswerTopic_wrapper">
        <div class="topic_title">
            <!-- 编辑模式，可编辑题目标题 -->
            <span class="topic-index" v-if="type === CLOUD_TEST_TYPE.EDIT || topic.index">{{topic.index}}. </span>
            <text-field
                :value="topic.title"
                :isEdit="type === CLOUD_TEST_TYPE.EDIT"
                :placeholder="lang.input_enter_tips"
                @change="(val) => handleFieldChange('title', val)"
            />
            <div class="topic_operation">
                <p class="topic_count" v-if="(type === CLOUD_TEST_TYPE.VIEW || type === CLOUD_TEST_TYPE.ANSWER || type === CLOUD_TEST_TYPE.CORRECT) && !isPassMode">{{ getTopicCount }}</p>
                <template v-else-if="type === CLOUD_TEST_TYPE.EDIT && !isPassMode">
                    <div class="topic_count">
                        <!-- 编辑模式，可设置题目分数 -->
                        <text-field
                            :value="topic.score"
                            :isEdit="true"
                            inputType="number"
                            @change="(val) => handleFieldChange('score', Number(val))"
                        />
                        {{ lang.point_tip }}
                    </div>
                </template>
                <p class="topic_score" v-else-if="type === CLOUD_TEST_TYPE.VIEW_RESULT && !isPassMode">
                    (<span>{{ topic.correctScore }}{{ lang.point_tip }}</span
                    >/{{ topic.score }}{{ lang.point_tip }})
                </p>
                <!-- Pass模式下显示通过/不通过状态 -->
                <p class="topic_pass_status" v-else-if="type === CLOUD_TEST_TYPE.VIEW_RESULT && isPassMode && topic.isPassed !== undefined">
                    <span :class="{'pass-status': true, 'passed': topic.isPassed === true, 'failed': topic.isPassed === false}">
                        {{ topic.isPassed === true ? '通过' : '不通过' }}
                    </span>
                </p>
                <!-- 编辑模式，可删除题目 -->
                <el-popconfirm
                    v-if="type === CLOUD_TEST_TYPE.EDIT"
                    :title="lang.homework.confirm_delete_topic"
                    @confirm="deleteTopic"
                    class="homework-delete-topic-btn"
                >
                    <el-button slot="reference" class="delete-topic-btn" type="text">
                        <i class="el-icon-delete"></i>
                    </el-button>
                </el-popconfirm>
            </div>
        </div>
        <!-- 编辑模式，在题目下方显示图片上传 -->
        <view-list
            class="topic_image_list"
            v-if="type === CLOUD_TEST_TYPE.EDIT"
            :image-list="topic.imageList"
            :disable-delete="false"
            :disable-upload="false"
            :type="type"
            @view-image="viewImage"
            @delete-image="(index) => deleteImage(index)"
            @upload-click="() => $emit('upload-click')"
            @order-changed="(evt) => handleImageOrderChange(evt)"
        />
        <!-- 非编辑模式，显示图片上传 -->
        <view-list
            class="topic_image_list"
            v-if="type !== CLOUD_TEST_TYPE.EDIT"
            :image-list="topic.imageList"
            :disable-delete="true"
            :disable-upload="true"
            :type="type"
            @view-image="viewImage"
        />
        <div class="topic_radio" v-if="type !== CLOUD_TEST_TYPE.EDIT">
            <!-- 非编辑模式，简答题显示输入框 -->
            <el-input
                v-model="localValue"
                type="textarea"
                :autosize="{ minRows: 8, maxRows: 12 }"
                :placeholder="lang.input_enter_tips"
                :disabled="disableModify"
                @input="updateValue"
            ></el-input>

            <!-- 作答记录区域 -->
            <answer-records :records="topic.answerRecords" />
        </div>

        <template v-if="type === CLOUD_TEST_TYPE.CORRECT && !isPassMode">
            <div class="correcting_item_wrapper">
                <div class="correcting_item">
                    <div
                        class="full_score"
                        @click="correctSelect(1)"
                        :class="{ active: topic.correctScore === topic.score }"
                    >
                        {{ lang.full_mark }}
                    </div>
                    <div class="empty_score" @click="correctSelect(0)" :class="{ active: topic.correctScore === 0 }">
                        {{ lang.zero_point }}
                    </div>
                    <div
                        class="current_score"
                        :class="{ active: topic.correctScore !== 0 && topic.correctScore !== topic.score }"
                    >
                        <input
                            type="text"
                            v-model.number="localCorrectScore"
                            @blur="checkScore"
                            @input="updateCorrectScore"
                        />
                    </div>
                </div>
                <!-- 批改模式，显示参考答案 -->
                <div class="reference-answer-display" v-if="topic.answer">
                    <span class="ref-label">{{ lang.homework.reference_answer }}: </span>
                    <span class="ref-content">{{ topic.answer }}</span>
                </div>
            </div>
        </template>

        <!-- Pass模式下的批改界面 -->
        <template v-else-if="type === CLOUD_TEST_TYPE.CORRECT && isPassMode">
            <div class="correcting_item_wrapper pass-mode">
                <div class="pass-correcting-item">
                    <div
                        class="pass-btn"
                        @click="passSelect(true)"
                        :class="{ active: localIsPassed === true }"
                    >
                        通过
                    </div>
                    <div
                        class="fail-btn"
                        @click="passSelect(false)"
                        :class="{ active: localIsPassed === false }"
                    >
                        不通过
                    </div>
                </div>

                <!-- 评论框 -->
                <div class="comment-wrapper">
                    <label>Comment (可选)：</label>
                    <el-input
                        v-model="localComment"
                        type="textarea"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                        placeholder="请输入评论"
                        @input="updateComment"
                    ></el-input>
                </div>
            </div>

            <!-- 批改模式，显示参考答案 -->
            <div class="reference-answer-display pass-mode-answer" v-if="topic.answer">
                <span class="ref-label">{{ lang.homework.reference_answer }}: </span>
                <span class="ref-content">{{ topic.answer }}</span>
            </div>
        </template>

        <div class="reference-answer" v-else-if="type === CLOUD_TEST_TYPE.EDIT">
            <!-- 编辑模式，可编辑参考答案 -->
            <span class="ref-label">{{ lang.homework.reference_answer }}: </span>
            <textarea-field
                :value="topic.answer"
                :placeholder="lang.input_enter_tips"
                @change="(val) => handleFieldChange('answer', val)"
            />
        </div>
        <div v-else class="reference-answer display-only">
            <template v-if="topic.answer">
                <span class="ref-label">{{ lang.homework.reference_answer }}: </span>
                <span class="ref-content">{{ topic.answer }}</span>
            </template>
        </div>
        <image-viewer ref="imageViewer"></image-viewer>
    </div>
</template>

<script>
import base from "../../../lib/base";
import TextField from "./TextField.vue";
import TextareaField from "./TextareaField.vue";
import viewList from "./viewList.vue";
import imageViewer from "../../../MRComponents/imageViewer";
import AnswerRecords from "./AnswerRecords.vue";
import { CLOUD_TEST_TYPE } from '../../../lib/constants';

export default {
    name: "ShortAnswerTopic",
    components: {
        TextField,
        TextareaField,
        viewList,
        imageViewer,
        AnswerRecords,
    },
    mixins: [base],
    props: {
        topic: {
            type: Object,
            required: true,
        },
        type: {
            type: Number,
            required: true,
        },
        topicTypeIndex: {
            type: Number,
            required: true,
        },
        topicIndex: {
            type: Number,
            required: true,
        },
        disableModify: {
            type: Boolean,
            default: true,
        },
        isPassMode: {
            type: Boolean,
            default:false,
        },
    },
    data() {
        return {
            localValue: this.topic.value || "",
            localCorrectScore: this.topic.correctScore || null,
            localIsPassed: this.topic.isPassed !== undefined ? this.topic.isPassed : null,
            localComment: this.topic.comment || "",
            CLOUD_TEST_TYPE: CLOUD_TEST_TYPE,
        };
    },
    computed: {
        getTopicCount() {
            if (this.isPassMode) {
                return ''; // Pass模式下不显示分数
            }
            let topicCount = this.lang.topic_count;
            topicCount = topicCount.replace("{a}", this.topic.score);
            return topicCount;
        },
    },
    watch: {
        "topic.value": function (newVal) {
            this.localValue = newVal;
        },
        "topic.correctScore": function (newVal) {
            this.localCorrectScore = newVal;
        },
        "topic.isPassed": function (newVal) {
            this.localIsPassed = newVal;
        },
        "topic.comment": function (newVal) {
            this.localComment = newVal || "";
        },
    },
    methods: {


        handleFieldChange(field, value) {
            this.$emit("field-change", this.topic, field, value);
        },
        deleteTopic() {
            this.$emit("delete-topic", this.topicTypeIndex, this.topicIndex);
        },
        viewImage(imageList, index) {
            this.$refs.imageViewer.init(imageList, index);
        },
        deleteImage(index) {
            this.$emit("delete-image", this.topic.imageList, index);
        },
        updateValue(value) {
            this.$emit("update-value", this.topic, value);
        },
        updateCorrectScore(event) {
            const value = event.target.value !== "" ? Number(event.target.value) : null;
            this.$emit("update-correct-score", this.topic, value);
        },
        correctSelect(type) {
            let score = 0;
            if (type === 0) {
                score = 0;
            } else if (type === 1) {
                score = this.topic.score;
            }
            this.localCorrectScore = score;
            this.$emit("update-correct-score", this.topic, score);
        },
        checkScore() {
            const intScore = Number(this.localCorrectScore);
            if (isNaN(intScore) || intScore > this.topic.score || intScore < 0) {
                this.$message.error(this.lang.enter_correct_number);
            }
            if (!Number.isInteger(intScore)) {
                this.$message.error(this.lang.score_integer_tip);
            }
        },
        handleImageOrderChange(evt) {
            this.$emit("image-order-changed", this.topic.imageList, evt);
        },
        passSelect(isPassed) {
            this.localIsPassed = isPassed;
            this.$emit("update-pass-status", this.topic, isPassed);
        },
        updateComment(value) {
            this.localComment = value;
            this.$emit("update-comment", this.topic, value);
        },
    },
};
</script>

<style lang="scss" scoped>
.ShortAnswerTopic_wrapper {
    .topic_title {
        display: flex;
        align-items: center;

        :deep(.delete-topic-btn) {
            margin-left: 10px;
            padding: 0;
            font-size: 18px;
            color: #f56c6c;

            &:hover {
                color: #fff;
                background: #ff4d4d;
            }
        }
        .topic_operation {
            display: flex;
            align-items: center;
            margin-left: 10px;
            padding-right: 10px;
        }

        .topic_pass_status {
            .pass-status {
                display: inline-block;
                padding: 2px 8px;
                border-radius: 4px;
                text-align: center;
                font-weight: bold;

                &.passed {
                    background-color: #00c59d;
                    color: #fff;
                }

                &.failed {
                    background-color: #f56c6c;
                    color: #fff;
                }
            }
        }
    }

    .topic_image_list {
        margin-left: 20px;
    }

    .topic_radio {
        margin: 16px 0;
        display: flex;
        flex-direction: column;
        position: relative;
        cursor: pointer;

        :deep(.el-textarea__inner) {
            border-color: #ebeff2;
            background: #ebeff2;
            resize: none;
            &:focus {
                border: 1px solid #00c59d;
            }
        }
        :deep(.el-textarea.is-disabled .el-textarea__inner) {
            color: #333;
        }
    }

    .correcting_item_wrapper {
        border-top: 1px dashed #606266;
        display: flex;
        gap: 10px;
        padding-top: 20px;
        .reference-answer-display {
            display: grid;
            grid-template-columns: auto 1fr;
            align-items: center;
            margin-left: 20px;

            .ref-label {
                color: #666;
                margin-right: 8px;
                white-space: nowrap;
            }

            .ref-content {
                color: #ff9900;
                word-break: break-word;
            }
        }
    }

    .correcting_item {
        position: static;
        display: flex;
        flex-direction: row;
        gap: 10px;

        > div {
            width: 60px;
            height: 60px;
            margin: 20px 0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fff;
            text-align: center;
            cursor: pointer;
            box-sizing: border-box;
        }

        .full_score {
            color: #00c59d;
            border: 1px solid #00c59d;

            &.active {
                color: #fff;
                background-color: #00c59d;
            }
        }

        .empty_score {
            color: #f00;
            border: 1px solid #f00;

            &.active {
                color: #fff;
                background-color: #f00;
            }
        }

        .current_score {
            border: 1px solid #00c59d;
            color: #00c59d;
            input {
                width: 80%;
                text-align: center;
                background: #eceff2;
                border-radius: 10px;
                border: none;
            }
            &.active {
                color: #fff;
                background-color: #00c59d;
                input {
                    background-color: #4cd6ba;
                    color: #fff;
                }
            }
        }
    }

    .reference-answer {
        display: flex;
        align-items: center;
        margin-top: 10px;

        .ref-label {
            margin-right: 8px;
            flex-shrink: 0;
        }
    }

    .topic-index {
        white-space: nowrap;
        margin-right: 8px;
    }
    .topic_count {
        display: flex;
        color: #999;
        align-items: center;
        flex-shrink: 0;
        :deep(.editable-content) {
            display: inline-block;
            flex: initial;
            margin-right: 0px;

            input {
                width: 32px;
                height: 32px;
                padding: 0 4px;
                text-align: right;

                // 隐藏number类型输入框的上下箭头
                &::-webkit-outer-spin-button,
                &::-webkit-inner-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }
                &[type="number"] {
                    -moz-appearance: textfield;
                }
            }
        }
    }
    .pass-mode {
        border-top: 1px dashed #606266;
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 10px;

        .pass-correcting-item {
            display: flex;
            gap: 10px;
            flex-shrink: 0;

            .pass-btn, .fail-btn {
                width: 60px;
                height: 60px;
                margin: 0;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #fff;
                text-align: center;
                cursor: pointer;
                box-sizing: border-box;
            }

            .pass-btn {
                color: #00c59d;
                border: 1px solid #00c59d;

                &.active {
                    color: #fff;
                    background-color: #00c59d;
                }
            }

            .fail-btn {
                color: #f00;
                border: 1px solid #f00;

                &.active {
                    color: #fff;
                    background-color: #f00;
                }
            }
        }
    }

    .comment-wrapper {
        display: flex;
        flex-direction: column;
        gap: 10px;
        flex: 1;
        min-width: 0; /* 防止flex项目溢出 */
        padding-top: 20px;
        margin-left:20px;
        label {
            color: #666;
            margin-right: 8px;
            white-space: nowrap;
        }

        :deep(.el-textarea__inner) {
            border-color: #ebeff2;
            background: #ebeff2;
            resize: none;
            &:focus {
                border: 1px solid #00c59d;
            }
        }
        :deep(.el-textarea.is-disabled .el-textarea__inner) {
            color: #333;
        }
    }

    .pass-mode-answer {
        margin-top: 15px;
        width: 100%;
        display: flex;
        flex-direction: column;
        border-top: 1px dashed #e0e0e0;
        padding-top: 15px;

        .ref-label {
            color: #666;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .ref-content {
            color: #ff9900;
            word-break: break-word;
            line-height: 1.5;
            padding-left: 10px;
        }
    }
}
</style>
