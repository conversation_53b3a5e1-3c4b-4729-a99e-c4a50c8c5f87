<template>
    <transition name="slide">
        <div class="club_entry second_level_page">
            <mrHeader @click-left="back">
                <template #title>
                    Mindray Club
                </template>
            </mrHeader>
            <div class="container clearfix">
                <div class="club_item">
                    <img src="static/resource/images/club_bg_1.png">
                    <p>Consona Club</p>
                </div>
                <div class="club_item">
                    <img src="static/resource/images/club_bg_1.png">
                    <p>Nuewa Club</p>
                </div>
                <div class="club_item">
                    <img src="static/resource/images/club_bg_1.png">
                    <p>Resona Club</p>
                </div>
            </div>
            <router-view></router-view>
        </div>
    </transition>
</template>
<script>
import base from '../../lib/base';
export default {
    name:'clubEntry',
    mixins:[base],
    
    components:{
    },
    data(){
        return {
        }
    },
    computed:{
        
    },
    watch:{
        
    },
    mounted(){
        
    },
    beforeDestroy(){
        
    },
    methods:{
        
    }
}
</script>
<style lang="scss">
.club_entry{
    .container{
        overflow: auto;
    }
    .club_item{
        background: #f2f2f2;
        margin: .5rem .8rem;
        padding: .4rem 2rem;
        text-align: center;
        border-radius:.4rem;
        img{
            max-width: 100%;
        }
    }
    
}
</style>
