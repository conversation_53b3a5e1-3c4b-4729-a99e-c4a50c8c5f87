<template>
    <div class="qr-scan-action"></div>
</template>

<script>
import base from "../lib/base";
import Tool from "@/common/tool.js";
import { getLocalAvatar, openVisitingCard, parseImageListToLocal } from "../lib/common_base";

export default {
    mixins: [base],
    data() {
        return {
            getLocalAvatar,
            loading: false,
            inviteCode: '',
            inviterInfo: {},
            autoMakeFriend: false,
            from: '',
            groupInfo: {},
            status: 0,
        };
    },
    computed: {
        storeGlobalParams() {
            return this.$store.state.globalParams;
        },
        joinGroupState() {
            return this.$store.state.systemConfig.joinGroupState;
        },
        ConversationConfig() {
            return this.$store.state.systemConfig.ConversationConfig;
        },
        isApplying() {
            let applyFriendList = this.$store.state.relationship.applyFriendList || [];
            for (let id of applyFriendList) {
                if (id == this.inviterInfo.id) {
                    return true;
                }
            }
            return false;
        },
        isSelf() {
            return this.inviterInfo.id == this.user.uid;
        },
        isFriend() {
            let friendList = this.$store.state.friendList.list || [];
            for (let friend of friendList) {
                if (friend.id == this.inviterInfo.id) {
                    return true;
                }
            }
            return false;
        }
    },
    mounted() {
        this.$root.eventBus.$off("executeByAction").$on("executeByAction", this.executeByAction);
        this.$root.eventBus.$off("scanQRFromImage").$on("scanQRFromImage", this.scanQRFromImage);
    },
    methods: {
        openScanner() {
            let that = this;
            console.log("scanQRCode：enter");
            try {
                Tool.createCWorkstationCommunicationMng({
                    name: "scanQRCode",
                    emitName: "NotifyScanQRCode",
                    params: {},
                    timeout: null,
                }).then((res) => {
                    console.log("NotifyScanQRCode： ", res);
                    if (res.error_code == 0) {
                        if (res.status) {
                            let result = res.info;
                            var str = "";
                            if (result) {
                                var pos = result.indexOf("?");
                                if (pos != -1) {
                                    str = result.substr(pos + 1);
                                }
                            }
                            console.log("ScanQRConnect: " + str);
                            var data = {};
                            var strs = str.split("&");
                            for (let i = 0; i < strs.length; i++) {
                                data[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
                            }
                            this.executeByAction(data);
                        } else {
                            this.$message.error({
                                message: that.lang.unrecognized_qrcode,
                                showClose: true
                            });
                        }
                    } else if (res.error_code == 1) {
                        //扫码被取消了
                    } else {
                        this.$message.error({
                            message: that.lang.unrecognized_qrcode,
                            showClose: true
                        });
                    }
                });
            } catch (error) {
                this.$message.error({
                    message: error,
                    showClose: true
                });
            }
        },
        scanQRFromImage(imageUrl, qrCodeData) {
            let that = this;
            if (qrCodeData) {
                this.processQRCodeResult(qrCodeData);
                return;
            }
            that.$message.error({
                message: that.lang.unrecognized_qrcode,
                showClose: true
            });
        },
        processQRCodeResult(result) {
            console.log("处理二维码结果:", result);
            // 处理识别到的URL
            var str = "";
            if (result) {
                var pos = result.indexOf("?");
                if (pos != -1) {
                    str = result.substr(pos + 1);
                } else {
                    // 如果没有?，尝试使用整个URL
                    str = result;
                }
            }

            var data = {};

            // 解析参数
            if (str.includes("&")) {
                var strs = str.split("&");
                for (let i = 0; i < strs.length; i++) {
                    if (strs[i].includes("=")) {
                        data[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
                    }
                }
            } else if (str.includes("=")) {
                // 处理只有一个参数的情况
                data[str.split("=")[0]] = unescape(str.split("=")[1]);
            } else {
                // 处理没有参数的纯链接
                data.url = str;
            }

            // 调用executeByAction处理结果
            if (Object.keys(data).length > 0) {
                this.executeByAction(data);
            } else {
                this.$message.error({
                    message: this.lang.unrecognized_qrcode,
                    showClose: true
                });
            }
        },
        executeByAction(data) {
            const that = this;
            if (!data.act) {
                that.$message.error({
                    message: that.lang.unrecognized_qrcode,
                    showClose: true
                });
                return;
            }
            if (data.act == "con") {
                window.vm.$root.eventBus.$emit("ScanQRConnect", data);
            } else if (data.act == "add_friend") {
                that.loading = true;
                if (data.uid) {
                    const friendList = that.$store.state.friendList.list;
                    let user = {};
                    for (const friend of friendList) {
                        if (friend.id == data.uid) {
                            user = friend;
                            that.loading = false;
                            setTimeout(() => {
                                openVisitingCard(user, 2);
                            }, 50);
                            break;
                        }
                    }
                    // 扫码扫到的用户不在好友列表时发起数据请求
                    if (!user.id) {
                        that.requestDataById(1, data.uid, "query_user_basic_info", (err, user) => {
                            that.loading = false;
                            if (err) {
                                this.$message.error({
                                    message: that.no_found_user_text,
                                    showClose: true
                                });
                                return;
                            }
                            parseImageListToLocal([user], "avatar");
                            that.setDefaultImg([user]);
                            setTimeout(() => {
                                openVisitingCard(user, 2);
                            }, 50);
                        });
                    }
                } else {
                    this.$message.error({
                        message: that.lang.no_user_id,
                        showClose: true
                    });
                }
            } else if (data.act == "add_group") {
                this.doAddGroup(data);
            } else if (data.act == "login") {
                that.$router.push(`/index/scan_to_login/${data.id}`);
                return;
            } else {
                this.$message.error({
                    message: that.lang.unrecognized_qrcode,
                    showClose: true
                });
            }
        },
        requestDataById(type, id, url, cb) {
            // type=1 请求好友
            // type=2 请求群组
            let controller = window.main_screen.controller;
            controller.emit(url, { id }, (err, data) => {
                if (err) {
                    cb(err, null);
                } else {
                    cb(null, data);
                }
            });
        },
        doAddGroup(params) {
            const that = this;
            this.inviteCode = params.inviteCode;
            this.from = params.from || '';
            this.loading = true;

            if (!window.main_screen) {
                this.$message.error(this.lang.operate_err);
                this.loading = false;
                return;
            }

            // 第一步：获取群组信息
            window.main_screen.getInviteInfo({
                code: this.inviteCode
            }, (res) => {
                that.loading = false;
                if (res.error_code != 0) {
                    that.$message.error(that.lang.operate_err);
                    return;
                }

                const data = res.data;

                if (!data || !data.gid) {
                    that.$message.error(that.lang.operate_err);
                    return;
                }

                const gid = data.gid;
                const currentGroupInfo = data.groupInfo;

                if (!currentGroupInfo) {
                    that.$message.error(that.lang.operate_err);
                    return;
                }

                if (currentGroupInfo.is_obsolete) {
                    that.$message.error(that.lang.group_has_deleted_text);
                    return;
                }

                // 存储邀请者信息和群组信息
                that.inviterInfo = data.inviterInfo || {};
                that.groupInfo = currentGroupInfo;
                that.autoMakeFriend = data.autoMakeFriend;
                const serverJoinStatus = data.status || 0;

                // 根据服务器返回的群组加入状态判断是否已加入群组
                if (serverJoinStatus === that.joinGroupState.joined) {
                    that.$message(that.lang.group_has_joined);
                    const groupTemp = { id: gid, subject: currentGroupInfo.subject, is_single_chat: 0, avatar: currentGroupInfo.avatar, type: that.ConversationConfig.type.Group, service_type: that.systemConfig.ServiceConfig.type.None, is_public: currentGroupInfo.is_public };
                    that.setDefaultImg([groupTemp]);
                    parseImageListToLocal([groupTemp], "avatar");
                    that.$root.updateGroupAvatarUserList[gid] = groupTemp;
                    that.$store.commit("groupList/addGroup", groupTemp);
                    setTimeout(() => {
                        that.openConversation(gid, 5); 
                    }, 50);
                    return;
                }

                // 处理群组基本信息
                currentGroupInfo.is_single_chat = 0;
                parseImageListToLocal([currentGroupInfo], 'avatar');
                that.setDefaultImg([currentGroupInfo]);
                that.status = data.status || 0;

                // 第二步：检查用户是否已在群内
                const groupList = that.$store.state.groupList && that.$store.state.groupList.list ? that.$store.state.groupList.list : [];
                for (const item of groupList) {
                    if (item.id == gid) {
                        setTimeout(() => {
                            that.openConversation(gid, 5);
                        }, 50);
                        return;
                    }
                }

                // 第三步：用户不在群内，申请加入
                const source = that.from === 'weChat' ? 4 : 3;
                const is_public = currentGroupInfo.is_public;

                that.loading = true;
                window.main_screen.applyJoinGroup({
                    mark: '',
                    gid: gid,
                    inviterID: that.inviterInfo.id,
                    source: source,
                }, (joinRes) => {
                    that.loading = false;
                    console.warn('joinRes', joinRes)
                    if(joinRes.key == 'qrcode_expired'){
                        // 不在这里弹窗，会根据key自动弹窗
                        return;
                    }
                    if (joinRes.error_code == 0) {            
                        // 1st，如果满足条件，则发送好友请求
                        if (that.autoMakeFriend && !that.isApplying && !that.isFriend && !that.isSelf) {
                            that.requestAddFriend();
                        }

                        // 2nd，根据是否需要审核来处理群组加入逻辑
                        if (currentGroupInfo.more_details && currentGroupInfo.more_details.join_check) {
                            const conversation = that.$store.state.conversationList[gid];
                            let isCreator = false;
                            let isManager = false;
                            if (conversation) {
                                isCreator = conversation.creator_id == that.inviterInfo.id;
                                for(let key in conversation.attendeeList){
                                    let item = conversation.attendeeList[key];
                                    if (item.role == that.$store.state.systemConfig.groupRole.manager && item.userid == that.inviterInfo.id) {
                                        isManager = true;
                                        break;
                                    }
                                }
                            }
                            if(!isCreator && !isManager){
                                that.$message.success(that.lang.group_apply_success);
                            }else{
                                that.$message.success(that.lang.add_group_successful);
                            }
                        } else {
                            // 免审批，直接加入
                            try {
                                const groupTemp = {
                                    id: gid,
                                    subject: currentGroupInfo.subject,
                                    is_single_chat: 0,
                                    avatar: currentGroupInfo.avatar,
                                    avatar_local: currentGroupInfo.avatar_local,
                                    type: that.ConversationConfig.type.Group,
                                    service_type: that.systemConfig.ServiceConfig.type.None,
                                    is_public: is_public,
                                };
                                that.setDefaultImg([groupTemp]);
                                parseImageListToLocal([groupTemp], "avatar");
                                that.$root.updateGroupAvatarUserList[gid] = groupTemp;
                                that.$store.commit("groupList/addGroup", groupTemp);
                                that.$message.success(that.lang.add_group_successful);

                                setTimeout(() => {
                                    that.openConversation(gid, 5);
                                }, 50);
                            } catch (error) {
                                that.$message.error(that.lang.operate_err);
                            }
                        }
                    } else {
                        if (joinRes.error_msg === 'conversationJoinError:repeat' ||
                            (joinRes.error_msg && joinRes.error_msg.includes('repeat'))) {
                            this.$message(that.lang.group_has_joined);

                            // 更新群组列表
                            const groupTemp = {
                                id: gid,
                                subject: currentGroupInfo.subject,
                                is_single_chat: 0,
                                avatar: currentGroupInfo.avatar,
                                avatar_local: currentGroupInfo.avatar_local,
                                type: that.ConversationConfig.type.Group,
                                service_type: that.systemConfig.ServiceConfig.type.None,
                                is_public: is_public,
                            };
                            that.setDefaultImg([groupTemp]);
                            parseImageListToLocal([groupTemp], "avatar");
                            that.$root.updateGroupAvatarUserList[gid] = groupTemp;
                            that.$store.commit("groupList/addGroup", groupTemp);
                            // 直接跳转到群组会话，不显示错误
                            setTimeout(() => {
                                that.openConversation(gid, 5);
                            }, 50);
                            return;
                        }
                        that.$message.error(that.lang.operate_err);
                    }
                });
            });
        },
        requestAddFriend() {
            var user = this.inviterInfo;
            this.$root.socket.emit("request_add_friend", { id: user.id });
            this.$store.commit("relationship/addApplyFriend", user);
        }
    },
};
</script>
<style lang="scss" scoped>
</style>
