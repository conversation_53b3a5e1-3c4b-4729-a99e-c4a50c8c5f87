<template>
    <div>
        <popover name="menu" event="longtap" :show="menuDatas.length > 0" @hide="menuDatas = []">
            <tooltips-menu
                :menuDatas="menuDatas"
                @copyText="copyText"
                @revocationMessage="revocationMessage"
                @multiSelectImage="multiSelectImage"
                @setResourceExamInfo="setResourceExamInfo"
                @shareToWechat="shareToWechat"
                @saveFavorite="openFavoriteSelectAction"
                @transmit="transmit"
                @sendToAnalyze="sendToAnalyzeFn"
                @deleteChatMessages="deleteChatMessages"
                @shareToEmail="shareToEmail"
                @editReviewInfo="editReviewInfo"
                @searchInCaseData="searchInCaseData"
                @imageRename="imageRename"
                @deleteExam="handleDeleteExam"
                @quoteMessage="quoteMessage"
                @locateOriginalMessage="locateOriginalMessage"
            />
        </popover>
        <ImageRenameDialog :message="currentFile" v-model="imageRenameVisible"> </ImageRenameDialog>
        <van-action-sheet
            v-model="isShowSelectFavoriteType"
            :actions="selectFavoriteTypeActions"
            @select="onSelectFavoriteType"
            close-on-click-action
            :cancel-text="lang.cancel_btn"
            @cancel="isShowSelectFavoriteType = false"
        />
        <ReviewEditDialog :message="currentFile" ref="reviewEditDialog"></ReviewEditDialog>
        <CommonDialog
            v-model="groupFavoriteDialog"
            :title="lang.group_favorite_text"
            :showRejectButton="true"
            :beforeClose="submitInsertGroupFavorite"
        >
            <group-favorite
                :edit="editMode"
                :cid="cid"
                ref="chatMessageListGroupFavorite"
                v-if="groupFavoriteDialog"
            ></group-favorite>
        </CommonDialog>
    </div>
</template>
<script>
import Tool from "@/common/tool";
import { Toast } from "vant";
import base from "../lib/base";
import tooltipsMenu from "../MRComponents/tooltipsMenu.vue";
import ImageRenameDialog from "./imageRenameDialog.vue";
import ReviewEditDialog from "./messageItem/reviewEditDialog.vue";
import GroupFavorite from "../components/groupFavorite.vue";

import CommonDialog from "../MRComponents/commonDialog.vue";
import { initImportExamImageTempQueue } from "../lib/common_send_message";
import { ActionSheet } from "vant";
import service from "../service/service";
import { sendToAnalyze, checkIsCreator, checkIsManager } from "../lib/common_base";
export default {
    props: {
        currentFile: {
            type: Object,
            default: () => {
                return {};
            },
        },
        value: {
            type: Boolean,
            default: false,
        },
    },
    mixins: [base],
    components: {
        VanActionSheet: ActionSheet,
        tooltipsMenu,
        ImageRenameDialog,
        ReviewEditDialog,
        GroupFavorite,
        CommonDialog,
    },
    computed: {
        conversation() {
            return this.$store.state.conversationList[this.cid] || {};
        },
        cid() {
            return this.$route.query.cid || this.$route.params.cid;
        },
    },
    data() {
        return {
            menuDatas: [],
            imageRenameVisible: false,
            isShowSelectFavoriteType: false,
            selectFavoriteTypeActions: [],
            groupFavoriteDialog: false,
            editMode: true,
            from: "",
        };
    },
    created() {
        this.selectFavoriteTypeActions = [
            { name: this.lang.personal_favorite_text, key: 0 },
            { name: this.lang.group_favorite_text, key: 1 },
        ];
    },
    methods: {
        showTooltipsMenu(msg, from) {
            if (msg.is_default_image) {
                return;
            }
            this.menuDatas = [];
            this.from = from;
            let isSelf = this.user.uid === msg.sender_id;
            console.log(msg, from);
            let isCreator = checkIsCreator(this.cid);
            let isManager = checkIsManager(this.cid);
            const msg_type = msg.msg_type;
            const msgType = this.systemConfig.msg_type;
            if (msg_type === msgType.AI_ANALYZE) {
                if (msg.ai_analyze && msg.ai_analyze.messages) {
                    this.$root.transmitTempList = msg.ai_analyze.messages || [];
                }
            } else {
                this.$root.transmitTempList = [msg];
            }
            if (this.$parent.$refs.message_text) {
                this.$parent.$refs.message_text.blur();
            }
            if (msg_type === msgType.Image || msg_type === msgType.Frame || msg_type === msgType.OBAI) {
                this.menuDatas = this.menuDatas.concat([1, 2]);
                if (from !== "examImageItem") {
                    this.menuDatas.push(4);
                }
                if (this.checkShowSendToAnalyze(msg)) {
                    this.menuDatas.push(0);
                }
                if (this.checkShowImageSearch()) {
                    this.menuDatas.push(11);
                }
                if (msg.gmsg_id && from === "chatComponent") {
                    this.menuDatas.push(14);
                }
                if (this.checkShowItemWhenHasPermission({ isSelf, isCreator, isManager })) {
                    if (this.checkShowWithDraw(msg)) {
                        this.menuDatas.push(7);
                    } else {
                        this.menuDatas.push(9);
                    }
                    this.menuDatas.push(12);
                }
                if (this.isShowWechat) {
                    this.menuDatas.push(3);
                }
            } else if (msg_type == msgType.TAG || msg_type == msgType.COMMENT) {
                if (isSelf) {
                    if (this.checkShowWithDraw(msg)) {
                        this.menuDatas.push(7);
                    }
                }
            } else if (msg_type === msgType.Video || msg_type === msgType.Cine) {
                this.menuDatas = this.menuDatas.concat([1, 2]);
                if (from !== "examImageItem") {
                    this.menuDatas.push(4);
                }
                if (msg.gmsg_id && from === "chatComponent") {
                    this.menuDatas.push(14);
                }
                if (this.checkShowItemWhenHasPermission({ isSelf, isCreator, isManager })) {
                    if (this.checkShowWithDraw(msg)) {
                        this.menuDatas.push(7);
                    } else {
                        this.menuDatas.push(9);
                    }
                    this.menuDatas.push(12);
                }
                if (this.isShowWechat) {
                    this.menuDatas.push(3);
                }
            } else if (msg_type === msgType.RealTimeVideoReview) {
                this.menuDatas = this.menuDatas.concat([1, 2]);
                isSelf = this.user.uid === msg.live_record_data.creator_id;
                if (msg.gmsg_id && from === "chatComponent") {
                    this.menuDatas.push(14);
                }
                if (this.checkShowItemWhenHasPermission({ isSelf, isCreator, isManager })) {
                    if (this.checkShowWithDraw(msg)) {
                        this.menuDatas.push(7);
                    } else {
                        this.menuDatas.push(9);
                    }
                    this.menuDatas.push(6);
                }
                if (this.isShowWechat) {
                    this.menuDatas.push(3);
                }
            } else if (msg_type === msgType.VIDEO_CLIP) {
                this.menuDatas = this.menuDatas.concat([1, 2]);
                if (msg.gmsg_id && from === "chatComponent") {
                    this.menuDatas.push(14);
                }
                if (this.checkShowItemWhenHasPermission({ isSelf, isCreator, isManager })) {
                    if (this.checkShowWithDraw(msg)) {
                        this.menuDatas.push(7);
                    } else {
                        this.menuDatas.push(9);
                    }
                }
                if (this.isShowWechat) {
                    this.menuDatas.push(3);
                }
            } else if (msg_type === msgType.LIVE_INVITE) {
                this.menuDatas.push(1);
                if (this.checkShowItemWhenHasPermission({ isSelf, isCreator, isManager })) {
                    if (this.checkShowWithDraw(msg)) {
                        this.menuDatas.push(7);
                    } else {
                        this.menuDatas.push(9);
                    }
                }
            } else if (msg_type === msgType.IWORKS_PROTOCOL) {
                this.menuDatas.push(1);
                if (this.checkShowItemWhenHasPermission({ isSelf, isCreator, isManager })) {
                    if (this.checkShowWithDraw(msg)) {
                        this.menuDatas.push(7);
                    } else {
                        this.menuDatas.push(9);
                    }
                }
            } else if (msg_type === msgType.File) {
                if (!msg.uploading) {
                    if (!this.checkFileExpired(msg)) {
                        this.menuDatas.push(1);
                    }
                    if (this.checkShowItemWhenHasPermission({ isSelf, isCreator, isManager })) {
                        if (this.checkShowWithDraw(msg)) {
                            this.menuDatas.push(7);
                        } else {
                            this.menuDatas.push(9);
                        }
                    }
                }
            } else if (msg_type === msgType.AI_ANALYZE) {
                if (true) {
                    if (!msg.been_withdrawn && msg.resource_id) {
                        this.menuDatas.push(1);
                    }
                    if (isSelf) {
                        if (this.checkShowWithDraw(msg)) {
                            this.menuDatas.push(7);
                        }
                    }
                }
            } else if (msg_type === msgType.EXAM_IMAGES) {
                this.menuDatas = this.menuDatas.concat([1, 10]);
                if (this.checkShowSendToAnalyze(msg)) {
                    this.menuDatas.push(0);
                }
                if (this.isShowWechat) {
                    this.menuDatas.push(3);
                }
            } else if (msg_type === msgType.Text) {
                this.menuDatas.push(8);
                if (msg.gmsg_id && from === "chatComponent") {
                    this.menuDatas.push(14);
                }
                if (isSelf) {
                    if (this.checkShowWithDraw(msg)) {
                        this.menuDatas.push(7);
                    }
                }
            } else if (msg_type === msgType.Sound) {
                if (isSelf) {
                    if (this.checkShowWithDraw(msg)) {
                        this.menuDatas.push(7);
                    }
                }
            } else if (msg_type === msgType.HOMEWORK_DETAIL || msgType.HOMEWORK_DETAIL_INDIVIDUAL) {
                this.menuDatas.push(1);
                if (isSelf) {
                    if (this.checkShowWithDraw(msg)) {
                        this.menuDatas.push(7);
                    }
                }
            }
        },
        showQuoteMessageMenu(msg, from) {
            this.menuDatas = [15];
            this.from = from;
            this.$root.transmitTempList = [msg];
        },
        checkShowSendToAnalyze(msg) {
            let isNotAiAnalyzeServiceType = false;
            if (this.conversation) {
                isNotAiAnalyzeServiceType =
                    this.$store.state.systemConfig.user_service_type.AiAnalyze != this.conversation.service_type;
            }
            if (msg && (msg.been_withdrawn || !msg.resource_id)) {
                return false;
            }
            return (
                this.functionsStatus.breastAI &&
                this.systemConfig.serverInfo.enable_ai_analyze &&
                !this.globalParams.isCE &&
                isNotAiAnalyzeServiceType
            );
        },
        checkShowImageSearch() {
            let enable_ai_search =
                this.$store.state.systemConfig.serverInfo.ai_searcher_server &&
                this.$store.state.systemConfig.serverInfo.ai_searcher_server.enable &&
                !this.globalParams.isCE;
            return this.functionsStatus.breastCases && enable_ai_search;
        },
        checkShowItemWhenHasPermission({ isSelf, isCreator, isManager }) {
            console.log({ isSelf, isCreator, isManager });
            if (isSelf || isCreator || isManager) {
                return true;
            }
            return false;
        },
        checkShowWithDraw(msg) {
            //撤回和删除显示逻辑
            let showWithdrawMessage = false;
            if (this.from !== "chatComponent") {
                return showWithdrawMessage;
            }
            let isSelf = this.user.uid === msg.sender_id;
            if (!isSelf) {
                return showWithdrawMessage;
            }
            if (msg && Object.keys(msg).length > 0) {
                let msgTimestamp = new Date(msg.send_ts).getTime();
                let nowTimestamp = new Date().getTime();
                let isInnerTime = nowTimestamp - msgTimestamp < this.systemConfig.serverInfo.msg_withdrawal_max_time;
                if (isInnerTime) {
                    showWithdrawMessage = true;
                }
            }
            return showWithdrawMessage;
        },
        showExamItemToolTips(msg) {
            let isCreator = checkIsCreator(this.cid);
            let isManager = checkIsManager(this.cid);
            let isSender = false;
            if (Array.isArray(msg.sender_nickname)) {
                msg.sender_nickname.forEach((item) => {
                    if (item.sender_id == this.user.uid) {
                        isSender = true;
                    }
                });
            }
            console.log(msg, 11);
            if (msg.hasOwnProperty("examStatus") && msg.examStatus !== 0) {
                //多中心的病例，非未提交不允许删除
            } else {
                if (isSender || isCreator || isManager) {
                    this.menuDatas.push(13);
                }
            }
        },
        sendToAnalyzeFn() {
            this.menuDatas = [];
            sendToAnalyze();
        },
        handleDeleteExam() {
            console.error(this.currentFile);
            Tool.openMobileDialog({
                message: this.lang.delete_exam_tips,
                showRejectButton: true,
                confirm: () => {
                    this.deleteExam(this.currentFile);
                },
            });
        },
        deleteExam(file) {
            let params = {
                exam_id: file.exam_id,
            };
            let cid = file.group_id || this.cid;
            window.main_screen.conversation_list[cid].deleteExam(params, (res) => {
                if (res.error_code !== 0) {
                    console.error(res);
                    Toast(this.lang[res.error_msg] || this.lang.delete_case_fail);
                }
            });
        },
        imageRename() {
            this.imageRenameVisible = true;
            this.menuDatas = [];
        },
        searchInCaseData() {
            let list = this.$root.transmitTempList;
            if (list && list.length > 1) {
                Toast(this.lang.searc_in_case_database_many_picture);
            } else if (list && list.length == 1) {
                let msg = list[0];
                let islegal = Tool.isLegalForematForSearchImage(msg);
                if (islegal) {
                    let pattern = new RegExp("^data:image.*", "i");
                    msg.realUrl = msg.realUrl && pattern.test(msg.realUrl) ? "" : msg.realUrl;
                    let searchParams = {
                        type: "url", //text,url,file
                        content: msg,
                    };
                    this.$store.commit("caseDatabase/updateSearchParams", searchParams);
                    this.$router.push({ path: `/index/chat_window/${this.cid}/case_database` });
                } else {
                    Toast(this.lang.picture_is_only_jpg_jpeg_bmp_png);
                }
            } else {
                Toast(this.lang.searc_in_case_database_one_picture);
            }
        },
        copyText() {
            this.menuDatas = [];
            let text = Tool.replaceHtmlTag(this.currentFile.msg_body);
            console.log(text);
            if (Tool.checkAppClient("IOS") && !Tool.checkAppClient("Browser")) {
                window.CWorkstationCommunicationMng.setClipboard({ str: text });
                Toast(this.lang.text_has_copied);
            } else {
                Tool.copyToClipboard(text);
                Toast(this.lang.text_has_copied);
            }
        },
        revocationMessage() {
            // 撤回消息前判断
            let msg = this.$root.transmitTempList[0];
            let msgTimestamp = new Date(msg.send_ts).getTime();
            let nowTimestamp = new Date().getTime();
            if (nowTimestamp - msgTimestamp > this.systemConfig.serverInfo.msg_withdrawal_max_time) {
                // 超出可撤回时间内
                Toast(`${this.lang.exceeded_max_withdrawal}`);
                return;
            } else {
                // 可撤回
                this.tryToWithDrawMessages(this.conversation.id, [msg]);
            }
            this.menuDatas = [];
        },
        onSelectFavoriteType(item) {
            if (item.key === 0) {
                this.saveFavorite();
            } else {
                this.groupFavoriteDialog = true;
            }
        },
        openFavoriteSelectAction() {
            this.menuDatas = [];
            this.isShowSelectFavoriteType = true;
        },
        setResourceExamInfo() {
            var that = this;
            let list = this.$root.transmitTempList;
            this.menuDatas = [];
            initImportExamImageTempQueue(list, function (err) {
                if (!err) {
                    that.$router.push(that.$route.fullPath + "/exam_manager");
                }
            });
        },
        editReviewInfo() {
            let msg = this.$root.transmitTempList[0];
            service
                .checkActionPermissions({
                    action: "conference.update.recording",
                    businessData: {
                        resource_id: msg.resource_id,
                        group_id: this.cid,
                    },
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        if (!res.data.data.hasPermission) {
                            Toast(this.lang.no_permission_operate);
                            return;
                        }
                        this.$nextTick(() => {
                            this.$refs[`reviewEditDialog`].openEditTargetModal();
                        });
                    } else {
                        Toast(this.lang[res.data.key]);
                    }
                })
                .catch((error) => {
                    console.error(error);
                });

            this.menuDatas = [];
        },
        saveFavorite() {
            this.menuDatas = [];
            window.vm.$root.eventBus.$emit("initFavoriteConfirm", this.$root.transmitTempList);
        },
        shareToWechat() {
            this.menuDatas = [];
            this.$root.eventBus.$emit("shareToWechatHandler", this.$root.transmitTempList);
        },
        async submitInsertGroupFavorite(action, done) {
            console.log(action, done);
            if (action === "confirm") {
                try {
                    await this.$refs["chatMessageListGroupFavorite"].insertResourceToCategory(
                        this.$root.transmitTempList[0]
                    );
                    done();
                } catch (error) {
                    done(false);
                }
            } else {
                done();
            }
        },
        multiSelectImage() {
            this.menuDatas = [];
            this.$emit("multiSelectImage");
        },
        deleteChatMessages() {
            this.menuDatas = [];
            let currentFile = this.$root.transmitTempList[0];
            // this.tryToDeleteMessages(this.conversation.id, list);
            let gmsg_id = 0;
            if (this.from === "chatComponent") {
                gmsg_id = currentFile.gmsg_id;
            }
            this.deleteResourceByGroupId(currentFile, gmsg_id);
        },

        transmit() {
            this.menuDatas = [];
            if (this.currentFile.msg_type === this.systemConfig.msg_type.HOMEWORK_DETAIL) {
                service
                    .getanswerSheetByAssignmentID({
                        assignmentID: this.currentFile.assignmentInfo._id,
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            const assignmentInfo = res.data.data.assignmentInfo;
                            if (assignmentInfo && assignmentInfo.dueTime) {
                                if (Date.now() > assignmentInfo.dueTime) {
                                    // 检查当前用户是否为群主和管理员，只有管理者可以修改作业配置
                                    const isCreator = checkIsCreator(this.cid);
                                    const isManager = checkIsManager(this.cid);
                                    if (isCreator || isManager) {
                                        Tool.openMobileDialog({
                                            message: this.lang.exam_overdue_creator_tip,
                                            showRejectButton: true,
                                            confirm: () => {
                                                this.$store.commit("homework/setCurrentPaper", assignmentInfo);
                                                this.$router.push({
                                                    path: `/index/chat_window/${this.cid}/cloud_exam/correcting_exam/student/exam_setting`,
                                                });
                                            },
                                        });
                                    } else {
                                        Toast(this.lang.overdue_assignment_transmit_tip);
                                    }
                                    return;
                                }
                            }
                        }
                        this.$router.push(`/index/chat_window/${this.cid}/transmit`);
                    });
            } else {
                this.$router.push(`/index/chat_window/${this.cid}/transmit`);
            }
        },
        shareToEmail() {
            let msg = this.$root.transmitTempList[0];
            let resource_ids = [];
            for (let item of msg.resourceList) {
                resource_ids.push(item.id);
            }
            this.$router.push(`/index/chat_window/${this.cid}/share_link/${resource_ids.join()}/2`);
        },
        quoteMessage() {
            // 引用消息
            this.menuDatas = [];
            let msg = this.$root.transmitTempList[0];

            // 检查消息是否有 gmsg_id
            if (!msg || !msg.gmsg_id) {
                return;
            }

            // 将消息数据传递给事件总线，以便在输入框显示引用
            this.$root.eventBus.$emit("quoteMessage", msg);
        },
        locateOriginalMessage() {
            this.menuDatas = [];
            let msg = this.$root.transmitTempList[0];
            this.$emit("locateOriginalMessage", msg);
        },
    },
};
</script>
